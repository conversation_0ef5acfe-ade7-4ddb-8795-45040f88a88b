#!/usr/bin/env python3
"""
Test Real AI Vision System
"""

import sys
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw
import io

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def create_realistic_food_image():
    """Create a more realistic food image for testing"""
    # Create a 400x400 image with white background
    img = Image.new('RGB', (400, 400), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw a realistic red apple
    draw.ellipse([50, 50, 200, 200], fill='#FF4444', outline='#CC0000', width=3)
    # Add apple highlight
    draw.ellipse([80, 70, 120, 110], fill='#FF8888')
    
    # Draw a realistic banana
    banana_points = [(220, 100), (350, 120), (360, 160), (230, 140)]
    draw.polygon(banana_points, fill='#FFDD44', outline='#DDAA00', width=2)
    
    # Draw some broccoli
    draw.ellipse([100, 250, 180, 330], fill='#228B22', outline='#006400', width=2)
    draw.ellipse([120, 270, 160, 310], fill='#32CD32')
    draw.ellipse([110, 260, 130, 280], fill='#32CD32')
    draw.ellipse([150, 290, 170, 310], fill='#32CD32')
    
    # Add some texture with small dots
    for i in range(20):
        x = np.random.randint(0, 400)
        y = np.random.randint(0, 400)
        draw.ellipse([x, y, x+2, y+2], fill='#DDDDDD')
    
    return img

def test_real_ai_vision():
    """Test the real AI vision system"""
    print("🤖 Testing Real AI Vision System")
    print("=" * 40)
    
    try:
        from agents.nutritional_analyzer.real_ai_vision import RealFoodAI
        
        # Initialize real AI
        print("🔧 Initializing Real AI...")
        real_ai = RealFoodAI()
        print("✅ Real AI initialized successfully")
        
        # Create test image
        print("\n📸 Creating realistic test food image...")
        test_image = create_realistic_food_image()
        
        # Convert to bytes
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='JPEG')
        image_bytes = img_buffer.getvalue()
        
        print(f"✅ Test image created ({len(image_bytes)} bytes)")
        
        # Test food recognition
        print("\n🔍 Running AI food recognition...")
        detected_foods = real_ai.recognize_food(image_bytes)
        
        print(f"\n📊 AI Recognition Results:")
        print("=" * 30)
        
        if detected_foods:
            for i, food in enumerate(detected_foods):
                print(f"\n🍽️ Food {i+1}:")
                print(f"   Name: {food.get('label', 'Unknown')}")
                print(f"   Confidence: {food.get('confidence', 0):.2%}")
                print(f"   Method: {food.get('detection_method', 'AI model')}")
                
                # Test nutrition data
                nutrition = real_ai.get_nutrition_data(food['label'], 150)
                if nutrition and 'nutrition' in nutrition:
                    nutr = nutrition['nutrition']
                    print(f"   🔥 Calories (150g): {nutr.get('calories', 'N/A')}")
                    print(f"   🥚 Protein: {nutr.get('protein_g', 'N/A')}g")
                    print(f"   🥑 Fat: {nutr.get('fat_g', 'N/A')}g")
                    print(f"   🍞 Carbs: {nutr.get('carbohydrates_g', 'N/A')}g")
        else:
            print("❌ No foods detected")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Real AI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_nutritional_analyzer():
    """Test the enhanced nutritional analyzer with real AI"""
    print("\n🧪 Testing Enhanced Nutritional Analyzer")
    print("=" * 40)
    
    try:
        from agents.nutritional_analyzer.main import NutritionalAnalyzer
        
        # Initialize with real AI vision
        print("🔧 Initializing Enhanced Analyzer...")
        analyzer = NutritionalAnalyzer(use_real_apis=False, use_real_ai_vision=True)
        print("✅ Enhanced analyzer initialized")
        
        # Create test image
        test_image = create_realistic_food_image()
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='JPEG')
        image_bytes = img_buffer.getvalue()
        
        # Test image analysis
        print("\n🔍 Analyzing image with enhanced system...")
        result = analyzer.analyze_food_from_image(image_bytes)
        
        print(f"\n📊 Enhanced Analysis Results:")
        print("=" * 30)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        if "image_analysis" in result:
            analysis = result["image_analysis"]
            print(f"🍽️ Detected foods: {analysis.get('detected_foods', 0)}")
            print(f"🤖 AI method: {result.get('ai_method', 'unknown')}")
            
            if "nutrition_results" in analysis:
                for i, nutrition in enumerate(analysis["nutrition_results"]):
                    print(f"\n🥘 Food {i+1}: {nutrition.get('food_name', 'Unknown')}")
                    print(f"   📏 Estimated portion: {nutrition.get('estimated_portion_g', 'N/A')}g")
                    print(f"   🎯 Detection confidence: {nutrition.get('detection_confidence', 'N/A'):.2%}")
                    print(f"   📊 Data source: {nutrition.get('data_source', 'N/A')}")
                    
                    if "nutrition" in nutrition:
                        nutr = nutrition["nutrition"]
                        print(f"   🔥 Calories: {nutr.get('calories', 'N/A')}")
                        print(f"   🥚 Protein: {nutr.get('protein_g', 'N/A')}g")
                        print(f"   🥑 Fat: {nutr.get('fat_g', 'N/A')}g")
                        print(f"   🍞 Carbs: {nutr.get('carbohydrates_g', 'N/A')}g")
            
            if "combined_nutrition" in result and result["combined_nutrition"]:
                combined = result["combined_nutrition"]
                if "total_nutrition" in combined:
                    total = combined["total_nutrition"]
                    print(f"\n🍽️ Combined Nutrition:")
                    print(f"   🔥 Total Calories: {total.get('calories', 'N/A')}")
                    print(f"   🥚 Total Protein: {total.get('protein_g', 'N/A')}g")
                    print(f"   🥑 Total Fat: {total.get('fat_g', 'N/A')}g")
                    print(f"   🍞 Total Carbs: {total.get('carbohydrates_g', 'N/A')}g")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced analyzer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparison():
    """Compare simple vs real AI detection"""
    print("\n⚖️ Comparing Simple vs Real AI Detection")
    print("=" * 40)
    
    try:
        from agents.nutritional_analyzer.main import NutritionalAnalyzer
        
        # Test image
        test_image = create_realistic_food_image()
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='JPEG')
        image_bytes = img_buffer.getvalue()
        
        # Simple detection
        print("🔍 Simple Detection:")
        simple_analyzer = NutritionalAnalyzer(use_real_apis=False, use_real_ai_vision=False)
        simple_result = simple_analyzer.analyze_food_from_image(image_bytes)
        
        if "image_analysis" in simple_result:
            simple_foods = len(simple_result["image_analysis"].get("nutrition_results", []))
            print(f"   Detected: {simple_foods} foods")
        
        # Real AI detection
        print("\n🤖 Real AI Detection:")
        ai_analyzer = NutritionalAnalyzer(use_real_apis=False, use_real_ai_vision=True)
        ai_result = ai_analyzer.analyze_food_from_image(image_bytes)
        
        if "image_analysis" in ai_result:
            ai_foods = len(ai_result["image_analysis"].get("nutrition_results", []))
            print(f"   Detected: {ai_foods} foods")
            print(f"   Method: {ai_result.get('ai_method', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Real AI Vision Test Suite")
    print("=" * 50)
    
    tests = [
        ("Real AI Vision", test_real_ai_vision),
        ("Enhanced Nutritional Analyzer", test_enhanced_nutritional_analyzer),
        ("Simple vs AI Comparison", test_comparison)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 Running: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Real AI vision is working!")
        print("\n📱 Your mobile app now has REAL AI food recognition!")
        print("   - Uses actual AI models for food detection")
        print("   - Provides accurate nutritional data")
        print("   - No more mock color-based detection")
        print("\n🚀 Restart your web server to use the new AI system!")
    else:
        print("\n⚠️ Some tests failed. The system will fall back to simple detection.")
        print("   This is normal if AI packages aren't fully installed.")
