# NutraCompass AI Ecosystem

NutraCompass is a modular AI ecosystem focused on **personalized nutrition** and **behavior change**. This repository contains multiple specialized agents (Food Analyst, Nutritionist, Behavioral Psychologist, etc.) that collaborate to deliver holistic insights and recommendations for healthier living.

## Table of Contents

- [Features](#features)
- [Project Structure](#project-structure)
- [Setup](#setup)
  - [1. <PERSON><PERSON> the Repository](#1-clone-the-repository)
  - [2. <PERSON>reate and Activate a Virtual Environment](#2-create-and-activate-a-virtual-environment)
  - [3. Install Dependencies](#3-install-dependencies)
  - [4. Update Configuration (Optional)](#4-update-configuration-optional)
  - [5. Run the Main Script](#5-run-the-main-script)
- [Agents Overview](#agents-overview)
- [Usage Workflow](#usage-workflow)
- [Future Enhancements](#future-enhancements)
- [Contributing](#contributing)
- [License](#license)

---

## Features

1. **Orchestrator (Lead Agent)**

   - Central coordination of all AI agents.
   - Direct user interaction (via text or speech) and response generation.
   - Dynamically routes user requests to the correct agent(s).

2. **Food Analyst**

   - Image recognition of food items.
   - Nutritional breakdown calculation.
   - Suggests recipe modifications.

3. **Nutritionist**

   - Creates personalized meal plans based on user goals.
   - Identifies nutritional deficiencies.

4. **Behavioral Psychologist**

   - Encourages and supports positive behavior change.
   - Generates motivational messages.

5. **Additional Agents** (Recipe Generator, Database Manager, Alert Manager, etc.)
   - Each has a specialized role to enrich the user experience.

---

## Project Structure

A typical layout of this repository might be:

```
The-Forbidden-Library/
├── README.md
├── requirements.txt
├── .gitignore
├── src/
│   ├── main.py
│   ├── config.py (optional)
│   ├── core/
│   │   └── orchestrator.py
│   └── agents/
│       ├── food_analyst.py
│       ├── nutritionist.py
│       ├── behavioral_psychologist.py
│       ├── database_manager.py
│       ├── recipe_generator.py
│       ├── health_tracker.py
│       ├── alert_manager.py
│       ├── restaurant_recommender.py
│       ├── culinary_guide.py
│       ├── external_api_integrator.py
│       ├── content_creator.py
│       └── tailor.py
└── tests/
    └── (test files go here)
```

> **Note**: Your structure may vary slightly depending on your development style.

---

## Setup

Follow these steps to get the NutraCompass AI Ecosystem running on your machine.

### 1. Clone the Repository

```bash
git clone https://github.com/YourUserName/The-Forbidden-Library.git
cd The-Forbidden-Library
```

### 2. Create and Activate a Virtual Environment

**Using Python’s built-in venv**:

```bash
python3 -m venv venv
source venv/bin/activate  # Mac/Linux
venv\Scripts\activate     # Windows
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

**Dependencies** may include:

- `openai`
- `transformers`
- `torch`
- `torchvision`
- `scikit-learn`
- `fastapi`
- `requests`
- `pydantic`
- `sqlite3` (built-in for Python, no extra install needed)

_(Adjust based on your actual `requirements.txt`.)_

### 4. Update Configuration (Optional)

- If you have a `config.py` or `.env` file, make sure to provide any API keys or environment-specific settings here.
- For example, if the **External API Integrator** agent needs an API key, add it to a `.env` or `config.py` and **never** commit secrets to version control.

### 5. Run the Main Script

```bash
python src/main.py
```

You should see console output illustrating how the **Orchestrator** and the other agents process food images, generate meal plans, store data, and more.

---

## Agents Overview

Below are the key agents included in this project:

1. **Orchestrator (`orchestrator.py`)**

   - Handles **direct communication** with the user (text/speech).
   - Routes user requests to the appropriate agent(s).
   - Aggregates results and returns a final response.

2. **Food Analyst (`food_analyst.py`)**

   - Uses image recognition (e.g., PyTorch + torchvision) to identify food items.
   - Calculates nutritional breakdown.
   - Suggests recipe modifications.

3. **Nutritionist (`nutritionist.py`)**

   - Creates personalized meal plans.
   - Identifies nutritional deficiencies based on user dietary data.

4. **Behavioral Psychologist (`behavioral_psychologist.py`)**

   - Profiles user behavior.
   - Provides motivational messaging and behavior change techniques.

5. **Database Manager (`database_manager.py`)**

   - Uses SQLite (or other DB) for storage and retrieval of user data.

6. **Recipe Generator (`recipe_generator.py`)**

   - Generates and modifies recipes based on user goals or dietary restrictions.

7. **Health Tracker (`health_tracker.py`)**

   - Integrates with wearable devices for health metrics.

8. **Alert Manager (`alert_manager.py`)**

   - Schedules and sends notifications or reminders to the user.

9. **Restaurant Recommender (`restaurant_recommender.py`)**

   - Finds restaurants matching dietary needs.
   - Analyzes menus for healthy options.

10. **Culinary Guide (`culinary_guide.py`)**

    - Provides historical and cultural context for dishes and cuisines.

11. **External API Integrator (`external_api_integrator.py`)**

    - Fetches external data (e.g., from food or health APIs).
    - Standardizes API responses.

12. **Content Creator (`content_creator.py`)**

    - Generates audio, video, or visual summaries for user progress.

13. **Tailor (Ecosystem Manager) (`tailor.py`)**
    - Monitors overall system performance.
    - Proposes new agents or system improvements.

---

## Usage Workflow

1. **User Provides Input**

   - The user can type or speak a request (e.g., “Suggest a vegetarian meal plan.”).

2. **Orchestrator Routes Requests**

   - The `Orchestrator` examines the user’s input and decides which agent(s) can fulfill the request.

3. **Agents Process**

   - Relevant agents (e.g., `Nutritionist`, `FoodAnalyst`) do their specialized tasks (generating meal plans, analyzing food images, etc.).

4. **Orchestrator Aggregates & Responds**
   - The `Orchestrator` collects results from various agents, prepares a user-facing response, and delivers it (via text or optionally speech).

---

## Future Enhancements

- **Speech Input/Output**: Integrate speech recognition (e.g., `speech_recognition`) and TTS (e.g., `pyttsx3`) for a hands-free experience.
- **Real-time Health Monitoring**: Extend `HealthTracker` to ingest live data from wearables (Fitbit, Apple Watch, etc.).
- **Gamification**: Incorporate a system of badges, points, and achievements to increase user motivation.
- **Advanced Personalization**: Use machine learning to refine recommendations based on real-time feedback and progress.

---

## Contributing

1. **Fork** this repository.
2. **Create a feature branch**:
   ```bash
   git checkout -b new-feature
   ```
3. **Commit and push** your changes.
4. **Open a pull request** describing the changes.

We encourage contributions that **improve existing agents**, **add new agents**, or **enhance the orchestration logic**.

---

## License

This project is licensed under the [MIT License](https://opensource.org/licenses/MIT). Feel free to modify and distribute as allowed by the license terms.
