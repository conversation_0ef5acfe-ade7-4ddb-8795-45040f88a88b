#!/usr/bin/env python3
"""
Quick test for the web app to ensure it works before launching
"""

import sys
from pathlib import Path
import requests
import time
import subprocess
import signal
import os

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from agents.nutritional_analyzer.main import NutritionalAnalyzer
        print("✅ NutritionalAnalyzer import successful")
        
        import fastapi
        print("✅ FastAPI import successful")
        
        import uvicorn
        print("✅ Uvicorn import successful")
        
        from PIL import Image
        print("✅ PIL import successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_nutritional_analyzer():
    """Test the nutritional analyzer functionality"""
    print("\n🧪 Testing NutritionalAnalyzer...")
    
    try:
        from agents.nutritional_analyzer.main import NutritionalAnalyzer
        
        analyzer = NutritionalAnalyzer(use_real_apis=False)  # Use local for testing
        
        # Test basic food analysis
        result = analyzer.analyze_food_item("apple", 100)
        assert "nutrition" in result
        assert result["nutrition"]["calories"] > 0
        print("✅ Basic food analysis working")
        
        return True
    except Exception as e:
        print(f"❌ NutritionalAnalyzer test failed: {e}")
        return False

def test_web_app_startup():
    """Test that the web app can start up"""
    print("\n🧪 Testing web app startup...")
    
    web_app_dir = Path(__file__).parent / "src" / "web_app"
    
    if not web_app_dir.exists():
        print(f"❌ Web app directory not found: {web_app_dir}")
        return False
    
    main_py = web_app_dir / "main.py"
    if not main_py.exists():
        print(f"❌ main.py not found: {main_py}")
        return False
    
    print("✅ Web app files found")
    
    # Try to import the FastAPI app
    try:
        sys.path.insert(0, str(web_app_dir))
        from main import app
        print("✅ FastAPI app import successful")
        return True
    except Exception as e:
        print(f"❌ FastAPI app import failed: {e}")
        return False

def create_test_image():
    """Create a simple test image"""
    try:
        from PIL import Image, ImageDraw
        
        # Create a simple test image
        img = Image.new('RGB', (300, 300), color='white')
        draw = ImageDraw.Draw(img)
        
        # Draw a simple apple-like shape
        draw.ellipse([50, 50, 250, 250], fill='red', outline='darkred', width=3)
        draw.text((120, 130), "🍎", fill='white')
        
        # Save test image
        test_image_path = Path(__file__).parent / "test_apple.jpg"
        img.save(test_image_path, "JPEG")
        
        print(f"✅ Test image created: {test_image_path}")
        return test_image_path
    except Exception as e:
        print(f"❌ Failed to create test image: {e}")
        return None

def run_all_tests():
    """Run all tests"""
    print("🚀 Running Web App Tests")
    print("=" * 40)
    
    tests = [
        ("Import Tests", test_imports),
        ("NutritionalAnalyzer Tests", test_nutritional_analyzer),
        ("Web App Startup Tests", test_web_app_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Web app is ready to launch.")
        
        # Create test image
        test_image = create_test_image()
        
        print(f"\n🚀 Ready to launch! Run:")
        print(f"   python start_mobile_app.py")
        print(f"\n📱 Or start manually:")
        print(f"   cd src/web_app")
        print(f"   uvicorn main:app --host 0.0.0.0 --port 8000 --reload")
        
        return True
    else:
        print("❌ Some tests failed. Please fix issues before launching.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
