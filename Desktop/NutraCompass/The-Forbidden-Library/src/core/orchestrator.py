import logging
from agents.nutritionist.main import Nutritionist
from services.database_manager import DatabaseManager
from services.message_bus import MessageBus
from services.ai.ai_integration import call_advanced_model

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Orchestrator")

class Orchestrator:
    """
    Central coordinator with improved context handling and cache validation.
    Manages user sessions and agent coordination with atomic operations.
    """
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.nutritionist = Nutritionist(orchestrator=self)
        self.user_cache = {}
        self.current_user_id = None
        self.current_user_data = {}

    def update_user_context(self, context_key: str, context_data: dict) -> None:
        """Atomic context update with transaction support"""
        if not self.current_user_id:
            raise ValueError("No active user session")
        
        user_data = self.user_cache.get(self.current_user_id, {})
        user_data.setdefault(context_key, {}).update(context_data)
        
        try:
            success = self.db_manager.update_user_data(
                user_id=self.current_user_id,
                data={context_key: user_data[context_key]}
            )
            if success:
                self.user_cache[self.current_user_id] = user_data
                logger.info(f"Updated context: {context_key}")
            else:
                raise RuntimeError("Database update failed")
        except Exception as e:
            logger.error(f"Context update error: {str(e)}")
            raise

    def _refresh_cache(self, user_id: str) -> None:
        """Cache invalidation and refresh protocol"""
        fresh_data = self.db_manager.get_user_data(user_id)
        subcollections = {
            'customMealSections': self.db_manager.get_all_subcollection_data(user_id, "customMealSections")
        }
        self.user_cache[user_id] = {**fresh_data, **subcollections}
        logger.debug(f"Cache refreshed for {user_id}")

    def _interpret_command(self, input_text: str) -> str:
        """Stateful command interpretation with fallback"""
        prompt = f"""
        Analyze the user's intent from: '{input_text}'
        Options: meal_plan, log_food, fitness, settings, other
        Respond with only the matching keyword.
        """
        try:
            response = call_advanced_model(prompt).strip().lower()
            return response if response in {
                'meal_plan', 'log_food', 'fitness', 'settings'
            } else 'other'
        except Exception as e:
            logger.error(f"Interpretation fallback: {str(e)}")
            return 'other'

    def process_user_input(self, user_id: str, input_text: str) -> dict:
        """State-aware input processing pipeline"""
        self.current_user_id = user_id
        
        # Cache validation layer
        if user_id not in self.user_cache:
            self._refresh_cache(user_id)
            
        user_data = self.user_cache[user_id]
        self.current_user_data = user_data
        
        # Dynamic preference initialization
        user_data.setdefault("desired_weight_change", "maintain")
        user_data.setdefault("dietary_preferences", None)
        
        command = self._interpret_command(input_text)
        logger.info(f"Processed command: {command}")

        # Command routing matrix
        if command == 'meal_plan':
            return self._handle_meal_plan_request(user_data)
        elif command == 'log_food':
            return self._handle_food_logging()
        elif command == 'fitness':
            return self._handle_fitness_request(input_text)
        else:
            return {"response": "Command processed", "user_data": user_data}

    def _handle_meal_plan_request(self, user_data: dict) -> dict:
        """Meal planning workflow"""
        # Subcollection version check
        stored_sections = self.db_manager.get_all_subcollection_data(
            self.current_user_id, "customMealSections"
        )
        if stored_sections != user_data.get('customMealSections'):
            self._refresh_cache(self.current_user_id)
            user_data = self.user_cache[self.current_user_id]

        try:
            meal_plan = self.nutritionist.generate_structured_meal_plan(user_data)
            MessageBus.publish("meal_plan_generated", {
                "user_id": self.current_user_id,
                "plan": meal_plan
            })
            return {
                "status": "success",
                "meal_plan": meal_plan,
                "nutritional_targets": meal_plan.get('macroGoals')
            }
        except Exception as e:
            logger.error(f"Meal planning failed: {str(e)}")
            return {"status": "error", "message": "Meal plan generation failed"}

    def generate_system_report(self, user_id: str) -> dict:
        """Comprehensive system diagnostics"""
        report = {
            "user_id": user_id,
            "cache_status": "valid" if user_id in self.user_cache else "stale",
            "active_agents": ["nutritionist"],
            "last_operation": self.current_user_data.get('last_command')
        }
        if user_id in self.user_cache:
            report.update({
                "user_data_snapshot": {
                    k: type(v).__name__ for k, v in self.user_cache[user_id].items()
                }
            })
        return report

# Example usage:
if __name__ == "__main__":
    orch = Orchestrator()
    user_id = "xFucHw4hvHYP5yjc0kYtbZPH8rh1"  # Replace with a valid user ID in Firestore
    result = orch.process_user_input(user_id, "Please generate a meal plan for me")
    print("Process User Input Result:", result)
    report = orch.generate_system_report(user_id)
    print("System Report:", report)
