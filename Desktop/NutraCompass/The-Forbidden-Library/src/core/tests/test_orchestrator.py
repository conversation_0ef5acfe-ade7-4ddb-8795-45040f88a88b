import json
import logging
from core.orchestrator import Orchestrator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("OrchestratorTest")

def display_response(response: dict):
    """Universal response handler with structured visualization"""
    print("\n=== System Response ===")
    
    if response.get('status') == 'error':
        print(f"❌ Error: {response.get('message')}")
        return
    
    if 'meal_plan' in response:
        print("📅 Meal Plan Generated")
        print(f"Calories: {response['meal_plan'].get('calorieGoal', 'N/A')}")
        print(f"Dishes: {len(response['meal_plan'].get('dishes', []))}")
        
    elif 'nutritional_targets' in response:
        print("⚖️ Nutritional Targets")
        print(json.dumps(response['nutritional_targets'], indent=2))
        
    elif 'user_data' in response:
        print("📊 User Context Snapshot")
        print(f"Weight Goal: {response['user_data'].get('desired_weight_change', 'N/A')}")
        print(f"Dietary Prefs: {response['user_data'].get('dietary_preferences', 'None')}")
        
    print("\n🔗 Raw Response:")
    print(json.dumps(response, indent=2, default=str))

def test_command_processing():
    """Interactive orchestrator test harness"""
    print("🚀 Orchestrator Integration Test Environment")
    print("Type 'exit' to quit\n")
    
    orch = Orchestrator()
    test_user = "g6NQFUhfapWF6dn0ItvXOJcSLxK2"
    
    while True:
        try:
            # Get user input
            command = input("\nEnter command: ").strip()
            if command.lower() in ('exit', 'quit'):
                break

            # Process command
            response = orch.process_user_input(test_user, command)
            
            # Display formatted response
            display_response(response)
            
            # Show cache state
            print("\n💾 Cache State:")
            cache = orch.user_cache.get(test_user, {})
            print(f"Stored Sections: {len(cache.get('customMealSections', []))}")
            print(f"Last Update: {cache.get('last_updated', 'Never')}")

        except Exception as e:
            logger.error(f"Test Failure: {str(e)}")
            print("🔧 Check service logs and API connections")

def validate_cache_consistency(orch: Orchestrator, user_id: str):
    """Ensure cache matches database state"""
    print("\n=== Cache Validation ===")
    
    # Force cache refresh
    orch._refresh_cache(user_id)
    cache = orch.user_cache[user_id]
    
    # Direct database read
    db_data = orch.db_manager.get_user_data(user_id)
    sections = orch.db_manager.get_all_subcollection_data(user_id, "customMealSections")
    
    # Validation checks
    cache_match = cache.get('data') == db_data
    sections_match = cache.get('customMealSections') == sections
    
    print(f"Data Consistency: {'✅' if cache_match else '❌'}")
    print(f"Sections Consistency: {'✅' if sections_match else '❌'}")
    
    if not cache_match or not sections_match:
        print("⚠️ Cache inconsistency detected - manual refresh recommended")

if __name__ == "__main__":
    test_command_processing()
    
    # Optional: Run cache validation after tests
    # orch = Orchestrator()
    # validate_cache_consistency(orch, "g6NQFUhfapWF6dn0ItvXOJcSLxK2")