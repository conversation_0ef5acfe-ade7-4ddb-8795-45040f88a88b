"""
NutraCompass Orchestrator Integration Test Suite
"""

import logging
from core.orchestrator import Orchestrator
import sys
import os
import json
from pathlib import Path

# Add the src directory to the Python path
src_dir = Path(__file__).resolve().parents[3]
sys.path.append(str(src_dir))

from agents.nutritional_analyzer.main import NutritionalAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MainTest")

def orchestrator_test_harness():
    """Interactive test harness for end-to-end system validation"""
    print("🚀 NutraCompass Orchestrator Test Environment")
    print("Type 'exit' to quit\n")
    
    # Initialize core system
    orchestrator = Orchestrator()
    
    # Simulate user session
    test_user_id = "g6NQFUhfapWF6dn0ItvXOJcSLxK2"
    
    while True:
        try:
            # Get user input
            prompt = input("\nEnter your request: ").strip()
            if prompt.lower() in ('exit', 'quit'):
                break

            # Full processing cycle
            print("\n🔄 Processing...")
            result = orchestrator.process_user_input(test_user_id, prompt)
            
            # Display structured response
            print("\n📋 System Response:")
            if 'meal_plan' in result:
                _handle_meal_plan_response(result['meal_plan'])
            elif 'error' in result:
                print(f"❌ {result['error']}")
            else:
                print(json.dumps(result, indent=2))
            
            # Show context state
            print("\n🔮 Updated Context:")
            cache = orchestrator.user_cache.get(test_user_id, {})
            print(f"Last Update: {cache.get('last_updated', 'Never')}")
            print(f"Meal Sections: {len(cache.get('customMealSections', []))}")

        except Exception as e:
            logger.error(f"Test Harness Failure: {str(e)}")
            print("🔧 Recommendation: Check service logs")

def _handle_meal_plan_response(response: dict):
    """Handle meal plan responses"""
    print(f"📌 Generated Meal Plan ({response.get('calorieGoal', 'N/A')} calories)")
    
    if "dishes" in response:
        print(f"🍽️ Contains {len(response['dishes'])} meals:")
        for dish in response["dishes"]:
            nutrients = dish.get('totalNutrients', {})
            print(f"\n  🍲 {dish.get('dishName', 'Unnamed Dish')}")
            print(f"   🔥 {nutrients.get('ENERC_KCAL', {}).get('quantity', 'N/A')} kcal")
            print(f"   🥚 Protein: {nutrients.get('PROCNT', {}).get('quantity', 'N/A')}g")
            print(f"   🍞 Carbs: {nutrients.get('CHOCDF', {}).get('quantity', 'N/A')}g")
            print(f"   🥑 Fat: {nutrients.get('FAT', {}).get('quantity', 'N/A')}g")

def _handle_error_response(response: dict):
    """Handle error responses"""
    print(f"❌ Error: {response.get('message', 'Unknown error')}")
    if 'details' in response:
        print(f"🔍 Details: {response['details']}")

def test_analyze_food_item():
    analyzer = NutritionalAnalyzer()
    
    # Test with common food items
    foods_to_test = ["apple", "chicken breast", "brown rice", "broccoli"]
    
    print("\n=== Food Item Nutritional Analysis ===")
    for food in foods_to_test:
        result = analyzer.analyze_food_item(food)
        print(f"\n{food.title()}:")
        print(json.dumps(result, indent=2))
    
    return True

def test_analyze_meal():
    analyzer = NutritionalAnalyzer()
    
    # Test meal combinations
    test_meals = [
        {
            "name": "Balanced Lunch",
            "items": ["chicken breast", "brown rice", "broccoli"],
            "portions": [0.15, 0.3, 0.2]  # in kg or arbitrary units
        },
        {
            "name": "Breakfast",
            "items": ["oatmeal", "banana", "almond milk"],
            "portions": [0.5, 1, 0.25]  # cup, whole, cup
        }
    ]
    
    print("\n=== Meal Nutritional Analysis ===")
    for meal in test_meals:
        result = analyzer.analyze_meal(meal["items"], meal["portions"])
        print(f"\n{meal['name']}:")
        print(json.dumps(result, indent=2))
    
    return True

if __name__ == "__main__":
    print("Running Nutritional Analyzer tests...")
    test_analyze_food_item()
    test_analyze_meal()
    print("\nAll tests completed!")
    orchestrator_test_harness()
