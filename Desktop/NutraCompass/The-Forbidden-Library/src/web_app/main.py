"""
Mobile Food Analysis Web App
FastAPI server with camera integration for real-time food nutrition analysis
"""

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging
import sys
import io
from pathlib import Path
from typing import Dict, Any
import base64
from PIL import Image

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from agents.nutritional_analyzer.main import NutritionalAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("FoodAnalysisWebApp")

# Initialize FastAPI app
app = FastAPI(
    title="NutraCompass Food Analysis",
    description="Real-time food nutrition analysis using your phone camera",
    version="1.0.0"
)

# Add CORS middleware for mobile access
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize nutritional analyzer
nutritional_analyzer = NutritionalAnalyzer(use_real_apis=True)

# Mount static files
static_path = Path(__file__).parent / "static"
static_path.mkdir(exist_ok=True)
app.mount("/static", StaticFiles(directory=str(static_path)), name="static")

@app.get("/", response_class=HTMLResponse)
async def home():
    """Serve the main mobile interface"""
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🍎 NutraCompass Food Scanner</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: white;
            }
            
            .container {
                max-width: 400px;
                margin: 0 auto;
                padding: 20px;
                text-align: center;
            }
            
            .header {
                margin-bottom: 30px;
            }
            
            .header h1 {
                font-size: 2.5em;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            
            .header p {
                opacity: 0.9;
                font-size: 1.1em;
            }
            
            .camera-section {
                background: rgba(255,255,255,0.1);
                border-radius: 20px;
                padding: 30px 20px;
                margin-bottom: 20px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.2);
            }
            
            #video {
                width: 100%;
                max-width: 300px;
                height: 300px;
                border-radius: 15px;
                object-fit: cover;
                background: #333;
                margin-bottom: 20px;
            }
            
            .controls {
                display: flex;
                gap: 15px;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            button {
                background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                border: none;
                color: white;
                padding: 15px 25px;
                border-radius: 25px;
                font-size: 1em;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }
            
            button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            }
            
            button:disabled {
                background: #666;
                cursor: not-allowed;
                transform: none;
            }
            
            .results {
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 20px;
                margin-top: 20px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.2);
                text-align: left;
            }
            
            .nutrition-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }
            
            .nutrition-item:last-child {
                border-bottom: none;
            }
            
            .loading {
                display: none;
                margin: 20px 0;
            }
            
            .spinner {
                border: 3px solid rgba(255,255,255,0.3);
                border-radius: 50%;
                border-top: 3px solid white;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin: 0 auto;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .error {
                background: rgba(255,0,0,0.2);
                border: 1px solid rgba(255,0,0,0.5);
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
            
            .success {
                background: rgba(0,255,0,0.2);
                border: 1px solid rgba(0,255,0,0.5);
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
            
            @media (max-width: 480px) {
                .container {
                    padding: 10px;
                }
                
                .header h1 {
                    font-size: 2em;
                }
                
                #video {
                    height: 250px;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🍎 NutraCompass</h1>
                <p>Point your camera at food to get instant nutrition analysis</p>
            </div>
            
            <div class="camera-section">
                <video id="video" autoplay playsinline></video>
                <div class="controls">
                    <button id="startCamera">📷 Start Camera</button>
                    <button id="capturePhoto" disabled>📸 Analyze Food</button>
                    <button id="stopCamera" disabled>⏹️ Stop</button>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Analyzing your food...</p>
            </div>
            
            <div id="results"></div>
        </div>

        <script>
            let video = document.getElementById('video');
            let stream = null;
            
            const startCameraBtn = document.getElementById('startCamera');
            const captureBtn = document.getElementById('capturePhoto');
            const stopCameraBtn = document.getElementById('stopCamera');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            
            startCameraBtn.addEventListener('click', startCamera);
            captureBtn.addEventListener('click', capturePhoto);
            stopCameraBtn.addEventListener('click', stopCamera);
            
            async function startCamera() {
                try {
                    stream = await navigator.mediaDevices.getUserMedia({
                        video: { 
                            facingMode: 'environment', // Use back camera
                            width: { ideal: 640 },
                            height: { ideal: 640 }
                        }
                    });
                    
                    video.srcObject = stream;
                    
                    startCameraBtn.disabled = true;
                    captureBtn.disabled = false;
                    stopCameraBtn.disabled = false;
                    
                    showMessage('Camera started! Point at food and tap "Analyze Food"', 'success');
                } catch (err) {
                    console.error('Error accessing camera:', err);
                    showMessage('Error accessing camera. Please allow camera permissions.', 'error');
                }
            }
            
            function stopCamera() {
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                    video.srcObject = null;
                    stream = null;
                }
                
                startCameraBtn.disabled = false;
                captureBtn.disabled = true;
                stopCameraBtn.disabled = true;
                
                showMessage('Camera stopped', 'success');
            }
            
            async function capturePhoto() {
                if (!stream) return;
                
                // Create canvas to capture frame
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                
                context.drawImage(video, 0, 0);
                
                // Convert to blob
                canvas.toBlob(async (blob) => {
                    await analyzeFood(blob);
                }, 'image/jpeg', 0.8);
            }
            
            async function analyzeFood(imageBlob) {
                loading.style.display = 'block';
                results.innerHTML = '';
                captureBtn.disabled = true;
                
                try {
                    const formData = new FormData();
                    formData.append('file', imageBlob, 'food.jpg');
                    
                    const response = await fetch('/analyze-food-image', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        displayResults(data);
                    } else {
                        showMessage(`Analysis failed: ${data.detail || 'Unknown error'}`, 'error');
                    }
                } catch (error) {
                    console.error('Analysis error:', error);
                    showMessage('Network error. Please try again.', 'error');
                } finally {
                    loading.style.display = 'none';
                    captureBtn.disabled = false;
                }
            }
            
            function displayResults(data) {
                if (data.error) {
                    showMessage(`Analysis error: ${data.error}`, 'error');
                    return;
                }
                
                let html = '<div class="results">';
                
                if (data.image_analysis) {
                    const analysis = data.image_analysis;
                    html += `<h3>🔍 Detected: ${analysis.detected_foods} food item(s)</h3>`;
                    
                    if (analysis.nutrition_results && analysis.nutrition_results.length > 0) {
                        analysis.nutrition_results.forEach((result, index) => {
                            if (result.nutrition) {
                                html += `
                                    <div style="margin: 15px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                                        <h4>🍽️ ${result.food_name} (${result.estimated_portion_g}g)</h4>
                                        <div class="nutrition-item">
                                            <span>🔥 Calories:</span>
                                            <span><strong>${result.nutrition.calories}</strong></span>
                                        </div>
                                        <div class="nutrition-item">
                                            <span>🥚 Protein:</span>
                                            <span><strong>${result.nutrition.protein_g}g</strong></span>
                                        </div>
                                        <div class="nutrition-item">
                                            <span>🥑 Fat:</span>
                                            <span><strong>${result.nutrition.fat_g}g</strong></span>
                                        </div>
                                        <div class="nutrition-item">
                                            <span>🍞 Carbs:</span>
                                            <span><strong>${result.nutrition.carbohydrates_g}g</strong></span>
                                        </div>
                                        <div class="nutrition-item">
                                            <span>📈 Confidence:</span>
                                            <span>${Math.round(result.detection_confidence * 100)}%</span>
                                        </div>
                                    </div>
                                `;
                            }
                        });
                    }
                    
                    // Show combined nutrition if multiple items
                    if (data.combined_nutrition && data.combined_nutrition.total_nutrition) {
                        const total = data.combined_nutrition.total_nutrition;
                        html += `
                            <div style="margin: 20px 0; padding: 20px; background: rgba(0,255,0,0.1); border-radius: 10px; border: 2px solid rgba(0,255,0,0.3);">
                                <h3>📊 Total Nutrition</h3>
                                <div class="nutrition-item">
                                    <span>🔥 Total Calories:</span>
                                    <span><strong>${total.calories}</strong></span>
                                </div>
                                <div class="nutrition-item">
                                    <span>🥚 Total Protein:</span>
                                    <span><strong>${total.protein_g}g</strong></span>
                                </div>
                                <div class="nutrition-item">
                                    <span>🥑 Total Fat:</span>
                                    <span><strong>${total.fat_g}g</strong></span>
                                </div>
                                <div class="nutrition-item">
                                    <span>🍞 Total Carbs:</span>
                                    <span><strong>${total.carbohydrates_g}g</strong></span>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    showMessage('No food detected in image. Try pointing camera directly at food.', 'error');
                }
                
                html += '</div>';
                results.innerHTML = html;
            }
            
            function showMessage(message, type) {
                const messageDiv = document.createElement('div');
                messageDiv.className = type;
                messageDiv.textContent = message;
                
                // Remove existing messages
                document.querySelectorAll('.error, .success').forEach(el => el.remove());
                
                results.appendChild(messageDiv);
                
                // Auto-remove after 5 seconds
                setTimeout(() => {
                    messageDiv.remove();
                }, 5000);
            }
            
            // Auto-start camera on mobile
            if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                window.addEventListener('load', () => {
                    setTimeout(startCamera, 1000);
                });
            }
        </script>
    </body>
    </html>
    """

@app.post("/analyze-food-image")
async def analyze_food_image(file: UploadFile = File(...)):
    """Analyze food from uploaded image"""
    try:
        logger.info(f"Received image for analysis: {file.filename}")
        
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read image data
        image_data = await file.read()
        
        # Analyze with nutritional analyzer
        result = nutritional_analyzer.analyze_food_from_image(image_data)
        
        logger.info(f"Analysis completed for {file.filename}")
        return result
        
    except Exception as e:
        logger.error(f"Error analyzing image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "NutraCompass Food Analysis"}

@app.get("/api/test-food/{food_name}")
async def test_food_analysis(food_name: str, portion: float = 100):
    """Test endpoint for food analysis without image"""
    try:
        result = nutritional_analyzer.analyze_food_item(food_name, portion)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",  # Allow external connections
        port=8000,
        reload=True,
        log_level="info"
    )
