import json
from services.external_api_integrator import ExternalAPIIntegrator

def test_search_food():
    print("\n=== Test: Search Food via Aggregator ===")
    integrator = ExternalAPIIntegrator()
    search_term = "banana"
    # Use the limit parameter to restrict the results.
    results = integrator.search_food("edamam", search_term, limit=2)
    print(json.dumps(results, indent=4, sort_keys=True))
    if results.get("results"):
        print("Search food test passed.")
    else:
        print("Search food test failed.")

def test_search_food_by_barcode():
    print("\n=== Test: Search Food by Barcode via Aggregator ===")
    integrator = ExternalAPIIntegrator()
    barcode = "012345678905"  # Replace with a valid barcode if available
    results = integrator.search_food_by_barcode("edamam", barcode)
    print(json.dumps(results, indent=4, sort_keys=True))
    if results.get("results"):
        print("Search food by barcode test passed.")
    else:
        print("Search food by barcode test failed.")

def test_get_nutrients():
    print("\n=== Test: Get Nutrients via Aggregator ===")
    integrator = ExternalAPIIntegrator()
    ingredients = {
        "ingredients": [
            {
                "quantity": 1,
                "measureURI": "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
                "foodId": "food_a1gb9ubb72c7snbuxr3weagwv0dd"
            }
        ]
    }
    nutrient_data = integrator.get_nutrients("edamam", ingredients)
    print(json.dumps(nutrient_data, indent=4, sort_keys=True))
    if nutrient_data:
        print("Get nutrients test passed.")
    else:
        print("Get nutrients test failed.")

def test_external_api_integrator():
    test_search_food()
    test_search_food_by_barcode()
    test_get_nutrients()

if __name__ == "__main__":
    test_external_api_integrator()
