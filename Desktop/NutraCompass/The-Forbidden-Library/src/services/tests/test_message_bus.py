import json
from services.message_bus import MessageBus

def reset_message_bus():
    """Clear all subscribers for isolation between tests."""
    MessageBus._subscribers = {}

def test_subscribe_and_publish():
    reset_message_bus()
    print("\n=== Test: Subscribe and Publish ===")
    events_received = []
    
    def callback(data):
        events_received.append(data)
    
    MessageBus.subscribe("test_event", callback)
    MessageBus.publish("test_event", {"key": "value"})
    
    if events_received:
        print("Callback received data:")
        print(json.dumps(events_received, indent=4, sort_keys=True))
    else:
        print("No event data received.")

def test_publish_without_subscribers():
    reset_message_bus()
    print("\n=== Test: Publish Without Subscribers ===")
    # Publish an event type that has no subscribers.
    MessageBus.publish("no_subscribers_event", {"data": "test"})
    print("Published 'no_subscribers_event' without any subscribers.")

def test_callback_error_handling():
    reset_message_bus()
    print("\n=== Test: Callback Error Handling ===")
    
    def error_callback(data):
        raise ValueError("Intentional error for testing")
    
    def normal_callback(data):
        print("Normal callback received:", data)
    
    # Subscribe both callbacks to an event.
    MessageBus.subscribe("error_event", error_callback)
    MessageBus.subscribe("error_event", normal_callback)
    
    MessageBus.publish("error_event", {"data": "error test"})

def test_message_bus():
    test_subscribe_and_publish()
    test_publish_without_subscribers()
    test_callback_error_handling()

if __name__ == "__main__":
    test_message_bus()
