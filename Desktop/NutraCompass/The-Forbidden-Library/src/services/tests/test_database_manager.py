import json
import sys
from firebase_admin import firestore  # For firestore.DELETE_FIELD
from services.database_manager import DatabaseManager

# ----- User Document Tests -----

def test_store_data(db_manager, uid, data):
    print(f"\n--- Storing data for UID: {uid} ---")
    result = db_manager.store_user_data(uid, data)
    if result:
        print("Data stored successfully.")
    else:
        print("Failed to store data.")
    return result

def test_retrieve_data(db_manager, uid):
    data = db_manager.get_user_data(uid)
    print(f"\n--- Retrieved data for UID: {uid} ---")
    print(json.dumps(data, indent=4, sort_keys=True, default=str))
    return data

def test_update_data(db_manager, uid, update_payload):
    print(f"\n--- Updating data for UID: {uid} ---")
    result = db_manager.update_user_data(uid, update_payload)
    if result:
        print("Data updated successfully.")
    else:
        print("Failed to update data.")
    return result

def test_delete_field(db_manager, uid, field_name):
    print(f"\n--- Deleting the '{field_name}' field for UID: {uid} ---")
    payload = {field_name: firestore.DELETE_FIELD}
    result = db_manager.update_user_data(uid, payload)
    if result:
        print(f"Field '{field_name}' deleted successfully.")
    else:
        print(f"Failed to delete field '{field_name}'.")
    return result

def test_add_field(db_manager, uid, field_name, field_value):
    print(f"\n--- Adding the '{field_name}' field back for UID: {uid} ---")
    payload = {field_name: field_value}
    result = db_manager.update_user_data(uid, payload)
    if result:
        print(f"Field '{field_name}' added back successfully.")
    else:
        print(f"Failed to add field '{field_name}' back.")
    return result

def test_delete_document(db_manager, uid, dummy_data):
    print(f"\n--- Testing document deletion for UID: {uid} ---")
    if db_manager.store_user_data(uid, dummy_data):
        print("Dummy data stored successfully.")
    else:
        print("Failed to store dummy data.")
        return
    print("Data before deletion:")
    print(json.dumps(db_manager.get_user_data(uid), indent=4, sort_keys=True, default=str))
    if db_manager.delete_user_data(uid):
        print("Document deleted successfully.")
    else:
        print("Failed to delete document.")
    print("Data after deletion (should be empty):")
    print(json.dumps(db_manager.get_user_data(uid), indent=4, sort_keys=True, default=str))

# ----- Subcollection Tests -----

def test_store_subcollection_data(db_manager, uid, subcollection, doc_id, data):
    print(f"\n--- Storing data in subcollection '{subcollection}' for UID: {uid}, Document ID: {doc_id} ---")
    result = db_manager.store_subcollection_data(uid, subcollection, doc_id, data)
    if result:
        print("Subcollection data stored successfully.")
    else:
        print("Failed to store subcollection data.")
    return result

def test_get_subcollection_data(db_manager, uid, subcollection, doc_id):
    data = db_manager.get_subcollection_data(uid, subcollection, doc_id)
    print(f"\n--- Retrieved subcollection data from '{subcollection}' for UID: {uid}, Document ID: {doc_id} ---")
    print(json.dumps(data, indent=4, sort_keys=True, default=str))
    return data

def test_update_subcollection_data(db_manager, uid, subcollection, doc_id, update_payload):
    print(f"\n--- Updating subcollection data in '{subcollection}' for UID: {uid}, Document ID: {doc_id} ---")
    result = db_manager.update_subcollection_data(uid, subcollection, doc_id, update_payload)
    if result:
        print("Subcollection data updated successfully.")
    else:
        print("Failed to update subcollection data.")
    return result

def test_delete_subcollection_document(db_manager, uid, subcollection, doc_id):
    print(f"\n--- Deleting document '{doc_id}' from subcollection '{subcollection}' for UID: {uid} ---")
    result = db_manager.delete_subcollection_document(uid, subcollection, doc_id)
    if result:
        print("Subcollection document deleted successfully.")
    else:
        print("Failed to delete subcollection document.")
    return result

def test_get_all_subcollection_data(db_manager, uid, subcollection):
    print(f"\n--- Retrieving all documents from subcollection '{subcollection}' for UID: {uid} ---")
    data = db_manager.get_all_subcollection_data(uid, subcollection)
    print(json.dumps(data, indent=4, sort_keys=True, default=str))
    return data

# ----- New Individual Subcollection Test Functions -----

def test_get_one_custom_meals(db_manager, uid):
    """
    Retrieve and print only one document from the 'customMeals' subcollection.
    """
    print(f"\n--- Retrieving one document from 'customMeals' for UID: {uid} ---")
    data = db_manager.get_all_subcollection_data(uid, "customMeals", limit=1)
    if data:
        print(json.dumps(data[0], indent=4, sort_keys=True, default=str))
    else:
        print("No documents found in 'customMeals'.")
    return data

def test_get_one_food_log_entry(db_manager, uid):
    """
    Retrieve and print only one document from the 'foodLogEntries' subcollection.
    """
    print(f"\n--- Retrieving one document from 'foodLogEntries' for UID: {uid} ---")
    data = db_manager.get_all_subcollection_data(uid, "foodLogEntries", limit=1)
    if data:
        print(json.dumps(data[0], indent=4, sort_keys=True, default=str))
    else:
        print("No documents found in 'foodLogEntries'.")
    return data

def test_get_recent_food_log_entries(db_manager, uid):
    """
    Retrieve and print food log entries from 'foodLogEntries' subcollection filtered by a condition.
    For example, only get entries with a timestamp >= a given value.
    """
    print(f"\n--- Retrieving recent documents from 'foodLogEntries' for UID: {uid} ---")
    filters = [{"field": "timestamp", "operator": ">=", "value": 1622505600}]
    data = db_manager.get_all_subcollection_data(uid, "foodLogEntries", limit=5, order_by="timestamp", descending=True, filters=filters)
    print(json.dumps(data, indent=4, sort_keys=True, default=str))
    return data

# ----- Main Test Runner -----

def test_database_manager():
    db_manager = DatabaseManager()
    dev_uid = "xFucHw4hvHYP5yjc0kYtbZPH8rh1"
    sample_data = {
        "appAppearance": {
            "isDark": True,
            "theme": "Default"
        },
        "nutritionalGoals": {
            "calorieGoal": 2112,
            "macroGoals": {
                "carb": {"dailyCalories": 845, "dailyGrams": 211, "dailyPercentage": 0.4},
                "fat": {"dailyCalories": 634, "dailyGrams": 70, "dailyPercentage": 0.3},
                "protein": {"dailyCalories": 634, "dailyGrams": 158, "dailyPercentage": 0.3}
            },
            "waterGoal": {"amount": 10000, "unit": "ml"}
        },
        "physicalFitnessGoals": {
            "distanceGoal": 8,
            "distanceUnit": "mi",
            "stepsGoal": 16896
        },
        "profile": {
            "age": 22,
            "birthday": "November 8, 2002",
            "bodyWeight": "190 lbs",
            "email": "<EMAIL>",
            "firstName": "Andres",
            "height": {
                "centimeters": 185.42,
                "inches": 73,
                "unit": "in"
            },
            "lastName": "Morocho",
            "pictureUrl": (
                "https://firebasestorage.googleapis.com/v0/b/nutracompass-individual.appspot.com/"
                "o/profilePictures%2FxFucHw4hvHYP5yjc0kYtbZPH8rh1%2FprofilePic.jpg?"
                "alt=media&token=77bf3047-5e83-4d24-b676-6da3a8628b1b"
            ),
            "sex": "Male",
            "userName": "Maruchan",
            "userNameLower": "maruchan"
        }
    }
    
    print("\n=== Test: Store User Data ===")
    test_store_data(db_manager, dev_uid, sample_data)
    
    print("\n=== Test: Retrieve User Data ===")
    test_retrieve_data(db_manager, dev_uid)
    
    print("\n=== Test: Update User Data (profile.firstName) ===")
    test_update_data(db_manager, dev_uid, {"profile.firstName": "Development Updated"})
    
    print("\n=== Test: Retrieve Data After Update ===")
    test_retrieve_data(db_manager, dev_uid)
    
    print("\n=== Test: Delete Field (nutritionalGoals) ===")
    test_delete_field(db_manager, dev_uid, "nutritionalGoals")
    
    print("\n=== Test: Retrieve Data After Field Deletion ===")
    test_retrieve_data(db_manager, dev_uid)
    
    print("\n=== Test: Add Field Back (nutritionalGoals) ===")
    test_add_field(db_manager, dev_uid, "nutritionalGoals", sample_data["nutritionalGoals"])
    
    print("\n=== Test: Retrieve Data After Adding Field ===")
    test_retrieve_data(db_manager, dev_uid)
    
    print("\n=== Test: Delete Entire User Document ===")
    test_uid = "test_user_delete"
    dummy_data = {
        "profile": {
            "firstName": "Dummy",
            "lastName": "User",
            "email": "<EMAIL>"
        },
        "nutritionalGoals": {
            "calorieGoal": 2000,
            "macroGoals": {
                "carb": {"dailyCalories": 800, "dailyGrams": 200, "dailyPercentage": 0.4},
                "fat": {"dailyCalories": 600, "dailyGrams": 67, "dailyPercentage": 0.3},
                "protein": {"dailyCalories": 600, "dailyGrams": 150, "dailyPercentage": 0.3}
            },
            "waterGoal": {"amount": 8000, "unit": "ml"}
        }
    }
    test_delete_document(db_manager, test_uid, dummy_data)

    # ----- Subcollection Tests -----
    subcollection_name = "activeNutritionalPrograms"
    sub_doc_id = "test_program_001"
    sub_data = {
        "programName": "Weight Loss Challenge",
        "startDate": "2023-01-01",
        "endDate": "2023-03-01",
        "calorieAdjustment": -500
    }
    
    print("\n=== Subcollection Test: Store Subcollection Data ===")
    test_store_subcollection_data(db_manager, dev_uid, subcollection_name, sub_doc_id, sub_data)
    
    print("\n=== Subcollection Test: Retrieve Subcollection Data ===")
    test_get_subcollection_data(db_manager, dev_uid, subcollection_name, sub_doc_id)
    
    print("\n=== Subcollection Test: Update Subcollection Data ===")
    test_update_subcollection_data(db_manager, dev_uid, subcollection_name, sub_doc_id, {"calorieAdjustment": -600})
    
    print("\n=== Subcollection Test: Retrieve Data After Subcollection Update ===")
    test_get_subcollection_data(db_manager, dev_uid, subcollection_name, sub_doc_id)
    
    print("\n=== Subcollection Test: Delete Subcollection Document ===")
    test_delete_subcollection_document(db_manager, dev_uid, subcollection_name, sub_doc_id)
    
    print("\n=== Subcollection Test: Retrieve Data After Subcollection Deletion ===")
    test_get_subcollection_data(db_manager, dev_uid, subcollection_name, sub_doc_id)
    
    # New tests for retrieving one document from subcollections
    print("\n=== Subcollection Test: Retrieve One Custom Meal (customMeals) ===")
    test_get_one_custom_meals(db_manager, dev_uid)
    
    print("\n=== Subcollection Test: Retrieve One Food Log Entry (foodLogEntries) ===")
    test_get_one_food_log_entry(db_manager, dev_uid)

def run_test(test_name):
    db_manager = DatabaseManager()
    dev_uid = "xFucHw4hvHYP5yjc0kYtbZPH8rh1"
    tests = {
        "store": lambda: test_store_data(db_manager, dev_uid, {
            "profile": {"firstName": "Test", "bodyWeight": "190 lbs"},
            "nutritionalGoals": {"calorieGoal": 2112}
        }),
        "retrieve": lambda: test_retrieve_data(db_manager, dev_uid),
        "update": lambda: test_update_data(db_manager, dev_uid, {"profile.firstName": "Updated"}),
        "delete_field": lambda: test_delete_field(db_manager, dev_uid, "nutritionalGoals"),
        "add_field": lambda: test_add_field(db_manager, dev_uid, "nutritionalGoals", {
            "calorieGoal": 2112,
            "macroGoals": {
                "carb": {"dailyCalories": 845, "dailyGrams": 211, "dailyPercentage": 0.4},
                "fat": {"dailyCalories": 634, "dailyGrams": 70, "dailyPercentage": 0.3},
                "protein": {"dailyCalories": 634, "dailyGrams": 158, "dailyPercentage": 0.3}
            },
            "waterGoal": {"amount": 10000, "unit": "ml"}
        }),
        "delete_document": lambda: test_delete_document(db_manager, "test_user_delete", {
            "profile": {"firstName": "Dummy", "lastName": "User", "email": "<EMAIL>"},
            "nutritionalGoals": {"calorieGoal": 2000}
        }),
        "store_sub": lambda: test_store_subcollection_data(db_manager, dev_uid, "activeNutritionalPrograms", "test_program_001", {
            "programName": "Weight Loss Challenge",
            "startDate": "2023-01-01",
            "endDate": "2023-03-01",
            "calorieAdjustment": -500
        }),
        "get_sub": lambda: test_get_subcollection_data(db_manager, dev_uid, "activeNutritionalPrograms", "test_program_001"),
        "update_sub": lambda: test_update_subcollection_data(db_manager, dev_uid, "activeNutritionalPrograms", "test_program_001", {"calorieAdjustment": -600}),
        "delete_sub": lambda: test_delete_subcollection_document(db_manager, dev_uid, "activeNutritionalPrograms", "test_program_001"),
        "get_all_sub": lambda: test_get_all_subcollection_data(db_manager, dev_uid, "activeNutritionalPrograms"),
        "get_custom_meals": lambda: test_get_one_custom_meals(db_manager, dev_uid),
        "get_food_entries": lambda: test_get_one_food_log_entry(db_manager, dev_uid),
        "get_recent_food_log": lambda: test_get_recent_food_log_entries(db_manager, dev_uid)
    }
    if test_name in tests:
        tests[test_name]()
    else:
        print(f"Test '{test_name}' not found. Available tests: {list(tests.keys())}")

def test_get_recent_food_log_entries(db_manager, uid):
    """
    Retrieve and print food log entries from 'foodLogEntries' subcollection filtered by a condition.
    For example, only get entries with a timestamp >= a given value.
    """
    print(f"\n--- Retrieving recent documents from 'foodLogEntries' for UID: {uid} ---")
    filters = [{"field": "timestamp", "operator": ">=", "value": 1622505600}]
    data = db_manager.get_all_subcollection_data(uid, "foodLogEntries", limit=5, order_by="timestamp", descending=True, filters=filters)
    print(json.dumps(data, indent=4, sort_keys=True, default=str))
    return data

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_to_run = sys.argv[1]
        run_test(test_to_run)
    else:
        print("Please provide a test name. Available tests: store, retrieve, update, delete_field, add_field, delete_document, store_sub, get_sub, update_sub, delete_sub, get_all_sub, get_custom_meals, get_food_entries, get_recent_food_log")
