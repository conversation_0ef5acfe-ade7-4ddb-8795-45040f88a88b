import logging
from services.apis.food.edamam_api import EdamamAPI
# For future API integrations, import additional modules as needed.

logger = logging.getLogger("ExternalAPIIntegrator")
logging.basicConfig(level=logging.INFO)

class ExternalAPIIntegrator:
    """
    A facade for external API integrations using the Singleton pattern.
    Provides a unified interface for multiple external APIs.
    """
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(ExternalAPIIntegrator, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # Ensure initialization happens only once.
        if not hasattr(self, "initialized"):
            self.food_api = EdamamAPI()
            # self.spoonacular_api = SpoonacularAPI()  # For future integration
            self.initialized = True

    def search_food(self, api_name: str, search_term: str, limit: int = 10) -> dict:
        """
        Route a food search request to the appropriate API integration.
        
        Args:
            api_name (str): The API key to use (e.g., "edamam").
            search_term (str): The search term.
            limit (int, optional): Maximum number of food items to return. Defaults to 10.
        
        Returns:
            dict: The API response.
        """
        if api_name.lower() == "edamam":
            return self.food_api.search_food(search_term, limit)
        else:
            logger.error(f"API '{api_name}' not supported for food search.")
            return {}

    def search_food_by_barcode(self, api_name: str, barcode: str) -> dict:
        """
        Route a barcode search request to the appropriate API integration.
        
        Args:
            api_name (str): The API key to use (e.g., "edamam").
            barcode (str): The barcode to search for.
        
        Returns:
            dict: The API response.
        """
        if api_name.lower() == "edamam":
            return self.food_api.search_food_by_barcode(barcode)
        else:
            logger.error(f"API '{api_name}' not supported for barcode search.")
            return {}

    def get_nutrients(self, api_name: str, ingredients: dict) -> dict:
        """
        Route a nutrient search request to the appropriate API integration.
        
        Args:
            api_name (str): The API key to use (e.g., "edamam").
            ingredients (dict): The ingredients object.
        
        Returns:
            dict: The API response.
        """
        if api_name.lower() == "edamam":
            return self.food_api.get_nutrients(ingredients)
        else:
            logger.error(f"API '{api_name}' not supported for nutrient search.")
            return {}

# Example usage:
if __name__ == "__main__":
    integrator1 = ExternalAPIIntegrator()
    integrator2 = ExternalAPIIntegrator()
    print("Singleton check:", integrator1 is integrator2)
    results = integrator1.search_food("edamam", "apple", limit=5)
    print("Food Search Results:", results)
