import json
import firebase_admin
from firebase_admin import credentials, firestore
import logging
from typing import Dict, Any, List, Optional
from config import FIREBASE_ADMIN_CREDENTIALS_JSON  # Import from central config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DatabaseManager")

class DatabaseManager:
    """
    Handles data storage and retrieval using Firebase Firestore.
    Supports CRUD operations on user documents as well as on subcollections.
    """
    def __init__(self, service_account_path: Optional[str] = None):
        """
        Initialize Firebase and Firestore client.
        
        Args:
            service_account_path (Optional[str]): Path to your Firebase service account JSON file.
                If not provided, attempts to load from the FIREBASE_ADMIN_CREDENTIALS_JSON variable in config.
                If that variable is not set, it defaults to "serviceAccountKey.json".
        """
        try:
            if service_account_path is None:
                if FIREBASE_ADMIN_CREDENTIALS_JSON:
                    service_account_data = json.loads(FIREBASE_ADMIN_CREDENTIALS_JSON)
                    cred = credentials.Certificate(service_account_data)
                    logger.info("Firebase credentials loaded from FIREBASE_ADMIN_CREDENTIALS_JSON in config.")
                else:
                    service_account_path = "serviceAccountKey.json"
                    cred = credentials.Certificate(service_account_path)
                    logger.info(f"Firebase credentials loaded from file: {service_account_path}")
            else:
                cred = credentials.Certificate(service_account_path)
                logger.info(f"Firebase credentials loaded from provided file: {service_account_path}")

            # Initialize Firebase Admin SDK only if it hasn't been initialized already.
            if not firebase_admin._apps:
                firebase_admin.initialize_app(cred)
                logger.info("Firebase initialized successfully.")
            else:
                logger.info("Firebase already initialized.")
            self.db = firestore.client()
        except Exception as e:
            logger.error(f"Error initializing Firebase: {e}")
            raise

    # ---------- User Document CRUD Operations ----------

    def store_user_data(self, user_id: str, data: Dict[str, Any]) -> bool:
        """
        Store or overwrite user data in Firestore.
        
        Args:
            user_id (str): Unique identifier for the user.
            data (Dict[str, Any]): The data to store.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            self.db.collection('users').document(user_id).set(data)
            logger.info(f"User data for {user_id} stored successfully.")
            return True
        except Exception as e:
            logger.error(f"Error storing user data for {user_id}: {e}")
            return False

    def get_user_data(self, user_id: str) -> Dict[str, Any]:
        """
        Retrieve user data from Firestore.
        
        Args:
            user_id (str): Unique identifier for the user.
        
        Returns:
            Dict[str, Any]: The retrieved user data, or an empty dict if not found.
        """
        try:
            doc = self.db.collection('users').document(user_id).get()
            if doc.exists:
                logger.info(f"User data for {user_id} retrieved successfully.")
                return doc.to_dict()
            else:
                logger.warning(f"No user data found for {user_id}.")
                return {}
        except Exception as e:
            logger.error(f"Error retrieving user data for {user_id}: {e}")
            return {}

    def update_user_data(self, user_id: str, data: Dict[str, Any]) -> bool:
        """
        Update specific fields in a user document in Firestore.
        
        Args:
            user_id (str): Unique identifier for the user.
            data (Dict[str, Any]): The fields to update.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            self.db.collection('users').document(user_id).update(data)
            logger.info(f"User data for {user_id} updated successfully.")
            return True
        except Exception as e:
            logger.error(f"Error updating user data for {user_id}: {e}")
            return False

    def delete_user_data(self, user_id: str) -> bool:
        """
        Delete the entire user document from Firestore.
        
        Args:
            user_id (str): Unique identifier for the user.
        
        Returns:
            bool: True if deletion is successful, False otherwise.
        """
        try:
            self.db.collection('users').document(user_id).delete()
            logger.info(f"User data for {user_id} deleted successfully.")
            return True
        except Exception as e:
            logger.error(f"Error deleting user data for {user_id}: {e}")
            return False

    # ---------- Subcollection CRUD Operations ----------

    def store_subcollection_data(self, user_id: str, subcollection: str, doc_id: str, data: Dict[str, Any]) -> bool:
        """
        Store or overwrite data in a specific document within a subcollection of a user document.
        
        Args:
            user_id (str): Unique identifier for the user.
            subcollection (str): The subcollection name.
            doc_id (str): The document ID within the subcollection.
            data (Dict[str, Any]): The data to store.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            self.db.collection('users').document(user_id) \
                .collection(subcollection).document(doc_id).set(data)
            logger.info(f"Data for document '{doc_id}' in subcollection '{subcollection}' for user '{user_id}' stored successfully.")
            return True
        except Exception as e:
            logger.error(f"Error storing data in subcollection '{subcollection}' for user '{user_id}': {e}")
            return False

    def get_subcollection_data(self, user_id: str, subcollection: str, doc_id: str) -> Dict[str, Any]:
        """
        Retrieve data from a specific document in a subcollection.
        
        Args:
            user_id (str): Unique identifier for the user.
            subcollection (str): The subcollection name.
            doc_id (str): The document ID within the subcollection.
        
        Returns:
            Dict[str, Any]: The document data, or an empty dict if not found.
        """
        try:
            doc = self.db.collection('users').document(user_id) \
                .collection(subcollection).document(doc_id).get()
            if doc.exists:
                logger.info(f"Data for document '{doc_id}' in subcollection '{subcollection}' for user '{user_id}' retrieved successfully.")
                return doc.to_dict()
            else:
                logger.warning(f"No data found for document '{doc_id}' in subcollection '{subcollection}' for user '{user_id}'.")
                return {}
        except Exception as e:
            logger.error(f"Error retrieving data from subcollection '{subcollection}' for user '{user_id}': {e}")
            return {}

    def update_subcollection_data(self, user_id: str, subcollection: str, doc_id: str, data: Dict[str, Any]) -> bool:
        """
        Update specific fields in a document within a subcollection.
        
        Args:
            user_id (str): Unique identifier for the user.
            subcollection (str): The subcollection name.
            doc_id (str): The document ID within the subcollection.
            data (Dict[str, Any]): The fields to update.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            self.db.collection('users').document(user_id) \
                .collection(subcollection).document(doc_id).update(data)
            logger.info(f"Data for document '{doc_id}' in subcollection '{subcollection}' for user '{user_id}' updated successfully.")
            return True
        except Exception as e:
            logger.error(f"Error updating data in subcollection '{subcollection}' for user '{user_id}': {e}")
            return False

    def delete_subcollection_document(self, user_id: str, subcollection: str, doc_id: str) -> bool:
        """
        Delete a specific document from a subcollection.
        
        Args:
            user_id (str): Unique identifier for the user.
            subcollection (str): The subcollection name.
            doc_id (str): The document ID to delete.
        
        Returns:
            bool: True if deletion is successful, False otherwise.
        """
        try:
            self.db.collection('users').document(user_id) \
                .collection(subcollection).document(doc_id).delete()
            logger.info(f"Document '{doc_id}' in subcollection '{subcollection}' for user '{user_id}' deleted successfully.")
            return True
        except Exception as e:
            logger.error(f"Error deleting document '{doc_id}' in subcollection '{subcollection}' for user '{user_id}': {e}")
            return False

    def get_all_subcollection_data(self, user_id: str, subcollection: str, 
                                   limit: int = None, order_by: str = None, 
                                   descending: bool = False, 
                                   filters: list = None) -> List[Dict[str, Any]]:
        """
        Retrieve documents from a specified subcollection for a given user with optional query parameters.
        
        Args:
            user_id (str): Unique identifier for the user.
            subcollection (str): The subcollection name.
            limit (int, optional): Maximum number of documents to return.
            order_by (str, optional): The field name to order the results by.
            descending (bool, optional): If True, order results in descending order.
            filters (list, optional): A list of filter conditions, where each condition is a dict
                with keys "field", "operator", and "value". For example:
                [{"field": "timestamp", "operator": ">=", "value": <some date>}]
        
        Returns:
            List[Dict[str, Any]]: A list of documents (as dictionaries). If no documents are found,
            logs a warning and returns an empty list.
        """
        try:
            query = self.db.collection('users').document(user_id).collection(subcollection)
            if filters:
                for condition in filters:
                    query = query.where(condition["field"], condition["operator"], condition["value"])
            if order_by:
                direction = firestore.Query.DESCENDING if descending else firestore.Query.ASCENDING
                query = query.order_by(order_by, direction=direction)
            if limit:
                query = query.limit(limit)
            docs = query.get()
            logger.info(f"Retrieved {len(docs)} documents from subcollection '{subcollection}' for user '{user_id}'.")
            if not docs:
                logger.warning(f"No data found for subcollection '{subcollection}' for user '{user_id}'.")
            return [doc.to_dict() for doc in docs]
        except Exception as e:
            logger.error(f"Error retrieving subcollection '{subcollection}' for user '{user_id}': {e}")
            return []
