import logging
from typing import Callable, Any, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MessageBus")

class MessageBus:
    """
    A simple publish/subscribe message bus for inter-agent communication.
    """
    _subscribers: Dict[str, List[Callable[[Any], None]]] = {}

    @classmethod
    def subscribe(cls, event_type: str, callback: Callable[[Any], None]) -> None:
        """
        Subscribe a callback function to a specific event type.
        
        Args:
            event_type (str): The type of event to subscribe to.
            callback (callable): A function that accepts one argument (the event data).
        """
        cls._subscribers.setdefault(event_type, []).append(callback)
        logger.info(f"Subscribed a new callback to event '{event_type}'.")

    @classmethod
    def publish(cls, event_type: str, data: Any) -> None:
        """
        Publish an event to all subscribers.
        
        Args:
            event_type (str): The type of event.
            data (any): The data associated with the event.
        """
        callbacks = cls._subscribers.get(event_type, [])
        if callbacks:
            logger.info(f"Publishing event '{event_type}' to {len(callbacks)} subscriber(s).")
            for callback in callbacks:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"Error in callback for event '{event_type}': {e}")
        else:
            logger.info(f"No subscribers for event '{event_type}'.")

# Example usage:
if __name__ == "__main__":
    def sample_callback(event_data):
        print("Received event data:", event_data)

    # Subscribe to an event type
    MessageBus.subscribe("user_updated", sample_callback)

    # Publish an event
    MessageBus.publish("user_updated", {"user_id": "user_123", "status": "active"})
