import logging
import os
import time
import requests
from typing import Optional
from config import ANTHROPIC_API_KEY

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AIIntegration")

# Updated constants with valid model IDs
DEFAULT_MODEL = "claude-3-sonnet-20240229"
VALID_MODELS = {
    "sonnet": "claude-3-sonnet-20240229",
    "haiku": "claude-3-haiku-20240307",
    "opus": "claude-3-opus-20240229"
}
API_VERSION = "2023-06-01"
DEFAULT_TEMPERATURE = 0.7
DEFAULT_MAX_TOKENS = 1000
REQUEST_LIMIT = 10  # Adjusted to <PERSON><PERSON><PERSON>'s recommended rate limits

# Throttling state
last_call_time = 0
call_count = 0

def _throttle():
    """Intelligent rate limiting that adapts to API guidelines"""
    global last_call_time, call_count
    current_time = time.time()
    
    # Reset counter if more than a minute has passed
    if current_time - last_call_time > 60:
        last_call_time = current_time
        call_count = 0
        
    # Enforce rate limit with buffer
    if call_count >= REQUEST_LIMIT:
        sleep_time = 60 - (current_time - last_call_time) + 5  # 5-second buffer
        logger.warning(f"Rate limit approaching. Sleeping for {sleep_time:.1f}s")
        time.sleep(max(sleep_time, 10))  # Minimum 10-second sleep
        last_call_time = time.time()
        call_count = 0
        
    call_count += 1

def call_advanced_model(
    prompt: str,
    model: str = DEFAULT_MODEL,
    max_tokens: int = DEFAULT_MAX_TOKENS,
    temperature: float = DEFAULT_TEMPERATURE,
    system_prompt: Optional[str] = None,
    stop_sequences: list = ["\nHuman:"],
    timeout: int = 20
) -> str:
    """
    Robust AI integration handler with enhanced error handling.
    
    Args:
        prompt: User input text
        model: Model identifier (name or full ID)
        max_tokens: Response length limit (100-4096)
        temperature: Creativity control (0-1)
        system_prompt: Base instructions/context
        stop_sequences: Generation stopping criteria
        timeout: Network timeout in seconds
    
    Returns:
        str: AI-generated response or empty string on failure
    """
    # Test mode bypass
    if os.getenv("TEST_MODE") == "true":
        logger.info("Test mode active - returning mock response")
        return f"TEST RESPONSE: {prompt[:50]}..." if len(prompt) > 50 else prompt
    
    try:
        _throttle()
        
        # Validate and resolve model ID
        model_id = VALID_MODELS.get(model.lower(), model)
        if model_id not in VALID_MODELS.values():
            raise ValueError(f"Invalid model specified: {model}")

        headers = {
            "Content-Type": "application/json",
            "x-api-key": ANTHROPIC_API_KEY,
            "anthropic-version": API_VERSION,
            "anthropic-beta": "messages-2023-12-15"
        }
        
        payload = {
            "model": model_id,
            "max_tokens": max_tokens,
            "temperature": max(0.0, min(temperature, 1.0)),
            "stop_sequences": stop_sequences,
            "messages": [{"role": "user", "content": prompt}]
        }
        
        if system_prompt:
            payload["system"] = system_prompt

        response = requests.post(
            "https://api.anthropic.com/v1/messages",
            json=payload,
            headers=headers,
            timeout=timeout
        )
        response.raise_for_status()
        
        # Parse response with structural validation
        response_json = response.json()
        if not isinstance(response_json, dict):
            raise ValueError("Invalid API response structure")
            
        content = response_json.get("content", [])
        if content and isinstance(content, list):
            first_content = content[0]
            if isinstance(first_content, dict):
                return first_content.get("text", "").strip()
        
        return ""
        
    except requests.exceptions.RequestException as e:
        error_msg = f"API request failed: {e}"
        if e.response is not None:
            error_msg += f"\nStatus: {e.response.status_code}"
            error_msg += f"\nResponse: {e.response.text[:200]}"
        logger.error(error_msg)
        return ""
    
    except ValueError as ve:
        logger.error(f"Configuration error: {ve}")
        return ""
    
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return ""