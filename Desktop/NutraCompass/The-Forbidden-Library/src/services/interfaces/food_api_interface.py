from abc import ABC, abstractmethod
from typing import Dict, Any

class FoodAPIInterface(ABC):
    """
    Defines the interface for food-related API integrations.
    """
    @abstractmethod
    def search_food(self, search_term: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search for food items by a search term.
        
        Args:
            search_term (str): The term to search for.
            limit (int, optional): Maximum number of food items to fetch. Defaults to 10.
        
        Returns:
            Dict[str, Any]: The API response data.
        """
        pass

    @abstractmethod
    def search_food_by_barcode(self, barcode: str) -> Dict[str, Any]:
        """
        Search for a food item using its barcode (UPC).
        
        Args:
            barcode (str): The barcode to search for.
        
        Returns:
            Dict[str, Any]: The API response data.
        """
        pass

    @abstractmethod
    def get_nutrients(self, ingredients: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retrieve nutrient information for the given ingredients.
        
        Args:
            ingredients (Dict[str, Any]): An object containing ingredient data.
        
        Returns:
            Dict[str, Any]: The nutrient data.
        """
        pass
