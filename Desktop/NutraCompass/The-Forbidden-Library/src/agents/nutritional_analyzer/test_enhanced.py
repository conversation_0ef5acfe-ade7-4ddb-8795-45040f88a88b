"""
Test suite for Enhanced Nutritional Analyzer
Tests API integration, image analysis, and AI capabilities
"""

import logging
import sys
from pathlib import Path
import json

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from agents.nutritional_analyzer.main import NutritionalAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("TestEnhancedAnalyzer")

def test_basic_functionality():
    """Test basic food analysis functionality"""
    print("\n=== Testing Basic Functionality ===")
    
    analyzer = NutritionalAnalyzer(use_real_apis=False)  # Use local database for reliable testing
    
    # Test known food
    result = analyzer.analyze_food_item("apple", 150)
    assert "nutrition" in result
    assert result["nutrition"]["calories"] > 0
    print("✅ Basic food analysis working")
    
    # Test meal analysis
    meal_result = analyzer.analyze_meal(["apple", "banana"], [150, 120])
    assert "total_nutrition" in meal_result
    assert meal_result["total_nutrition"]["calories"] > 0
    print("✅ Meal analysis working")
    
    return True

def test_api_integration():
    """Test real API integration (if available)"""
    print("\n=== Testing API Integration ===")
    
    try:
        analyzer = NutritionalAnalyzer(use_real_apis=True)
        
        # Test with a common food that should be in API
        result = analyzer.analyze_food_item("banana", 100)
        
        if "error" in result:
            print("⚠️ API integration not available (expected in demo mode)")
            return True
        
        if result.get("data_source") == "edamam_api":
            print("✅ Real API integration working")
            print(f"   Found {result.get('api_response_summary', {}).get('total_nutrients_found', 'unknown')} nutrients")
        else:
            print("ℹ️ Fell back to local database")
        
        return True
        
    except Exception as e:
        print(f"⚠️ API test failed (expected in demo mode): {str(e)}")
        return True

def test_ai_estimation():
    """Test AI-based nutrition estimation for unknown foods"""
    print("\n=== Testing AI Estimation ===")
    
    analyzer = NutritionalAnalyzer(use_real_apis=False)
    
    # Test with unknown food
    result = analyzer.analyze_food_item("exotic_dragon_fruit_smoothie", 200)
    
    assert "nutrition" in result
    assert result.get("data_source") == "ai_estimation"
    assert result.get("confidence", 0) < 1.0  # Should have lower confidence
    print("✅ AI estimation working for unknown foods")
    
    return True

def test_enhanced_features():
    """Test enhanced features like portion estimation and analysis"""
    print("\n=== Testing Enhanced Features ===")
    
    analyzer = NutritionalAnalyzer(use_real_apis=False)
    
    # Test different portion sizes
    small_portion = analyzer.analyze_food_item("chicken breast", 50)
    large_portion = analyzer.analyze_food_item("chicken breast", 200)
    
    small_calories = small_portion["nutrition"]["calories"]
    large_calories = large_portion["nutrition"]["calories"]
    
    # Large portion should have 4x the calories of small portion
    ratio = large_calories / small_calories
    assert 3.8 < ratio < 4.2, f"Portion scaling incorrect: {ratio}"
    print("✅ Portion size scaling working correctly")
    
    # Test meal summary generation
    meal_result = analyzer.analyze_meal(
        ["chicken breast", "brown rice", "broccoli"], 
        [150, 100, 100]
    )
    
    summary = meal_result.get("analysis_summary", {})
    assert "macronutrient_breakdown" in summary
    assert "meal_classification" in summary
    assert "health_notes" in summary
    print("✅ Meal summary generation working")
    
    return True

def test_data_sources():
    """Test different data source priorities"""
    print("\n=== Testing Data Source Priorities ===")
    
    # Test with API enabled
    api_analyzer = NutritionalAnalyzer(use_real_apis=True)
    
    # Test with API disabled
    local_analyzer = NutritionalAnalyzer(use_real_apis=False)
    
    test_food = "apple"
    
    api_result = api_analyzer.analyze_food_item(test_food, 100)
    local_result = local_analyzer.analyze_food_item(test_food, 100)
    
    # Both should return valid results
    assert "nutrition" in api_result
    assert "nutrition" in local_result
    
    print(f"✅ API analyzer source: {api_result.get('data_source')}")
    print(f"✅ Local analyzer source: {local_result.get('data_source')}")
    
    return True

def test_error_handling():
    """Test error handling for various edge cases"""
    print("\n=== Testing Error Handling ===")
    
    analyzer = NutritionalAnalyzer(use_real_apis=False)
    
    # Test empty food name
    result = analyzer.analyze_food_item("", 100)
    # Should handle gracefully (either error or estimation)
    assert isinstance(result, dict)
    print("✅ Empty food name handled")
    
    # Test zero portion
    result = analyzer.analyze_food_item("apple", 0)
    assert isinstance(result, dict)
    print("✅ Zero portion handled")
    
    # Test mismatched meal inputs
    result = analyzer.analyze_meal(["apple", "banana"], [100])  # Mismatched lengths
    assert "error" in result
    print("✅ Mismatched meal inputs handled")
    
    return True

def test_nutrition_data_quality():
    """Test the quality and consistency of nutrition data"""
    print("\n=== Testing Nutrition Data Quality ===")
    
    analyzer = NutritionalAnalyzer(use_real_apis=False)
    
    # Test that nutrition values are reasonable
    result = analyzer.analyze_food_item("chicken breast", 100)
    nutrition = result["nutrition"]
    
    # Chicken breast should be high protein, low carb
    assert nutrition["protein_g"] > 20, "Chicken breast should be high protein"
    assert nutrition["carbohydrates_g"] < 5, "Chicken breast should be low carb"
    assert nutrition["calories"] > 100, "Chicken breast should have substantial calories"
    print("✅ Nutrition data quality checks passed")
    
    # Test consistency across portion sizes
    result_50g = analyzer.analyze_food_item("apple", 50)
    result_100g = analyzer.analyze_food_item("apple", 100)
    
    ratio = result_100g["nutrition"]["calories"] / result_50g["nutrition"]["calories"]
    assert 1.9 < ratio < 2.1, "Portion scaling should be linear"
    print("✅ Portion scaling consistency verified")
    
    return True

def run_all_tests():
    """Run all test functions"""
    print("🧪 Running Enhanced Nutritional Analyzer Tests")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_api_integration,
        test_ai_estimation,
        test_enhanced_features,
        test_data_sources,
        test_error_handling,
        test_nutrition_data_quality
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__} failed: {str(e)}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced Nutritional Analyzer is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
