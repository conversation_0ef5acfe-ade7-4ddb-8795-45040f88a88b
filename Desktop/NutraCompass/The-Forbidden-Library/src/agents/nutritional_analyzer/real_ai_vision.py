"""
Real AI Food Recognition System
Uses pre-trained models to actually recognize food items from images
"""

import logging
import torch
import torchvision.transforms as transforms
from PIL import Image
import io
import numpy as np
from typing import Dict, List, Any, Tuple
import requests
import json

logger = logging.getLogger(__name__)

class RealFoodAI:
    """Real AI food recognition using pre-trained models"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Food-101 class names (common foods)
        self.food_classes = [
            'apple_pie', 'baby_back_ribs', 'baklava', 'beef_carpaccio', 'beef_tartare',
            'beet_salad', 'beignets', 'bibimbap', 'bread_pudding', 'breakfast_burrito',
            'bruschetta', 'caesar_salad', 'cannoli', 'caprese_salad', 'carrot_cake',
            'ceviche', 'cheese_plate', 'cheesecake', 'chicken_curry', 'chicken_quesadilla',
            'chicken_wings', 'chocolate_cake', 'chocolate_mousse', 'churros', 'clam_chowder',
            'club_sandwich', 'crab_cakes', 'creme_brulee', 'croque_madame', 'cup_cakes',
            'deviled_eggs', 'donuts', 'dumplings', 'edamame', 'eggs_benedict',
            'escargots', 'falafel', 'filet_mignon', 'fish_and_chips', 'foie_gras',
            'french_fries', 'french_onion_soup', 'french_toast', 'fried_calamari', 'fried_rice',
            'frozen_yogurt', 'garlic_bread', 'gnocchi', 'greek_salad', 'grilled_cheese_sandwich',
            'grilled_salmon', 'guacamole', 'gyoza', 'hamburger', 'hot_and_sour_soup',
            'hot_dog', 'huevos_rancheros', 'hummus', 'ice_cream', 'lasagna',
            'lobster_bisque', 'lobster_roll_sandwich', 'macaroni_and_cheese', 'macarons', 'miso_soup',
            'mussels', 'nachos', 'omelette', 'onion_rings', 'oysters',
            'pad_thai', 'paella', 'pancakes', 'panna_cotta', 'peking_duck',
            'pho', 'pizza', 'pork_chop', 'poutine', 'prime_rib',
            'pulled_pork_sandwich', 'ramen', 'ravioli', 'red_velvet_cake', 'risotto',
            'samosa', 'sashimi', 'scallops', 'seaweed_salad', 'shrimp_and_grits',
            'spaghetti_bolognese', 'spaghetti_carbonara', 'spring_rolls', 'steak', 'strawberry_shortcake',
            'sushi', 'tacos', 'takoyaki', 'tiramisu', 'tuna_tartare',
            'waffles', 'apple', 'banana', 'orange', 'broccoli', 'carrot'
        ]
        
        # Initialize models
        self._load_models()
        
        # Nutrition database for common foods (calories per 100g)
        self.nutrition_db = {
            'apple': {'calories': 52, 'protein': 0.3, 'fat': 0.2, 'carbs': 14, 'fiber': 2.4},
            'banana': {'calories': 89, 'protein': 1.1, 'fat': 0.3, 'carbs': 23, 'fiber': 2.6},
            'orange': {'calories': 47, 'protein': 0.9, 'fat': 0.1, 'carbs': 12, 'fiber': 2.4},
            'broccoli': {'calories': 34, 'protein': 2.8, 'fat': 0.4, 'carbs': 7, 'fiber': 2.6},
            'carrot': {'calories': 41, 'protein': 0.9, 'fat': 0.2, 'carbs': 10, 'fiber': 2.8},
            'pizza': {'calories': 266, 'protein': 11, 'fat': 10, 'carbs': 33, 'fiber': 2.3},
            'hamburger': {'calories': 295, 'protein': 17, 'fat': 14, 'carbs': 28, 'fiber': 2.0},
            'french_fries': {'calories': 365, 'protein': 4, 'fat': 17, 'carbs': 63, 'fiber': 4.0},
            'hot_dog': {'calories': 290, 'protein': 10, 'fat': 26, 'carbs': 4, 'fiber': 0.0},
            'ice_cream': {'calories': 207, 'protein': 3.5, 'fat': 11, 'carbs': 24, 'fiber': 0.7},
            'chicken_wings': {'calories': 203, 'protein': 30, 'fat': 8.1, 'carbs': 0, 'fiber': 0},
            'grilled_salmon': {'calories': 231, 'protein': 25, 'fat': 14, 'carbs': 0, 'fiber': 0},
            'caesar_salad': {'calories': 158, 'protein': 3, 'fat': 13, 'carbs': 8, 'fiber': 2},
            'spaghetti_bolognese': {'calories': 151, 'protein': 8, 'fat': 5, 'carbs': 19, 'fiber': 2},
            'chocolate_cake': {'calories': 371, 'protein': 5, 'fat': 16, 'carbs': 56, 'fiber': 3},
            'pancakes': {'calories': 227, 'protein': 6, 'fat': 9, 'carbs': 28, 'fiber': 1},
            'waffles': {'calories': 291, 'protein': 6, 'fat': 9, 'carbs': 48, 'fiber': 2},
            'donuts': {'calories': 452, 'protein': 5, 'fat': 25, 'carbs': 51, 'fiber': 2},
            'tacos': {'calories': 226, 'protein': 9, 'fat': 13, 'carbs': 18, 'fiber': 3},
            'sushi': {'calories': 143, 'protein': 6, 'fat': 3.5, 'carbs': 21, 'fiber': 3}
        }
        
        self.logger.info("Real AI Food Recognition initialized")
    
    def _load_models(self):
        """Load pre-trained food recognition models"""
        try:
            # Try to use YOLOv8 for object detection first
            try:
                from ultralytics import YOLO
                self.yolo_model = YOLO('yolov8n.pt')  # Nano version for speed
                self.use_yolo = True
                self.logger.info("YOLO model loaded for object detection")
            except Exception as e:
                self.logger.warning(f"YOLO not available: {e}")
                self.use_yolo = False
                self.yolo_model = None

            # Use ResNet18 as backup for classification
            import timm
            self.model = timm.create_model('resnet18', pretrained=True, num_classes=1000)  # Keep ImageNet classes
            self.model.eval()
            self.model.to(self.device)

            # Image preprocessing
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

            # ImageNet class names that are food-related
            self.imagenet_food_classes = {
                'apple': 948, 'banana': 954, 'orange': 950, 'lemon': 951,
                'strawberry': 949, 'pineapple': 953, 'pomegranate': 957,
                'broccoli': 937, 'cauliflower': 938, 'mushroom': 947,
                'bell_pepper': 945, 'cucumber': 943, 'artichoke': 944,
                'corn': 987, 'acorn_squash': 988, 'butternut_squash': 989,
                'pizza': 963, 'cheeseburger': 933, 'hot_dog': 934,
                'ice_cream': 928, 'chocolate_sauce': 960, 'pretzel': 932,
                'bagel': 931, 'muffin': 925, 'croissant': 930,
                'french_loaf': 930, 'meat_loaf': 935, 'carbonara': 959
            }

            self.logger.info("Food recognition model loaded successfully")

        except Exception as e:
            self.logger.error(f"Failed to load models: {e}")
            # Fallback to simple classification
            self.model = None
            self.use_yolo = False
            self.yolo_model = None
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor()
            ])
    
    def recognize_food(self, image_bytes: bytes) -> List[Dict[str, Any]]:
        """
        Recognize food items in image using real AI
        """
        try:
            # Load and preprocess image
            image = Image.open(io.BytesIO(image_bytes))
            if image.mode != 'RGB':
                image = image.convert('RGB')

            results = []

            # Try YOLO first for object detection
            if self.use_yolo and self.yolo_model:
                yolo_results = self._detect_with_yolo(image)
                results.extend(yolo_results)

            # Try ImageNet classification for food items
            if self.model is not None:
                imagenet_results = self._classify_with_imagenet(image)
                results.extend(imagenet_results)

            # If no results, fall back to enhanced heuristics
            if not results:
                results = self._classify_with_heuristics(image)

            # Remove duplicates and sort by confidence
            results = self._deduplicate_results(results)
            results.sort(key=lambda x: x.get('confidence', 0), reverse=True)

            return results[:3]  # Return top 3 results

        except Exception as e:
            self.logger.error(f"Food recognition failed: {e}")
            return []

    def _detect_with_yolo(self, image: Image.Image) -> List[Dict[str, Any]]:
        """Detect objects using YOLO"""
        try:
            results = self.yolo_model(image, verbose=False)
            detected_foods = []

            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get class name
                        class_id = int(box.cls[0])
                        class_name = self.yolo_model.names[class_id]
                        confidence = float(box.conf[0])

                        # Check if it's food-related
                        if self._is_food_class(class_name) and confidence > 0.3:
                            detected_foods.append({
                                'label': class_name,
                                'confidence': confidence,
                                'detection_method': 'yolo',
                                'bbox': box.xyxy[0].tolist()
                            })

            return detected_foods

        except Exception as e:
            self.logger.error(f"YOLO detection failed: {e}")
            return []

    def _classify_with_imagenet(self, image: Image.Image) -> List[Dict[str, Any]]:
        """Classify using ImageNet model"""
        try:
            # Preprocess image
            input_tensor = self.transform(image).unsqueeze(0).to(self.device)

            # Get predictions
            with torch.no_grad():
                outputs = self.model(input_tensor)
                probabilities = torch.nn.functional.softmax(outputs[0], dim=0)

            # Check food-related classes
            results = []
            for food_name, class_idx in self.imagenet_food_classes.items():
                prob = probabilities[class_idx].item()
                if prob > 0.1:  # 10% threshold
                    results.append({
                        'label': food_name,
                        'confidence': prob,
                        'detection_method': 'imagenet',
                        'class_index': class_idx
                    })

            return results

        except Exception as e:
            self.logger.error(f"ImageNet classification failed: {e}")
            return []

    def _is_food_class(self, class_name: str) -> bool:
        """Check if YOLO class is food-related"""
        food_keywords = [
            'apple', 'banana', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza',
            'donut', 'cake', 'sandwich', 'food', 'fruit', 'vegetable'
        ]
        return any(keyword in class_name.lower() for keyword in food_keywords)

    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate detections"""
        seen_foods = set()
        unique_results = []

        for result in results:
            food_name = result['label'].lower()
            if food_name not in seen_foods:
                seen_foods.add(food_name)
                unique_results.append(result)

        return unique_results

    def _classify_with_model(self, image: Image.Image) -> List[Dict[str, Any]]:
        """Classify food using the neural network model"""
        try:
            # Preprocess image
            input_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            # Get predictions
            with torch.no_grad():
                outputs = self.model(input_tensor)
                probabilities = torch.nn.functional.softmax(outputs[0], dim=0)
            
            # Get top 3 predictions
            top_probs, top_indices = torch.topk(probabilities, 3)
            
            results = []
            for i in range(len(top_probs)):
                prob = top_probs[i].item()
                if prob > 0.1:  # Only include predictions with >10% confidence
                    class_name = self.food_classes[top_indices[i].item()]
                    
                    # Clean up class name (remove underscores, etc.)
                    clean_name = class_name.replace('_', ' ')
                    
                    results.append({
                        'label': clean_name,
                        'confidence': prob,
                        'raw_class': class_name
                    })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Model classification failed: {e}")
            return []
    
    def _classify_with_heuristics(self, image: Image.Image) -> List[Dict[str, Any]]:
        """Enhanced heuristic classification when model isn't available"""
        try:
            import cv2
            
            # Convert to numpy array
            img_array = np.array(image)
            
            # Analyze image properties
            hsv = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)
            
            # More sophisticated color analysis
            results = []
            
            # Analyze dominant colors
            dominant_colors = self._get_dominant_colors(img_array)
            
            # Map colors to likely foods
            color_food_mapping = {
                'red': ['apple', 'strawberry', 'tomato', 'red_pepper'],
                'yellow': ['banana', 'corn', 'lemon', 'yellow_pepper'],
                'orange': ['orange', 'carrot', 'sweet_potato', 'pumpkin'],
                'green': ['broccoli', 'lettuce', 'spinach', 'green_pepper'],
                'brown': ['chocolate_cake', 'bread', 'coffee', 'nuts'],
                'white': ['rice', 'bread', 'milk', 'cheese']
            }
            
            for color_name, foods in color_food_mapping.items():
                if self._color_present(hsv, color_name):
                    # Pick the most likely food for this color
                    confidence = self._calculate_color_confidence(hsv, color_name)
                    if confidence > 0.3:
                        results.append({
                            'label': foods[0],  # Most common food for this color
                            'confidence': confidence,
                            'detection_method': 'color_analysis'
                        })
            
            # If no clear color matches, try texture analysis
            if not results:
                texture_result = self._analyze_texture(img_array)
                if texture_result:
                    results.append(texture_result)
            
            return results[:3]  # Return top 3 results
            
        except Exception as e:
            self.logger.error(f"Heuristic classification failed: {e}")
            return [{'label': 'food_item', 'confidence': 0.5, 'detection_method': 'fallback'}]
    
    def _get_dominant_colors(self, img_array: np.ndarray) -> List[str]:
        """Get dominant colors in the image"""
        try:
            import cv2
            from collections import Counter
            
            # Resize for faster processing
            small_img = cv2.resize(img_array, (50, 50))
            hsv = cv2.cvtColor(small_img, cv2.COLOR_RGB2HSV)
            
            # Classify each pixel into color categories
            colors = []
            for pixel in hsv.reshape(-1, 3):
                h, s, v = pixel
                
                if v < 50:  # Very dark
                    colors.append('black')
                elif s < 50:  # Low saturation
                    colors.append('white' if v > 200 else 'gray')
                elif h < 10 or h > 170:  # Red range
                    colors.append('red')
                elif h < 25:  # Orange range
                    colors.append('orange')
                elif h < 35:  # Yellow range
                    colors.append('yellow')
                elif h < 85:  # Green range
                    colors.append('green')
                elif h < 130:  # Blue range
                    colors.append('blue')
                else:  # Purple range
                    colors.append('purple')
            
            # Get most common colors
            color_counts = Counter(colors)
            return [color for color, count in color_counts.most_common(3)]
            
        except Exception as e:
            self.logger.error(f"Color analysis failed: {e}")
            return ['unknown']
    
    def _color_present(self, hsv: np.ndarray, color_name: str) -> bool:
        """Check if a specific color is present in the image"""
        try:
            import cv2
            
            color_ranges = {
                'red': [(0, 50, 50), (10, 255, 255)],
                'orange': [(10, 50, 50), (25, 255, 255)],
                'yellow': [(25, 50, 50), (35, 255, 255)],
                'green': [(35, 50, 50), (85, 255, 255)],
                'blue': [(85, 50, 50), (130, 255, 255)],
                'purple': [(130, 50, 50), (170, 255, 255)]
            }
            
            if color_name in color_ranges:
                lower, upper = color_ranges[color_name]
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                return np.sum(mask > 0) > (hsv.shape[0] * hsv.shape[1] * 0.05)  # 5% threshold
            
            return False
            
        except Exception as e:
            return False
    
    def _calculate_color_confidence(self, hsv: np.ndarray, color_name: str) -> float:
        """Calculate confidence for color detection"""
        try:
            import cv2
            
            color_ranges = {
                'red': [(0, 50, 50), (10, 255, 255)],
                'orange': [(10, 50, 50), (25, 255, 255)],
                'yellow': [(25, 50, 50), (35, 255, 255)],
                'green': [(35, 50, 50), (85, 255, 255)],
                'blue': [(85, 50, 50), (130, 255, 255)],
                'purple': [(130, 50, 50), (170, 255, 255)]
            }
            
            if color_name in color_ranges:
                lower, upper = color_ranges[color_name]
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                color_ratio = np.sum(mask > 0) / (hsv.shape[0] * hsv.shape[1])
                return min(0.9, color_ratio * 5)  # Scale and cap at 90%
            
            return 0.0
            
        except Exception as e:
            return 0.0
    
    def _analyze_texture(self, img_array: np.ndarray) -> Dict[str, Any]:
        """Analyze texture to guess food type"""
        try:
            import cv2
            
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # Calculate texture features
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (gray.shape[0] * gray.shape[1])
            
            # Guess based on texture
            if edge_density > 0.3:
                return {'label': 'textured_food', 'confidence': 0.6, 'detection_method': 'texture'}
            elif edge_density > 0.1:
                return {'label': 'smooth_food', 'confidence': 0.5, 'detection_method': 'texture'}
            else:
                return {'label': 'liquid_food', 'confidence': 0.4, 'detection_method': 'texture'}
                
        except Exception as e:
            return None
    
    def get_nutrition_data(self, food_name: str, portion_grams: float = 100) -> Dict[str, Any]:
        """Get nutrition data for recognized food"""
        # Clean food name
        clean_name = food_name.lower().replace(' ', '_')
        
        # Look up in our database
        if clean_name in self.nutrition_db:
            base_nutrition = self.nutrition_db[clean_name]
            multiplier = portion_grams / 100.0
            
            return {
                'food_name': food_name,
                'portion_size_g': portion_grams,
                'nutrition': {
                    'calories': round(base_nutrition['calories'] * multiplier, 1),
                    'protein_g': round(base_nutrition['protein'] * multiplier, 1),
                    'fat_g': round(base_nutrition['fat'] * multiplier, 1),
                    'carbohydrates_g': round(base_nutrition['carbs'] * multiplier, 1),
                    'fiber_g': round(base_nutrition.get('fiber', 0) * multiplier, 1)
                },
                'data_source': 'real_nutrition_database',
                'confidence': 0.9
            }
        else:
            # Fallback estimation
            return {
                'food_name': food_name,
                'portion_size_g': portion_grams,
                'nutrition': {
                    'calories': round(portion_grams * 2.0, 1),
                    'protein_g': round(portion_grams * 0.1, 1),
                    'fat_g': round(portion_grams * 0.05, 1),
                    'carbohydrates_g': round(portion_grams * 0.15, 1),
                    'fiber_g': round(portion_grams * 0.02, 1)
                },
                'data_source': 'estimated',
                'confidence': 0.5
            }
