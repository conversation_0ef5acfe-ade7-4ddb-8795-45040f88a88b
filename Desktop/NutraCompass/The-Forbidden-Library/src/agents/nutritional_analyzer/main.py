"""
Nutritional Analyzer - AI system for extracting nutritional information from food items
Enhanced with real API integration and computer vision capabilities
"""

import logging
import json
import io
import numpy as np
from typing import Dict, List, Any, Optional
import requests
from dataclasses import dataclass
import sys
import os
from pathlib import Path

# Add services to path for API integration
sys.path.append(str(Path(__file__).parent.parent.parent))
from services.external_api_integrator import ExternalAPIIntegrator

logger = logging.getLogger(__name__)

@dataclass
class NutritionData:
    """Structure for nutritional information"""
    calories: float
    protein: float  # grams
    fat: float      # grams
    carbohydrates: float  # grams
    fiber: Optional[float] = None
    sugar: Optional[float] = None
    sodium: Optional[float] = None

class NutritionalAnalyzer:
    """
    AI-powered nutritional analyzer that extracts nutritional information
    from food items and meals using multiple data sources and AI models.
    Enhanced with real API integration and computer vision capabilities.
    """

    def __init__(self, use_real_apis: bool = True, use_real_ai_vision: bool = True):
        """Initialize the nutritional analyzer"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.use_real_apis = use_real_apis
        self.use_real_ai_vision = use_real_ai_vision

        # Initialize API integrator for real nutrition data
        if self.use_real_apis:
            try:
                self.api_integrator = ExternalAPIIntegrator()
                self.logger.info("Initialized with real API integration")
            except Exception as e:
                self.logger.warning(f"Failed to initialize API integrator: {e}. Falling back to local database.")
                self.api_integrator = None
                self.use_real_apis = False
        else:
            self.api_integrator = None

        # Initialize Eagle Eye vision system
        if self.use_real_ai_vision:
            try:
                from agents.eagle_eye.main import EagleEye
                # Try to initialize Eagle Eye with a model path
                # For now, we'll handle the case where no model is available
                self.eagle_eye = None  # Will be initialized when needed
                self.logger.info("Eagle Eye vision system ready (will initialize on first use)")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Eagle Eye: {e}. Falling back to simple detection.")
                self.eagle_eye = None
                self.use_real_ai_vision = False
        else:
            self.eagle_eye = None

        # Nutritional database - basic food items with nutritional info per 100g
        self.nutrition_db = {
            "apple": NutritionData(52, 0.3, 0.2, 14, 2.4, 10.4, 1),
            "banana": NutritionData(89, 1.1, 0.3, 23, 2.6, 12.2, 1),
            "chicken breast": NutritionData(165, 31, 3.6, 0, 0, 0, 74),
            "brown rice": NutritionData(111, 2.6, 0.9, 23, 1.8, 0.4, 5),
            "broccoli": NutritionData(34, 2.8, 0.4, 7, 2.6, 1.5, 33),
            "oatmeal": NutritionData(68, 2.4, 1.4, 12, 1.7, 0.3, 4),
            "almond milk": NutritionData(17, 0.6, 1.1, 1.5, 0.4, 1.3, 63),
            "salmon": NutritionData(208, 25.4, 12.4, 0, 0, 0, 59),
            "spinach": NutritionData(23, 2.9, 0.4, 3.6, 2.2, 0.4, 79),
            "sweet potato": NutritionData(86, 1.6, 0.1, 20, 3.0, 4.2, 7),
            "avocado": NutritionData(160, 2.0, 15, 9, 7, 0.7, 7),
            "quinoa": NutritionData(120, 4.4, 1.9, 22, 2.8, 0.9, 7),
            "greek yogurt": NutritionData(59, 10, 0.4, 3.6, 0, 3.2, 36),
            "almonds": NutritionData(579, 21, 50, 22, 12, 4.4, 1),
            "eggs": NutritionData(155, 13, 11, 1.1, 0, 0.6, 124)
        }
        
        self.logger.info("NutritionalAnalyzer initialized with basic nutrition database")
    
    def analyze_food_item(self, food_name: str, portion_size: float = 100) -> Dict[str, Any]:
        """
        Analyze nutritional content of a single food item
        
        Args:
            food_name: Name of the food item
            portion_size: Portion size in grams (default: 100g)
            
        Returns:
            Dictionary containing nutritional analysis
        """
        try:
            self.logger.info(f"Analyzing food item: {food_name} ({portion_size}g)")
            
            # Normalize food name
            normalized_name = food_name.lower().strip()
            
            # Try real API first if available
            if self.use_real_apis and self.api_integrator:
                api_result = self._get_nutrition_from_api(food_name, portion_size)
                if api_result and "nutrition" in api_result:
                    return api_result

            # Fallback to local database
            if normalized_name in self.nutrition_db:
                base_nutrition = self.nutrition_db[normalized_name]

                # Calculate for portion size
                multiplier = portion_size / 100.0

                result = {
                    "food_name": food_name,
                    "portion_size_g": portion_size,
                    "nutrition": {
                        "calories": round(base_nutrition.calories * multiplier, 1),
                        "protein_g": round(base_nutrition.protein * multiplier, 1),
                        "fat_g": round(base_nutrition.fat * multiplier, 1),
                        "carbohydrates_g": round(base_nutrition.carbohydrates * multiplier, 1),
                        "fiber_g": round(base_nutrition.fiber * multiplier, 1) if base_nutrition.fiber else None,
                        "sugar_g": round(base_nutrition.sugar * multiplier, 1) if base_nutrition.sugar else None,
                        "sodium_mg": round(base_nutrition.sodium * multiplier, 1) if base_nutrition.sodium else None
                    },
                    "data_source": "internal_database",
                    "confidence": 0.9
                }

                self.logger.info(f"Successfully analyzed {food_name} from local database")
                return result
            else:
                # Try AI-based estimation for unknown foods
                return self._estimate_nutrition_ai(food_name, portion_size)
                
        except Exception as e:
            self.logger.error(f"Error analyzing food item {food_name}: {str(e)}")
            return {
                "food_name": food_name,
                "error": str(e),
                "nutrition": None
            }
    
    def analyze_meal(self, food_items: List[str], portions: List[float]) -> Dict[str, Any]:
        """
        Analyze nutritional content of a complete meal
        
        Args:
            food_items: List of food item names
            portions: List of portion sizes in grams
            
        Returns:
            Dictionary containing meal nutritional analysis
        """
        try:
            self.logger.info(f"Analyzing meal with {len(food_items)} items")
            
            if len(food_items) != len(portions):
                raise ValueError("Number of food items must match number of portions")
            
            meal_analysis = {
                "meal_items": [],
                "total_nutrition": {
                    "calories": 0,
                    "protein_g": 0,
                    "fat_g": 0,
                    "carbohydrates_g": 0,
                    "fiber_g": 0,
                    "sugar_g": 0,
                    "sodium_mg": 0
                },
                "analysis_summary": {}
            }
            
            # Analyze each food item
            for food_name, portion in zip(food_items, portions):
                item_analysis = self.analyze_food_item(food_name, portion)
                meal_analysis["meal_items"].append(item_analysis)
                
                # Add to totals if analysis was successful
                if "nutrition" in item_analysis and item_analysis["nutrition"]:
                    nutrition = item_analysis["nutrition"]
                    meal_analysis["total_nutrition"]["calories"] += nutrition.get("calories", 0)
                    meal_analysis["total_nutrition"]["protein_g"] += nutrition.get("protein_g", 0)
                    meal_analysis["total_nutrition"]["fat_g"] += nutrition.get("fat_g", 0)
                    meal_analysis["total_nutrition"]["carbohydrates_g"] += nutrition.get("carbohydrates_g", 0)
                    
                    # Handle optional nutrients
                    if nutrition.get("fiber_g"):
                        meal_analysis["total_nutrition"]["fiber_g"] += nutrition["fiber_g"]
                    if nutrition.get("sugar_g"):
                        meal_analysis["total_nutrition"]["sugar_g"] += nutrition["sugar_g"]
                    if nutrition.get("sodium_mg"):
                        meal_analysis["total_nutrition"]["sodium_mg"] += nutrition["sodium_mg"]
            
            # Round totals
            for key in meal_analysis["total_nutrition"]:
                meal_analysis["total_nutrition"][key] = round(meal_analysis["total_nutrition"][key], 1)
            
            # Add analysis summary
            meal_analysis["analysis_summary"] = self._generate_meal_summary(meal_analysis["total_nutrition"])
            
            self.logger.info("Successfully analyzed complete meal")
            return meal_analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing meal: {str(e)}")
            return {
                "error": str(e),
                "meal_items": food_items,
                "portions": portions
            }
    
    def _estimate_nutrition_ai(self, food_name: str, portion_size: float) -> Dict[str, Any]:
        """
        Use AI-based estimation for unknown food items
        This is a simplified version - in production, this would call external APIs
        """
        self.logger.info(f"Using AI estimation for unknown food: {food_name}")
        
        # Simple heuristic-based estimation (placeholder for actual AI)
        # In a real implementation, this would call nutrition APIs or ML models
        
        estimated_nutrition = {
            "food_name": food_name,
            "portion_size_g": portion_size,
            "nutrition": {
                "calories": round(portion_size * 2.0, 1),  # Rough estimate: 2 cal/g
                "protein_g": round(portion_size * 0.1, 1),  # 10% protein
                "fat_g": round(portion_size * 0.05, 1),     # 5% fat
                "carbohydrates_g": round(portion_size * 0.15, 1),  # 15% carbs
                "fiber_g": round(portion_size * 0.02, 1),
                "sugar_g": round(portion_size * 0.03, 1),
                "sodium_mg": round(portion_size * 0.5, 1)
            },
            "data_source": "ai_estimation",
            "confidence": 0.6,
            "note": "Estimated values - consider using specific nutrition database for accuracy"
        }
        
        return estimated_nutrition

    def _analyze_image_locally(self, image_bytes: bytes) -> Dict[str, Any]:
        """
        Analyze image using Eagle Eye or fallback to simple computer vision
        """
        try:
            from PIL import Image
            import numpy as np

            # Load image
            image = Image.open(io.BytesIO(image_bytes))
            image_array = np.array(image)

            # Try to use Eagle Eye if available
            if self.use_real_ai_vision:
                try:
                    self.logger.info("Attempting to use Eagle Eye vision system")
                    eagle_result = self._analyze_with_eagle_eye(image_bytes)
                    if eagle_result and "detection_results" in eagle_result:
                        self.logger.info("Eagle Eye analysis successful")
                        return eagle_result
                    else:
                        self.logger.warning("Eagle Eye returned no results, falling back to simple detection")
                except Exception as e:
                    self.logger.warning(f"Eagle Eye failed: {e}, falling back to simple detection")

            # Fallback to simple detection
            self.logger.info("Using fallback simple detection")
            detected_foods = self._detect_food_simple(image_array)
            physical_analysis = self._analyze_physical_simple(image_array)

            return {
                "detection_results": detected_foods,
                "physical_analysis": physical_analysis,
                "ai_method": "simple_detection"
            }

        except Exception as e:
            self.logger.error(f"Local image analysis failed: {str(e)}")
            return {"error": str(e)}

    def _analyze_with_eagle_eye(self, image_bytes: bytes) -> Dict[str, Any]:
        """
        Analyze image using the real Eagle Eye system
        """
        try:
            # Check if we have Google Cloud Vision credentials
            from src.config import CLOUD_VISION_CREDENTIALS_JSON
            if not CLOUD_VISION_CREDENTIALS_JSON or CLOUD_VISION_CREDENTIALS_JSON == "demo_credentials.json":
                self.logger.warning("No valid Google Cloud Vision credentials, cannot use Eagle Eye")
                return None

            # Try to initialize Eagle Eye if not already done
            if not self.eagle_eye:
                from agents.eagle_eye.main import EagleEye
                # For now, we'll create a minimal Eagle Eye without a trained model
                # In production, you'd have a trained segmentation model
                self.logger.info("Initializing Eagle Eye with Google Cloud Vision")
                self.eagle_eye = EagleEye(
                    model_path=None,  # No custom model for now
                    api_integrator=self.api_integrator,
                    detection_threshold=0.5
                )

            # Analyze the image
            result = self.eagle_eye.analyze_image(image_bytes)

            if "error" in result:
                self.logger.error(f"Eagle Eye analysis failed: {result['error']}")
                return None

            # Convert Eagle Eye results to our format
            detection_results = []
            for detection in result.get("detection_results", []):
                # Map Eagle Eye detection to our nutrition analysis format
                food_name = detection.get("label", "unknown_food")
                confidence = detection.get("confidence", 0.0)
                geometry = detection.get("geometry", {})

                detection_results.append({
                    "label": food_name,
                    "confidence": confidence,
                    "geometry": geometry,
                    "area_pixels": self._calculate_area_from_geometry(geometry),
                    "detection_method": "eagle_eye"
                })

            return {
                "detection_results": detection_results,
                "physical_analysis": result.get("physical_analysis", {}),
                "ai_method": "eagle_eye",
                "perception_data": result.get("perception_data", {})
            }

        except Exception as e:
            self.logger.error(f"Eagle Eye analysis failed: {str(e)}")
            return None

    def _calculate_area_from_geometry(self, geometry: Dict[str, float]) -> int:
        """Calculate pixel area from geometry data"""
        try:
            width = geometry.get("width", 0.3)
            height = geometry.get("height", 0.3)
            # Assume image size of 640x480 for estimation
            return int(width * height * 640 * 480)
        except:
            return 1000

    def _detect_food_simple(self, image_array: np.ndarray) -> List[Dict[str, Any]]:
        """
        Simple food detection using basic image analysis
        """
        try:
            import cv2

            # Convert to different color spaces for analysis
            hsv = cv2.cvtColor(image_array, cv2.COLOR_RGB2HSV)

            # Define color ranges for common foods
            food_colors = {
                "apple": {"lower": np.array([0, 50, 50]), "upper": np.array([10, 255, 255])},
                "banana": {"lower": np.array([20, 100, 100]), "upper": np.array([30, 255, 255])},
                "orange": {"lower": np.array([10, 100, 100]), "upper": np.array([20, 255, 255])},
                "broccoli": {"lower": np.array([40, 50, 50]), "upper": np.array([80, 255, 255])},
                "carrot": {"lower": np.array([10, 100, 100]), "upper": np.array([25, 255, 255])},
            }

            detected_foods = []

            # Check for each food color
            for food_name, color_range in food_colors.items():
                mask = cv2.inRange(hsv, color_range["lower"], color_range["upper"])

                # Find contours
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                if contours:
                    # Get largest contour
                    largest_contour = max(contours, key=cv2.contourArea)
                    area = cv2.contourArea(largest_contour)

                    # Only consider significant areas
                    if area > 1000:  # Minimum area threshold
                        # Calculate confidence based on area and color match
                        total_pixels = image_array.shape[0] * image_array.shape[1]
                        confidence = min(0.9, area / total_pixels * 10)  # Scale confidence

                        # Get bounding box
                        x, y, w, h = cv2.boundingRect(largest_contour)

                        detected_foods.append({
                            "label": food_name,
                            "confidence": confidence,
                            "geometry": {
                                "x": x / image_array.shape[1],
                                "y": y / image_array.shape[0],
                                "width": w / image_array.shape[1],
                                "height": h / image_array.shape[0]
                            },
                            "area_pixels": int(area)
                        })

            # If no specific foods detected, create a generic food detection
            if not detected_foods:
                # Use edge detection to find food-like objects
                gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
                edges = cv2.Canny(gray, 50, 150)
                contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                if contours:
                    largest_contour = max(contours, key=cv2.contourArea)
                    area = cv2.contourArea(largest_contour)

                    if area > 500:
                        x, y, w, h = cv2.boundingRect(largest_contour)
                        detected_foods.append({
                            "label": "food_item",
                            "confidence": 0.6,
                            "geometry": {
                                "x": x / image_array.shape[1],
                                "y": y / image_array.shape[0],
                                "width": w / image_array.shape[1],
                                "height": h / image_array.shape[0]
                            },
                            "area_pixels": int(area)
                        })

            return detected_foods

        except Exception as e:
            self.logger.error(f"Simple food detection failed: {str(e)}")
            return []

    def _analyze_physical_simple(self, image_array: np.ndarray) -> Dict[str, Any]:
        """
        Simple physical analysis of the image
        """
        try:
            import cv2

            # Basic image properties
            height, width = image_array.shape[:2]
            total_pixels = height * width

            # Convert to grayscale for analysis
            gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)

            # Find edges to estimate object boundaries
            edges = cv2.Canny(gray, 50, 150)
            edge_pixels = np.sum(edges > 0)

            # Estimate object area (simplified)
            # Areas with significant color variation likely contain objects
            blur = cv2.GaussianBlur(gray, (15, 15), 0)
            diff = cv2.absdiff(gray, blur)
            object_pixels = np.sum(diff > 30)

            # Simple volume estimation based on pixel area
            # Assume average food thickness of 3cm and pixel density
            pixel_area = object_pixels
            estimated_thickness_m = 0.03  # 3cm average thickness
            pixel_to_m2 = 0.0001  # Rough conversion (depends on camera distance)
            estimated_volume_m3 = pixel_area * pixel_to_m2 * estimated_thickness_m

            return {
                "pixel_area": int(pixel_area),
                "estimated_volume_m3": estimated_volume_m3,
                "image_dimensions": {"width": width, "height": height},
                "edge_density": edge_pixels / total_pixels,
                "object_coverage": object_pixels / total_pixels
            }

        except Exception as e:
            self.logger.error(f"Simple physical analysis failed: {str(e)}")
            return {
                "pixel_area": 1000,
                "estimated_volume_m3": 0.0001,
                "image_dimensions": {"width": 640, "height": 480}
            }

    def _estimate_food_geometry_simple(self, image_array: np.ndarray) -> Dict[str, float]:
        """Estimate food geometry from image"""
        try:
            import cv2

            # Convert to grayscale and find contours
            gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # Get largest contour (assumed to be main food item)
                largest_contour = max(contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(largest_contour)

                height, width = image_array.shape[:2]
                return {
                    "x": x / width,
                    "y": y / height,
                    "width": w / width,
                    "height": h / height
                }
            else:
                # Default to center of image
                return {"x": 0.25, "y": 0.25, "width": 0.5, "height": 0.5}

        except Exception as e:
            self.logger.error(f"Geometry estimation failed: {e}")
            return {"x": 0.25, "y": 0.25, "width": 0.5, "height": 0.5}

    def _estimate_food_area(self, image_array: np.ndarray) -> int:
        """Estimate food area in pixels"""
        try:
            import cv2

            gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                return int(cv2.contourArea(largest_contour))
            else:
                # Estimate based on non-background pixels
                return int(np.sum(gray > 50))  # Pixels that aren't too dark

        except Exception as e:
            return 1000  # Default area

    def _get_nutrition_from_api(self, food_name: str, portion_size: float) -> Optional[Dict[str, Any]]:
        """
        Get nutritional information from real APIs (Edamam)
        """
        try:
            self.logger.info(f"Searching API for: {food_name}")

            # Search for food item
            search_result = self.api_integrator.search_food("edamam", food_name, limit=1)

            if not search_result.get("results"):
                self.logger.info(f"No API results found for {food_name}")
                return None

            food_item = search_result["results"][0]
            food_id = food_item.get("foodId")

            if not food_id:
                self.logger.warning(f"No food ID found for {food_name}")
                return None

            # Get detailed nutrition information
            ingredients = {
                "ingredients": [
                    {
                        "quantity": portion_size / 100.0,  # Convert to standard serving
                        "measureURI": "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
                        "foodId": food_id
                    }
                ]
            }

            nutrition_result = self.api_integrator.get_nutrients("edamam", ingredients)

            if nutrition_result and "totalNutrients" in nutrition_result:
                return self._parse_edamam_response(food_name, portion_size, nutrition_result)

            return None

        except Exception as e:
            self.logger.error(f"API nutrition lookup failed for {food_name}: {str(e)}")
            return None

    def _parse_edamam_response(self, food_name: str, portion_size: float, api_response: Dict) -> Dict[str, Any]:
        """
        Parse Edamam API response into our standard format
        """
        nutrients = api_response.get("totalNutrients", {})

        def get_nutrient_value(nutrient_key: str) -> Optional[float]:
            nutrient = nutrients.get(nutrient_key, {})
            if isinstance(nutrient, dict) and "quantity" in nutrient:
                return round(float(nutrient["quantity"]), 1)
            return None

        result = {
            "food_name": food_name,
            "portion_size_g": portion_size,
            "nutrition": {
                "calories": get_nutrient_value("ENERC_KCAL") or 0,
                "protein_g": get_nutrient_value("PROCNT") or 0,
                "fat_g": get_nutrient_value("FAT") or 0,
                "carbohydrates_g": get_nutrient_value("CHOCDF") or 0,
                "fiber_g": get_nutrient_value("FIBTG"),
                "sugar_g": get_nutrient_value("SUGAR"),
                "sodium_mg": get_nutrient_value("NA")
            },
            "data_source": "edamam_api",
            "confidence": 0.95,
            "api_response_summary": {
                "total_nutrients_found": len(nutrients),
                "has_detailed_breakdown": len(nutrients) > 5
            }
        }

        self.logger.info(f"Successfully parsed API response for {food_name}")
        return result

    def analyze_food_from_image(self, image_bytes: bytes) -> Dict[str, Any]:
        """
        Analyze food from image using local computer vision (no Google Cloud Vision)
        """
        try:
            self.logger.info("Analyzing food from image using local computer vision")

            # Use local computer vision for food detection
            vision_result = self._analyze_image_locally(image_bytes)

            if "error" in vision_result:
                return {
                    "error": f"Vision analysis failed: {vision_result['error']}",
                    "stage": "image_analysis"
                }

            # Extract detected food items
            detected_foods = vision_result.get("detection_results", [])
            physical_analysis = vision_result.get("physical_analysis", {})

            if not detected_foods:
                return {
                    "error": "No food items detected in image",
                    "stage": "food_detection"
                }

            # Analyze nutrition for each detected food
            nutrition_results = []
            for food_item in detected_foods:
                food_name = food_item.get("label", "unknown")
                confidence = food_item.get("confidence", 0)

                # Estimate portion size from physical analysis
                estimated_portion = self._estimate_portion_from_vision(physical_analysis)

                # Get nutrition information
                nutrition = self.analyze_food_item(food_name, estimated_portion)

                nutrition["detection_confidence"] = confidence
                nutrition["estimated_portion_g"] = estimated_portion

                nutrition_results.append(nutrition)

            return {
                "image_analysis": {
                    "detected_foods": len(detected_foods),
                    "physical_analysis": physical_analysis,
                    "nutrition_results": nutrition_results
                },
                "combined_nutrition": self._combine_detected_nutrition(nutrition_results) if len(nutrition_results) > 1 else nutrition_results[0] if nutrition_results else None
            }

        except Exception as e:
            self.logger.error(f"Image analysis failed: {str(e)}")
            return {
                "error": str(e),
                "stage": "image_processing"
            }

    def _estimate_portion_from_vision(self, physical_analysis: Dict) -> float:
        """
        Estimate portion size in grams from vision analysis
        """
        # This is a simplified estimation - in production, this would use
        # more sophisticated volume-to-weight conversion based on food density

        pixel_area = physical_analysis.get("pixel_area", 1000)
        estimated_volume = physical_analysis.get("estimated_volume_m3", 0.0001)

        # Rough estimation: assume average food density of 1000 kg/m³ (like water)
        estimated_weight_kg = estimated_volume * 1000
        estimated_weight_g = estimated_weight_kg * 1000

        # Clamp to reasonable values (10g to 500g)
        estimated_weight_g = max(10, min(500, estimated_weight_g))

        self.logger.info(f"Estimated portion size: {estimated_weight_g}g from vision analysis")
        return estimated_weight_g

    def _combine_detected_nutrition(self, nutrition_results: List[Dict]) -> Dict[str, Any]:
        """
        Combine nutrition from multiple detected food items
        """
        if not nutrition_results:
            return {}

        # Extract food names and portions for meal analysis
        food_names = []
        portions = []

        for result in nutrition_results:
            if "nutrition" in result and result["nutrition"]:
                food_names.append(result["food_name"])
                portions.append(result.get("estimated_portion_g", 100))

        if food_names:
            return self.analyze_meal(food_names, portions)

        return {"error": "No valid nutrition data to combine"}

    def _generate_meal_summary(self, total_nutrition: Dict[str, float]) -> Dict[str, Any]:
        """Generate a summary analysis of the meal's nutritional profile"""
        
        total_calories = total_nutrition["calories"]
        
        if total_calories == 0:
            return {"status": "no_nutritional_data"}
        
        # Calculate macronutrient percentages
        protein_calories = total_nutrition["protein_g"] * 4
        fat_calories = total_nutrition["fat_g"] * 9
        carb_calories = total_nutrition["carbohydrates_g"] * 4
        
        summary = {
            "total_calories": total_calories,
            "macronutrient_breakdown": {
                "protein_percent": round((protein_calories / total_calories) * 100, 1),
                "fat_percent": round((fat_calories / total_calories) * 100, 1),
                "carbohydrate_percent": round((carb_calories / total_calories) * 100, 1)
            },
            "meal_classification": self._classify_meal(total_nutrition),
            "health_notes": self._generate_health_notes(total_nutrition)
        }
        
        return summary
    
    def _classify_meal(self, nutrition: Dict[str, float]) -> str:
        """Classify meal based on nutritional content"""
        calories = nutrition["calories"]
        
        if calories < 200:
            return "light_snack"
        elif calories < 400:
            return "snack"
        elif calories < 600:
            return "light_meal"
        elif calories < 800:
            return "regular_meal"
        else:
            return "large_meal"
    
    def _generate_health_notes(self, nutrition: Dict[str, float]) -> List[str]:
        """Generate health-related notes about the meal"""
        notes = []
        
        protein_percent = (nutrition["protein_g"] * 4 / nutrition["calories"]) * 100 if nutrition["calories"] > 0 else 0
        
        if protein_percent > 30:
            notes.append("High protein content - good for muscle building")
        elif protein_percent < 10:
            notes.append("Low protein content - consider adding protein sources")
        
        if nutrition.get("fiber_g", 0) > 5:
            notes.append("Good fiber content - supports digestive health")
        
        if nutrition.get("sodium_mg", 0) > 800:
            notes.append("High sodium content - monitor salt intake")
        
        return notes

    def get_supported_foods(self) -> List[str]:
        """Return list of foods in the internal database"""
        return list(self.nutrition_db.keys())
    
    def add_food_to_database(self, food_name: str, nutrition_data: NutritionData):
        """Add a new food item to the internal database"""
        self.nutrition_db[food_name.lower().strip()] = nutrition_data
        self.logger.info(f"Added {food_name} to nutrition database")
