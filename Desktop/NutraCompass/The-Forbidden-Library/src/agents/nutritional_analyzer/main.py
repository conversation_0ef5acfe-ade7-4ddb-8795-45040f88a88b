"""
Nutritional Analyzer - AI system for extracting nutritional information from food items
"""

import logging
import json
from typing import Dict, List, Any, Optional
import requests
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class NutritionData:
    """Structure for nutritional information"""
    calories: float
    protein: float  # grams
    fat: float      # grams
    carbohydrates: float  # grams
    fiber: Optional[float] = None
    sugar: Optional[float] = None
    sodium: Optional[float] = None

class NutritionalAnalyzer:
    """
    AI-powered nutritional analyzer that extracts nutritional information
    from food items and meals using multiple data sources and AI models.
    """
    
    def __init__(self):
        """Initialize the nutritional analyzer"""
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Nutritional database - basic food items with nutritional info per 100g
        self.nutrition_db = {
            "apple": NutritionData(52, 0.3, 0.2, 14, 2.4, 10.4, 1),
            "banana": NutritionData(89, 1.1, 0.3, 23, 2.6, 12.2, 1),
            "chicken breast": NutritionData(165, 31, 3.6, 0, 0, 0, 74),
            "brown rice": NutritionData(111, 2.6, 0.9, 23, 1.8, 0.4, 5),
            "broccoli": NutritionData(34, 2.8, 0.4, 7, 2.6, 1.5, 33),
            "oatmeal": NutritionData(68, 2.4, 1.4, 12, 1.7, 0.3, 4),
            "almond milk": NutritionData(17, 0.6, 1.1, 1.5, 0.4, 1.3, 63),
            "salmon": NutritionData(208, 25.4, 12.4, 0, 0, 0, 59),
            "spinach": NutritionData(23, 2.9, 0.4, 3.6, 2.2, 0.4, 79),
            "sweet potato": NutritionData(86, 1.6, 0.1, 20, 3.0, 4.2, 7),
            "avocado": NutritionData(160, 2.0, 15, 9, 7, 0.7, 7),
            "quinoa": NutritionData(120, 4.4, 1.9, 22, 2.8, 0.9, 7),
            "greek yogurt": NutritionData(59, 10, 0.4, 3.6, 0, 3.2, 36),
            "almonds": NutritionData(579, 21, 50, 22, 12, 4.4, 1),
            "eggs": NutritionData(155, 13, 11, 1.1, 0, 0.6, 124)
        }
        
        self.logger.info("NutritionalAnalyzer initialized with basic nutrition database")
    
    def analyze_food_item(self, food_name: str, portion_size: float = 100) -> Dict[str, Any]:
        """
        Analyze nutritional content of a single food item
        
        Args:
            food_name: Name of the food item
            portion_size: Portion size in grams (default: 100g)
            
        Returns:
            Dictionary containing nutritional analysis
        """
        try:
            self.logger.info(f"Analyzing food item: {food_name} ({portion_size}g)")
            
            # Normalize food name
            normalized_name = food_name.lower().strip()
            
            # Look up in database
            if normalized_name in self.nutrition_db:
                base_nutrition = self.nutrition_db[normalized_name]
                
                # Calculate for portion size
                multiplier = portion_size / 100.0
                
                result = {
                    "food_name": food_name,
                    "portion_size_g": portion_size,
                    "nutrition": {
                        "calories": round(base_nutrition.calories * multiplier, 1),
                        "protein_g": round(base_nutrition.protein * multiplier, 1),
                        "fat_g": round(base_nutrition.fat * multiplier, 1),
                        "carbohydrates_g": round(base_nutrition.carbohydrates * multiplier, 1),
                        "fiber_g": round(base_nutrition.fiber * multiplier, 1) if base_nutrition.fiber else None,
                        "sugar_g": round(base_nutrition.sugar * multiplier, 1) if base_nutrition.sugar else None,
                        "sodium_mg": round(base_nutrition.sodium * multiplier, 1) if base_nutrition.sodium else None
                    },
                    "data_source": "internal_database",
                    "confidence": 0.9
                }
                
                self.logger.info(f"Successfully analyzed {food_name}")
                return result
            else:
                # Try AI-based estimation for unknown foods
                return self._estimate_nutrition_ai(food_name, portion_size)
                
        except Exception as e:
            self.logger.error(f"Error analyzing food item {food_name}: {str(e)}")
            return {
                "food_name": food_name,
                "error": str(e),
                "nutrition": None
            }
    
    def analyze_meal(self, food_items: List[str], portions: List[float]) -> Dict[str, Any]:
        """
        Analyze nutritional content of a complete meal
        
        Args:
            food_items: List of food item names
            portions: List of portion sizes in grams
            
        Returns:
            Dictionary containing meal nutritional analysis
        """
        try:
            self.logger.info(f"Analyzing meal with {len(food_items)} items")
            
            if len(food_items) != len(portions):
                raise ValueError("Number of food items must match number of portions")
            
            meal_analysis = {
                "meal_items": [],
                "total_nutrition": {
                    "calories": 0,
                    "protein_g": 0,
                    "fat_g": 0,
                    "carbohydrates_g": 0,
                    "fiber_g": 0,
                    "sugar_g": 0,
                    "sodium_mg": 0
                },
                "analysis_summary": {}
            }
            
            # Analyze each food item
            for food_name, portion in zip(food_items, portions):
                item_analysis = self.analyze_food_item(food_name, portion)
                meal_analysis["meal_items"].append(item_analysis)
                
                # Add to totals if analysis was successful
                if "nutrition" in item_analysis and item_analysis["nutrition"]:
                    nutrition = item_analysis["nutrition"]
                    meal_analysis["total_nutrition"]["calories"] += nutrition.get("calories", 0)
                    meal_analysis["total_nutrition"]["protein_g"] += nutrition.get("protein_g", 0)
                    meal_analysis["total_nutrition"]["fat_g"] += nutrition.get("fat_g", 0)
                    meal_analysis["total_nutrition"]["carbohydrates_g"] += nutrition.get("carbohydrates_g", 0)
                    
                    # Handle optional nutrients
                    if nutrition.get("fiber_g"):
                        meal_analysis["total_nutrition"]["fiber_g"] += nutrition["fiber_g"]
                    if nutrition.get("sugar_g"):
                        meal_analysis["total_nutrition"]["sugar_g"] += nutrition["sugar_g"]
                    if nutrition.get("sodium_mg"):
                        meal_analysis["total_nutrition"]["sodium_mg"] += nutrition["sodium_mg"]
            
            # Round totals
            for key in meal_analysis["total_nutrition"]:
                meal_analysis["total_nutrition"][key] = round(meal_analysis["total_nutrition"][key], 1)
            
            # Add analysis summary
            meal_analysis["analysis_summary"] = self._generate_meal_summary(meal_analysis["total_nutrition"])
            
            self.logger.info("Successfully analyzed complete meal")
            return meal_analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing meal: {str(e)}")
            return {
                "error": str(e),
                "meal_items": food_items,
                "portions": portions
            }
    
    def _estimate_nutrition_ai(self, food_name: str, portion_size: float) -> Dict[str, Any]:
        """
        Use AI-based estimation for unknown food items
        This is a simplified version - in production, this would call external APIs
        """
        self.logger.info(f"Using AI estimation for unknown food: {food_name}")
        
        # Simple heuristic-based estimation (placeholder for actual AI)
        # In a real implementation, this would call nutrition APIs or ML models
        
        estimated_nutrition = {
            "food_name": food_name,
            "portion_size_g": portion_size,
            "nutrition": {
                "calories": round(portion_size * 2.0, 1),  # Rough estimate: 2 cal/g
                "protein_g": round(portion_size * 0.1, 1),  # 10% protein
                "fat_g": round(portion_size * 0.05, 1),     # 5% fat
                "carbohydrates_g": round(portion_size * 0.15, 1),  # 15% carbs
                "fiber_g": round(portion_size * 0.02, 1),
                "sugar_g": round(portion_size * 0.03, 1),
                "sodium_mg": round(portion_size * 0.5, 1)
            },
            "data_source": "ai_estimation",
            "confidence": 0.6,
            "note": "Estimated values - consider using specific nutrition database for accuracy"
        }
        
        return estimated_nutrition
    
    def _generate_meal_summary(self, total_nutrition: Dict[str, float]) -> Dict[str, Any]:
        """Generate a summary analysis of the meal's nutritional profile"""
        
        total_calories = total_nutrition["calories"]
        
        if total_calories == 0:
            return {"status": "no_nutritional_data"}
        
        # Calculate macronutrient percentages
        protein_calories = total_nutrition["protein_g"] * 4
        fat_calories = total_nutrition["fat_g"] * 9
        carb_calories = total_nutrition["carbohydrates_g"] * 4
        
        summary = {
            "total_calories": total_calories,
            "macronutrient_breakdown": {
                "protein_percent": round((protein_calories / total_calories) * 100, 1),
                "fat_percent": round((fat_calories / total_calories) * 100, 1),
                "carbohydrate_percent": round((carb_calories / total_calories) * 100, 1)
            },
            "meal_classification": self._classify_meal(total_nutrition),
            "health_notes": self._generate_health_notes(total_nutrition)
        }
        
        return summary
    
    def _classify_meal(self, nutrition: Dict[str, float]) -> str:
        """Classify meal based on nutritional content"""
        calories = nutrition["calories"]
        
        if calories < 200:
            return "light_snack"
        elif calories < 400:
            return "snack"
        elif calories < 600:
            return "light_meal"
        elif calories < 800:
            return "regular_meal"
        else:
            return "large_meal"
    
    def _generate_health_notes(self, nutrition: Dict[str, float]) -> List[str]:
        """Generate health-related notes about the meal"""
        notes = []
        
        protein_percent = (nutrition["protein_g"] * 4 / nutrition["calories"]) * 100 if nutrition["calories"] > 0 else 0
        
        if protein_percent > 30:
            notes.append("High protein content - good for muscle building")
        elif protein_percent < 10:
            notes.append("Low protein content - consider adding protein sources")
        
        if nutrition.get("fiber_g", 0) > 5:
            notes.append("Good fiber content - supports digestive health")
        
        if nutrition.get("sodium_mg", 0) > 800:
            notes.append("High sodium content - monitor salt intake")
        
        return notes

    def get_supported_foods(self) -> List[str]:
        """Return list of foods in the internal database"""
        return list(self.nutrition_db.keys())
    
    def add_food_to_database(self, food_name: str, nutrition_data: NutritionData):
        """Add a new food item to the internal database"""
        self.nutrition_db[food_name.lower().strip()] = nutrition_data
        self.logger.info(f"Added {food_name} to nutrition database")
