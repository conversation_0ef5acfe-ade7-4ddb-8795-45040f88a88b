import logging
from typing import Dict, List, Optional, Any
from services.ai.ai_integration import call_advanced_model
from services.external_api_integrator import ExternalAPIIntegrator
from agents.recipe_generator.main import RecipeGenerator

logger = logging.getLogger("Nutritionist")
logging.basicConfig(level=logging.INFO)

class NutritionalDeficiency:
    def __init__(self, nutrient: str, current_intake: float, recommended_intake: float, deficiency_level: str):
        self.nutrient = nutrient
        self.current_intake = current_intake
        self.recommended_intake = recommended_intake
        self.deficiency_level = deficiency_level

    def __str__(self):
        return f"{self.nutrient}: {self.current_intake}/{self.recommended_intake} ({self.deficiency_level})"

class Nutritionist:
    ACTIVITY_FACTORS = {
        "sedentary": 1.2,
        "light": 1.375,
        "moderate": 1.55,
        "active": 1.725,
        "very_active": 1.9
    }
    
    DEFAULT_MACRO_PERCENTAGES = {
        "carb": 0.4,
        "fat": 0.3,
        "protein": 0.3
    }

    def __init__(self, orchestrator: Optional[Any] = None):
        self.orchestrator = orchestrator
        self.nutrient_db = self._initialize_nutrient_db()
        self.meal_db = self._initialize_meal_db()
        self.external_api = ExternalAPIIntegrator()
        self.recipe_generator = RecipeGenerator()

    # Data extraction methods
    def _extract_relevant_data(self, user_data: Dict) -> Dict:
        """Extract and normalize key user data for nutritional calculations."""
        return {
            "weight_kg": self._extract_weight(user_data),
            "height_cm": self._extract_height(user_data),
            "age": self._get_nested_field(user_data, ["age", "profile.age"], 30),
            "sex": self._get_nested_field(user_data, ["sex", "profile.sex"], "male").lower(),
            "activity_level": self._get_nested_field(user_data, ["activity_level", "profile.activity_level"], "moderate"),
            "goal": self._get_nested_field(user_data, ["goal", "profile.goal"], "maintenance"),
            "waterGoal": user_data.get("nutritionalGoals", {}).get("waterGoal", {"amount": 10000, "unit": "ml"})
        }

    def _extract_weight(self, user_data: Dict) -> float:
        """Extract and convert weight to kilograms."""
        weight = user_data.get("weight_kg")
        if weight is not None:
            return float(weight)
            
        body_weight = user_data.get("profile", {}).get("bodyWeight")
        if body_weight:
            try:
                lbs = float(body_weight.split()[0])
                return lbs * 0.453592
            except (ValueError, AttributeError) as e:
                logger.error(f"Error parsing weight: {e}")
                
        return 70.0  # Default weight

    def _extract_height(self, user_data: Dict) -> float:
        """Extract height in centimeters."""
        if "height_cm" in user_data:
            return user_data["height_cm"]
        return user_data.get("profile", {}).get("height", {}).get("centimeters", 170)

    def _get_nested_field(self, data: Dict, keys: List[str], default: Any) -> Any:
        """Safely retrieve a field from multiple possible locations."""
        for key in keys:
            parts = key.split('.')
            value = data
            try:
                for part in parts:
                    value = value.get(part, {})
                if value:
                    return value
            except AttributeError:
                continue
        return default

    # Calculation methods
    def calculate_daily_needs(self, user_data: Dict) -> Dict:
        """Calculate or retrieve daily nutritional targets."""
        if self._has_complete_nutritional_goals(user_data):
            logger.info("Using stored nutritionalGoals from user data.")
            return self._format_stored_goals(user_data["nutritionalGoals"])
            
        return self._compute_daily_needs(user_data)

    def _has_complete_nutritional_goals(self, user_data: Dict) -> bool:
        """Check if user data contains complete nutritional goals."""
        goals = user_data.get("nutritionalGoals", {})
        return "calorieGoal" in goals and "macroGoals" in goals

    def _format_stored_goals(self, stored_goals: Dict) -> Dict:
        """Format stored goals with defaults."""
        return {
            "calorieGoal": stored_goals["calorieGoal"],
            "macroGoals": stored_goals["macroGoals"],
            "waterGoal": stored_goals.get("waterGoal", {"amount": 10000, "unit": "ml"})
        }

    def _compute_daily_needs(self, user_data: Dict) -> Dict:
        """Compute nutritional needs from user profile data."""
        relevant_data = self._extract_relevant_data(user_data)
        bmr = self._calculate_bmr(relevant_data)
        daily_calories = self._adjust_for_activity(bmr, relevant_data["activity_level"])
        macro_goals = self._compute_macro_goals(daily_calories)
        
        return {
            "calorieGoal": round(daily_calories),
            "macroGoals": macro_goals,
            "waterGoal": relevant_data["waterGoal"]
        }

    def _calculate_bmr(self, data: Dict) -> float:
        """Calculate Basal Metabolic Rate using Mifflin-St Jeor equation."""
        sex_factor = 5 if data["sex"] == "male" else -161
        return 10 * data["weight_kg"] + 6.25 * data["height_cm"] - 5 * data["age"] + sex_factor

    def _adjust_for_activity(self, bmr: float, activity_level: str) -> float:
        """Adjust calories based on activity level."""
        return bmr * self.ACTIVITY_FACTORS.get(activity_level.lower(), 1.55)

    def _compute_macro_goals(self, daily_calories: float) -> Dict:
        """Calculate macronutrient distribution goals."""
        return {
            macro: self._calculate_macro_values(macro, pct, daily_calories)
            for macro, pct in self.DEFAULT_MACRO_PERCENTAGES.items()
        }

    def _calculate_macro_values(self, macro: str, pct: float, total_calories: float) -> Dict:
        """Calculate specific macro values based on percentage allocation."""
        macro_cal = total_calories * pct
        calories_per_gram = 9 if macro == "fat" else 4
        return {
            "dailyCalories": round(macro_cal),
            "dailyGrams": round(macro_cal / calories_per_gram),
            "dailyPercentage": pct
        }

    # Meal planning methods
    def generate_structured_meal_plan(self, user_data: Dict) -> Dict:
        """Generate complete meal plan with recipes and nutritional targets."""
        targets = self.calculate_daily_needs(user_data)
        return {
            "calorieGoal": targets["calorieGoal"],
            "macroGoals": targets["macroGoals"],
            "waterGoal": targets["waterGoal"],
            "dishes": self._generate_dishes(user_data, targets)
        }

    def _generate_dishes(self, user_data: Dict, targets: Dict) -> List[Dict]:
        """Generate all dishes for the meal plan."""
        dish_names = self._determine_dish_names(user_data)
        food_recommendations = self._determine_food_recommendations(user_data)
        calories_per_dish = targets["calorieGoal"] // len(dish_names)
        
        return [
            self._generate_dish_recipe(name, calories_per_dish, targets["macroGoals"], food_recommendations)
            for name in dish_names
        ]

    def _determine_dish_names(self, user_data: Dict) -> List[str]:
        """Determine meal sections from user data or use defaults."""
        if "customMealSections" not in user_data:
            return ["Breakfast", "Lunch", "Dinner"]
            
        return [
            section["name"].strip()
            for section in user_data["customMealSections"]
            if section.get("name") and section["name"].strip().lower() != "water"
        ]

    def _generate_dish_recipe(self, dish_name: str, calories: int, macros: Dict, foods: List[str]) -> Dict:
        """Generate a single dish recipe."""
        instructions = {
            "dishName": dish_name,
            "calorieTarget": calories,
            "macroGoals": macros,
            "foodCandidates": foods
        }
        return self.recipe_generator.generate_recipe(instructions)

    # Recommendation methods
    def _determine_food_recommendations(self, user_data: dict) -> list:
        """Get personalized food recommendations using AI model."""
        try:
            prompt = self._build_recommendation_prompt(user_data)
            recommendations_text = call_advanced_model(prompt)
            return [item.strip() for item in recommendations_text.split(",")]
        except Exception as e:
            logger.error(f"Error generating food recommendations: {e}")
            return self._get_fallback_recommendations()

    def _build_recommendation_prompt(self, user_data: Dict) -> str:
        """Construct the AI prompt for food recommendations."""
        base_prompt = (
            f"User profile: {user_data.get('profile')}. "
            f"Nutritional goals: {user_data.get('nutritionalGoals')}. "
            f"The user wants to {user_data.get('desired_weight_change', 'maintain')} weight."
        )
        
        if dietary_prefs := user_data.get("dietary_preferences"):
            base_prompt += f" The user follows a {dietary_prefs} diet."
            
        return base_prompt + " Generate a comma-separated list of five recommended food items for a balanced diet."

    def _get_fallback_recommendations(self) -> List[str]:
        """Default food recommendations when AI fails."""
        return ["apples", "eggs", "chicken", "ground beef", "milk"]

    # Analysis methods
    def analyze_nutrient_intake(self, intake_data: Dict) -> List[NutritionalDeficiency]:
        """Analyze nutrient intake for deficiencies."""
        return [
            NutritionalDeficiency(nutrient, intake_data.get(nutrient, 0), 
                                 data["rdi"], self._assess_deficiency_level(intake_data.get(nutrient, 0), data["rdi"]))
            for nutrient, data in self.nutrient_db.items()
            if data["rdi"] > 0 and intake_data.get(nutrient, 0) < 0.7 * data["rdi"]
        ]

    def _assess_deficiency_level(self, current: float, recommended: float) -> str:
        """Determine severity level of a nutrient deficiency."""
        return "severe" if current < 0.5 * recommended else "moderate"

    # Initialization methods
    def _initialize_nutrient_db(self) -> Dict[str, Any]:
        return {
            "protein": {"rdi": 56},
            "carbs": {"rdi": 300},
            "fat": {"rdi": 70},
            "fiber": {"rdi": 28},
            "vitamin_c": {"rdi": 90}
        }

    def _initialize_meal_db(self) -> List[Dict[str, Any]]:
        return [
            {
                "foodLabel": "Grilled Chicken Salad",
                "foodCategory": "Salads",
                "foodBrand": "",
                "numberOfServings": 1,
                "activeMeasure": {"label": "Serving", "uri": "", "weight": 450},
                "measures": [{"label": "Serving", "uri": "", "weight": 450}],
                "defaultNutrients": {"ENERC_KCAL": 450}
            },
            {
                "foodLabel": "Oatmeal with Fruits",
                "foodCategory": "Breakfast",
                "foodBrand": "",
                "numberOfServings": 1,
                "activeMeasure": {"label": "Serving", "uri": "", "weight": 350},
                "measures": [{"label": "Serving", "uri": "", "weight": 350}],
                "defaultNutrients": {"ENERC_KCAL": 350}
            }
        ]

# Example usage:
if __name__ == "__main__":
    nutritionist = Nutritionist()
    sample_user_data = {
        "nutritionalGoals": {
            "calorieGoal": 2112,
            "macroGoals": {
                "carb": {"dailyCalories": 845, "dailyGrams": 211, "dailyPercentage": 0.4},
                "fat": {"dailyCalories": 634, "dailyGrams": 70, "dailyPercentage": 0.3},
                "protein": {"dailyCalories": 634, "dailyGrams": 158, "dailyPercentage": 0.3}
            },
            "waterGoal": {"amount": 10000, "unit": "ml"}
        },
        "profile": {
            "age": 22,
            "bodyWeight": "190 lbs",
            "sex": "Male",
            "height": {"centimeters": 185.42, "inches": 73, "unit": "in"}
        },
        "physicalFitnessGoals": {"distanceGoal": 8, "distanceUnit": "mi", "stepsGoal": 16896},
        "customMealSections": [
            {"name": "Breakfast"},
            {"name": "Lunch"},
            {"name": "Snack"},
            {"name": "Dinner"},
            {"name": "Water"}  # This one will be ignored.
        ],
        # Additional dynamic preferences extracted from user input:
        "desired_weight_change": "lose",         # Options: "lose", "gain", "maintain"
        "dietary_preferences": "vegetarian"        # e.g., "vegetarian", "gluten-free", etc.
    }
    targets = nutritionist.calculate_daily_needs(sample_user_data)
    print("Daily Nutritional Targets:", targets)
    meal_plan = nutritionist.generate_structured_meal_plan(sample_user_data)
    print("Meal Plan:", meal_plan)
    intake = {"protein": 30, "carbs": 100, "fat": 40, "fiber": 10, "vitamin_c": 50}
    deficiencies = nutritionist.analyze_nutrient_intake(intake)
    print("Deficiencies:")
    for d in deficiencies:
        print(d)
