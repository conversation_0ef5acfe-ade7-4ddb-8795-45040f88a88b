import json
from agents.nutritionist.main import Nutritionist

def filter_nutrients(nutrients: dict) -> dict:
    """Return a copy of the nutrients dict with only the keys ENERC_KCAL, PROCNT, FAT, and CHOCDF."""
    keys = ["ENERC_KCAL", "PROCNT", "FAT", "CHOCDF"]
    return {k: nutrients[k] for k in keys if k in nutrients}

def test_calculate_daily_needs():
    nutritionist = Nutritionist()
    sample_user_data = {
        "nutritionalGoals": {
            "calorieGoal": 2112,
            "macroGoals": {
                "carb": {"dailyCalories": 845, "dailyGrams": 211, "dailyPercentage": 0.4},
                "fat": {"dailyCalories": 634, "dailyGrams": 70, "dailyPercentage": 0.3},
                "protein": {"dailyCalories": 634, "dailyGrams": 158, "dailyPercentage": 0.3}
            },
            "waterGoal": {"amount": 10000, "unit": "ml"}
        },
        "profile": {
            "age": 22,
            "bodyWeight": "190 lbs",
            "sex": "Male",
            "height": {"centimeters": 185.42, "inches": 73, "unit": "in"}
        },
        "physicalFitnessGoals": {"distanceGoal": 8, "distanceUnit": "mi", "stepsGoal": 16896}
    }
    targets = nutritionist.calculate_daily_needs(sample_user_data)
    print("\n=== Daily Nutritional Targets ===")
    print(json.dumps(targets, indent=4, sort_keys=True))
    return targets

def test_generate_meal_plan():
    nutritionist = Nutritionist()
    sample_user_data = {
        "nutritionalGoals": {
            "calorieGoal": 2112,
            "macroGoals": {
                "carb": {"dailyCalories": 845, "dailyGrams": 211, "dailyPercentage": 0.4},
                "fat": {"dailyCalories": 634, "dailyGrams": 70, "dailyPercentage": 0.3},
                "protein": {"dailyCalories": 634, "dailyGrams": 158, "dailyPercentage": 0.3}
            },
            "waterGoal": {"amount": 10000, "unit": "ml"}
        },
        "profile": {
            "age": 22,
            "bodyWeight": "190 lbs",
            "sex": "Male",
            "height": {"centimeters": 185.42, "inches": 73, "unit": "in"}
        },
        "physicalFitnessGoals": {"distanceGoal": 8, "distanceUnit": "mi", "stepsGoal": 16896},
        "customMealSections": [
            {"name": "Breakfast"},
            {"name": "Lunch"},
            {"name": "Snack"},
            {"name": "Dinner"},
            {"name": "Water"}  # Should be ignored.
        ]
    }
    meal_plan = nutritionist.generate_structured_meal_plan(sample_user_data)
    print("\n=== Generated Meal Plan Summary ===")
    print(f"Calorie Goal: {meal_plan.get('calorieGoal')}")
    print(f"Macro Goals: {meal_plan.get('macroGoals')}")
    print(f"Water Goal: {meal_plan.get('waterGoal')}")
    
    # Compute per-dish target nutrients.
    num_dishes = len(meal_plan.get("dishes", []))
    overall_calories = meal_plan.get("calorieGoal", 0)
    overall_macro = meal_plan.get("macroGoals", {})
    target_calories = overall_calories // num_dishes if num_dishes > 0 else "N/A"
    target_protein = overall_macro.get("protein", {}).get("dailyGrams", 0) // num_dishes if num_dishes > 0 else "N/A"
    target_fat = overall_macro.get("fat", {}).get("dailyGrams", 0) // num_dishes if num_dishes > 0 else "N/A"
    target_carb = overall_macro.get("carb", {}).get("dailyGrams", 0) // num_dishes if num_dishes > 0 else "N/A"
    print("\n--- Target Nutrients per Dish ---")
    print(f"Target Nutrients -> Calories: {target_calories} | Protein: {target_protein}g | Fat: {target_fat}g | Carbs: {target_carb}g")
    
    # For each dish, log the generated recipe summary and for each food item, log only:
    # foodLabel  --> Calories: <value> | Protein: <value>g | Fat: <value>g | Carbs: <value>g
    for dish in meal_plan.get("dishes", []):
        print("\nGenerated Recipe Summary for Dish:")
        print(f"Dish Name: {dish.get('dishName')}")
        print(f"Recipe Instructions: {dish.get('recipeInstructions')}")
        print("Aggregated Nutrients:")
        aggregated = dish.get("totalNutrients", {})
        filtered = filter_nutrients(aggregated)
        agg_calories = filtered.get("ENERC_KCAL", {}).get("quantity", "N/A")
        agg_protein = filtered.get("PROCNT", {}).get("quantity", "N/A")
        agg_fat = filtered.get("FAT", {}).get("quantity", "N/A")
        agg_carbs = filtered.get("CHOCDF", {}).get("quantity", "N/A")
        print(f"  Aggregated Nutrients -> Calories: {agg_calories} | Protein: {agg_protein}g | Fat: {agg_fat}g | Carbs: {agg_carbs}g")
        print("Food Items:")
        for item in dish.get("foodItems", []):
            food_label = item.get("foodLabel", "Unknown")
            nutrients = item.get("nutrients", {})
            filtered_nuts = filter_nutrients(nutrients)
            calories = filtered_nuts.get("ENERC_KCAL", {}).get("quantity", "N/A")
            protein = filtered_nuts.get("PROCNT", {}).get("quantity", "N/A")
            fat = filtered_nuts.get("FAT", {}).get("quantity", "N/A")
            carbs = filtered_nuts.get("CHOCDF", {}).get("quantity", "N/A")
            print(f"{food_label}  --> Calories: {calories} | Protein: {protein}g | Fat: {fat}g | Carbs: {carbs}g")
        print("-----")
    return meal_plan

def test_analyze_nutrient_intake():
    nutritionist = Nutritionist()
    intake = {"protein": 30, "carbs": 100, "fat": 40, "fiber": 10, "vitamin_c": 50}
    deficiencies = nutritionist.analyze_nutrient_intake(intake)
    print("\n=== Nutritional Deficiencies ===")
    for d in deficiencies:
        print(str(d))
    return deficiencies

def test_nutritionist():
    test_calculate_daily_needs()
    test_generate_meal_plan()
    test_analyze_nutrient_intake()

if __name__ == "__main__":
    test_nutritionist()
