import numpy as np
from PIL import Image, ImageDraw
import os
import random

def generate_synthetic_dataset(output_dir, num_images=5000):
    """Generate synthetic food images with masks"""
    image_dir = os.path.join(output_dir, "train/images")
    mask_dir = os.path.join(output_dir, "train/masks")
    os.makedirs(image_dir, exist_ok=True)
    os.makedirs(mask_dir, exist_ok=True)

    for i in range(num_images):
        # Random background
        bg_color = (random.randint(0,50), random.randint(0,50), random.randint(0,50))
        img = Image.new('RGB', (512, 512), bg_color)
        draw = ImageDraw.Draw(img)
        
        # Create mask
        mask = Image.new('L', (512, 512), 0)
        mask_draw = ImageDraw.Draw(mask)

        # Generate 1-3 food-like shapes
        for _ in range(random.randint(1, 3)):
            shape_type = random.choice(["ellipse", "rectangle", "polygon"])
            color = (random.randint(100,255), random.randint(100,255), random.randint(100,255))
            
            # Random position/size
            x1 = random.randint(50, 400)
            y1 = random.randint(50, 400)
            x2 = x1 + random.randint(50, 200)
            y2 = y1 + random.randint(50, 200)

            if shape_type == "ellipse":
                draw.ellipse([x1, y1, x2, y2], fill=color)
                mask_draw.ellipse([x1, y1, x2, y2], fill=255)
            elif shape_type == "rectangle":
                draw.rectangle([x1, y1, x2, y2], fill=color)
                mask_draw.rectangle([x1, y1, x2, y2], fill=255)
            else:  # Polygon
                points = [(x1, y1), (x2, y1), 
                        (x1+random.randint(-50,50), y2), 
                        (x1, y2)]
                draw.polygon(points, fill=color)
                mask_draw.polygon(points, fill=255)

        # Add noise and save
        img = add_noise(img)
        img.save(os.path.join(image_dir, f"synthetic_{i}.jpg"))
        mask.save(os.path.join(mask_dir, f"synthetic_{i}.png"))

def add_noise(img):
    """Add realistic noise augmentation"""
    arr = np.array(img).astype(np.float32)
    noise = np.random.normal(0, 15, arr.shape)
    noisy = np.clip(arr + noise, 0, 255).astype(np.uint8)
    return Image.fromarray(noisy)

if __name__ == "__main__":
    generate_synthetic_dataset("src/data/eagle_eye", 5000)