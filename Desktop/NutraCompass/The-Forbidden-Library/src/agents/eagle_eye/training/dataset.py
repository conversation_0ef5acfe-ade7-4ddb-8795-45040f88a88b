import torch
from torch.utils.data import Dataset
from PIL import Image
import os
import torchvision.transforms as T

class FoodSegmentationDataset(Dataset):
    def __init__(self, data_dir, transform=None):
        self.image_dir = os.path.join(data_dir, "images")
        self.mask_dir = os.path.join(data_dir, "masks")
        self.images = os.listdir(self.image_dir)
        
        self.transform = transform or T.<PERSON>se([
            T<PERSON>(256),
            T.RandomRotation(20),
            T.Random<PERSON>lip(),
            T.ColorJitter(0.2, 0.2, 0.2),
            <PERSON><PERSON>or(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.mask_transform = T.Co<PERSON>([
            <PERSON><PERSON>(256),
            T<PERSON>To<PERSON>ensor()
        ])

    def __len__(self):
        return len(self.images)

    def __getitem__(self, idx):
      img_path = os.path.join(self.image_dir, self.images[idx])
      base_name = os.path.splitext(self.images[idx])[0]  # Split filename and extension
      mask_path = os.path.join(self.mask_dir, f"{base_name}.png")  # Use base name with .png
      
      image = Image.open(img_path).convert("RGB")
      mask = Image.open(mask_path)
      
      image = self.transform(image)
      mask = self.mask_transform(mask) > 0.5  # Binarize
      
      return image, mask.float()