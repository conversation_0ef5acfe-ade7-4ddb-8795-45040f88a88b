Training Workflow
1. Synthetic Data Generation
   └─ Random food shapes + realistic augmentations
   
2. Data Loading & Augmentation
   └─ Resize (256x256) + Rotation + Color Jitter
   
3. Model Initialization
   └─ EfficientNet-B3 backbone (ImageNet pretrained)
   └─ Optional: FoodSeg103 pretrained weights
   
4. Training Process
   └─ BCE Loss + Adam Optimizer
   └─ Learning Rate Scheduling
   └─ Checkpoint Saving