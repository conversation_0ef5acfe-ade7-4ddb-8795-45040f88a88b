<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="1" failures="0" skipped="0" tests="1" time="80.725" timestamp="2025-05-21T09:47:34.460988" hostname="Andress-MBP-2.attlocal.net"><testcase classname="" name="src.agents.eagle_eye.tests.test_main" time="0.000"><error message="collection failure">ImportError while importing test module '/Users/<USER>/NutraCompass LLC/NutraCompass-B2C/The-Forbidden-Library/src/agents/eagle_eye/tests/test_main.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.8/lib/python3.8/importlib/__init__.py:127: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
src/agents/eagle_eye/tests/test_main.py:25: in &lt;module&gt;
    from main import EagleEye  # Now should import correctly
E   ImportError: cannot import name '<PERSON><PERSON><PERSON>' from 'main' (/Users/<USER>/NutraCompass LLC/NutraCompass-B2C/The-Forbidden-Library/src/main.py)</error></testcase></testsuite></testsuites>