import pytest
import sys
import os
import json
import time
import torch
import numpy as np
from pathlib import Path
from PIL import Image
import io
from unittest.mock import Mock, patch, MagicMock
import logging
import signal

# Configure absolute paths
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
SRC_DIR = PROJECT_ROOT / "src"
DATA_DIR = PROJECT_ROOT / "data" / "eagle_eye"
MODEL_ROOT = DATA_DIR / "FoodSeg103"

# Add to Python path
sys.path.insert(0, str(SRC_DIR))
sys.path.insert(0, str(SRC_DIR / "agents" / "eagle_eye"))

# Mock the config module before importing EagleEye
mock_config = MagicMock()
mock_config.CLOUD_VISION_CREDENTIALS_JSON = json.dumps({
    "type": "service_account",
    "project_id": "mock-project",
    "private_key_id": "mock-key",
    "private_key": "-----B<PERSON>IN PRIVATE KEY-----\nMOCK\n-----<PERSON><PERSON> PRIVATE KEY-----\n",
    "client_email": "<EMAIL>",
    "client_id": "mock-client-id"
})
sys.modules['src.config'] = mock_config

# Now import EagleEye and DepthEstimator
from src.agents.eagle_eye.main import EagleEye
from src.agents.eagle_eye.depth_estimator import DepthEstimator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("EagleEye")

# ----- Test Configurations -----
TEST_IMAGE = DATA_DIR / "test_images" / "food_sample.jpg"
CALIBRATION_PROFILES = {
    "default": {"pixels_per_meter": 1000, "focal_length": 1.0}
}

# ----- Timeout Handling -----
class TimeoutException(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutException("Test timed out")

# ----- Fixtures -----
@pytest.fixture(scope="module")
def test_results_dir():
    """Create timestamped results directory"""
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    results_dir = DATA_DIR / "results" / f"eagle_eye_test_{timestamp}"
    results_dir.mkdir(parents=True, exist_ok=True)
    yield results_dir

@pytest.fixture
def sample_image():
    """Load standard test image"""
    if not TEST_IMAGE.exists():
        # Create placeholder image if missing
        img = Image.new('RGB', (800, 600), (73, 109, 137))
        img.save(TEST_IMAGE)
        logger.info(f"Created placeholder test image at {TEST_IMAGE}")
    return Image.open(TEST_IMAGE)

def download_midas_model():
    """Download MiDaS model with retries and progress indication"""
    import sys
    from tqdm import tqdm
    
    class DownloadProgress(tqdm):
        def update_to(self, b=1, bsize=1, tsize=None):
            if tsize is not None:
                self.total = tsize
            self.update(b * bsize - self.n)
    
    try:
        print("Downloading MiDaS model...")
        url = 'https://github.com/intel-isl/MiDaS/releases/download/v3_1/midas_v3_1_small-70d6b9c8.pt'
        dest = Path.home() / '.cache/torch/hub/checkpoints/midas_v3_1_small-70d6b9c8.pt'
        dest.parent.mkdir(parents=True, exist_ok=True)
        
        with DownloadProgress(unit='B', unit_scale=True, miniters=1, desc=url.split('/')[-1]) as t:
            torch.hub.download_url_to_file(url, dest, progress=t.update_to)
        
        print("Download complete")
        return dest
    except Exception as e:
        print(f"Download failed: {str(e)}")
        return None
    
@pytest.fixture(scope="module")
def depth_estimator():
    """Depth estimator instance with timeout protection"""
     # Check if model exists
    model_path = Path.home() / '.cache/torch/hub/checkpoints/midas_v3_1_small-70d6b9c8.pt'
    
    if not model_path.exists():
        downloaded = download_midas_model()
        if not downloaded or not downloaded.exists():
            pytest.skip("MiDaS model download failed")
            
    # Set timeout alarm (30 seconds)
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(30)
    
    try:
        estimator = DepthEstimator(model_name='MiDaS_small', use_gpu=False)
        signal.alarm(0)  # Disable alarm
        return estimator
    except TimeoutException:
        pytest.skip("Depth estimator initialization timed out")
    except Exception as e:
        pytest.skip(f"Depth estimator failed to initialize: {str(e)}")

@pytest.fixture
def eagle_eye_instance(depth_estimator):
    """EagleEye instance with real components and timeout protection"""
    # Mock model loading
    with patch('src.agents.eagle_eye.main.EagleEyeSegmenter.load') as mock_load:
        # Mock vision client
        with patch('google.cloud.vision.ImageAnnotatorClient'):
            # Create a mock segmentation model
            mock_model = MagicMock()
            mock_model.return_value = torch.sigmoid(torch.randn(1, 1, 256, 256))
            mock_load.return_value = mock_model
            
            # Initialize with real depth estimator
            analyzer = EagleEye(
                model_path=str(MODEL_ROOT/"pretrained"/"checkpoints"/"CCNet"/"iter_80000.pth"),
                calibration_data=CALIBRATION_PROFILES["default"]
            )
            
            # Replace with real depth estimator
            analyzer.depth_estimator = depth_estimator
            yield analyzer

# ----- Core EagleEye Tests -----
def test_eagle_eye_initialization(eagle_eye_instance, depth_estimator):
    """Test EagleEye initializes correctly with real depth estimator"""
    assert eagle_eye_instance is not None
    assert eagle_eye_instance.seg_model is not None
    assert eagle_eye_instance.vision_client is not None
    assert eagle_eye_instance.depth_estimator == depth_estimator
    logger.info("EagleEye initialized successfully with depth estimator")

def test_full_analysis_pipeline(eagle_eye_instance, sample_image, test_results_dir):
    """End-to-end test of EagleEye analysis pipeline with real depth estimation"""
    # Convert image to bytes
    img_byte_arr = io.BytesIO()
    sample_image.save(img_byte_arr, format='JPEG')
    image_bytes = img_byte_arr.getvalue()
    
    # Mock Vision API response
    with patch.object(eagle_eye_instance.vision_client, 'label_detection') as mock_label:
        # Mock segmentation
        with patch.object(eagle_eye_instance, '_segment_food') as mock_segment:
            # Set up mock responses
            mock_label.return_value.label_annotations = [
                MagicMock(description="Pizza", score=0.95),
                MagicMock(description="Salad", score=0.87)
            ]
            
            # Create a realistic food mask
            mock_mask = np.zeros((sample_image.height, sample_image.width))
            mock_mask[100:300, 200:400] = 1  # Food area
            mock_segment.return_value = mock_mask
            
            # Run analysis with real depth estimation
            results = eagle_eye_instance.analyze_image(image_bytes)
    
    # Validate results structure
    assert "detection_results" in results
    assert "physical_analysis" in results
    assert "perception_data" in results
    
    # Validate detection results
    assert len(results["detection_results"]) == 2
    assert results["detection_results"][0]["label"] == "Pizza"
    
    # Validate physical measurements - real depth values
    phys = results["physical_analysis"]
    assert phys["pixel_area"] == 200*200  # 200x200 mock food area
    assert phys["estimated_volume_m3"] > 0
    
    # Validate depth stats
    depth_stats = phys["depth_stats"]
    assert depth_stats["min"] >= 0
    assert depth_stats["max"] > 0
    assert depth_stats["median"] > 0
    
    # Save visualization
    viz = eagle_eye_instance.visualize_results(image_bytes, results)
    viz_path = test_results_dir / "analysis_visualization.jpg"
    viz.save(viz_path)
    assert viz_path.exists()
    logger.info(f"Saved visualization to {viz_path}")

def test_depth_estimation_quality(depth_estimator, sample_image):
    """Test depth estimator produces valid results"""
    # Set timeout for depth estimation (10 seconds)
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(10)
    
    try:
        # Estimate depth
        depth_map = depth_estimator.estimate_depth(sample_image)
        
        # Convert to actual depth
        actual_depth = depth_estimator.estimate_actual_depth(
            depth_map, 
            baseline=0.1,
            focal_pixels=1000
        )
        
        # Validate output
        assert actual_depth.shape == (sample_image.height, sample_image.width)
        assert np.min(actual_depth) >= 0
        assert np.max(actual_depth) > 0
        assert np.median(actual_depth) > 0
        logger.info(f"Depth estimation valid: min={np.min(actual_depth):.2f}, max={np.max(actual_depth):.2f}, median={np.median(actual_depth):.2f}")
    
    except TimeoutException:
        pytest.skip("Depth estimation timed out")
    finally:
        signal.alarm(0)  # Disable alarm

def test_error_handling(eagle_eye_instance):
    """Test invalid input handling"""
    # Test empty image
    with pytest.raises(ValueError):
        eagle_eye_instance.analyze_image(b'')
    
    # Test invalid image format
    with pytest.raises(ValueError):
        eagle_eye_instance.analyze_image(b'invalid image data')

# ----- Performance Tests -----
@pytest.mark.benchmark
def test_inference_performance(eagle_eye_instance, sample_image, benchmark):
    """Benchmark inference performance with real depth estimation"""
    img_byte_arr = io.BytesIO()
    sample_image.save(img_byte_arr, format='JPEG')
    image_bytes = img_byte_arr.getvalue()
    
    # Mock Vision API to avoid network calls
    with patch.object(eagle_eye_instance.vision_client, 'label_detection'):
        # Mock segmentation to speed up test
        with patch.object(eagle_eye_instance, '_segment_food'):
            def run_analysis():
                return eagle_eye_instance.analyze_image(image_bytes)
            
            result = benchmark(run_analysis)
            assert "physical_analysis" in result
            logger.info(f"Analysis completed in {result['physical_analysis'].get('processing_time_ms', 0):.2f} ms")

if __name__ == "__main__":
    pytest.main(["-v", "--html=test_report.html", "--self-contained-html"])

# # src/agents/eagle_eye/tests/test_main.py
# import pytest
# import sys
# import os
# import json
# import io
# import torch
# import numpy as np
# import cv2
# from pathlib import Path
# from PIL import Image, ImageDraw
# from unittest.mock import Mock, patch, create_autospec
# from datetime import datetime
# from google.cloud import vision
# from config import CLOUD_VISION_CREDENTIALS_JSON
# from agents.eagle_eye.main import EagleEye
# from agents.eagle_eye.segmenter import EagleEyeSegmenter

# # Configure absolute paths
# PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
# SRC_DIR = PROJECT_ROOT / "src"
# AGENT_DIR = SRC_DIR / "agents" / "eagle_eye"
# DATA_DIR = PROJECT_ROOT / "data" / "eagle_eye"

# sys.path.insert(0, str(SRC_DIR))
# sys.path.insert(0, str(AGENT_DIR))

# # Test Configuration
# TEST_IMAGE = DATA_DIR / "datasets" / "train" / "images" / "food_sample.jpg"
# MODEL_CHECKPOINT = DATA_DIR / "FoodSeg103" / "pretrained" / "foodseg103.pth"
# CALIBRATION_PROFILES = {
#     "default": {"pixels_per_meter": 1000, "focal_length": 1.0},
#     "high_res": {"pixels_per_meter": 2500, "focal_length": 1.8}
# }

# @pytest.fixture(scope="module")
# def test_results_dir():
#     """Create timestamped results directory"""
#     results_dir = DATA_DIR / "results" / datetime.now().strftime("%Y%m%d_%H%M%S")
#     results_dir.mkdir(parents=True, exist_ok=True)
#     yield results_dir
#     for f in results_dir.glob("*"):
#         if f.is_file():
#             f.unlink()

# @pytest.fixture(scope="module")
# def mock_vision_client():
#     """Mock Google Cloud Vision client with config credentials"""
#     client = create_autospec(vision.ImageAnnotatorClient)
#     client.label_detection.return_value = vision.AnnotateImageResponse(
#         label_annotations=[
#             vision.EntityAnnotation(description="Pizza", score=0.95),
#             vision.EntityAnnotation(description="Salad", score=0.87)
#         ]
#     )
#     with patch('google.oauth2.service_account.Credentials.from_service_account_info') as mock_creds:
#         # Use actual credentials from config
#         mock_creds.return_value = None  # Bypass real credential validation
#         yield client

# @pytest.fixture
# def mock_analyst(request, mock_vision_client):
#     """Analyst instance with mocked dependencies"""
#     cal_profile = request.param if hasattr(request, 'param') else 'default'
    
#     with patch('src.agents.eagle_eye.main.vision.ImageAnnotatorClient', return_value=mock_vision_client), \
#          patch('src.agents.eagle_eye.segmenter.EagleEyeSegmenter.load') as mock_seg_load, \
#          patch('src.agents.eagle_eye.main.DepthEstimator') as mock_depth:

#         # Mock Segmentation Model - bypass file loading completely
#         mock_seg = Mock(spec=EagleEyeSegmenter)
#         mock_seg.return_value = torch.ones(1, 1, 256, 256)  # Match expected output size
#         mock_seg_load.return_value = mock_seg  # Return mock instead of loading

#         # Mock Depth Estimator
#         mock_depth.return_value.estimate_depth.return_value = np.full((256, 256), 0.5)
#         mock_depth.return_value.estimate_actual_depth.return_value = np.full((256, 256), 0.8)

#         # Initialize analyst with dummy path (file doesn't need to exist)
#         analyst = EagleEye(
#             model_path="mocked.pt",  # Doesn't require real file
#             calibration_data=CALIBRATION_PROFILES[cal_profile],
#             api_integrator={"credentials_env_var": "CLOUD_VISION_CREDENTIALS_JSON"},
#             use_gpu=False
#         )

#         # Add consistent mock segmentation
#         def mock_segment(image_array):
#             h, w = image_array.shape[:2]
#             mask = np.zeros((h, w))
#             cv2.ellipse(mask, (w//2, h//2), (w//4, h//5), 0, 0, 360, 1, -1)
#             return mask.astype(np.float32)
        
#         analyst._segment_food = mock_segment
        
#         yield analyst

# @pytest.fixture
# def sample_image():
#     """Generate synthetic test image with food-like region"""
#     img = Image.new('RGB', (800, 600), (73, 109, 137))
#     draw = ImageDraw.Draw(img)
#     draw.ellipse((200, 150, 600, 450), fill=(94, 185, 84))
#     img_bytes = io.BytesIO()
#     img.save(img_bytes, format='JPEG')
#     return img_bytes.getvalue()

# class TestCoreFunctionality:
#     """Core functionality tests with config-based credentials"""
    
#     def test_config_validation(self):
#         """Verify Cloud Vision credentials configuration"""
#         credentials = json.loads(CLOUD_VISION_CREDENTIALS_JSON)
#         assert isinstance(credentials, dict), "Credentials should be a dictionary"
#         assert "client_email" in credentials, "Missing service account email"
#         assert "private_key" in credentials, "Missing private key"

#     @pytest.mark.parametrize("mock_analyst", ["default", "high_res"], indirect=True)
#     def test_calibration_impact(self, mock_analyst, sample_image):
#         """Test calibration profile impacts results"""
#         results = mock_analyst.analyze_image(sample_image)
#         phys = results['physical_analysis']
#         assert phys['estimated_volume_m3'] > 0, "Volume should be positive"
#         if mock_analyst.pixels_per_meter == 2500:
#             assert phys['estimated_volume_m3'] < 0.5, "High-res calibration should reduce volume"

#     def test_vision_api_integration(self, mock_analyst, sample_image):
#         """Test Cloud Vision API integration"""
#         results = mock_analyst.analyze_image(sample_image)
#         labels = [item["label"] for item in results["detection_results"]]
#         assert "Pizza" in labels, "Expected food label not found"
#         assert "Salad" in labels, "Expected food label not found"

# class TestProductionIntegration:
#     """Integration tests with real config credentials"""
    
#     @pytest.fixture(scope="class")
#     def production_analyst(self, trained_model):  # Use trained_model fixture
#         """Analyst with real configuration"""
#         with patch('src.agents.eagle_eye.main.vision.ImageAnnotatorClient'):
#             return EagleEye(
#                 model_path=str(trained_model),  # Use the generated model
#                 calibration_data=CALIBRATION_PROFILES["default"],
#                 api_integrator={"credentials_env_var": "CLOUD_VISION_CREDENTIALS_JSON"},
#                 use_gpu=False
#         )

#     def test_real_inference(self, production_analyst, test_results_dir, sample_image):
#         """End-to-end inference test"""
#         results = production_analyst.analyze_image(sample_image)
#         assert len(results["detection_results"]) >= 1, "No detections found"
        
#         output_path = test_results_dir / "production_result.jpg"
#         viz = production_analyst.visualize_results(sample_image, results)
#         viz.save(output_path)
#         assert output_path.exists(), "Visualization output missing"

# class TestTrainingWorkflow:
#     """End-to-end training tests"""
    
#     @pytest.fixture(scope="class")
#     def trained_model(self, tmp_path_factory):
#         """Train minimal model through agent CLI"""
#         from src.agents.eagle_eye.generate_synthetic import generate_synthetic_dataset
#         from src.agents.eagle_eye.main import main as train_main

#         # Create synthetic data
#         data_dir = tmp_path_factory.mktemp("data")
#         generate_synthetic_dataset(data_dir, num_images=10)
        
#         work_dir = tmp_path_factory.mktemp("training")
#         test_args = [
#             "train",
#             "--data-dir", str(data_dir),
#             "--output-dir", str(work_dir),
#             "--epochs", "1",
#             "--batch-size", "2",
#             "--pretrained", ""  # Ensure fresh training
#         ]
        
#         # Preserve original arguments
#         original_argv = sys.argv
#         try:
#             sys.argv = ['main.py'] + test_args
#             train_main()
#         finally:
#             sys.argv = original_argv
            
#         return work_dir / "seg_epoch1.pth"

#     def test_training_integration(self, trained_model, sample_image):
#         """Verify trained model functionality"""
#         analyst = EagleEye(
#             model_path=str(trained_model),
#             calibration_data=CALIBRATION_PROFILES["default"]
#         )
#         results = analyst.analyze_image(sample_image)
#         assert results["physical_analysis"]["pixel_area"] > 0, "Invalid segmentation"

# if __name__ == "__main__":
#     pytest.main(["-v", "--html=test_report.html", "--self-contained-html"])