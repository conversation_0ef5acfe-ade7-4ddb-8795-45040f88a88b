import sys
import os
import torch
import numpy as np
from PIL import Image
from pathlib import Path
import mmseg

# Configure paths
current_dir = Path(__file__).parent.resolve()
sys.path.insert(0, str(current_dir.parent.parent.parent / "data" / "eagle_eye" / "FoodSeg103"))
sys.path.insert(0, str(current_dir))

from mmseg.apis import inference_model, init_model, show_result_pyplot

def main():
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--checkpoint', required=True)
    parser.add_argument('--config', required=True)
    parser.add_argument('--image-path', required=True)
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--output-dir', default='results')
    parser.add_argument('--palette', default='foodseg103')
    args = parser.parse_args()

    # Build model
    model = init_model(args.config, args.checkpoint, device=args.device)

    # Run inference
    result = inference_model(model, args.image_path)
    
    # Visualize
    vis_image = show_result_pyplot(
        model,
        args.image_path,
        result,
        palette=args.palette,
        opacity=0.5
    )
    
    # Save output
    output_path = Path(args.output_dir) / f"{Path(args.image_path).stem}_seg.png"
    output_path.parent.mkdir(parents=True, exist_ok=True)
    vis_image.save(output_path)
    print(f"Saved result to: {output_path}")

if __name__ == '__main__':
    main()