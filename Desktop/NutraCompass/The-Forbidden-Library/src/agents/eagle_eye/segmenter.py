import torch
from torchvision import models

class EagleEyeSegmenter(torch.nn.Module):
    """Custom segmentation model for Eagle Eye"""
    
    def __init__(self):
        super().__init__()
        base_model = models.efficientnet_b3(pretrained=True)
        self.backbone = base_model.features
        self.decoder = torch.nn.Sequential(
            torch.nn.Conv2d(1536, 512, 3, padding=1),
            torch.nn.Upsample(scale_factor=2),  # 8x -> 16x
            torch.nn.Conv2d(512, 256, 3, padding=1),
            torch.nn.Upsample(scale_factor=2),  # 16x -> 32x
            torch.nn.Conv2d(256, 128, 3, padding=1),
            torch.nn.Upsample(scale_factor=8),  # 32x -> 256x
            torch.nn.Conv2d(128, 1, 1)
        )
        
    def forward(self, x):
        x = self.backbone(x)
        return torch.sigmoid(self.decoder(x))
    
    @classmethod
    def load(cls, model_path):
        """Load trained model weights"""
        model = cls()
        model.load_state_dict(torch.load(model_path))
        model.eval()
        return model