# src/agents/eagle_eye/main.py
import io
import os
import json
import logging
import numpy as np
from PIL import Image, ImageDraw
import torch
import torch.nn.functional as F
from google.cloud import vision
from torchvision import transforms
import cv2
from functools import lru_cache
from pathlib import Path
import argparse
from torch.utils.data import DataLoader
from torch import nn, optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
from tqdm import tqdm
from .depth_estimator import DepthEstimator
from src.config import CLOUD_VISION_CREDENTIALS_JSON
from .training.dataset import FoodSegmentationDataset
from .segmenter import EagleEyeSegmenter  # Import your custom segmenter

logger = logging.getLogger("EagleEye")
logging.basicConfig(level=logging.INFO)

def main():
    """Unified CLI for both training and inference"""
    import sys  # Added import
    
    parser = argparse.ArgumentParser(description="Eagle Eye Main Interface")
    subparsers = parser.add_subparsers(dest='command', required=True)

    # Training parser
    train_parser = subparsers.add_parser('train', help='Train segmentation model')
    train_parser.add_argument("--data-dir", required=True,
                            help="Root directory containing train/val folders")
    train_parser.add_argument("--batch-size", type=int, default=8)
    train_parser.add_argument("--epochs", type=int, default=50)
    train_parser.add_argument("--pretrained", help="Path to pretrained weights")
    train_parser.add_argument("--output-dir", required=True,
                            help="Directory to save trained models")

    # Inference parser
    infer_parser = subparsers.add_parser('infer', help='Run inference')
    infer_parser.add_argument("--image", required=True, help="Input image path")
    infer_parser.add_argument("--model", required=True, 
                            help="Path to trained model weights")
    infer_parser.add_argument("--calibration", help="Calibration config JSON")

    # Parse arguments from command line
    args = parser.parse_args(sys.argv[1:])  # Explicit argument handling

    if args.command == "train":
        train_segmenter(args)
    elif args.command == "infer":
        run_inference(args)

def train_segmenter(args):
    """Consolidated training from train_segmenter.py with enhancements"""
    logger.info("="*50)
    logger.info(f"Starting training with config:")
    logger.info(f"Data directory: {args.data_dir}")
    logger.info(f"Output directory: {args.output_dir}")
    logger.info(f"Batch size: {args.batch_size}")
    logger.info(f"Epochs: {args.epochs}")
    logger.info("="*50)

    # Convert to absolute paths
    data_dir = Path(args.data_dir).resolve()
    output_dir = Path(args.output_dir).resolve()
    
    # Validate paths
    if not data_dir.exists():
        raise FileNotFoundError(f"Data directory not found: {data_dir}")
    if not (data_dir/"train/images").exists():
        raise ValueError("Missing train/images subdirectory")
    if not (data_dir/"train/masks").exists():
        raise ValueError("Missing train/masks subdirectory")

    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Initialize model
    model = EagleEyeSegmenter().to(device)
    if args.pretrained:
        pretrained_path = Path(args.pretrained).resolve()
        if not pretrained_path.exists():
            raise FileNotFoundError(f"Pretrained weights not found: {pretrained_path}")
        model.load_state_dict(torch.load(str(pretrained_path)))
        logger.info(f"Loaded pretrained weights from {pretrained_path}")

    # Dataset and Loader
    train_dataset = FoodSegmentationDataset(data_dir/"train")
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    # Optimization
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    scheduler = ReduceLROnPlateau(optimizer, 'min', patience=3)
    criterion = nn.BCELoss()

    # Training loop
    for epoch in range(args.epochs):
        model.train()
        epoch_loss = 0
        
        for images, masks in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            images = images.to(device, non_blocking=True)
            masks = masks.to(device, non_blocking=True)
            
            optimizer.zero_grad(set_to_none=True)
            outputs = model(images)
            loss = criterion(outputs, masks)
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
        
        avg_loss = epoch_loss / len(train_loader)
        scheduler.step(avg_loss)
        
        logger.info(f"Epoch {epoch+1}/{args.epochs} | Loss: {avg_loss:.4f} | LR: {optimizer.param_groups[0]['lr']:.6f}")

        # Save checkpoints
        if (epoch+1) % 5 == 0 or epoch == args.epochs - 1:
            ckpt_path = output_dir / f"seg_epoch{epoch+1}.pt"
            torch.save(model.state_dict(), str(ckpt_path))
            logger.info(f"Saved checkpoint to {ckpt_path}")

    # Final save
    final_path = output_dir / "eagle_eye_segmenter_final.pt"
    torch.save(model.state_dict(), str(final_path))

def run_inference(args):
    """Inference workflow using trained model"""
    # Load model
    model = EagleEyeSegmenter.load(args.model)
    
    # Initialize EagleEye analyzer
    analyzer = EagleEye(
        model_path=args.model,
        calibration_data=json.loads(args.calibration) if args.calibration else None
    )
    
    # Process image
    with open(args.image, "rb") as f:
        results = analyzer.analyze_image(f.read())
    
    print("Analysis Results:")
    print(json.dumps(results, indent=2))

class EagleEye:
    """Autonomous food analysis agent with core vision capabilities and optional nutritional integration"""
    
  # region Initialization and Configuration
    def __init__(self, model_path, api_integrator=None, calibration_data=None, detection_threshold=0.7, use_gpu=True):
        """Initialize EagleEye analyzer with optional calibration
        
        Args:
            model_path (str): Path to trained segmentation model (.pt)
            api_integrator: Optional nutrition API integration
            calibration_data (dict): Camera calibration parameters:
                - pixels_per_meter: Resolution calibration
                - focal_length: Depth calibration factor
            detection_threshold (float): Minimum confidence for food labels (0-1)
        """
        # Validate and load credentials
        self._validate_credentials()
        
        # Vision API client setup
        credentials_info = json.loads(CLOUD_VISION_CREDENTIALS_JSON)
        self.vision_client = vision.ImageAnnotatorClient.from_service_account_info(
            credentials_info
        )
        
        # Model initialization
        self._load_vision_models(model_path)
        self._configure_transforms()
        
        # System configuration
        self.calibration_data = calibration_data or {}
        self._setup_calibration()
        
        self.api_integrator = api_integrator
        self.detection_threshold = detection_threshold
        self.depth_estimator = DepthEstimator(use_gpu=use_gpu)

    def _validate_credentials(self):
        """Ensure valid credentials are present"""
        if not CLOUD_VISION_CREDENTIALS_JSON:
            raise ValueError("Missing CLOUD_VISION_CREDENTIALS_JSON environment variable")
        try:
            json.loads(CLOUD_VISION_CREDENTIALS_JSON)
        except json.JSONDecodeError as e:
            raise ValueError("Invalid credentials JSON format") from e

    def _setup_calibration(self):
        """Initialize physical measurement parameters"""
        self.baseline = self.calibration_data.get('baseline', 0.1)  # meters
        self.focal_pixels = self.calibration_data.get('focal_pixels', 1000)
        self.pixels_per_meter = self.calibration_data.get('pixels_per_meter', 1500)

    def _load_vision_models(self, model_path):
        """Load trained segmentation model with validation"""
        try:
            if model_path.endswith(('.pt', '.pth')):
                # Load custom EagleEyeSegmenter
                self.seg_model = EagleEyeSegmenter.load(model_path)
                logger.info(f"Loaded custom segmenter from {model_path}")
            else:
                raise ValueError("Unsupported model format. Use .pt or .pth files")
                
            # Send to GPU if available
            if torch.cuda.is_available():
                self.seg_model = self.seg_model.cuda()
                
            self.seg_model.eval()
            
        except Exception as e:
            logger.error(f"Model load failed: {str(e)}")
            raise

    def _validate_model(self):
        """Sanity check for loaded model with correct input size"""
        try:
            # Use input size matching training (256x256)
            test_input = torch.randn(1, 3, 256, 256)
            if torch.cuda.is_available():
                test_input = test_input.cuda()
                
            with torch.no_grad():
                output = self.seg_model(test_input)
                
            logger.debug(f"Model validation passed. Output shape: {output.shape}")
        except Exception as e:
            logger.error(f"Model validation failed: {str(e)}")
            raise RuntimeError("Invalid model architecture")

    def _configure_transforms(self):
        """Configure transforms to match training preprocessing"""
        self.food_transform = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(256),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                            std=[0.229, 0.224, 0.225])
        ])
    # endregion

    # region Core Analysis Pipeline
    def analyze_image(self, image_bytes):
        """Main analysis pipeline - returns core visual analysis results"""
        try:
            self._validate_image_input(image_bytes)
            image = Image.open(io.BytesIO(image_bytes))
            image_array = np.array(image)
            
            return {
                "detection_results": self._detect_food_items(image_array),
                "physical_analysis": self._analyze_physical_properties(image),
                "perception_data": {
                    "depth_map": self._estimate_depth(image).tolist(),
                    "food_mask": self._segment_food(image_array).tolist()
                }
            }
        except Exception as e:
            logger.error(f"Analysis pipeline failed: {str(e)}")
            return {"error": str(e), "stage": "core_analysis"}

    def _validate_image_input(self, image_bytes):
        """Validate input image requirements"""
        if not isinstance(image_bytes, bytes) or len(image_bytes) < 1024:
            raise ValueError("Invalid image data")
        
        img = Image.open(io.BytesIO(image_bytes))
        if min(img.size) < 100:
            raise ValueError("Image resolution too low (min 100x100 pixels)")
        if img.mode != 'RGB':
            raise ValueError("Only RGB images supported")
    # endregion

    # region Vision Processing Operations
    @lru_cache(maxsize=100)
    def _call_vision_api(self, image_content):
        """Cached Vision API call for label detection"""
        return self.vision_client.label_detection(
            image=vision.Image(content=image_content)
        )

    def _detect_food_items(self, image_array):
        """Integrate mask data with Vision API results"""
        try:
            # Convert numpy array to JPEG bytes
            buffer = io.BytesIO()
            Image.fromarray(image_array).save(buffer, format='JPEG')
            image_bytes = buffer.getvalue()
            
            response = self._call_vision_api(image_bytes)
            mask = self._segment_food(image_array)

            return [
                {
                    "label": label.description,
                    "confidence": float(label.score),
                    "geometry": self._estimate_food_geometry(label, mask)
                }
                for label in response.label_annotations
                if label.score > self.detection_threshold
            ]
        except Exception as e:
            logger.error(f"Detection failed: {str(e)}")
            return []

    def _segment_food(self, image_array):
        """Segment food regions using neural network"""
        try:
            image = Image.fromarray(image_array)
            original_size = image.size  # (width, height)
            tensor = self.food_transform(image).unsqueeze(0)
            
            with torch.no_grad():
                output = torch.sigmoid(self.seg_model(tensor))
            
            # Post-process and resize mask
            output = F.interpolate(
                output, 
                size=original_size[::-1],  # (height, width)
                mode='bilinear', 
                align_corners=False
            )
            
            # Clean up small artifacts
            mask = output.squeeze().cpu().numpy()
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
            return cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
        except Exception as e:
            logger.error(f"Segmentation failed: {str(e)}")
            return np.zeros_like(image_array)

    def _estimate_depth(self, image):
        """Get metric depth map in meters"""
        try:
            disparity_map = self.depth_estimator.estimate_depth(image)
            return self.depth_estimator.estimate_actual_depth(
                disparity_map,
                baseline=self.baseline,
                focal_pixels=self.focal_pixels
            )
        except Exception as e:
            logger.error(f"Depth estimation failed: {str(e)}")
            return np.zeros((image.height, image.width))
    # endregion

    # region Physical Analysis
    def _analyze_physical_properties(self, image):
        """Updated physical analysis with metric depth"""
        try:
            depth_map = self._estimate_depth(image)
            mask = self._segment_food(np.array(image))
            
            return {
                "pixel_area": int(np.sum(mask > 0.5)),
                "estimated_volume_m3": self._calculate_volume(mask, depth_map),
                "depth_stats": {
                    "min": float(np.min(depth_map)),
                    "max": float(np.max(depth_map)),
                    "median": float(np.median(depth_map))
                },
                "relative_scale": self._calculate_relative_scale(image.size, mask)
            }
        except Exception as e:
            logger.error(f"Physical analysis failed: {str(e)}")
            return {}

    def _calculate_volume(self, mask, depth_map):
        """Physical volume calculation using metric depth"""
        try:
            food_pixels = np.sum(mask > 0.5)
            px_to_m = 1 / self.pixels_per_meter
            area_m2 = food_pixels * (px_to_m ** 2)
            
            depth_values = depth_map[mask > 0.5]
            if depth_values.size == 0:
                logger.warning("No valid depth values in food region")
                return 0.0
                
            # Use median depth to mitigate outliers
            return area_m2 * np.median(depth_values)  # m³
            
        except Exception as e:
            logger.error(f"Volume calculation error: {str(e)}")
            return 0.0

    def _calculate_relative_scale(self, image_size, mask):
        """Calculate food portion relative to image size"""
        food_pixels = np.sum(mask > 0.5)
        total_pixels = image_size[0] * image_size[1]
        return food_pixels / total_pixels

    def _estimate_food_geometry(self, label, mask):
        """Calculate actual food region from segmentation mask"""
        try:
            contours, _ = cv2.findContours(
                (mask > 0.5).astype(np.uint8),
                cv2.RETR_EXTERNAL,
                cv2.CHAIN_APPROX_SIMPLE
            )
            
            if not contours:
                logger.warning("No food contours detected")
                return {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0}
                
            largest = max(contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(largest)
            
            return {
                "x": x / mask.shape[1],  # Normalized coordinates
                "y": y / mask.shape[0],
                "width": w / mask.shape[1],
                "height": h / mask.shape[0]
            }
            
        except Exception as e:
            logger.error(f"Geometry estimation failed: {str(e)}")
            return {"x": 0, "y": 0, "width": 0, "height": 0}
    # endregion

    # region Nutritional Integration
    def estimate_nutrition(self, detection_results):
        """Optional nutritional analysis using external APIs"""
        if not self.api_integrator:
            return {"error": "Nutrition integration not configured"}
        
        try:
            return {
                item["label"]: self._get_nutrition_info(item["label"])
                for item in detection_results.get("detection_results", [])
            }
        except Exception as e:
            logger.error(f"Nutrition estimation failed: {str(e)}")
            return {"error": str(e)}

    def _get_nutrition_info(self, food_name):
        """Retrieve nutrition data from external APIs"""
        try:
            if self.api_integrator:
                for api in ["edamam", "usda"]:
                    result = self.api_integrator.search_food(api, food_name, limit=1)
                    if result.get("results"):
                        return self._parse_api_response(result["results"][0])
            return {"status": "no_data_available"}
        except Exception as e:
            logger.warning(f"Nutrition lookup failed for {food_name}: {str(e)}")
            return {"error": "api_failure"}

    def _parse_api_response(self, api_data):
        """Standardize API response format"""
        return {
            "calories": api_data.get("energy", 0),
            "protein": api_data.get("protein", 0),
            "carbs": api_data.get("carbohydrates", 0),
            "fat": api_data.get("fat", 0),
            "density": api_data.get("density", 1000)
        }
    # endregion

    # region Visualization Utilities
    def visualize_results(self, image_bytes, analysis_results):
        """Generate annotated output image"""
        try:
            img = Image.open(io.BytesIO(image_bytes))
            draw = ImageDraw.Draw(img)
            
            self._draw_detection_boxes(draw, analysis_results.get("detection_results", []))
            self._apply_food_mask(img, analysis_results.get("perception_data", {}).get("food_mask", []))
            
            return img
        except Exception as e:
            logger.error(f"Visualization failed: {str(e)}")
            return Image.new("RGB", (800, 600), color="red")

    def _draw_detection_boxes(self, draw, detections):
        """Draw bounding boxes and labels on image"""
        for detection in detections:
            geom = detection.get("geometry", {})
            x = geom.get("x", 0) * draw.im.size[0]
            y = geom.get("y", 0) * draw.im.size[1]
            width = geom.get("width", 0.3) * draw.im.size[0]
            height = geom.get("height", 0.3) * draw.im.size[1]
            
            draw.rectangle([x, y, x+width, y+height], outline="red", width=3)
            draw.text((x+5, y+5), 
                     f"{detection.get('label', '')} ({detection.get('confidence', 0):.0%})",
                     fill="white")

    def _apply_food_mask(self, img, mask_data):
        """Apply food region overlay"""
        try:
            if mask_data:
                mask_array = (np.array(mask_data) > 0.5).astype(np.uint8) * 255
                overlay = Image.new("RGBA", img.size, (255, 0, 0, 50))
                img.paste(overlay, mask=Image.fromarray(mask_array))
        except Exception as e:
            logger.error(f"Mask application failed: {str(e)}")
    # endregion