# src/agents/eagle_eye/configs/custom_foodseg.py
import os
from pathlib import Path

# Base configuration with absolute path
current_dir = Path(__file__).parent.resolve()
_base_ = str(current_dir / "../../../../data/eagle_eye/FoodSeg103/configs/pspnet/pspnet_r50-d8_512x512_40k_cityscapes.py")

# Dataset configuration
data_root = str(Path(__file__).parent.parent.parent.parent / "data" / "eagle_eye" / "datasets")

# Model adjustments
model = dict(
    pretrained="data/eagle_eye/FoodSeg103/pretrained/foodseg103.pth",
    backbone=dict(
        norm_cfg=dict(type='BN', requires_grad=True)),
    decode_head=dict(
        num_classes=103)  # Match FoodSeg103 class count
)

# Dataset pipeline
train_pipeline = [
    dict(type='LoadImageFromFile'),
    dict(type='LoadAnnotations'),
    dict(type='RandomRotate', prob=0.5, degree=20),
    dict(type='RandomFlip', prob=0.5),
    dict(type='PhotoMetricDistortion',
         brightness_delta=32,
         contrast_range=(0.5, 1.5),
         saturation_range=(0.5, 1.5),
         hue_delta=18),
    dict(type='Normalize',
         mean=[123.675, 116.28, 103.53],  # FoodSeg103 specific
         std=[58.395, 57.12, 57.375],
         to_rgb=True),
    dict(type='DefaultFormatBundle'),
    dict(type='Collect', keys=['img', 'gt_semantic_seg'])
]

# Data configuration
data = dict(
    samples_per_gpu=4,
    workers_per_gpu=4,
    train=dict(
        type='FoodDataset',
        data_root=data_root,
        img_dir='train/images',
        ann_dir='train/masks',
        pipeline=train_pipeline),
    val=dict(
        type='FoodDataset',
        data_root=data_root,
        img_dir='val/images',
        ann_dir='val/masks'),
    test=dict(
        type='FoodDataset',
        data_root=data_root,
        img_dir='test/images')
)

# Training schedule
optimizer = dict(type='SGD', lr=0.01, momentum=0.9, weight_decay=0.0005)
lr_config = dict(policy='poly', power=0.9, min_lr=1e-4, by_epoch=False)
runner = dict(type='IterBasedRunner', max_iters=40000)
checkpoint_config = dict(by_epoch=False, interval=4000)
evaluation = dict(interval=4000, metric='mIoU')