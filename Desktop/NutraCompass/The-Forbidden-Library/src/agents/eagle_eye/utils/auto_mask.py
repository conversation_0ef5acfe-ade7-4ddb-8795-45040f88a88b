# src/agents/eagle_eye/utils/auto_mask.py
import cv2
import numpy as np
import os
import sys
from pathlib import Path

def detect_food_regions(img):
    """Enhanced food detection using multi-channel analysis"""
    # Convert to LAB color space
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    l_channel, a_channel, b_channel = cv2.split(lab)

    # Adaptive thresholding with <PERSON><PERSON>'s method
    def get_otsu(channel):
        _, thresh = cv2.threshold(channel, 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        return thresh

    # Combine luminance and color channels
    combined = cv2.bitwise_and(get_otsu(l_channel), get_otsu(a_channel))
    
    # Morphological cleanup
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (31,31))
    return cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)

def foodseg103_mask(img_path, mask_path):
    """FoodSeg103 mask generation with dependency checks"""
    try:
        from mmseg.apis import inference_model, init_model
    except ImportError:
        raise RuntimeError(
            "FoodSeg103 dependencies missing! Run:\n"
            "pip install mmcv-full==1.7.1 mmsegmentation==0.30.0"
        )
    
    config = str(Path("src/data/eagle_eye/FoodSeg103/configs/foodseg103.py").resolve())
    checkpoint = str(Path("src/data/eagle_eye/FoodSeg103/pretrained/foodseg103.pth").resolve())
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    model = init_model(config, checkpoint, device=device)
    result = inference_model(model, str(img_path))
    
    mask = result.pred_sem_seg.data[0].cpu().numpy().astype(np.uint8)
    cv2.imwrite(str(mask_path), mask)

def generate_masks(input_dir, output_dir, use_foodseg=True):
    """Generate masks with fallback strategies"""
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    output_path.mkdir(parents=True, exist_ok=True)
    
    for img_path in input_path.glob("*.*"):
        if img_path.suffix.lower() not in [".jpg", ".jpeg", ".png"]:
            continue
            
        mask_path = output_path / f"{img_path.stem}.png"
        if mask_path.exists():
            continue

        try:
            if use_foodseg:
                foodseg103_mask(img_path, mask_path)
                print(f"FoodSeg103 mask: {mask_path}")
                continue
        except Exception as e:
            print(f"FoodSeg103 failed: {str(e)}")
            print("Falling back to automatic detection...")

        # Color-based fallback
        img = cv2.imread(str(img_path))
        if img is None:
            continue
            
        food_mask = detect_food_regions(img)
        
        # Quality check and manual fallback
        if cv2.countNonZero(food_mask) < 0.1 * img.size:
            print(f"Low confidence: {img_path.name}")
            if "annotate_mask.py" in sys.argv:
                os.system(f"python utils/annotate_mask.py {img_path} {mask_path}")
            else:
                cv2.imwrite(str(mask_path), food_mask)
        else:
            cv2.imwrite(str(mask_path), food_mask)
            print(f"Generated mask: {mask_path}")

def main():
    """CLI interface with options"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Food Mask Generator")
    parser.add_argument("input_dir", help="Directory containing food images")
    parser.add_argument("output_dir", help="Directory to save generated masks")
    parser.add_argument("--no-foodseg", action="store_false", dest="use_foodseg",
                       help="Disable FoodSeg103 model")
    parser.add_argument("--annotate", action="store_true",
                       help="Enable manual annotation fallback")
    
    args = parser.parse_args()
    
    if args.annotate:
        sys.argv.append("annotate_mask.py")
    
    generate_masks(args.input_dir, args.output_dir, args.use_foodseg)

if __name__ == "__main__":
    main()