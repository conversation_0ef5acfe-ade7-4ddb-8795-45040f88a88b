# src/agents/eagle_eye/utils/foodseg_wrapper.py
import torch
import cv2
import numpy as np

def generate_foodseg103_mask(img_path, mask_path):
    """Safe FoodSeg103 mask generation with dependency checks"""
    try:
        from mmseg.apis import inference_model, init_model
    except ImportError:
        raise RuntimeError("MMSegmentation not installed correctly. Follow:\n"
                           "pip install mmcv-full==1.7.1 mmsegmentation==0.30.0")
    
    config = "src/data/eagle_eye/FoodSeg103/configs/foodseg103.py"
    checkpoint = "src/data/eagle_eye/FoodSeg103/pretrained/foodseg103.pth"
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    model = init_model(config, checkpoint, device=device)
    
    result = inference_model(model, img_path)
    mask = result.pred_sem_seg.data[0].cpu().numpy().astype(np.uint8)
    cv2.imwrite(mask_path, mask)