import hashlib
import os
import torch

def verify_training_readiness(split_file, work_dir):
    # Check for existing training
    if os.path.exists(work_dir):
        raise RuntimeError(f"Work directory {work_dir} already exists!")
    
    # Record split file checksum
    with open(split_file, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def check_for_duplicates(checkpoint_path, current_split_hash):
    checkpoint = torch.load(checkpoint_path)
    if checkpoint['split_hash'] == current_split_hash:
        raise ValueError("Exact training split already processed!")