# src/agents/eagle_eye/utils/foodseg_mask.py
import cv2
import numpy as np
import torch
from pathlib import Path
import sys

class FoodSegWrapper:
    def __init__(self):
        self.model = None
        self.initialized = False
        
    def initialize(self):
        """Lazy-load FoodSeg103 model"""
        try:
            from mmseg.apis import init_model
            config = Path("src/data/eagle_eye/FoodSeg103/configs/foodseg103.py").resolve()
            checkpoint = Path("src/data/eagle_eye/FoodSeg103/pretrained/foodseg103.pth").resolve()
            self.model = init_model(str(config), str(checkpoint), device='cpu')
            self.initialized = True
        except Exception as e:
            print(f"FoodSeg103 init failed: {str(e)}")
            self.initialized = False

    def generate_mask(self, img_path, mask_path):
        """Generate mask with FoodSeg103 or fallback"""
        if not self.initialized:
            return False
            
        try:
            from mmseg.apis import inference_model
            result = inference_model(self.model, str(img_path))
            mask = result.pred_sem_seg.data[0].cpu().numpy().astype(np.uint8)
            cv2.imwrite(str(mask_path), mask)
            return True
        except:
            return False

def color_based_mask(img_path, mask_path):
    """Fallback color-based masking"""
    img = cv2.imread(str(img_path))
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    _, a_thresh = cv2.threshold(lab[:,:,1], 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (31,31))
    mask = cv2.morphologyEx(a_thresh, cv2.MORPH_CLOSE, kernel)
    cv2.imwrite(str(mask_path), mask)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", required=True)
    parser.add_argument("--output", required=True)
    parser.add_argument("--fallback-strategy", choices=["color", "manual"], default="color")
    args = parser.parse_args()

    seg_wrapper = FoodSegWrapper()
    seg_wrapper.initialize()

    Path(args.output).mkdir(parents=True, exist_ok=True)
    
    for img_path in Path(args.input).glob("*.*"):
        mask_path = Path(args.output) / f"{img_path.stem}.png"
        
        # Try FoodSeg103 first
        success = seg_wrapper.generate_mask(img_path, mask_path)
        
        if not success:
            print(f"FoodSeg failed: {img_path.name}")
            if args.fallback_strategy == "color":
                color_based_mask(img_path, mask_path)
                print(f"Generated color-based mask: {mask_path}")
            else:
                os.system(f"python utils/annotate_mask.py {img_path} {mask_path}")