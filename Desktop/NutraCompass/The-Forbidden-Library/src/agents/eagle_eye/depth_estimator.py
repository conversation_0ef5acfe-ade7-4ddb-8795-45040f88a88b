import torch
import torch.nn.functional as F
from torchvision import transforms
from PIL import Image
import logging

logger = logging.getLogger("EagleEye")

class DepthEstimator:
    """MiDaS depth estimation processor with proper device handling and normalization
    
    Args:
        model_name (str): MiDaS model variant (default: 'MiDaS_small')
        use_gpu (bool): Enable GPU acceleration if available (default: True)
    """
    
    def __init__(self, model_name='MiDaS_small', use_gpu=True):
        try:
            self.device = torch.device(
                'cuda' if torch.cuda.is_available() and use_gpu else 'cpu'
            )
            
            # Load model with optimization settings
            self.model = torch.hub.load(
                'intel-isl/MiDaS', 
                model_name,
                trust_repo=True
            ).to(self.device)
            
            self.model.eval()
            
            # Configure mixed precision for faster inference
            self.amp_enabled = self.device.type == 'cuda'
            
            self._configure_transforms(model_name)
            logger.info(f"DepthEstimator initialized on {self.device} with model {model_name}")
            
        except Exception as e:
            logger.error(f"Depth estimator init failed: {str(e)}")
            raise RuntimeError(f"Failed to initialize DepthEstimator: {str(e)}")

    def _configure_transforms(self, model_name):
        """Configure input transforms based on model architecture"""
        target_size = 384 if 'small' in model_name else 512
        self.transform = transforms.Compose([
            transforms.Resize(target_size, antialias=True),
            transforms.CenterCrop(target_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5], std=[0.5])  # MiDaS-specific normalization
        ])

    def estimate_depth(self, image):
        """Process image and return depth map (disparity values)
        
        Args:
            image (PIL.Image): Input RGB image
            
        Returns:
            numpy.ndarray: Depth map (disparity) with original image dimensions
        """
        if not isinstance(image, Image.Image):
            raise ValueError("Input must be PIL.Image")
            
        try:
            with torch.inference_mode():
                # Convert and move to device
                tensor = self.transform(image).unsqueeze(0).to(self.device)
                
                # Mixed precision context
                with torch.autocast(device_type=self.device.type, enabled=self.amp_enabled):
                    prediction = self.model(tensor)
                    
                return self._postprocess_output(prediction, image.size)
                
        except Exception as e:
            logger.error(f"Depth estimation failed: {str(e)}")
            raise RuntimeError(f"Depth processing error: {str(e)}")

    def _postprocess_output(self, prediction, original_size):
        """Resize output to match original image dimensions"""
        try:
            # Convert from (1, H, W) to (1, 1, H, W) for interpolation
            if prediction.ndim == 3:
                prediction = prediction.unsqueeze(0)
                
            # Maintain tensor on device during processing
            resized = F.interpolate(
                prediction,
                size=original_size[::-1],  # (height, width)
                mode="bicubic",
                align_corners=False
            )
            
            # Return CPU numpy array with squeezed dimensions
            return resized.squeeze().cpu().numpy()
            
        except Exception as e:
            logger.error(f"Depth postprocessing failed: {str(e)}")
            raise

    def estimate_actual_depth(self, disparity_map, baseline=0.1, focal_pixels=1000):
        """Convert disparity map to physical depth values (meters)
        
        Args:
            disparity_map (numpy.ndarray): Raw disparity from estimate_depth()
            baseline (float): Stereo baseline in meters
            focal_pixels (float): Camera focal length in pixels
            
        Returns:
            numpy.ndarray: Depth map in meters (1/disparity scaled by baseline*focal)
        """
        with np.errstate(divide='ignore'):
            depth = (baseline * focal_pixels) / (disparity_map + 1e-6)
        depth[disparity_map < 0] = 0  # Handle invalid disparities
        return depth