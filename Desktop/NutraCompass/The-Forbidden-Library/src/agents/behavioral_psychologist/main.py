from typing import Dict, Any
import random


class BehavioralPsychologist:
    def __init__(self):
        """
        Initialize the Behavioral Psychologist agent.

        Attributes:
            user_behavior_history (Dict): Stores past user behaviors and outcomes.
        """
        self.user_behavior_history = {}

    def profile_behavior(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create or update a behavioral profile for the user.

        :param user_data: Includes interactions, achievements, or struggles.
        :return: An updated behavioral profile.
        """
        motivation_levels = ["Low", "Moderate", "High"]
        habit_strengths = ["Weak", "Moderate", "Strong"]

        profile = {
            "motivation_level": random.choice(motivation_levels),
            "habit_strength": random.choice(habit_strengths),
            "recent_activity": user_data.get("recent_activity", "No recent data")
        }

        self.user_behavior_history[user_data.get("user_id", "default")] = profile
        return profile

    def provide_motivational_messaging(self, profile: Dict[str, Any]) -> str:
        """
        Generate motivational messages tailored to the user’s profile.

        :param profile: The user's current behavioral profile.
        :return: A message encouraging positive behavior.
        """
        messages = {
            "Low": "Starting is the hardest part, but small steps lead to big changes!",
            "Moderate": "You're doing well! Keep building on your progress each day.",
            "High": "You're crushing it! Stay focused and keep pushing your limits."
        }

        return messages.get(profile["motivation_level"], "Keep going! Every step counts.")

    def apply_bct_techniques(self, profile: Dict[str, Any]) -> Dict[str, str]:
        """
        Apply Behavior Change Techniques (BCTs) based on the user's profile.

        :param profile: The user’s behavioral profile.
        :return: Specific techniques or action items.
        """
        bct_techniques = {
            "Low": {"technique": "Implementation Intentions",
                    "action_item": "Plan when and where you'll exercise next."},
            "Moderate": {"technique": "Self-Monitoring",
                         "action_item": "Track your daily steps and set weekly improvement goals."},
            "High": {"technique": "Social Commitment",
                     "action_item": "Challenge a friend to keep up with your fitness routine."}
        }

        return bct_techniques.get(profile["motivation_level"],
                                  {"technique": "General Encouragement", "action_item": "Keep up the great work!"})
