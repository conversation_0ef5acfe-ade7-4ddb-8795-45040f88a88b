import logging
from typing import Dict, Optional, Any, List
from services.external_api_integrator import ExternalAPIIntegrator

logger = logging.getLogger("RecipeGenerator")
logging.basicConfig(level=logging.INFO)

# Standard nutrient schema for food items.
NUTRIENT_SCHEMA = {
    "CHOCDF": {"label": "Total Carbohydrate", "unit": "g"},
    "CHOLE": {"label": "Cholesterol", "unit": "mg"},
    "ENERC_KCAL": {"label": "Calories", "unit": "kcal"},
    "FAMS": {"label": "Monounsaturated Fat", "unit": "g"},
    "FAPU": {"label": "Polyunsaturated Fat", "unit": "g"},
    "FASAT": {"label": "Saturated Fat", "unit": "g"},
    "FAT": {"label": "Total Fat", "unit": "g"},
    "FATRN": {"label": "Trans Fat", "unit": "g"},
    "FIBTG": {"label": "Dietary Fiber", "unit": "g"},
    "PROCNT": {"label": "Protein", "unit": "g"},
    "SUGAR": {"label": "Total Sugar", "unit": "g"},
    "minerals": {
        "CA": {"label": "Calcium", "unit": "mg"},
        "FE": {"label": "Iron", "unit": "mg"},
        "K": {"label": "Potassium", "unit": "mg"},
        "MG": {"label": "Magnesium", "unit": "mg"},
        "NA": {"label": "Sodium", "unit": "mg"},
        "P": {"label": "Phosphorus", "unit": "mg"},
        "ZN": {"label": "Zinc", "unit": "mg"}
    },
     "vitamins": {
        "FOLDFE": {"label": "Folate (Vitamin B9)", "unit": "µg"},
        "NIA": {"label": "Niacin (Vitamin B3)", "unit": "mg"},
        "RIBF": {"label": "Riboflavin (Vitamin B2)", "unit": "mg"},
        "THIA": {"label": "Thiamin (Vitamin B1)", "unit": "mg"},
        "TOCPHA": {"label": "Vitamin E", "unit": "mg"},
        "VITA_RAE": {"label": "Vitamin A", "unit": "µg"},
        "VITB12": {"label": "Vitamin B12", "unit": "µg"},
        "VITB6A": {"label": "Vitamin B6", "unit": "mg"},
        "VITC": {"label": "Vitamin C", "unit": "mg"},
        "VITD": {"label": "Vitamin D", "unit": "µg"},
        "VITK1": {"label": "Vitamin K1", "unit": "µg"}
    }
}

class RecipeGenerator:
    """
    Recipe Generator/Modifier agent that creates or adjusts recipes based on high-level instructions.
    The Nutritionist supplies a list of recommended food names (foodCandidates). For each candidate,
    the Recipe Generator uses the External API to search for detailed food data, normalizes the results,
    and then builds a complete recipe.
    """
    def __init__(self):
        self.external_api = ExternalAPIIntegrator()

    def _process_nutrient(self, food_item: Dict[str, Any], key: str, sub_key: Optional[str] = None) -> Optional[str]:
        """
        Helper to extract a nutrient value from a food_item.
        
        Args:
            food_item: The raw food item.
            key: The nutrient key (e.g., "ENERC_KCAL" or "minerals").
            sub_key: Optional sub-key if key is a compound nutrient (e.g., "CA" in minerals).
        
        Returns:
            The nutrient value as a string if present; otherwise, None.
        """
        default_nutrients = food_item.get("defaultNutrients", {})
        if sub_key:
            if key in default_nutrients and isinstance(default_nutrients[key], dict):
                val = default_nutrients[key].get(sub_key)
                return str(val) if val is not None else None
            return None
        else:
            val = default_nutrients.get(key)
            return str(val) if val is not None else None

    def _normalize_food_item(self, food_item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize a food item to the standard nutrient schema.
        Removes keys: id, date, timestamp, calories.
        
        Args:
            food_item: Raw food item.
        
        Returns:
            Normalized food item.
        """
        normalized = {
            "foodLabel": food_item.get("foodLabel", ""),
            "foodCategory": food_item.get("foodCategory", ""),
            "foodBrand": food_item.get("foodBrand", ""),
            "numberOfServings": food_item.get("numberOfServings", 1),
            "activeMeasure": food_item.get("activeMeasure", {}),
            "measures": food_item.get("measures", []),
            "nutrients": {}
        }
        for key, schema in NUTRIENT_SCHEMA.items():
            if key in ["minerals", "vitamins"]:
                normalized["nutrients"][key] = {}
                for sub_key, sub_schema in schema.items():
                    value = self._process_nutrient(food_item, key, sub_key)
                    normalized["nutrients"][key][sub_key] = {
                        "label": sub_schema["label"],
                        "quantity": value,
                        "totalDaily": {"quantity": None},
                        "unit": sub_schema["unit"]
                    }
            else:
                value = self._process_nutrient(food_item, key)
                normalized["nutrients"][key] = {
                    "label": schema["label"],
                    "quantity": value,
                    "totalDaily": {"quantity": None},
                    "unit": schema["unit"]
                }
        return normalized


    def _aggregate_nutrient_value(self, food_items: List[Dict[str, Any]], key: str, sub_key: Optional[str] = None) -> Optional[str]:
        """
        Helper to sum nutrient values from a list of normalized food items.
        
        Args:
            food_items: List of normalized food items.
            key: Nutrient key.
            sub_key: Optional sub-key for compound nutrients.
        
        Returns:
            The sum of the nutrient values as a formatted string, or None if no value was found.
        """
        total = 0.0
        found = False
        for item in food_items:
            try:
                if sub_key:
                    val = float(item["nutrients"].get(key, {}).get(sub_key, {}).get("quantity", 0))
                else:
                    val = float(item["nutrients"].get(key, {}).get("quantity", 0))
                total += val
                found = True
            except (ValueError, TypeError, AttributeError):
                continue
        return f"{total:.2f}" if found else None

    def _aggregate_nutrients(self, food_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Aggregate nutrient values from a list of normalized food items.
        
        Returns:
            Aggregated nutrients following the standard schema.
        """
        total_nutrients = {}
        for key, schema in NUTRIENT_SCHEMA.items():
            if key in ["minerals", "vitamins"]:
                total_nutrients[key] = {}
                for sub_key, sub_schema in schema.items():
                    total_nutrients[key][sub_key] = {
                        "label": sub_schema["label"],
                        "quantity": self._aggregate_nutrient_value(food_items, key, sub_key),
                        "totalDaily": {"quantity": None},
                        "unit": sub_schema["unit"]
                    }
            else:
                total_nutrients[key] = {
                    "label": schema["label"],
                    "quantity": self._aggregate_nutrient_value(food_items, key),
                    "totalDaily": {"quantity": None},
                    "unit": schema["unit"]
                }
        return total_nutrients


    def generate_recipe(self, instructions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a detailed recipe based on high-level instructions and a candidate pool of food names.
        For each food candidate (a string), the Recipe Generator uses the External API to search for a food item,
        normalizes the returned item, and adds it to the recipe's foodItems list.
        It then aggregates the nutrient values across the food items.
        
        Args:
            instructions (Dict[str, Any]): Should include:
                - dishName: str
                - calorieTarget: int
                - macroGoals: Dict
                - foodCandidates: List[str] (e.g., ["apples", "eggs", "chicken", ...])
        
        Returns:
            Dict[str, Any]: A complete recipe with:
                - dishName
                - foodItems: List of normalized food items (without id, date, timestamp, calories)
                - totalNutrients: Aggregated nutrient values across foodItems
                - recipeInstructions: Culinary instructions.
        """
        dish_name = instructions.get("dishName", "Unnamed Dish")
        calorie_target = instructions.get("calorieTarget", 500)
        candidates = instructions.get("foodCandidates", [])
        food_items = []
        # For each candidate food name, use the external API to search and retrieve one detailed food item.
        for candidate in candidates:
            result = self.external_api.search_food("edamam", candidate, limit=1)
            if result.get("results"):
                food_item = result["results"][0]
                normalized = self._normalize_food_item(food_item)
                food_items.append(normalized)
        # If no candidates yielded results, fallback to an empty list.
        total_nutrients = self._aggregate_nutrients(food_items)
        recipe_instructions = f"Prepare {dish_name} by combining the selected ingredients in a balanced and tasty manner."
        recipe = {
            "dishName": dish_name,
            "calorieTarget": calorie_target,
            "foodItems": food_items,
            "totalNutrients": total_nutrients,
            "recipeInstructions": recipe_instructions
        }
        logger.info(f"Generated recipe for {dish_name} using candidates: {candidates}")
        return recipe

if __name__ == "__main__":
    rg = RecipeGenerator()
    instructions = {
        "dishName": "Test Dish",
        "calorieTarget": 500,
        "macroGoals": {
            "carb": {"dailyCalories": 845, "dailyGrams": 211, "dailyPercentage": 0.4},
            "fat": {"dailyCalories": 634, "dailyGrams": 70, "dailyPercentage": 0.3},
            "protein": {"dailyCalories": 634, "dailyGrams": 158, "dailyPercentage": 0.3}
        },
        "foodCandidates": ["apples", "eggs", "chicken", "ground beef", "milk"]
    }
    import json
    print(json.dumps(rg.generate_recipe(instructions), indent=4, sort_keys=True))
