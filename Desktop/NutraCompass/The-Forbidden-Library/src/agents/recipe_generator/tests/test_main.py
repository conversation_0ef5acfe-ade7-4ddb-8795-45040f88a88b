import json
import sys
from agents.recipe_generator.main import RecipeGenerator

def filter_nutrients(nutrients: dict) -> dict:
    """Return a dict with only ENERC_KCAL, PROCNT, FAT, and CHOCDF keys."""
    keys = ["ENERC_KCAL", "PROCNT", "FAT", "CHOCDF"]
    return {k: nutrients[k] for k in keys if k in nutrients}

def filter_food_item(food_item: dict) -> dict:
    """
    Return a copy of a food item without the 'measures' key and with its nutrients
    filtered to only include ENERC_KCAL, PROCNT, FAT, and CHOCDF.
    """
    filtered = dict(food_item)
    filtered.pop("measures", None)
    if "nutrients" in filtered:
        filtered["nutrients"] = filter_nutrients(filtered["nutrients"])
    return filtered

def test_generate_recipe_long():
    """Long version: Display full recipe summary as a pretty-printed JSON."""
    rg = RecipeGenerator()
    instructions = {
        "dishName": "Healthy Salad",
        "calorieTarget": 500,
        "macroGoals": {
            "carb": {"dailyCalories": 845, "dailyGrams": 211, "dailyPercentage": 0.4},
            "fat": {"dailyCalories": 634, "dailyGrams": 70, "dailyPercentage": 0.3},
            "protein": {"dailyCalories": 634, "dailyGrams": 158, "dailyPercentage": 0.3}
        },
        "foodCandidates": ["apples", "eggs", "chicken", "ground beef", "milk"]
    }
    recipe = rg.generate_recipe(instructions)
    print("\n=== Generated Recipe Summary (Long) ===")
    print(json.dumps(recipe, indent=4, sort_keys=True))
    return recipe

def test_generate_recipe_short():
    """Short version: Log a concise one-line summary for each food item in the recipe."""
    rg = RecipeGenerator()
    instructions = {
        "dishName": "Healthy Salad",
        "calorieTarget": 500,
        "macroGoals": {
            "carb": {"dailyCalories": 845, "dailyGrams": 211, "dailyPercentage": 0.4},
            "fat": {"dailyCalories": 634, "dailyGrams": 70, "dailyPercentage": 0.3},
            "protein": {"dailyCalories": 634, "dailyGrams": 158, "dailyPercentage": 0.3}
        },
        "foodCandidates": ["apples", "eggs", "chicken", "ground beef", "milk"]
    }
    recipe = rg.generate_recipe(instructions)
    print("\n=== Generated Recipe Summary (Short) ===")
    print(f"Dish Name: {recipe.get('dishName')}")
    target_calories = instructions.get("calorieTarget", "N/A")
    print(f"Target Nutrients -> Calories: {target_calories}")
    
    aggregated = recipe.get("totalNutrients", {})
    filtered_aggregated = filter_nutrients(aggregated)
    agg_calories = filtered_aggregated.get("ENERC_KCAL", {}).get("quantity", "N/A")
    agg_protein = filtered_aggregated.get("PROCNT", {}).get("quantity", "N/A")
    agg_fat = filtered_aggregated.get("FAT", {}).get("quantity", "N/A")
    agg_carbs = filtered_aggregated.get("CHOCDF", {}).get("quantity", "N/A")
    print(f"Aggregated Nutrients -> Calories: {agg_calories} | Protein: {agg_protein}g | Fat: {agg_fat}g | Carbs: {agg_carbs}g")
    
    print("\n--- Food Items Detail (Short) ---")
    for item in recipe.get("foodItems", []):
        filtered_item = filter_food_item(item)
        food_label = filtered_item.get("foodLabel", "Unknown")
        nutrients = filtered_item.get("nutrients", {})
        filtered_nuts = filter_nutrients(nutrients)
        calories = filtered_nuts.get("ENERC_KCAL", {}).get("quantity", "N/A")
        protein = filtered_nuts.get("PROCNT", {}).get("quantity", "N/A")
        fat = filtered_nuts.get("FAT", {}).get("quantity", "N/A")
        carbs = filtered_nuts.get("CHOCDF", {}).get("quantity", "N/A")
        print(f"{food_label}  --> Calories: {calories} | Protein: {protein}g | Fat: {fat}g | Carbs: {carbs}g")
    return recipe

def test_recipe_generator(display: str = "short"):
    if display.lower() == "long":
        test_generate_recipe_long()
    else:
        test_generate_recipe_short()

if __name__ == "__main__":
    # Use command-line argument to select display mode ("long" or "short").
    display_mode = "short"
    if len(sys.argv) > 1:
        display_mode = sys.argv[1]
    test_recipe_generator(display_mode)
