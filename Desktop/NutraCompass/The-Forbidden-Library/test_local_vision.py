#!/usr/bin/env python3
"""
Test local computer vision functionality
"""

import sys
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw
import io

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from agents.nutritional_analyzer.main import NutritionalAnalyzer

def create_test_food_image():
    """Create a test image with food-like colors"""
    # Create a 400x400 image
    img = Image.new('RGB', (400, 400), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw an apple-like red circle
    draw.ellipse([50, 50, 150, 150], fill='red', outline='darkred', width=3)
    
    # Draw a banana-like yellow shape
    draw.ellipse([200, 100, 350, 180], fill='yellow', outline='orange', width=2)
    
    # Draw some green broccoli-like shapes
    draw.ellipse([100, 250, 180, 330], fill='green', outline='darkgreen', width=2)
    draw.ellipse([120, 270, 160, 310], fill='darkgreen')
    
    return img

def test_local_vision():
    """Test the local computer vision functionality"""
    print("🧪 Testing Local Computer Vision")
    print("=" * 40)
    
    # Create test image
    print("📸 Creating test food image...")
    test_image = create_test_food_image()
    
    # Convert to bytes
    img_buffer = io.BytesIO()
    test_image.save(img_buffer, format='JPEG')
    image_bytes = img_buffer.getvalue()
    
    print(f"✅ Test image created ({len(image_bytes)} bytes)")
    
    # Initialize analyzer
    print("\n🤖 Initializing NutritionalAnalyzer...")
    analyzer = NutritionalAnalyzer(use_real_apis=False)
    
    # Test image analysis
    print("\n🔍 Analyzing test image...")
    result = analyzer.analyze_food_from_image(image_bytes)
    
    print("\n📊 Analysis Results:")
    print("=" * 30)
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
        return False
    
    if "image_analysis" in result:
        analysis = result["image_analysis"]
        print(f"🍽️ Detected foods: {analysis.get('detected_foods', 0)}")
        
        if "nutrition_results" in analysis:
            for i, nutrition in enumerate(analysis["nutrition_results"]):
                print(f"\n🥘 Food {i+1}: {nutrition.get('food_name', 'Unknown')}")
                print(f"   📏 Estimated portion: {nutrition.get('estimated_portion_g', 'N/A')}g")
                print(f"   🎯 Detection confidence: {nutrition.get('detection_confidence', 'N/A')}")
                
                if "nutrition" in nutrition:
                    nutr = nutrition["nutrition"]
                    print(f"   🔥 Calories: {nutr.get('calories', 'N/A')}")
                    print(f"   🥚 Protein: {nutr.get('protein_g', 'N/A')}g")
                    print(f"   🥑 Fat: {nutr.get('fat_g', 'N/A')}g")
                    print(f"   🍞 Carbs: {nutr.get('carbohydrates_g', 'N/A')}g")
        
        if "physical_analysis" in analysis:
            physical = analysis["physical_analysis"]
            print(f"\n📐 Physical Analysis:")
            print(f"   Pixel area: {physical.get('pixel_area', 'N/A')}")
            print(f"   Estimated volume: {physical.get('estimated_volume_m3', 'N/A')} m³")
            print(f"   Object coverage: {physical.get('object_coverage', 'N/A'):.2%}")
        
        if "combined_nutrition" in result and result["combined_nutrition"]:
            combined = result["combined_nutrition"]
            if "total_nutrition" in combined:
                total = combined["total_nutrition"]
                print(f"\n🍽️ Combined Nutrition:")
                print(f"   🔥 Total Calories: {total.get('calories', 'N/A')}")
                print(f"   🥚 Total Protein: {total.get('protein_g', 'N/A')}g")
                print(f"   🥑 Total Fat: {total.get('fat_g', 'N/A')}g")
                print(f"   🍞 Total Carbs: {total.get('carbohydrates_g', 'N/A')}g")
    
    print("\n✅ Local computer vision test completed!")
    return True

def test_simple_detection():
    """Test simple color-based detection"""
    print("\n🎨 Testing Simple Color Detection")
    print("=" * 40)
    
    analyzer = NutritionalAnalyzer(use_real_apis=False)
    
    # Create simple colored image
    img = Image.new('RGB', (200, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw a red apple
    draw.ellipse([50, 50, 150, 150], fill='red')
    
    # Convert to numpy array
    img_array = np.array(img)
    
    # Test detection
    detected = analyzer._detect_food_simple(img_array)
    
    print(f"🔍 Detected {len(detected)} food items:")
    for food in detected:
        print(f"   - {food['label']}: {food['confidence']:.2f} confidence")
    
    return len(detected) > 0

if __name__ == "__main__":
    print("🚀 Local Computer Vision Test Suite")
    print("=" * 50)
    
    try:
        # Test simple detection first
        simple_success = test_simple_detection()
        
        # Test full image analysis
        full_success = test_local_vision()
        
        if simple_success and full_success:
            print("\n🎉 All tests passed! Local computer vision is working.")
            print("\n📱 Your phone app should now work without Google Cloud Vision!")
            print("   Try taking a photo of food with your phone camera.")
        else:
            print("\n⚠️ Some tests failed. Check the output above.")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
