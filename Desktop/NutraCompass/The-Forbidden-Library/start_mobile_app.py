#!/usr/bin/env python3
"""
Mobile Food Analysis App Launcher
Starts the web server and provides instructions for phone access
"""

import subprocess
import socket
import sys
import time
import webbrowser
from pathlib import Path
import qrcode
from io import BytesIO
import base64

def get_local_ip():
    """Get the local IP address"""
    try:
        # Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "localhost"

def generate_qr_code(url):
    """Generate QR code for easy phone access"""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(url)
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to ASCII art for terminal display
        qr_ascii = qr.get_matrix()
        ascii_qr = ""
        for row in qr_ascii:
            line = ""
            for cell in row:
                line += "██" if cell else "  "
            ascii_qr += line + "\n"
        
        return ascii_qr
    except ImportError:
        return "QR code generation requires 'qrcode' package. Install with: pip install qrcode[pil]"

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = ['fastapi', 'uvicorn', 'pillow']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    print("🚀 NutraCompass Mobile Food Analysis App")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Get network information
    local_ip = get_local_ip()
    port = 8000
    local_url = f"http://{local_ip}:{port}"
    localhost_url = f"http://localhost:{port}"
    
    print(f"\n🌐 Starting server...")
    print(f"   Local access: {localhost_url}")
    print(f"   Phone access: {local_url}")
    
    # Generate QR code for phone access
    print(f"\n📱 QR Code for phone access:")
    print("=" * 30)
    qr_code = generate_qr_code(local_url)
    print(qr_code)
    print("=" * 30)
    
    print(f"\n📋 Instructions:")
    print(f"   1. Make sure your phone and computer are on the same WiFi network")
    print(f"   2. On your phone, scan the QR code above OR")
    print(f"   3. Open your phone's browser and go to: {local_url}")
    print(f"   4. Allow camera permissions when prompted")
    print(f"   5. Point camera at food and tap 'Analyze Food'")
    
    print(f"\n🔧 Troubleshooting:")
    print(f"   - If QR code doesn't work, manually type: {local_ip}:{port}")
    print(f"   - Make sure firewall allows connections on port {port}")
    print(f"   - Try different browsers if camera doesn't work")
    
    print(f"\n⚡ Starting FastAPI server...")
    print(f"   Press Ctrl+C to stop the server")
    print(f"   Server logs will appear below:")
    print("-" * 50)
    
    # Change to web app directory
    web_app_dir = Path(__file__).parent / "src" / "web_app"
    
    try:
        # Start the FastAPI server
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", str(port),
            "--reload"
        ], cwd=web_app_dir)
    except KeyboardInterrupt:
        print(f"\n\n👋 Server stopped. Thanks for using NutraCompass!")
    except FileNotFoundError:
        print(f"❌ Error: Could not find uvicorn. Install with: pip install uvicorn")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
