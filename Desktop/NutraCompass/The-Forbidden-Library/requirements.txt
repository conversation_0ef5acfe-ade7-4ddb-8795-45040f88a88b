# Core requirements (Updated for compatibility)
openai>=1.30.0
transformers>=4.28.1
torch>=2.0.0
torchvision>=0.15.0
scikit-learn>=1.0.2
fastapi>=0.110.0
pydantic>=2.0.0
sqlalchemy>=2.0.29
requests>=2.32.3
firebase-admin>=6.2.0
python-dotenv>=1.0.1
numpy>=1.24.4

# Vision/Imaging
pillow>=10.3.0
opencv-python-headless>=4.8.0
google-cloud-vision>=2.7.2
python-multipart>=0.0.9

# Web App Dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
qrcode[pil]>=7.4.2

# Real AI Food Recognition
timm>=0.9.0
torchvision>=0.15.0
ultralytics>=8.0.0
huggingface-hub>=0.16.0

# Testing
pytest>=7.4.4