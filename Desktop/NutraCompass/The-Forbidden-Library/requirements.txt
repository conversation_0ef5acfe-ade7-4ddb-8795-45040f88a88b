# Core requirements (Python 3.8 compatible)
openai==1.30.0
transformers==4.28.1
torch==1.13.1
torchvision==0.14.1
scikit-learn==1.0.2
fastapi==0.110.0
pydantic==1.10.13  # Downgraded for Python 3.8
sqlalchemy==2.0.29
requests==2.32.3
firebase-admin==6.2.0
python-dotenv==1.0.1
numpy==1.24.4

# Vision/Imaging
pillow==10.3.0
opencv-python-headless==********  # Last version with Python 3.8 wheels
google-cloud-vision==2.7.2  # Compatible version
python-multipart==0.0.9

# MMSegmentation
mmcv-full==1.7.1
mmsegmentation==0.30.0

# Testing
pytest==7.4.4  # Last Python 3.8-compatible version