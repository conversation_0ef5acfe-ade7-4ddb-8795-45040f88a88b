module.exports = {
  adminConfig:
    process.env.NUTRA_ADMIN_JSON || process.env.FIREBASE_ADMIN_CREDENTIALS_JSON,
  storageBucket:
    process.env.NUTRA_STORAGE_BUCKET || process.env.FIREBASE_STORAGE_BUCKET,
  // Edamam Food DB Config
  edamamConfig: {
    APP_ID: process.env.EDAMAM_APP_ID,
    APP_KEY: process.env.EDAMAM_APP_KEY,
    PARSER_BASE_URL: "https://api.edamam.com/api/food-database/v2/parser",
    NUTRIENTS_BASE_URL: "https://api.edamam.com/api/food-database/v2/nutrients",
  },
};
