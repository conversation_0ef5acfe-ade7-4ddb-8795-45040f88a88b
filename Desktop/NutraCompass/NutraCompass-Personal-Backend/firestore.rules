rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Rules for user documents
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Rules for various user-specific subcollections
       
       // Notification system security
      match /notifications/{document=**} {
        allow read: if request.auth.uid == userId;
        
        // Settings document specific rules
        match /settings {
          allow write: if 
            request.auth.uid == userId &&
            request.resource.data._version == resource.data._version + 1 &&
            request.resource.data._meta.timezone is string &&
            request.resource.data._meta.timezone.matches('^[A-Za-z_/]+$');
        }
        
        match /pushTokens/{tokenId} {
  				allow create: if 
    			request.auth.uid == userId &&
    			request.resource.data.token.matches("^ExponentPushToken\\[.+\\]$");
  
  				allow delete: if request.auth.uid == userId;
  				allow read: if request.auth.uid == userId;
  				allow update: if false; // No updates allowed once created
				}
        
        // Scheduled notifications
        match /scheduled_notifications/{notificationId} {
          allow create: if false; // Only backend creates these
          allow read, delete: if request.auth.uid == userId;
        }
        
        // Notification receipts
        match /notification_receipts/{receiptId} {
          allow read: if request.auth.uid == userId;
          allow write: if false; // Only backend writes receipts
        }
      }
      
      // Lock documents - no direct access
      match /operation_locks/{lockId} {
        allow read, write: if false;
      }
    
    	// Lock collection security
    	match /operation_locks/{lockId} {
      	allow read, write: if false; // Only accessible via Admin SDK
    	}
      
      match /settings/{settingsId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /customMealSections/{customMealSectionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /customMeals/{customMealId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /foodLogEntries/{foodLogEntryId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /activeNutritionalPrograms/{programId} {
        allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      }

      match /inactiveNutritionalPrograms/{programId} {
        allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      }

      match /friends/{friendId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /blocked/{blockedUserId} {
        allow read, write: if request.auth != null && request.auth.uid == blockedUserId;
      }

      match /outgoingRequests/{requestId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /incomingRequests/{requestId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

   // Global rules for blocked users collection group
		match /{path=**}/blocked/{document} {
  		allow read: if request.auth != null; // Make sure to add more specific conditions as needed
		}
  }
}
