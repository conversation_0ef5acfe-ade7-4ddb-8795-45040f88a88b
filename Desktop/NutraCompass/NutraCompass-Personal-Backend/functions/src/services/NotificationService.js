const admin = require("../config/firebase-admin");
const db = admin.firestore();
const Expo = require("expo-server-sdk");
const expo = new Expo.Expo();
const {
  MEAL_NOTIFICATION_TEMPLATES,
  STEP_GOAL_TEMPLATES,
} = require("../models/notificationTemplates");

// Lock configuration
const LOCK_TIMEOUT_MS = 30000;
const LOCK_RETRY_INTERVAL = 500;
const MAX_RETRY_ATTEMPTS = 5;

class NotificationService {
  static instance = null;

  constructor() {
    if (!NotificationService.instance) {
      NotificationService.instance = this;
      this.db = db;
    }
    return NotificationService.instance;
  }

  // ====================================================================
  // Distributed Locking Subsystem
  // ====================================================================

  async _acquireLock(userId) {
    const lockRef = this.db.collection("operation_locks").doc(userId);
    let attempts = 0;

    while (attempts < MAX_RETRY_ATTEMPTS) {
      try {
        await this.db.runTransaction(async (t) => {
          const doc = await t.get(lockRef);
          if (doc.exists) {
            const { expiresAt } = doc.data();
            const now = admin.firestore.Timestamp.now().toDate();
            if (expiresAt.toDate() > now) {
              throw new Error("Resource contention - lock active");
            }
          }
          t.set(lockRef, {
            acquiredAt: admin.firestore.FieldValue.serverTimestamp(),
            expiresAt: new Date(Date.now() + LOCK_TIMEOUT_MS),
          });
        });
        return;
      } catch (error) {
        attempts++;
        await new Promise((resolve) =>
          setTimeout(resolve, LOCK_RETRY_INTERVAL * Math.pow(2, attempts))
        );
      }
    }
    throw new Error(
      `Lock acquisition failed for ${userId} after ${attempts} retries`
    );
  }

  async _releaseLock(userId) {
    const lockRef = this.db.collection("operation_locks").doc(userId);
    await lockRef
      .delete()
      .catch((error) =>
        console.error(`Lock cleanup failed for ${userId}:`, error)
      );
  }

  async _withLock(userId, operation) {
    try {
      await this._acquireLock(userId);
      return await operation();
    } finally {
      await this._releaseLock(userId).catch((error) =>
        console.error("Non-critical lock release failure:", error)
      );
    }
  }

  // ====================================================================
  // Core Business Logic
  // ====================================================================

  async updateSettings(uid, settings, expoPushToken) {
    return this._withLock(uid, async () => {
      const userRef = this.db.collection("users").doc(uid);
      const settingsRef = userRef.collection("notifications").doc("settings");

      await this.db.runTransaction(async (t) => {
        if (expoPushToken) {
          t.update(userRef, {
            pushTokens: admin.firestore.FieldValue.arrayUnion(expoPushToken),
          });
        }
        t.set(
          settingsRef,
          {
            ...settings,
            _version: admin.firestore.FieldValue.increment(1),
          },
          { merge: true }
        );
      });
      return this.scheduleNotifications(uid);
    });
  }

  async scheduleNotifications(uid) {
    return this._withLock(uid, async () => {
      const [userDoc, settingsDoc] = await Promise.all([
        this.db.collection("users").doc(uid).get(),
        this.db
          .collection("users")
          .doc(uid)
          .collection("notifications")
          .doc("settings")
          .get(),
      ]);

      const userData = userDoc.data() || {};
      const settings = settingsDoc.data() || {};
      const pushTokens = this._sanitizePushTokens(userData.pushTokens);
      const userName = userData.profile?.firstName || "User";

      const messages = this._generateNotifications(
        pushTokens,
        settings,
        userName
      );
      const receipts = await this._deliverNotifications(messages);
      await this._persistNotificationIds(uid, receipts);
      return receipts;
    });
  }

  async cancelMealReminders(userId, mealIds) {
    return this._withLock(userId, async () => {
      if (mealIds.length > 450) {
        throw new Error("Too many meal IDs for single batch operation");
      }

      const batch = this.db.batch();
      const settingsRef = this.db
        .collection("users")
        .doc(userId)
        .collection("notifications")
        .doc("settings");

      mealIds.forEach((mealId) => {
        batch.update(settingsRef, {
          [`mealReminders.${mealId}`]: admin.firestore.FieldValue.delete(),
        });
      });

      const notifications = await this.db
        .collection("users")
        .doc(userId)
        .collection("scheduled_notifications")
        .where("mealId", "in", mealIds)
        .get();

      notifications.forEach((doc) => batch.delete(doc.ref));
      await batch.commit();
      return { success: true, canceled: notifications.size };
    });
  }

  async rescheduleUserNotifications(userId) {
    return this._withLock(userId, async () => {
      // Clear local notification records
      await this._clearNotificationRecords(userId);

      // Clean up expired receipts (24h+ old)
      await this._cleanupNotificationRecords(userId);

      // Regenerate notifications with current templates
      return this.scheduleNotifications(userId);
    });
  }

  // ====================================================================
  // Notification Hygiene
  // ====================================================================

  async _cleanupNotificationRecords(userId) {
    const receiptsRef = db.collection(`users/${userId}/notification_receipts`);
    const expiredReceipts = await receiptsRef
      .where("createdAt", "<", new Date(Date.now() - 24 * 60 * 60 * 1000))
      .get();

    const batch = db.batch();
    expiredReceipts.forEach((doc) => batch.delete(doc.ref));
    await batch.commit();
  }

  async _clearNotificationRecords(userId) {
    const activeRef = this.db
      .collection("users")
      .doc(userId)
      .collection("scheduled_notifications")
      .doc("active");
    await activeRef.delete();
  }

  // ====================================================================
  // Notification Pipeline
  // ====================================================================

  _generateNotifications(tokens, settings, userName) {
    const messages = [];
    if (settings.mealReminders) {
      Object.entries(settings.mealReminders).forEach(([mealId, config]) => {
        if (config.enabled && config.time) {
          const template = this._getVersionedTemplate(
            settings._meta?.mealTemplateIndex,
            MEAL_NOTIFICATION_TEMPLATES
          );
          messages.push(
            ...this._buildNotificationBatch(
              tokens,
              template,
              config.time,
              userName,
              config.mealName,
              mealId
            )
          );
        }
      });
    }
    if (settings.stepGoal?.enabled) {
      const template = this._getVersionedTemplate(
        settings._meta?.stepTemplateIndex,
        STEP_GOAL_TEMPLATES
      );
      messages.push(
        ...this._buildNotificationBatch(
          tokens,
          template,
          settings.stepGoal.time,
          userName
        )
      );
    }
    return messages;
  }

  _buildNotificationBatch(tokens, template, time, userName, mealName, mealId) {
    const notifyTime = new Date(time);
    return tokens.map((token) => ({
      to: token,
      title: template.title
        .replace(/{mealName}/g, mealName || "")
        .replace(/{userName}/g, userName),
      body: template.body
        .replace(/{mealName}/g, mealName || "")
        .replace(/{userName}/g, userName),
      sound: "default",
      trigger: {
        hour: notifyTime.getHours(),
        minute: notifyTime.getMinutes(),
        repeats: true,
        timezone: "America/Los_Angeles",
      },
      metadata: {
        mealId,
        userId: userName,
        scheduledAt: admin.firestore.FieldValue.serverTimestamp(),
      },
    }));
  }

  // ====================================================================
  // Utilities
  // ====================================================================

  async _persistNotificationIds(uid, receipts) {
    const receiptsRef = db.collection(`users/${uid}/notification_receipts`);
    const batch = db.batch();
    receipts.forEach((receipt) => {
      if (receipt?.id && typeof receipt.id === "string") {
        const docRef = receiptsRef.doc();
        batch.set(docRef, {
          receiptId: receipt.id,
          status: receipt.status || "unknown",
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
    });
    await batch.commit();
  }

  _sanitizePushTokens(tokens) {
    return (Array.isArray(tokens) ? tokens : []).filter((token) =>
      Expo.Expo.isExpoPushToken(token)
    );
  }

  _getVersionedTemplate(index, templates) {
    const safeIndex =
      typeof index === "number"
        ? index % templates.length
        : Math.floor(Math.random() * templates.length);
    return templates[safeIndex];
  }
}

module.exports = new NotificationService();
