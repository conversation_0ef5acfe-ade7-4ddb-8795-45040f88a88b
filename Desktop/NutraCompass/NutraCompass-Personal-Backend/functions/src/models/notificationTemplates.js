// Notification templates
const MEAL_NOTIFICATION_TEMPLATES = [
  {
    title: "🍽️ Time to log {mealName}",
    body: "Hey {userName}, record your meal!",
  },
  { title: "Ready for {mealName}? 😋", body: "Log your meal, {userName}!" },
  {
    title: "🍴 {mealName} Time!",
    body: "{userName}, your {mealName} is waiting to be logged!",
  },
  {
    title: "Hungry? Let's log {mealName}!",
    body: "Fuel your day right, {userName}! Track your {mealName} now 🥗",
  },
  {
    title: "📱 Time to Snap & Log {mealName}",
    body: "Don't forget to record your {mealName}, {userName}! 📸",
  },
  {
    title: "🌟 {mealName} Reminder",
    body: "Consistency is key, {userName}! Log your {mealName} for progress",
  },
  {
    title: "🥄 Did you eat {mealName}?",
    body: "Track it now, {userName}! Small entries lead to big results 💪",
  },
  {
    title: "🔔 {mealName} Alert!",
    body: "Hey {userName}, don't let this meal go unrecorded!",
  },
  {
    title: "📝 {mealName} Journal Time",
    body: "Your future self will thank you for tracking, {userName}!",
  },
  {
    title: "⏰ Time Check: {mealName}",
    body: "Quick reminder to log your meal, {userName}! ⏳",
  },
  {
    title: "🍔 Variety Matters!",
    body: "How was your {mealName}, {userName}? Log it for nutritional balance",
  },
  {
    title: "🧠 Mindful Eating Reminder",
    body: "{userName}, take a moment to log your {mealName} consciously",
  },
];

const STEP_GOAL_TEMPLATES = [
  {
    title: "🚶♂️ Step Goal Reminder",
    body: "Hey {userName}, time to hit your daily step target!",
  },
  {
    title: "Walking Time! 🏃♀️",
    body: "You're close to your step goal, {userName}! Keep moving!",
  },
  {
    title: "👟 Step It Up!",
    body: "{userName}, your daily step target is calling! 🚶♂️",
  },
  {
    title: "🌞 Midday Movement Check",
    body: "How's your step progress, {userName}? Time to stretch those legs!",
  },
  {
    title: "🎯 Step Goal Ahead!",
    body: "You've got this, {userName}! Crush your daily step target 💥",
  },
  {
    title: "🚦 Step Counter Active",
    body: "Hey {userName}, keep moving! Every step counts towards your goal",
  },
  {
    title: "🏅 Step Champion Reminder",
    body: "{userName}, your step throne awaits! Claim it today 👑",
  },
  {
    title: "🌆 Evening Step Boost",
    body: "Sunset stroll time, {userName}! Let's hit those step numbers 🌇",
  },
  {
    title: "📈 Progress Checkpoint",
    body: "{userName}, you're X steps away from your goal! Keep going 🚶♀️",
  },
  {
    title: "🎵 Walking Rhythm Alert",
    body: "Put on your favorite tunes and step it out, {userName}! 🎧",
  },
  {
    title: "🌱 Fresh Air + Steps = 🌟",
    body: "Combine nature with progress, {userName}! Time for a walk",
  },
  {
    title: "🏃♀️ Step Sprint Opportunity",
    body: "Quick burst of steps could make all the difference, {userName}!",
  },
];

module.exports = {
  MEAL_NOTIFICATION_TEMPLATES,
  STEP_GOAL_TEMPLATES,
};
