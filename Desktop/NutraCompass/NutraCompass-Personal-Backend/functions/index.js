const functions = require("firebase-functions/v1");
const crypto = require("crypto");
const admin = require("./src/config/firebase-admin");
const NotificationService = require("./src/services/NotificationService");
const {
  MEAL_NOTIFICATION_TEMPLATES,
  STEP_GOAL_TEMPLATES,
} = require("./src/models/notificationTemplates.js");

const db = admin.firestore();

// =============================================
// Global Configuration
// =============================================

// Global constants for scheduler configuration
const TIME_ZONE = "America/Los_Angeles"; // Production timezone
const TEMPLATE_ROTATION_HOUR = 0; // Midnight execution
const NOTIFICATION_RETENTION_DAYS = 30; // Compliance with data retention policies

// =============================================
// Core Template Rotation Engine
// =============================================

/**
 * Daily Notification Template Rotator
 *
 * Purpose: Maintains user engagement by cycling through notification content variants
 * Trigger: Daily execution via Cloud Scheduler (00:00 PST)
 *
 * Key Operations:
 * 1. Batch retrieval of users with active meal reminders
 * 2. Deterministic template index calculation per user
 * 3. Atomic Firestore updates for template positions
 * 4. Notification rescheduling through dedicated service
 */
exports.rotateNotificationTemplates = functions.pubsub
  .schedule(`0 ${TEMPLATE_ROTATION_HOUR} * * *`) // Maintain original PST midnight schedule
  .timeZone(TIME_ZONE)
  .onRun(async (context) => {
    try {
      // Retrieve active notification configurations
      const usersSnapshot = await db
        .collectionGroup("notifications")
        .where("mealReminders", "!=", null)
        .limit(500)
        .get();

      if (usersSnapshot.empty) {
        console.log("No users with active meal reminders");
        return { status: "NO_OPERATION" };
      }

      // Process user batches with template rotation
      await executeRotation(usersSnapshot.docs);

      return {
        success: true,
        processedCount: usersSnapshot.size,
        executionTime: context.timestamp,
      };
    } catch (error) {
      console.error("Template rotation failed:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Scheduled rotation failure",
        { error: error.message }
      );
    }
  });

// =============================================
// Rotation Execution Logic
// =============================================

/**
 * Executes template rotation for user batch
 * @param {QueryDocumentSnapshot[]} settingsDocs - Notification settings documents
 */
async function executeRotation(settingsDocs) {
  const batch = db.batch();
  const rotationTimestamp = admin.firestore.FieldValue.serverTimestamp();

  // Phase 1: Template index calculation
  const userContexts = await Promise.all(
    settingsDocs.map(async (doc) => {
      const userRef = doc.ref.parent.parent;
      const userDoc = await userRef.get();

      // Robust timezone access
      const userData = userDoc.data() || {};
      const userTz =
        (userData.location && userData.location.timezone) || TIME_ZONE;

      // Calculate local calendar day for user
      const localDate = new Date(
        new Date().toLocaleString("en-US", { timeZone: userTz })
      );

      return {
        settingsRef: doc.ref,
        userId: userRef.id,
        dayOfYear: getDayOfYear(localDate),
        timezone: userTz,
      };
    })
  );

  // Phase 2: Batch index updates
  userContexts.forEach(({ settingsRef, userId, dayOfYear, timezone }) => {
    const indices = calculateRotationIndices(userId, dayOfYear, timezone);

    batch.update(settingsRef, {
      "_meta.mealTemplateIndex": indices.mealIndex,
      "_meta.stepTemplateIndex": indices.stepIndex,
      "_meta.lastRotation": rotationTimestamp,
      _version: admin.firestore.FieldValue.increment(1),
    });
  });

  // Atomic commit of all index updates
  await batch.commit();

  // Phase 3: Notification rescheduling
  await Promise.all(
    userContexts.map(async ({ userId }) => {
      try {
        await NotificationService.rescheduleUserNotifications(userId);
        console.log(`Successfully updated notifications for ${userId}`);
      } catch (error) {
        console.error(`Rescheduling failure for ${userId}:`, error);
      }
    })
  );
}

// =============================================
// Calculation Utilities
// =============================================

/**
 * Generates template positions using stable hashing
 * @param {string} userId - Target user ID
 * @param {number} dayOfYear - Current day in local timezone
 * @param {string} timezone - User's IANA timezone
 */
function calculateRotationIndices(userId, dayOfYear, timezone) {
  // Create unique seed combining user context and temporal factors
  const baseSeed = `${userId}-${timezone}-${dayOfYear}`;

  return {
    mealIndex: generateStableIndex(
      baseSeed,
      "meal",
      MEAL_NOTIFICATION_TEMPLATES.length
    ),
    stepIndex: generateStableIndex(
      baseSeed,
      "step",
      STEP_GOAL_TEMPLATES.length
    ),
  };
}

/**
 * Creates deterministic index from seed input
 * @param {string} seedBase - Root identification string
 * @param {string} templateType - Template category identifier
 * @param {number} templateCount - Available template variants
 */
function generateStableIndex(seedBase, templateType, templateCount) {
  if (templateCount === 0)
    throw new Error(`Missing templates for ${templateType}`);

  const compositeSeed = `${seedBase}-${templateType}`;
  const hashBuffer = crypto.createHash("sha256").update(compositeSeed).digest();
  return hashBuffer.readUInt32BE(0) % templateCount;
}

/**
 * Calculates day of year in local time context
 * @param {Date} date - Localized date instance
 */
function getDayOfYear(date) {
  const start = new Date(Date.UTC(date.getFullYear(), 0, 0));
  const diff = date - start;
  return Math.floor(diff / 86400000); // 86400000 = milliseconds per day
}

// =============================================
// Firestore Triggers
// =============================================

/**
 * Meal Configuration Synchronization
 *
 * Purpose: Maintains parity between meal diary entries and notifications
 * Trigger: Firestore writes to customMealSections collection
 *
 * Key Functions:
 * - Adds/updates meal reminder configurations
 * - Removes orphaned meal references
 * - Maintains version tracking for conflict resolution
 */
exports.syncMealNotifications = functions.firestore
  .document("users/{userId}/customMealSections/{mealId}")
  .onWrite(async (change, context) => {
    const { userId } = context.params;
    const notificationsRef = db.collection(`users/${userId}/notifications`);

    try {
      // Atomic update operation
      await db.runTransaction(async (transaction) => {
        const settingsDoc = await transaction.get(
          notificationsRef.doc("settings")
        );

        if (!settingsDoc.exists) {
          throw new Error("Notification settings not found for user");
        }

        const currentMeals = settingsDoc.data().mealReminders || [];
        let updatedMeals = [...currentMeals];

        if (change.after.exists) {
          // Add/update meal configuration
          const mealData = change.after.data();
          const existingIndex = currentMeals.findIndex(
            (m) => m.mealId === context.params.mealId
          );

          const mealConfig = {
            mealId: context.params.mealId,
            scheduledTime: mealData.preferredTime,
            templateOverride: mealData.customMessage,
            _syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          };

          if (existingIndex > -1) {
            updatedMeals[existingIndex] = mealConfig;
          } else {
            updatedMeals.push(mealConfig);
          }
        } else {
          // Remove deleted meal
          const filtered = currentMeals.filter(
            (m) => m.mealId !== context.params.mealId
          );
          updatedMeals = filtered;
        }

        transaction.update(notificationsRef.doc("settings"), {
          mealReminders: updatedMeals,
          _version: admin.firestore.FieldValue.increment(1),
          _updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
      });

      console.log(
        `Successfully synced meal ${context.params.mealId} for user ${userId}`
      );
      return { success: true };
    } catch (error) {
      console.error(`Meal sync failed for ${userId}:`, error);
      throw new functions.https.HttpsError(
        "aborted",
        "Failed to synchronize meal notifications",
        { userId, mealId: context.params.mealId }
      );
    }
  });

/**
 * Notification Data Hygiene Maintenance
 *
 * Purpose: Enforces data retention policies
 * Trigger: Daily execution via Cloud Scheduler
 *
 * Key Functions:
 * - Removes notifications older than retention period
 * - Maintains system performance
 * - Complies with data protection regulations
 */
exports.cleanupNotifications = functions.pubsub
  .schedule("every 24 hours")
  .timeZone(TIME_ZONE)
  .onRun(async (context) => {
    const cutoffDate = new Date(
      Date.now() - NOTIFICATION_RETENTION_DAYS * 86400000
    ); // Use retention constant

    try {
      let deletedCount = 0;
      let batch = db.batch();

      const expiredNotifications = await db
        .collectionGroup("notifications")
        .where("timestamp", "<=", cutoffDate)
        .limit(500)
        .get();

      if (expiredNotifications.empty) {
        console.log("No expired notifications found");
        return { status: "NO_OPERATION" };
      }

      expiredNotifications.forEach((doc) => {
        batch.delete(doc.ref);
        deletedCount++;
      });

      await batch.commit();

      console.log(`Cleaned up ${deletedCount} expired notifications`);
      return {
        cleanedCount: deletedCount,
        retentionPeriod: `${NOTIFICATION_RETENTION_DAYS} days`,
        executionTime: admin.firestore.Timestamp.now(),
      };
    } catch (error) {
      console.error("Notification cleanup failed:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Failed to execute notification cleanup",
        {
          retentionDays: NOTIFICATION_RETENTION_DAYS,
          cutoffDate: cutoffDate.toISOString(),
          error: error.message,
        }
      );
    }
  });

// =============================================
// Required Setup & Configuration
// =============================================

/*
DEPLOYMENT REQUIREMENTS (Heroku Specific):

1. Environment Variables:
   - FIREBASE_SERVICE_ACCOUNT: JSON-stringified service account credentials
   - FIREBASE_DATABASE_URL: Firebase project database URL
   - GOOGLE_APPLICATION_CREDENTIALS: Path to service account file

2. Firebase Project Setup:
   - Enable Firestore, Cloud Scheduler, and Pub/Sub APIs
   - Create necessary Firestore indexes:
     * Collection Group index on 'notifications'
     * Composite index on 'mealReminders' + timestamp

3. IAM Permissions:
   - Cloud Scheduler Service Agent role
   - Pub/Sub Publisher role
   - Firestore Service Agent role

4. Heroku Add-ons:
   - Redis for distributed locking (if scaling beyond single dyno)
   - Logging add-on for function diagnostics

5. Scheduler Configuration:
   - Cloud Scheduler jobs must match function timezone
   - Retry policies configured for 3 attempts

6. Runtime Configuration:
   - Node.js 16+ runtime
   - Firebase CLI tools in buildpack
   - Appropriate dyno sizing (minimum Standard-2X)
*/
