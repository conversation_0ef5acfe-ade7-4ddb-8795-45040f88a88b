{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "build": "echo 'No build needed' && exit 0", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "predeploy": "cp ../src/config/firebase-admin.js ./src/config/ && cp ../src/services/NotificationService.js ./src/services/ && cp ../src/models/notificationTemplates.js ./src/models/ && eslint .", "postdeploy": "rm -rf ./src/config/firebase-admin.js ./src/services/NotificationService.js ./src/models/notificationTemplates.js"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"@google-cloud/tasks": "^5.5.0", "crypto": "^1.0.1", "dotenv": "^16.4.7", "expo-server-sdk": "^3.14.0", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.4.1"}, "private": true}