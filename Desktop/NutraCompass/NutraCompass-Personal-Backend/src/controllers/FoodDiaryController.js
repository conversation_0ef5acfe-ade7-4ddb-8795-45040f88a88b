const FoodDiaryService = require("../services/FoodDiaryService");

/**
 * Controller for handling requests related to the food diary functionalities.
 */
class FoodDiaryController {
  /**
   * Retrieves all meal sections for a specified user.
   * @param {Object} req - The HTTP request object.
   * @param {Object} res - The HTTP response object.
   */
  static async getMealSections(req, res) {
    try {
      const { userId } = req.params;
      const sections = await FoodDiaryService.getMealSections(userId);
      res.json(sections);
    } catch (error) {
      console.error("Error loading meal sections:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Updates the names of the meal sections for a specified user.
   * @param {Object} req - The HTTP request object
   * @param {Object} res - The HTTP response object
   */
  static async updateMealSectionNames(req, res) {
    try {
      const { userId } = req.params;

      // Validate request body format
      if (!Array.isArray(req.body)) {
        return res.status(400).json({
          message:
            "Invalid request format. Expected array of { mealSectionId, newName }",
        });
      }

      // Update and get fresh data
      const updatedSections = await FoodDiaryService.updateMealSectionNames(
        userId,
        req.body
      );

      // Return complete updated list
      res.status(200).json({
        success: true,
        mealSections: updatedSections,
        message: "Meal sections updated successfully",
      });
    } catch (error) {
      console.error("Error updating meal section names:", error);
      const statusCode = error.message.includes("Invalid") ? 400 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Retrieves the most recent food history for a specified user, limited to 20 items.
   * @param {Object} req - The HTTP request object.
   * @param {Object} res - The HTTP response object.
   */
  static async getFoodHistory(req, res) {
    try {
      const { userId } = req.params;
      const history = await FoodDiaryService.fetchUserFoodHistory(userId);
      res.json(history);
    } catch (error) {
      console.error("Error fetching food history:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Retrieves all food entries for a specified user, optionally filtered by meal type.
   * @param {Object} req - The HTTP request object.
   * @param {Object} res - The HTTP response object.
   */
  static async getFoodEntries(req, res) {
    try {
      const { userId } = req.params;
      const entries = await FoodDiaryService.getFoodEntries(
        userId,
        req.query.mealType
      );
      res.json(entries);
    } catch (error) {
      console.error("Error retrieving food entries:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Saves a complete meal to the food log for a specified user.
   * @param {Object} req - The HTTP request object.
   * @param {Object} res - The HTTP response object.
   */
  static async saveMealToFoodLog(req, res) {
    try {
      const { userId } = req.params;
      const { mealType, selectedDate, mealItems, mealId } = req.body;
      const result = await FoodDiaryService.saveMeal(
        userId,
        mealType,
        selectedDate,
        mealItems,
        mealId
      );
      res.status(201).json({ message: "Meal saved successfully", result });
    } catch (error) {
      console.error("Error saving meal to food log:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Saves or updates a single food entry in the food log for a specified user.
   * @param {Object} req - The HTTP request object.
   * @param {Object} res - The HTTP response object.
   */
  static async saveOrUpdateFoodEntry(req, res) {
    const { userId, entryId } = req.params;
    const { mealType, updatedEntry, selectedDate } = req.body;

    try {
      const result = await FoodDiaryService.saveOrUpdateSingleFoodItemToFoodLog(
        userId,
        mealType,
        entryId,
        updatedEntry,
        selectedDate
      );
      res.json({ message: "Operation successful", result });
    } catch (error) {
      console.error("Error saving or updating food entry:", error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Deletes a specific food entry from the food log for a specified user.
   * @param {Object} req - The HTTP request object.
   * @param {Object} res - The HTTP response object.
   */
  static async deleteFoodEntry(req, res) {
    try {
      const { userId, entryId } = req.params;
      await FoodDiaryService.deleteFoodEntry(userId, entryId);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting food entry:", error);
      res.status(500).json({ message: error.message });
    }
  }

  static async addQuickFoodEntry(req, res) {
    const { userId } = req.params;
    const { mealType, entryDetails, selectedDate } = req.body;

    try {
      const result = await FoodDiaryService.addQuickFoodEntry(
        userId,
        mealType,
        entryDetails,
        selectedDate
      );
      res.status(200).json(result);
    } catch (error) {
      console.error("Error in adding quick food entry:", error);
      res.status(500).json({
        message: "Failed to add quick food entry",
        error: error.message,
      });
    }
  }

  /**
   * Deletes all entries for a specified meal section on a given date.
   * This method handles the HTTP DELETE request, extracting parameters
   * and invoking the service method to execute the deletion.
   *
   * @param {object} req - The HTTP request object, containing parameters.
   * @param {object} res - The HTTP response object used to send responses.
   */
  static async deleteMealSectionEntries(req, res) {
    const { userId, mealSectionId, date } = req.params; // Extract parameters from the request

    try {
      // Call service function to delete entries by meal section and date
      await FoodDiaryService.deleteAllEntriesByMealSectionAndDate(
        userId,
        mealSectionId,
        date
      );
      // Send a no content response indicating successful deletion
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting meal section entries:", error);
      // Respond with server error status and error message
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Handles requests to copy food diary entries between meal sections on different dates.
   * Validates input, processes the copying operation, and returns appropriate HTTP responses.
   *
   * @param {object} req - HTTP request object with user and payload.
   * @param {object} res - HTTP response object for sending responses.
   */
  static async copyEntriesToAnotherDate(req, res) {
    const { userId } = req.params;
    const { entries, destinationMealType, destinationDate } = req.body;

    if (!entries || entries.length === 0) {
      return res.status(400).json({ message: "No entries provided to copy." });
    }

    try {
      const result = await FoodDiaryService.copyEntries(
        userId,
        entries,
        destinationMealType,
        destinationDate
      );

      if (result.count === 0) {
        return res.status(404).json({ message: "No entries found to copy." });
      }

      res.status(201).json(result);
    } catch (error) {
      console.error("Failed to copy entries:", error);
      res.status(500).json({ message: "Error processing your request." });
    }
  }

  static async addOrUpdateWaterEntry(req, res) {
    try {
      const { userId } = req.params;
      const { volume, unit, date, id } = req.body; // Include 'id' to check if updating

      // Pass 'id' to the service method; if 'id' is undefined, a new entry will be created
      const result = await FoodDiaryService.addOrUpdateWaterEntry(
        userId,
        volume,
        unit,
        date,
        id
      );
      res.status(201).json(result);
    } catch (error) {
      console.error("Error logging water consumption:", error);
      res.status(500).json({ message: error.message });
    }
  }
}

module.exports = FoodDiaryController;
