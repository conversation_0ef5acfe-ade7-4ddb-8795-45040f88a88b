const FoodMenuService = require("../services/FoodMenuService");
const multer = require("multer");
const upload = multer({ storage: multer.memoryStorage() });

class FoodMenuController {
  /**
   * Retrieves all custom meals associated with a user. Validates user ID and handles errors gracefully.
   *
   * @param {Object} req - The request object containing the user identifier.
   * @param {Object} res - The response object used to send back data to the client.
   */
  static async getCustomMeals(req, res) {
    try {
      const { userId } = req.params;
      if (!userId) {
        return res.status(400).json({ message: "User ID is required" });
      }
      const meals = await FoodMenuService.getCustomMeals(userId);
      res.json(meals);
    } catch (error) {
      console.error("Error loading custom meals:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Saves or updates a custom meal. Handles the meal data and optional image upload.
   * @param {Object} req - The HTTP request object containing the custom meal data and parameters.
   * @param {Object} res - The HTTP response object used for sending back data or errors.
   * This method expects the custom meal data in JSON format and an optional image as file input.
   * It utilizes `multer` for handling memory storage of the uploaded image.
   */
  static async saveCustomMeal(req, res) {
    try {
      const { userId } = req.params;
      const customMeal = req.body;

      // Parse mealItems if it exists and is a string
      if (customMeal.mealItems && typeof customMeal.mealItems === "string") {
        customMeal.mealItems = JSON.parse(customMeal.mealItems);
      }

      // Parse nutrients if it exists and is a string
      if (customMeal.nutrients && typeof customMeal.nutrients === "string") {
        customMeal.nutrients = JSON.parse(customMeal.nutrients);
      }

      const imageFile = req.file; // Assumes multer middleware is used to handle file upload

      if (!userId || !customMeal) {
        return res.status(400).json({ message: "Missing required fields" });
      }

      // Delegate to the service layer to save or update the custom meal, passing along the image file if provided.
      const result = await FoodMenuService.saveCustomMeal(
        userId,
        customMeal,
        imageFile
      );
      res.status(201).json(result);
    } catch (error) {
      console.error("Error saving custom meal:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Deletes a custom meal for a user. Validates necessary identifiers before performing the deletion.
   *
   * @param {Object} req - The request object containing user and meal identifiers.
   * @param {Object} res - The response object for sending back the HTTP status.
   */
  static async deleteCustomMeal(req, res) {
    try {
      const { userId, mealId } = req.params;
      if (!userId || !mealId) {
        return res.status(400).json({ message: "Missing required fields" });
      }
      await FoodMenuService.deleteCustomMeal(userId, mealId);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting custom meal:", error);
      res.status(500).json({ message: error.message });
    }
  }
}

module.exports = FoodMenuController;
