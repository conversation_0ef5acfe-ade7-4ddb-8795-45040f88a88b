const AuthService = require("../services/AuthService");

class AuthController {
  /**
   * Verifies the ID token provided by the client to authenticate the user session.
   * This method checks the validity of an ID token, ensuring that the user is currently authenticated.
   *
   * @param {Object} req - The HTTP request object, expected to contain an 'idToken' in the body.
   * @param {Object} res - The HTTP response object used to return the user's UID or an error message.
   */
  static async verifyUserSession(req, res) {
    try {
      const { idToken } = req.body;
      const decodedToken = await AuthService.verifyToken(idToken);
      res.json({ uid: decodedToken.uid });
    } catch (error) {
      res.status(401).json({ error: error.message });
    }
  }

  /**
   * Registers a new user with provided email, password, and default settings. On successful registration,
   * a Firebase ID token and email verification link are sent for immediate authentication.
   *
   * @param {Object} req - The HTTP request object containing email, password, and defaultSettings.
   * @param {Object} res - The HTTP response object to return the user ID, token, and a success message.
   */
  static async registerUser(req, res) {
    try {
      const { email, password, defaultSettings } = req.body;
      const result = await AuthService.register(
        email,
        password,
        defaultSettings
      );

      res.status(201).json({
        userId: result.userId,
        idToken: result.idToken,
        message: "User registered successfully. Please verify your email.",
      });
    } catch (error) {
      res
        .status(500)
        .json({ message: "Registration failed", error: error.message });
    }
  }

  /**
   * Handles the HTTP POST request to register multiple users simultaneously.
   * Extracts an array of user data from the request body and delegates the registration process to the AuthService.
   * Returns a JSON response with the results of the registration attempts for each user.
   *
   * @param {Object} req - The HTTP request object, expecting an array of user data under `req.body.users`.
   * @param {Object} res - The HTTP response object used to send back the results or errors.
   */
  static async registerMultipleUsers(req, res) {
    const { users } = req.body; // Expecting an array of user data from the request body.

    // Validate that we received an array of users.
    if (!users || !Array.isArray(users)) {
      return res
        .status(400)
        .json({ message: "Invalid user data, expected an array of users." });
    }

    try {
      const results = await AuthService.registerMultipleUsers(users); // Process bulk registration.
      res.status(200).json(results); // Send successful results.
    } catch (error) {
      console.error("Failed to register users:", error); // Log any errors encountered.
      res.status(500).json({ message: "Failed to register users" }); // Respond with an error status.
    }
  }

  /**
   * Handles password reset or change requests by generating a password reset link.
   * Sends the reset link to the user's email address.
   *
   * @param {Object} req - The HTTP request object containing the user's email.
   * @param {Object} res - The HTTP response object to send back a message.
   */
  static async resetPassword(req, res) {
    try {
      const { email, firstName } = req.body;

      if (!email) {
        return res.status(400).json({ message: "Email is required" });
      }

      await AuthService.resetPassword(email, firstName);

      res.status(200).json({
        message:
          "Password reset email sent successfully. Please check your inbox.",
      });
    } catch (error) {
      console.error("Error in password reset:", error);
      res.status(500).json({
        message: "Failed to process password reset.",
        error: error.message,
      });
    }
  }

  /**
   * Handles the deletion of a user's account and all associated data.
   * This method ensures that both authentication records and Firestore data are deleted.
   *
   * @param {Object} req - The HTTP request object, expected to contain `userId` in the authenticated session.
   * @param {Object} res - The HTTP response object to return a success or error message.
   */
  static async deleteAccount(req, res) {
    try {
      const { userId, firstName } = req.body;

      if (!userId) {
        return res.status(400).json({ message: "User ID is required" });
      }

      await AuthService.deleteUserAccount(userId, firstName);

      res.status(200).json({ message: "Account deleted successfully." });
    } catch (error) {
      console.error("Error deleting account:", error);
      res
        .status(500)
        .json({ message: "Failed to delete account.", error: error.message });
    }
  }

  /**
   * Checks if an email already exists in Firebase Authentication.
   * @param {Object} req - The HTTP request object containing the email in the body.
   * @param {Object} res - The HTTP response object used to send back the existence status.
   */
  static async checkEmailExists(req, res) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ error: "Email is required." });
      }

      const exists = await AuthService.checkEmailExists(email);

      res.status(200).json({ exists });
    } catch (error) {
      console.error("Error checking email existence:", error);
      res.status(500).json({ error: "Failed to check email existence." });
    }
  }

  /**
   * Sends a verification code to the user's email.
   * This method generates a one-time code and emails it to the provided address.
   *
   * @param {Object} req - The HTTP request object containing the user's email.
   * @param {Object} res - The HTTP response object used to send success or error messages.
   */
  static async sendVerificationCode(req, res) {
    try {
      const { email, firstName } = req.body;

      if (!email) {
        return res.status(400).json({ message: "Email is required." });
      }

      await AuthService.sendVerificationCode(email, firstName);

      res.status(200).json({ message: "Verification code sent successfully." });
    } catch (error) {
      console.error("Error sending verification code:", error);
      res.status(500).json({
        message: "Failed to send verification code.",
        error: error.message,
      });
    }
  }

  /**
   * Verifies the code entered by the user to confirm their email.
   * Validates the provided code against the generated code in the system.
   *
   * @param {Object} req - The HTTP request object containing the email and code.
   * @param {Object} res - The HTTP response object used to send success or error messages.
   */
  static async verifyCode(req, res) {
    try {
      const { email, code } = req.body;

      if (!email || !code) {
        return res
          .status(400)
          .json({ message: "Both email and code are required." });
      }

      const isVerified = await AuthService.verifyCode(email, code);

      if (isVerified) {
        res.status(200).json({ message: "Verification successful." });
      } else {
        res.status(400).json({ message: "Invalid verification code." });
      }
    } catch (error) {
      console.error("Error verifying code:", error);
      res.status(500).json({
        message: "Failed to verify code.",
        error: error.message,
      });
    }
  }

  /**
   * Resends a verification code to the user's email.
   * Generates a new one-time code and emails it to the user.
   *
   * @param {Object} req - The HTTP request object containing the user's email.
   * @param {Object} res - The HTTP response object used to send success or error messages.
   */
  static async resendCode(req, res) {
    try {
      const { email, firstName } = req.body;

      if (!email) {
        return res.status(400).json({ message: "Email is required." });
      }

      await AuthService.sendVerificationCode(email, firstName);

      res
        .status(200)
        .json({ message: "Verification code resent successfully." });
    } catch (error) {
      console.error("Error resending verification code:", error);
      res.status(500).json({
        message: "Failed to resend verification code.",
        error: error.message,
      });
    }
  }
}

module.exports = AuthController;
