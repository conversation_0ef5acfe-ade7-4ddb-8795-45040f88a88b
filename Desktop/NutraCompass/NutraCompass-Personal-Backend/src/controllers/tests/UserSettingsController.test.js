const UserSettingsController = require("../UserSettingsController.js");
const UserSettingsService = require("../../services/UserSettingsService.js");
jest.mock("../../services/UserSettingsService.js");

describe("UserSettingsController", () => {
  let req, res;

  beforeEach(() => {
    req = { params: { userId: "user123" }, body: {} };
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
    UserSettingsService.mockClear();
  });

  describe("getSettings", () => {
    it("should send user settings correctly", async () => {
      const mockSettings = { theme: "dark" };
      UserSettingsService.prototype.getSettings.mockResolvedValue(mockSettings);

      await UserSettingsController.getSettings(req, res);
      expect(res.json).toHaveBeenCalledWith(mockSettings);
    });

    it("should handle errors when settings not found", async () => {
      UserSettingsService.prototype.getSettings.mockRejectedValue(
        new Error("Settings not found")
      );

      await UserSettingsController.getSettings(req, res);
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: "Settings not found" });
    });
  });

  describe("updateSettings", () => {
    it("should handle successful settings update", async () => {
      UserSettingsService.prototype.updateSettings.mockResolvedValue();
      await UserSettingsController.updateSettings(req, res);
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.send).toHaveBeenCalled();
    });

    it("should handle errors during settings update", async () => {
      UserSettingsService.prototype.updateSettings.mockRejectedValue(
        new Error("Failed to update")
      );

      await UserSettingsController.updateSettings(req, res);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ message: "Failed to update" });
    });
  });
});
