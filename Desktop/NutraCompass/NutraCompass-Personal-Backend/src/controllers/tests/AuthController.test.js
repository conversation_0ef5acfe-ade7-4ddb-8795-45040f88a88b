// Import the AuthController and AuthService to test the controller functions
const AuthController = require("../AuthController.js");
const AuthService = require("../../services/AuthService");

// Mock the AuthService to isolate the tests from external dependencies
jest.mock("../../services/AuthService");

// Define the test suite for the AuthController
describe("AuthController", () => {
  // Helper function to create a mock request object
  const mockRequest = (body = {}) => ({ body });

  // Helper function to create a mock response object
  const mockResponse = () => {
    const res = {};
    res.json = jest.fn().mockReturnValue(res); // Mocks the json method to allow chaining
    res.status = jest.fn().mockReturnValue(res); // Mocks the status method to allow chaining
    return res;
  };

  // Tests for the verifyUserSession method
  describe("verifyUserSession", () => {
    it("should verify user session and return UID", async () => {
      const req = mockRequest({ idToken: "validToken" });
      const res = mockResponse();
      AuthService.prototype.verifyToken.mockResolvedValue({ uid: "123" });

      await AuthController.verifyUserSession(req, res);

      // Expect the json method to be called with the UID of the verified user
      expect(res.json).toHaveBeenCalledWith({ uid: "123" });
    });

    it("should handle verification failure", async () => {
      const req = mockRequest({ idToken: "invalidToken" });
      const res = mockResponse();
      AuthService.prototype.verifyToken.mockRejectedValue(
        new Error("Verification failed")
      );

      await AuthController.verifyUserSession(req, res);

      // Expect the response to indicate an unauthorized status and provide an error message
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ error: "Verification failed" });
    });
  });

  // Tests for the registerUser method
  describe("registerUser", () => {
    it("should register a user and return token", async () => {
      const req = mockRequest({
        email: "<EMAIL>",
        password: "password",
        defaultSettings: {},
      });
      const res = mockResponse();
      AuthService.prototype.register.mockResolvedValue({
        userId: "123",
        idToken: "fakeToken",
      });

      await AuthController.registerUser(req, res);

      // Expect the response to indicate success and provide user details and a success message
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        userId: "123",
        idToken: "fakeToken",
        message: "User registered successfully. Please verify your email.",
      });
    });
  });

  // Tests for the resetPassword method
  describe("resetPassword", () => {
    it("should send a password reset email successfully", async () => {
      const req = mockRequest({ email: "<EMAIL>" });
      const res = mockResponse();

      AuthService.prototype.resetPassword.mockResolvedValue();

      await AuthController.resetPassword(req, res);

      // Expect the response to confirm the email was sent
      expect(res.json).toHaveBeenCalledWith({
        message: "Password reset email sent successfully.",
      });
    });

    it("should handle errors during password reset", async () => {
      const req = mockRequest({ email: "<EMAIL>" });
      const res = mockResponse();

      AuthService.prototype.resetPassword.mockRejectedValue(
        new Error("Failed to send reset email")
      );

      await AuthController.resetPassword(req, res);

      // Expect the response to indicate an error and provide an error message
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: "Failed to send reset email",
      });
    });
  });
});
