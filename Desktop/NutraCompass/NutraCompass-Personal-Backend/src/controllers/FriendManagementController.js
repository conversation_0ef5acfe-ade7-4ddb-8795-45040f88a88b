const FriendManagementService = require("../services/FriendManagementService");

class FriendManagementController {
  /**
   * Fetches recommended users excluding the requester. Intended for initial user display.
   * @param {object} req - The request object containing all the necessary parameters.
   * @param {object} res - The response object used to return data or errors.
   */
  static async getRecommendedUsers(req, res) {
    const requesterId = req.query.userId; // Get the user ID from query parameters

    try {
      const users = await FriendManagementService.getRecommendedUsers(
        requesterId
      );
      res.json(users);
    } catch (error) {
      console.error("Error fetching recommended users:", error);
      res.status(500).json({ message: "Failed to fetch recommended users" });
    }
  }

  /**
   * Searches users based on the provided text query.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async searchUsers(req, res) {
    const { searchQuery, userId } = req.query;
    if (!searchQuery) {
      return res.status(400).json({ message: "Search query is required" });
    }

    try {
      const users = await FriendManagementService.searchUsers(
        userId,
        searchQuery
      );
      res.json(users);
    } catch (error) {
      console.error("Error searching users:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Sends a friend request from one user to another.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async sendFriendRequest(req, res) {
    const { senderId } = req.query;
    const { receiverId } = req.params;

    if (!senderId || !receiverId) {
      return res
        .status(400)
        .json({ message: "User ID and Friend ID are required" });
    }

    try {
      const result = await FriendManagementService.sendFriendRequest(
        senderId,
        receiverId
      );
      res.status(201).json(result);
    } catch (error) {
      console.error("Error sending friend request:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Cancel a friend request.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async cancelFriendRequest(req, res) {
    const { senderId } = req.query;
    const { receiverId } = req.params;

    if (!senderId || !receiverId) {
      return res
        .status(400)
        .json({ message: "Both sender and receiver IDs are required" });
    }

    try {
      const result = await FriendManagementService.cancelFriendRequest(
        senderId,
        receiverId
      );
      res.json(result);
    } catch (error) {
      console.error("Error cancelling friend request:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Accepts a friend request between two users.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async acceptFriendRequest(req, res) {
    const { receiverId } = req.query;
    const { senderId } = req.params;

    if (!receiverId || !senderId) {
      return res
        .status(400)
        .json({ message: "Both requester and sender IDs are required" });
    }

    try {
      const result = await FriendManagementService.acceptFriendRequest(
        receiverId,
        senderId
      );
      res.status(201).json(result);
    } catch (error) {
      console.error("Error accepting friend request:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Rejects a friend request.
   * @param {object} req - The request object containing the IDs.
   * @param {object} res - The response object used to send back the HTTP response.
   */
  static async rejectFriendRequest(req, res) {
    const { receiverId } = req.query;
    const { senderId } = req.params;

    if (!receiverId || !senderId) {
      return res
        .status(400)
        .json({ message: "Both requester and sender IDs are required" });
    }

    try {
      const result = await FriendManagementService.rejectFriendRequest(
        receiverId,
        senderId
      );
      res.status(200).json(result);
    } catch (error) {
      console.error("Error rejecting friend request:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Removes a friend connection between two users.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async removeFriend(req, res) {
    const { userId } = req.query;
    const { friendId } = req.params;

    if (!friendId) {
      return res.status(400).json({ message: "Friend ID is required" });
    }

    try {
      const result = await FriendManagementService.removeFriend(
        userId,
        friendId
      );
      res.status(204).json(result);
    } catch (error) {
      console.error("Error removing friend:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Blocks a user.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async blockUser(req, res) {
    const { userId } = req.query;
    const { blockedUserId } = req.params;

    if (!blockedUserId) {
      return res.status(400).json({ message: "Blocked user ID is required" });
    }

    try {
      const result = await FriendManagementService.blockUser(
        userId,
        blockedUserId
      );
      res.status(201).json(result);
    } catch (error) {
      console.error("Error blocking user:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Unblocks a user.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async unblockUser(req, res) {
    const { userId } = req.query;
    const { blockedUserId } = req.params;

    if (!blockedUserId) {
      return res.status(400).json({ message: "Blocked user ID is required" });
    }

    try {
      const result = await FriendManagementService.unblockUser(
        userId,
        blockedUserId
      );
      res.status(204).json(result);
    } catch (error) {
      console.error("Error unblocking user:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Retrieves a list of blocked users for a given user.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async getBlockedUsersList(req, res) {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ message: "User ID is required." });
    }

    try {
      const blockedUsers = await FriendManagementService.getBlockedUsersList(
        userId
      );
      res.status(200).json(blockedUsers);
    } catch (error) {
      console.error("Error retrieving blocked users list:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Retrieves the friends list of a user.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async getFriendsList(req, res) {
    const { userId } = req.query;

    try {
      const friends = await FriendManagementService.getFriendsList(userId);
      res.json(friends);
    } catch (error) {
      console.error("Error retrieving friends list:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Retrieves incoming friend requests for a user.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async getIncomingRequests(req, res) {
    const { userId } = req.query;

    try {
      const requests = await FriendManagementService.getIncomingRequests(
        userId
      );
      res.json(requests);
    } catch (error) {
      console.error("Error fetching incoming friend requests:", error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Retrieves outgoing friend requests for a user.
   * @param {object} req - The request object.
   * @param {object} res - The response object.
   */
  static async getOutgoingRequests(req, res) {
    const { userId } = req.query;

    try {
      const requests = await FriendManagementService.getOutgoingRequests(
        userId
      );
      res.json(requests);
    } catch (error) {
      console.error("Error fetching outgoing friend requests:", error);
      res.status(500).json({ message: error.message });
    }
  }
}

module.exports = FriendManagementController;
