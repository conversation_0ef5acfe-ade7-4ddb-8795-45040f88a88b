const userSettingsService = require("../services/UserSettingsService.js");

class UserSettingsController {
  /**
   * Handles HTTP GET requests to fetch user settings.
   * @param {Object} req - The HTTP request object. Expects an id parameter in the URL.
   * @param {Object} res - The HTTP response object.
   */
  static async getSettings(req, res) {
    const { userId } = req.params;
    try {
      const settings = await userSettingsService.getSettings(userId);
      res.json(settings);
    } catch (error) {
      res.status(404).json({ message: error.message });
    }
  }

  /**
   * Handles HTTP POST requests to update user settings.
   * @param {Object} req - The HTTP request object. Expects an id parameter in the URL and a JSON body with settings.
   * @param {Object} res - The HTTP response object.
   */
  static async updateSettings(req, res) {
    const { userId } = req.params;
    const settings = req.body;
    try {
      await userSettingsService.updateSettings(userId, settings);
      res.status(204).send(); // HTTP 204 No Content: successful and no content to return
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }
}

module.exports = UserSettingsController;
