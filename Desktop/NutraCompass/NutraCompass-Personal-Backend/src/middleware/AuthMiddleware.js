const admin = require("../config/firebase-admin"); // Import Firebase Admin SDK for authentication and Firestore

const getAuthToken = (req) => {
  // First check cookies for SSR compatibility
  if (req.cookies?.__session) {
    return req.cookies.__session;
  }

  // Then check headers
  const authHeader = req.headers.authorization || "";
  if (authHeader.startsWith("Bearer ")) {
    return authHeader.split("Bearer ")[1];
  }

  return null;
};

/**
 * Middleware to verify Firebase ID tokens and attach user information to the request object.
 * @param {Object} req - The request object.
 * @param {Object} res - The response object.
 * @param {Function} next - The next middleware function in the stack.
 */
const checkAuthAndOwnership = async (req, res, next) => {
  try {
    const token = getAuthToken(req);

    // Add empty token validation
    if (!token || token === "Bearer null") {
      throw new Error("Empty authorization token");
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    const userRecord = await admin.auth().getUser(decodedToken.uid);

    // Safer token revocation check
    if (userRecord.tokensValidAfterTime) {
      const validAfter = new Date(userRecord.tokensValidAfterTime).getTime();
      const tokenIssuedAt = decodedToken.iat * 1000;

      if (validAfter > tokenIssuedAt) {
        throw new Error("Token revoked - reauthentication required");
      }
    }

    next();
  } catch (error) {
    // Improved error logging
    console.error("Auth Failure", {
      error: error.message,
      tokenStatus: token ? "present" : "missing",
      authHeader: req.headers.authorization?.slice(0, 15),
    });

    res.status(401).json({
      error: "AUTH_FAILURE",
      message: "Please re-login to continue",
      authRequired: true,
    });
  }
};

/**
 * Currently Not Used
 * Middleware to check if the authenticated user has an admin role.
 * @param {Object} req - The request object.
 * @param {Object} res - The response object.
 * @param {Function} next - The next middleware function in the stack.
 */
function checkAdminRole(req, res, next) {
  if (req.user.role === "admin") {
    next();
  } else {
    res.status(403).json({ error: "Insufficient permissions" });
  }
}

module.exports = { checkAuthAndOwnership, checkAdminRole };
