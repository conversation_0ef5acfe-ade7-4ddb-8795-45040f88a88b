const multer = require("multer");

// Configure multer for image storage in memory
const storage = multer.memoryStorage();

// Set up file filter to only accept images
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("image/")) {
    cb(null, true); // Accept file
  } else {
    cb(new Error("Not an image! Please upload only images."), false);
  }
};

// Initialize multer with options
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // now allowing for 10MB files
  },
});

// Export the configured multer middleware
module.exports = upload;
