const mailchimp = require("@mailchimp/mailchimp_transactional")(
  process.env.MAILCHIMP_API_KEY
);

/**
 * Sends an email using a Mailchimp Transactional template.
 *
 * @param {string} templateName - The slug of the Mailchimp template.
 * @param {string} recipientEmail - The recipient's email address.
 * @param {string} subject - The subject of the email.
 * @param {Object} vars - Array of variables in { name, content } format.
 */
const sendEmailWithTemplate = async (
  templateName,
  recipientEmail,
  subject,
  vars
) => {
  if (!templateName) {
    throw new Error("Template name is required but not provided.");
  }

  try {
    const message = {
      from_email: process.env.EMAIL_USER,
      from_name: "NutraCompass",
      to: [
        {
          email: recipientEmail,
          type: "to",
        },
      ],
      subject,
      merge_vars: [
        {
          rcpt: recipientEmail,
          vars: vars,
        },
      ],
    };

    console.log("Final Message Object:", JSON.stringify(message, null, 2));

    const response = await mailchimp.messages.sendTemplate({
      template_name: templateName,
      template_content: [],
      message,
    });

    console.log(
      `Email sent using template ${templateName} to ${recipientEmail}`,
      response
    );
  } catch (error) {
    console.error(
      `Error sending email with template ${templateName}:`,
      error.response?.data?.message || error.message
    );
    throw new Error("Failed to send email.");
  }
};

module.exports = { sendEmailWithTemplate };
