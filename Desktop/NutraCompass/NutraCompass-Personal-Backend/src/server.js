const express = require("express");
const cors = require("cors");
const { rateLimit } = require("express-rate-limit");
const { RedisStore } = require("rate-limit-redis");
const { createClient } = require("redis");
require("dotenv").config({
  path: `.env.${process.env.NODE_ENV}`,
});

// Temporarily commented Redis client
const redisClient = createClient({
  url: process.env.REDIS_URL,
  socket: {
    tls: process.env.NODE_ENV === "production",
    rejectUnauthorized: false,
    keepAlive: 5000,
    connectionTimeout: 15000,
    reconnectStrategy: (retries) => {
      if (retries > 5) {
        console.log("Maximum Redis retries exceeded");
        return new Error("Redis connection failed");
      }
      return Math.min(retries * 500, 5000);
    },
  },
  disableOfflineQueue: true,
});

// Redis event handlers commented out
redisClient.on("error", (err) => console.error("Redis client error:", err));
redisClient.on("connect", () => console.log("Redis connection established"));
redisClient.on("ready", () => console.log("Redis client ready"));
redisClient.on("reconnecting", () => console.log("Redis reconnecting..."));

// Temporary memory-based rate limiter
const createRateLimiter = (maxRequests, windowMinutes, message) =>
  rateLimit({
    store: new RedisStore({
      sendCommand: (...args) => redisClient.sendCommand(args),
    }),
    windowMs: windowMinutes * 60 * 1000,
    max: maxRequests,
    message: message,
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => process.env.NODE_ENV === "test",
    keyGenerator: (req) => req.user?.uid || req.ip,
  });

const initializeApp = async () => {
  try {
    // Temporarily disable Redis connection
    // console.log("Skipping Redis connection temporarily");
    await redisClient.connect();

    const app = express();
    app.set("trust proxy", process.env.NODE_ENV === "production" ? 2 : 0);

    app.use(
      cors({
        origin: process.env.CORS_ORIGIN || "*",
        methods: ["GET", "POST", "PUT", "DELETE"],
        allowedHeaders: ["Content-Type", "Authorization"],
        credentials: true,
      })
    );
    app.use(express.json({ limit: "20mb" }));
    app.use(express.urlencoded({ limit: "20mb", extended: true }));

    // ======================
    // Rate Limit Config Options
    // ======================
    // Choose ONE tier based on your Heroku Redis plan:

    const RATE_CONFIG = {
      global: {
        window: process.env.REDIS_RATE_WINDOW_GLOBAL || 15, // Read from env vars
        max: process.env.REDIS_RATE_MAX_GLOBAL || 150,
      },
      auth: {
        window: process.env.REDIS_AUTH_WINDOW || 5,
        max: process.env.REDIS_AUTH_MAX || 15,
      },
    };

    // Tier 1: Mini (25MB RAM, 20 connections) - $3/month
    // const RATE_CONFIG = {
    //   global: { window: 60, max: 100 }, // 100 requests/hour
    //   auth: { window: 60, max: 10 }, // 10 auth requests/hour
    //   cost: "Mini Tier ($3/mo",
    // };

    // Tier 2: Premium 0 (512MB RAM, 40 connections) - $15/month
    // const RATE_CONFIG = {
    //   global: { window: 15, max: 150 }, // 150 requests/15min
    //   auth: { window: 5, max: 15 }, // 15 auth requests/5min
    //   cost: "Premium 0 ($15/mo)",
    // };

    // Tier 3: Premium 1 (1GB RAM, 80 connections) - $60/month
    // const RATE_CONFIG = {
    //   global: { window: 1, max: 300 },   // 300 requests/minute
    //   auth: { window: 1, max: 30 },      // 30 auth requests/minute
    //   cost: "Premium 1 ($60/mo)"
    // };

    // Tier 4: Premium 2 (2.5GB RAM, 160 connections) - $120/month
    // const RATE_CONFIG = {
    //   global: { window: 1, max: 1000 },  // 1000 requests/minute
    //   auth: { window: 1, max: 100 },     // 100 auth requests/minute
    //   cost: "Premium 2 ($120/mo)"
    // };

    // Tier 5: Premium 3 (5GB RAM, 200 connections) - $240/month
    // const RATE_CONFIG = {
    //   global: { window: 1, max: 2000 },  // 2000 requests/minute
    //   auth: { window: 1, max: 200 },     // 200 auth requests/minute
    //   cost: "Premium 3 ($240/mo)"
    // };

    // Basic rate limiting without Redis
    app.use(
      createRateLimiter(
        RATE_CONFIG.global.max,
        RATE_CONFIG.global.window,
        "Too many requests, please try again later."
      )
    );

    app.use(
      "/v1/authentication",
      createRateLimiter(
        RATE_CONFIG.auth.max,
        RATE_CONFIG.auth.window,
        "Too many authentication attempts, please try later."
      )
    );

    const routes = [
      require("./routes/AuthRoutes"),
      require("./routes/UserSettingsRoutes"),
      require("./routes/FoodDiaryRoutes"),
      require("./routes/FoodMenuRoutes"),
      require("./routes/NutritionalProgramRoutes"),
      require("./routes/FriendManagementRoutes"),
      require("./routes/NotificationRoutes"),
    ];
    routes.forEach((route) => app.use(route));

    // Test endpoint for rate limits
    app.get("/rate-test", (req, res) => {
      res.json({ remaining: req.rateLimit.remaining });
    });

    // Simplified health check
    app.get("/health", (req, res) => {
      res.status(200).json({
        status: "ok",
        environment: process.env.NODE_ENV,
      });
    });

    app.get("/", (req, res) => {
      res.send("Welcome to the NutraCompass Backend API!");
    });

    app.use((err, req, res, next) => {
      console.error(`[${new Date().toISOString()}] Error:`, err.stack);
      res.status(err.statusCode || 500).json({
        error: err.name || "InternalServerError",
        message:
          process.env.NODE_ENV === "production"
            ? "An unexpected error occurred"
            : err.message,
        timestamp: new Date().toISOString(),
      });
    });

    const PORT = process.env.PORT || 3000;
    const server = app.listen(PORT, "0.0.0.0", () => {
      console.log(`Server running on port ${PORT}`);
      console.log("Environment:", process.env.NODE_ENV);
      console.log("CORS Origin:", process.env.CORS_ORIGIN || "All origins");
    });

    // Removed Redis cleanup from graceful shutdown
    process.on("SIGTERM", async () => {
      console.log("SIGTERM signal received: closing server");
      server.close(() => {
        console.log("HTTP server closed");
        process.exit(0);
      });
    });
  } catch (error) {
    console.error("Failed to initialize application:", error);
    process.exit(1);
  }
};

initializeApp();
