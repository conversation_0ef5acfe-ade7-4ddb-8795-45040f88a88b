const admin = require("../config/firebase-admin");
const db = admin.firestore();
const uuid = require("uuid");
const FoodMenuService = require("./FoodMenuService");

/**
 * Service class for handling the data layer concerning food diary functionalities.
 * This includes operations on food entries, meal sections, and custom meals.
 */
class FoodDiaryService {
  constructor() {
    this.db = db;
  }

  /**
   * Retrieves all custom meal sections for a given user, ensuring the user ID is valid.
   * @param {string} userId - User ID to identify the correct user's meal sections.
   * @returns {Promise<Array>} A list of meal sections or an empty array if none found.
   */
  async getMealSections(userId) {
    if (!userId) {
      throw new Error("Invalid user ID provided.");
    }
    try {
      const collectionRef = this.db.collection(
        `users/${userId}/customMealSections`
      );
      const snapshot = await collectionRef.get();

      // If the collection is empty, create default meal sections
      if (snapshot.empty) {
        const defaultSections = [
          { id: "Meal 1", name: "Breakfast" },
          { id: "Meal 2", name: "Lunch" },
          { id: "Meal 3", name: "Dinner" },
          { id: "Meal 4", name: "" },
          { id: "Meal 5", name: "" },
          { id: "Meal 6", name: "" },
          { id: "Water", name: "Water" },
        ];

        // Use a batch to create all default meal sections
        const batch = this.db.batch();
        defaultSections.forEach((section) => {
          const sectionRef = collectionRef.doc(section.id);
          batch.set(sectionRef, { name: section.name });
        });
        await batch.commit();

        // Return the default sections after creating them
        return defaultSections;
      }

      // Otherwise, return the existing sections
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error("Failed to fetch meal sections:", error);
      throw new Error("Error retrieving meal sections.");
    }
  }

  /**
   * Updates the names of meal sections for a given user.
   * Checks if each section exists before attempting an update.
   * Uses batch processing to efficiently handle multiple updates.
   * @param {string} userId - User's unique identifier.
   * @param {Array} updates - Array of objects containing meal section IDs and their new names.
   * @returns {Promise<Array>} The updated meal sections.
   */
  async updateMealSectionNames(userId, updates) {
    if (!userId || !updates || !Array.isArray(updates)) {
      throw new Error("Invalid input parameters.");
    }

    const batch = this.db.batch();
    for (const update of updates) {
      const { id, newName } = update;

      const docRef = this.db.doc(`users/${userId}/customMealSections/${id}`);
      const docSnap = await docRef.get();

      if (docSnap.exists) {
        batch.update(docRef, { name: newName });
      } else {
        batch.set(docRef, { name: newName });
      }
    }

    try {
      await batch.commit();
      console.log("Batch update committed successfully.");
      return this.getMealSections(userId);
    } catch (error) {
      console.error("Failed to commit batch update:", error);
      throw new Error("Error updating meal section names.");
    }
  }

  /**
   * Fetches the most recent food history for a specific user.
   * Filters out water entries and standalone quick add entries that are not part of any custom meals.
   * Groups consecutive entries that belong to the same custom meal to avoid inflating the history limit.
   * Uses Promise.all for concurrent fetching of entries and custom meals for efficiency.
   * @param {string} userId - User's unique identifier.
   * @returns {Promise<Array>} A list of filtered and grouped food history items, limiting to the first 20 entries.
   * @throws {Error} If the userId is invalid or the database query fails.
   */
  async fetchUserFoodHistory(userId) {
    if (!userId) {
      throw new Error("Invalid user ID provided.");
    }

    try {
      // Fetch both entries and custom meals concurrently
      const [entriesSnapshot, customMeals] = await Promise.all([
        this.db
          .collection(`users/${userId}/foodLogEntries`)
          .orderBy("timestamp", "desc")
          .get(),
        FoodMenuService.getCustomMeals(userId),
      ]);

      const entries = entriesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Filter out water entries and standalone quick add entries
      const filteredEntries = entries.filter(
        (entry) =>
          !(
            entry.foodCategory === "Water" ||
            (entry.foodCategory === "Quick Add" &&
              !entry.inACustomMeal?.isPartOfMeal)
          )
      );

      // Map custom meals by their ID for easy lookup
      const customMealsById = new Map(
        customMeals.map((meal) => [meal.id, meal])
      );

      const combinedHistory = [];
      let i = 0;

      // Traverse the filtered entries and group consecutive ones that belong to the same custom meal
      while (i < filteredEntries.length) {
        const entry = filteredEntries[i];
        const { inACustomMeal } = entry;

        if (inACustomMeal?.isPartOfMeal && inACustomMeal.mealId) {
          const mealId = inACustomMeal.mealId;
          const savedMeal = customMealsById.get(mealId);

          if (savedMeal) {
            // Add the saved meal to the combined history without the timestamp
            const { timestamp, ...mealWithoutTimestamp } = savedMeal;
            combinedHistory.push(mealWithoutTimestamp);

            // Skip over all entries that are part of this meal
            while (
              i < filteredEntries.length &&
              filteredEntries[i].inACustomMeal?.mealId === mealId
            ) {
              i++;
            }
            continue; // Skip to the next iteration of the loop
          }
        }

        // If it's not part of a custom meal, add the entry to the history
        combinedHistory.push(entry);
        i++;
      }

      // Return the first 20 items from the combined history
      return combinedHistory.slice(0, 20);
    } catch (error) {
      console.error("Failed to fetch food history:", error);
      throw new Error("Error retrieving food history.");
    }
  }

  /**
   * Fetches all food entries for a specific user, optionally filtered by meal type.
   * Validates user input before querying the database.
   * @param {string} userId - User's unique identifier.
   * @param {string} mealType - Optional meal type to filter the entries.
   * @returns {Promise<Array>} A list of food entries.
   */
  async getFoodEntries(userId, mealType) {
    if (!userId) {
      throw new Error("Invalid user ID provided.");
    }
    try {
      const collectionRef = this.db.collection(
        `users/${userId}/foodLogEntries`
      );
      const queryRef = mealType
        ? collectionRef.where("mealType", "==", mealType)
        : collectionRef;
      const snapshot = await queryRef.get();
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error("Failed to fetch food entries:", error);
      throw new Error("Error retrieving food entries.");
    }
  }

  /**
   * Saves a complete meal to the user's food log, creating new food entry documents.
   * Each item in the meal has a unique document associated with it.
   * @param {string} userId - User's unique identifier.
   * @param {string} mealType - The type of meal (e.g., breakfast, lunch).
   * @param {string} selectedDate - The date on which the meal is logged.
   * @param {Array} mealItems - Array of food items making up the meal.
   * @returns {Promise<Object>} Confirmation message and details of the new entries.
   */
  async saveMeal(userId, mealType, selectedDate, mealItems, mealId) {
    if (!userId || !mealType || !selectedDate || !mealItems || !mealId) {
      throw new Error("Missing required parameters for saving meal.");
    }
    try {
      const collectionRef = this.db.collection(
        `users/${userId}/foodLogEntries`
      );
      const batch = this.db.batch();
      const entryItems = []; // Array to hold newly created entries for response

      mealItems.forEach((item) => {
        const uniqueId = uuid.v4(); // Ensures each entry has a unique identifier
        let newEntry;

        if (item.foodCategory === "Quick Add") {
          // Handle Quick Add entries differently
          newEntry = {
            id: uniqueId,
            foodLabel: item.foodLabel,
            nutrients: item.nutrients || {},
            foodCategory: "Quick Add",
            numberOfServings: item.numberOfServings || 1,
            mealType: mealType,
            date: selectedDate,
            inACustomMeal: { isPartOfMeal: true, mealId }, // Track the meal association
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
          };
        } else {
          // Standard entry processing
          newEntry = {
            id: uniqueId,
            foodId: item.foodId,
            foodLabel: item.foodLabel,
            foodCategory: item.foodCategory || "",
            foodBrand: item.foodBrand || "",
            numberOfServings: item.numberOfServings,
            activeMeasure: item.activeMeasure,
            measures: item.measures || [],
            nutrients: item.nutrients,
            mealType: mealType,
            date: selectedDate,
            inACustomMeal: { isPartOfMeal: true, mealId }, // Track the meal association
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
          };
        }

        const docRef = collectionRef.doc(uniqueId); // Create a document for each new entry
        batch.set(docRef, newEntry);
        entryItems.push(newEntry); // Collect new entry for batch response
      });

      await batch.commit(); // Commit all entries in a single batch
      return {
        message: "Food log meal entry added successfully",
        entryItems: entryItems,
      };
    } catch (error) {
      console.error("Failed to save meal:", error);
      throw new Error("Error saving meal to food log.");
    }
  }

  /**
   * Adds or updates a single food entry in the user's food log based on the presence of an entry ID.
   * If an entry ID is provided, it attempts to update the existing entry. If no entry ID is found,
   * or the provided ID does not correspond to an existing document, it treats the operation as a new entry.
   * @param {string} userId - User's unique identifier.
   * @param {string} mealType - The type of meal (e.g., breakfast, lunch).
   * @param {string} entryId - The Firestore document ID of the food entry to update.
   * @param {Object} updatedEntry - The new data for the food entry.
   * @param {string} selectedDate - The date of the food entry.
   * @returns {Promise<Object>} Result of the update operation or the addition of a new entry.
   */
  async saveOrUpdateSingleFoodItemToFoodLog(
    userId,
    mealType,
    entryId,
    updatedEntry,
    selectedDate
  ) {
    const foodLogEntriesCollectionRef = this.db.collection(
      `users/${userId}/foodLogEntries`
    );

    const editedEntry = {
      id: entryId, // Preserves existing ID for updates
      foodId: updatedEntry.foodId,
      foodLabel: updatedEntry.foodLabel,
      foodCategory: updatedEntry.foodCategory || "",
      foodBrand: updatedEntry.foodBrand || "",
      numberOfServings: updatedEntry.numberOfServings,
      activeMeasure: updatedEntry.activeMeasure,
      measures: updatedEntry.measures,
      nutrients: updatedEntry.nutrients,
      mealType: mealType,
      date: selectedDate,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    if (entryId && entryId.trim()) {
      const entryDocRef = foodLogEntriesCollectionRef.doc(entryId);
      const entryDoc = await entryDocRef.get();

      if (entryDoc.exists) {
        // If the entry exists, perform an update
        return await this.updateFoodEntry(userId, entryId, editedEntry);
      } else {
        // If no existing entry, log and proceed with a new entry
        console.error(
          `No document found for entryId: ${entryId}, treating as new entry.`
        );
        return await this.addFoodEntry(
          userId,
          mealType,
          editedEntry,
          selectedDate
        );
      }
    } else {
      // If no valid entryId provided, treat as a new entry
      return await this.addFoodEntry(
        userId,
        mealType,
        editedEntry,
        selectedDate
      );
    }
  }

  /**
   * Adds a new food entry for a user, checking for all necessary parameters.
   * @param {string} userId - User's unique identifier.
   * @param {string} mealType - The type of meal for the food entry.
   * @param {Object} foodEntry - The food entry data.
   * @returns {Promise<void>}
   */
  async addFoodEntry(userId, mealType, foodEntry, selectedDate) {
    const collectionRef = this.db.collection(`users/${userId}/foodLogEntries`);
    const uniqueId = uuid.v4();

    const newEntry = {
      id: uniqueId,
      foodId: foodEntry.foodId,
      foodLabel: foodEntry.foodLabel,
      foodCategory: foodEntry.foodCategory || "",
      foodBrand: foodEntry.foodBrand || "",
      numberOfServings: foodEntry.numberOfServings,
      activeMeasure: foodEntry.activeMeasure,
      measures: foodEntry.measures || [],
      nutrients: foodEntry.nutrients,
      mealType: mealType,
      date: selectedDate,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    try {
      await collectionRef.doc(uniqueId).set(newEntry);
      return { message: "Food log entry added successfully", entry: newEntry };
    } catch (error) {
      console.error("Error saving food log entry:", error);
      throw new Error("Failed to save food log entry.");
    }
  }

  /**
   * Updates a specific food entry, ensuring all parameters are valid and the entry exists.
   * @param {string} userId - User's unique identifier.
   * @param {string} entryId - The specific entry's ID to update.
   * @param {Object} foodEntry - Updated data for the food entry.
   * @returns {Promise<void>}
   */
  async updateFoodEntry(userId, entryId, updatedEntry) {
    const docRef = this.db
      .collection(`users/${userId}/foodLogEntries`)
      .doc(entryId);
    try {
      const docSnapshot = await docRef.get();
      if (docSnapshot.exists) {
        await docRef.set(updatedEntry, { merge: true });
        return {
          message: "Food log entry updated successfully",
          entry: updatedEntry,
        };
      } else {
        throw new Error("No such document!");
      }
    } catch (error) {
      console.error("Error updating food log entry:", error);
      throw new Error("Failed to update food log entry.");
    }
  }

  /**
   * Deletes a specific food entry, validating the existence of the user and entry ID.
   * @param {string} userId - User's unique identifier.
   * @param {string} entryId - The specific entry's ID to delete.
   * @returns {Promise<void>}
   */
  async deleteFoodEntry(userId, entryId) {
    if (!userId || !entryId) {
      throw new Error("Missing required parameters to delete food entry.");
    }
    try {
      const docRef = this.db.doc(`users/${userId}/foodLogEntries/${entryId}`);
      await docRef.delete();
    } catch (error) {
      console.error("Failed to delete food entry:", error);
      throw new Error("Error deleting food entry.");
    }
  }

  /**
   * Adds or updates a quick food entry in the user's food log.
   *
   * This function handles the addition of new quick food entries and the updating of existing entries
   * based on the presence of an `id` property in the entryDetails object. It is designed to be robust
   * and flexible, ensuring data integrity and providing clear feedback on the operation performed.
   *
   * @param {string} userId - The user's unique identifier.
   * @param {string} mealType - The type of meal, e.g., breakfast, lunch, etc.
   * @param {Object} entryDetails - Details of the food entry, including nutritional information.
   * @param {string} selectedDate - The date for which the food entry is logged.
   * @returns {Promise<Object>} A promise that resolves to a message and the entry details.
   * @throws {Error} Throws an error if required parameters are missing or if the Firestore operation fails.
   */
  async addQuickFoodEntry(userId, mealType, entryDetails, selectedDate) {
    // Validate required parameters to ensure robustness and avoid runtime errors.
    if (!userId || !mealType || !entryDetails || !selectedDate) {
      throw new Error(
        "Required parameters are missing for adding a quick food entry."
      );
    }

    // Reference to the Firestore collection where food log entries are stored.
    const foodLogEntriesCollectionRef = this.db.collection(
      `users/${userId}/foodLogEntries`
    );

    // Determine if this is an update operation by checking for an existing entry ID.
    const entryId = entryDetails.id ? entryDetails.id : uuid.v4(); // Use existing ID or generate a new one.

    // Construct the entry object to be stored in Firestore.
    const newEntry = {
      id: entryId,
      foodLabel: entryDetails.foodLabel,
      nutrients: entryDetails.nutrients,
      foodCategory: "Quick Add",
      numberOfServings: entryDetails.numberOfServings || 1, // Default to one serving if not specified.
      mealType: mealType,
      date: selectedDate,
      timestamp: admin.firestore.FieldValue.serverTimestamp(), // Ensure time consistency with server time.
    };

    try {
      // Create or update the document in Firestore based on the presence of an entry ID.
      const docRef = foodLogEntriesCollectionRef.doc(entryId);
      if (entryDetails.id) {
        // Update the existing document with the provided entry details.
        await docRef.update(newEntry);
        return {
          message: "Quick food entry updated successfully",
          entry: newEntry,
        };
      } else {
        // Create a new document with the new entry details.
        await docRef.set(newEntry);
        return {
          message: "Quick food entry added successfully",
          entry: newEntry,
        };
      }
    } catch (error) {
      // Log and rethrow errors related to Firestore operations to handle them upstream.
      console.error("Failed to save quick food entry:", error);
      throw new Error("Error saving quick food entry.");
    }
  }

  /**
   * Deletes all entries for a specific meal section on a given date. This method
   * constructs a query to find the relevant entries and uses a batch operation to delete them.
   * Ensures all parameters are provided before proceeding with the operation.
   *
   * @param {string} userId - The ID of the user whose entries are to be deleted.
   * @param {string} mealSectionId - The ID of the meal section.
   * @param {string} date - The date from which entries are to be deleted.
   * @throws {Error} - Throws an error if any required parameters are missing or if the delete operation fails.
   */
  async deleteAllEntriesByMealSectionAndDate(userId, mealSectionId, date) {
    if (!userId || !mealSectionId || !date) {
      throw new Error("Missing required parameters for deleting entries.");
    }
    try {
      const collectionRef = this.db.collection(
        `users/${userId}/foodLogEntries`
      );
      const queryRef = collectionRef
        .where("mealType", "==", mealSectionId)
        .where("date", "==", date);
      const snapshot = await queryRef.get();
      const batch = this.db.batch();
      snapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });
      await batch.commit();
    } catch (error) {
      console.error("Failed to delete entries:", error);
      throw new Error("Error deleting entries.");
    }
  }

  /**
   * Directly copies entries between meal sections and dates, applying new identifiers and updating timestamps.
   * Ensures that entries are processed within Firestore's batch limit to maintain atomicity of the batch operation.
   *
   * @param {string} userId - User identifier.
   * @param {Array} entries - Array of entries to be copied.
   * @param {string} destinationMealType - The meal type to which the entries are copied.
   * @param {string} destinationDate - The date to which the entries are copied.
   * @returns {Object} - Returns a summary including message, count of entries copied, and new entries data.
   * @throws {Error} - Throws an error if the operation fails.
   */
  async copyEntries(userId, entries, destinationMealType, destinationDate) {
    const collectionRef = this.db.collection(`users/${userId}/foodLogEntries`);
    const batch = this.db.batch();
    let operationCount = 0;
    const batchLimit = 500;
    const updatedEntries = [];

    entries.forEach((entry) => {
      if (operationCount >= batchLimit) {
        console.warn(
          "Batch limit reached, remaining entries will be in a new batch"
        );
        return;
      }

      const uniqueId = uuid.v4();
      const newEntry = {
        ...entry,
        id: uniqueId,
        mealType: destinationMealType,
        date: destinationDate,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      };

      const newDocRef = collectionRef.doc(uniqueId);
      batch.set(newDocRef, newEntry);
      updatedEntries.push(newEntry);
      operationCount++;
    });

    if (operationCount > 0) {
      await batch.commit();
      console.log(`Successfully copied ${operationCount} entries.`);
      return {
        message: "Entries copied successfully",
        count: operationCount,
        entries: updatedEntries,
      };
    } else {
      console.log("No valid entries to process in the batch.");
      return { message: "No valid entries to process", count: 0 };
    }
  }

  /**
   * Adds or updates a water entry in the user's food log.
   *
   * This function either creates a new water entry or updates an existing one based on the presence
   * of an entryId. It handles both operations within the same logical flow, providing flexibility
   * and ensuring that each entry is uniquely identified and stored correctly.
   *
   * @param {string} userId - The user's unique identifier.
   * @param {number} volume - The volume of water consumed.
   * @param {string} unit - The unit of measurement for the water volume, e.g., 'ml' or 'fl oz'.
   * @param {string} date - The date on which the water was consumed.
   * @param {string} entryId - Optional. The ID of the water entry to update. If not provided, a new entry is created.
   * @returns {Promise<Object>} A promise that resolves to a message and details of the water entry.
   * @throws {Error} Throws an error if required parameters are missing or if the Firestore operation fails.
   */
  async addOrUpdateWaterEntry(userId, volume, unit, date, entryId) {
    if (!userId || !volume || !unit || !date) {
      throw new Error("Missing required parameters.");
    }

    let isNewEntry = !entryId;
    const docRef = this.db
      .collection(`users/${userId}/foodLogEntries`)
      .doc(isNewEntry ? uuid.v4() : entryId);

    const newEntry = {
      id: docRef.id, // This ensures that the ID is correctly set whether new or existing
      volume,
      unit,
      date,
      foodLabel: "Water",
      foodCategory: "Water",
      mealType: "Water",
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    try {
      if (isNewEntry) {
        await docRef.set(newEntry); // Use set for a new entry
        return {
          message: "Water entry added successfully",
          entry: newEntry,
        };
      } else {
        await docRef.update(newEntry); // Use update for an existing entry
        return {
          message: "Water entry updated successfully",
          entry: newEntry,
        };
      }
    } catch (error) {
      console.error(
        `Failed to ${isNewEntry ? "add" : "update"} water entry:`,
        error
      );
      throw new Error(
        `Error ${isNewEntry ? "adding" : "updating"} water entry.`
      );
    }
  }
}

module.exports = new FoodDiaryService();
