const admin = require("../config/firebase-admin"); // Import Firebase Admin SDK for authentication and Firestore
const { sendEmailWithTemplate } = require("../coms/EmailService.js");

class AuthService {
  static instance = null;

  constructor() {
    if (!AuthService.instance) {
      AuthService.instance = this;
    }
    return AuthService.instance;
  }

  // ================================
  // User Registration & Authentication
  // ================================

  /**
   * Registers a new user with the given email, password, and default settings.
   * Stores the user's default settings in Firestore and generates a custom token for authentication.
   * Sends a verification email upon successful registration.
   *
   * @param {string} email - User's email address.
   * @param {string} password - User's password.
   * @param {Object} defaultSettings - Default settings for the user, saved in Firestore.
   * @returns {Promise<Object>} A response object containing the user's UID and a custom authentication token.
   */
  async register(email, password, defaultSettings) {
    try {
      const userRecord = await admin.auth().createUser({ email, password });
      const userDocRef = admin.firestore().doc(`users/${userRecord.uid}`);
      await userDocRef.set(defaultSettings);

      const idToken = await admin.auth().createCustomToken(userRecord.uid);

      const userName = defaultSettings?.profile?.firstName || "User";
      await sendEmailWithTemplate(
        "nutracompass-personal-user-registration",
        email,
        "Welcome to NutraCompass!",
        [
          {
            name: "userName",
            content: userName,
          },
        ]
      );

      return { userId: userRecord.uid, idToken };
    } catch (error) {
      console.error("Registration Error: ", error);
      throw new Error("Registration failed: " + error.message);
    }
  }

  /**
   * Registers multiple users in bulk.
   * Iterates over an array of user data to create accounts in Firebase Authentication
   * and stores default settings for each user in Firestore.
   * Sends verification emails for successfully registered users.
   *
   * @param {Array} users - An array of objects containing user details (email, password, default settings).
   * @returns {Promise<Array>} A list of registration results for each user.
   */
  async registerMultipleUsers(users) {
    const registrationResults = [];

    for (const userData of users) {
      try {
        const { email, password, defaultSettings } = userData;
        const userRecord = await admin.auth().createUser({ email, password });
        const userDocRef = admin.firestore().doc(`users/${userRecord.uid}`);
        await userDocRef.set(defaultSettings);

        const idToken = await admin.auth().createCustomToken(userRecord.uid);

        const userName = defaultSettings?.profile?.firstName || "User";
        await sendEmailWithTemplate(
          "nutracompass-personal-user-registration",
          email,
          "Welcome to NutraCompass! Verify Your Email",
          [
            {
              name: "userName",
              content: userName,
            },
          ]
        );

        registrationResults.push({
          userId: userRecord.uid,
          idToken,
          status: "Success",
        });
      } catch (error) {
        console.error("Registration Error for user:", userData.email, error);
        registrationResults.push({
          userId: null,
          status: "Failed",
          message: error.message,
        });
      }
    }

    return registrationResults;
  }

  /**
   * Verifies the provided ID token to authenticate a user.
   * Returns the associated UID if the token is valid.
   *
   * @param {string} idToken - Firebase ID token.
   * @returns {Promise<Object>} The decoded token containing the user's UID.
   */
  async verifyToken(idToken) {
    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      if (!decodedToken || !decodedToken.uid) throw new Error("Invalid token");
      return { uid: decodedToken.uid };
    } catch (error) {
      console.error("Token Verification Error: ", error);
      throw new Error("Failed to verify token: " + error.message);
    }
  }

  /**
   * Checks if a user with the given email exists in Firebase Authentication.
   * @param {string} email - The email address to check.
   * @returns {Promise<boolean>} True if the email exists, false otherwise.
   */
  async checkEmailExists(email) {
    try {
      const userRecord = await admin.auth().getUserByEmail(email);
      return !!userRecord; // Return true if the user exists
    } catch (error) {
      if (error.code === "auth/user-not-found") {
        return false; // Email does not exist
      }
      console.error("Error checking email existence:", error);
      throw new Error("Failed to check email existence.");
    }
  }

  // ================================
  // Verification Code Management
  // ================================

  /**
   * Sends a verification code to the specified email.
   * Generates a unique one-time code and emails it to the user.
   *
   * @param {string} email - The user's email address.
   * @param {string} firstName - The user's first name.
   */
  async sendVerificationCode(email, firstName) {
    try {
      const code = this.generateCode(); // Generate a unique 6-digit code
      const userName = firstName || (await this.getUserNameByEmail(email));
      await this.storeCode(email, code); // Store the code in a database or cache
      await sendEmailWithTemplate(
        "nutracompass-personal-registration-verification-code",
        email,
        "Your Registration Verification Code",
        [
          { name: "userName", content: userName },
          { name: "code", content: code },
        ]
      );
    } catch (error) {
      console.error("Error sending verification code:", error);
      throw new Error("Failed to send verification code.");
    }
  }

  /**
   * Verifies the code entered by the user.
   * Validates the provided code against the stored code.
   *
   * @param {string} email - The user's email address.
   * @param {string} code - The verification code entered by the user.
   * @returns {boolean} - True if the code is valid, otherwise false.
   */
  async verifyCode(email, code) {
    try {
      const isValid = await this.validateCode(email, code); // Validate the code
      if (!isValid) {
        throw new Error("Invalid or expired verification code.");
      }
      return true;
    } catch (error) {
      console.error("Error verifying code:", error);
      throw new Error("Failed to verify code.");
    }
  }

  // ================================
  // Password & Email Management
  // ================================

  /**
   * Generates a password reset link and sends it to the user's email address.
   * The reset link allows users to update their passwords securely.
   *
   * @param {string} email - The user's email address.
   * @param {string} firstName - The user's first name.
   */
  async resetPassword(email, firstName) {
    try {
      const resetLink = await this.generateResetPasswordLink(email);
      const userName = firstName || (await this.getUserNameByEmail(email));
      await sendEmailWithTemplate(
        "nutracompass-personal-user-changes-password",
        email,
        "Reset Your Password",
        [
          { name: "userName", content: userName },
          { name: "resetLink", content: resetLink },
        ]
      );
    } catch (error) {
      console.error("Error generating password reset link:", error);
      throw new Error("Failed to generate password reset link.");
    }
  }

  async generateResetPasswordLink(email) {
    return admin.auth().generatePasswordResetLink(email, {
      url: "https://nutracompass-individual.firebaseapp.com",
      handleCodeInApp: true,
    });
  }

  // ================================
  // Account Management
  // ================================

  /**
   * Deletes a user account, removing their authentication record, Firestore data, and associated storage files.
   * Sends a confirmation email upon successful deletion.
   *
   * @param {string} userId - The user's unique identifier.
   * @param {string} firstName - The user's first name.
   */
  async deleteUserAccount(userId, firstName) {
    try {
      const db = admin.firestore();
      const bucket = admin.storage().bucket();

      const user = await admin.auth().getUser(userId);
      const userDocRef = db.collection("users").doc(userId);

      // 1. Delete all subcollections first
      const subcollections = await userDocRef.listCollections();
      for (const subcollection of subcollections) {
        await this._deleteEntireCollection(subcollection);
      }

      // 2. Delete main user document
      await userDocRef.delete();

      // 3. Delete storage files
      const folders = ["profilePictures", "customMeals", "progressPictures"];
      for (const folder of folders) {
        const filePrefix = `${folder}/${userId}/`;
        const [files] = await bucket.getFiles({ prefix: filePrefix });

        for (const file of files) {
          await file.delete();
        }
      }

      // 4. Delete authentication record
      await admin.auth().deleteUser(userId);

      // 5. Send confirmation email
      const userName = firstName || (await this.getUserNameByEmail(user.email));
      await sendEmailWithTemplate(
        "nutracompass-personal-user-deletes-account",
        user.email,
        "Your Account Has Been Deleted",
        [{ name: "userName", content: userName }]
      );
    } catch (error) {
      console.error("Error deleting user account:", error);
      throw new Error("Failed to delete user account: " + error.message);
    }
  }

  // ================================
  // Helper Functions
  // ================================

  /**
   * Recursively deletes all documents in a Firestore collection/subcollection
   * using batched writes for safety and efficiency.
   *
   * @param {admin.firestore.CollectionReference} collectionRef - Reference to the collection
   * @param {number} [batchSize=500] - Maximum documents to delete per batch (Firestore limit: 500)
   * @returns {Promise<void>}
   *
   * @example
   * const userRef = db.collection('users').doc('userId');
   * const postsCollection = userRef.collection('posts');
   * await _deleteEntireCollection(postsCollection);
   */
  async _deleteEntireCollection(collectionRef, batchSize = 500) {
    // Query with ordering and batch size limit
    const query = collectionRef.orderBy("__name__").limit(batchSize);

    // Batch deletion loop
    while (true) {
      // Get current batch of documents
      const snapshot = await query.get();

      // Exit condition: no more documents
      if (snapshot.empty) break;

      // Initialize batched write
      const batch = collectionRef.firestore.batch();

      // Queue all documents in current batch for deletion
      snapshot.docs.forEach((doc) => batch.delete(doc.ref));

      // Commit batched deletions
      await batch.commit();
    }
  }

  /**
   * Generates a unique 6-digit verification code.
   * @returns {string} - A 6-digit numeric code.
   */
  generateCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Stores a verification code for a user's email.
   * Typically stores the code in an in-memory cache or database with an expiration time.
   *
   * @param {string} email - The user's email address.
   * @param {string} code - The verification code to store.
   */
  async storeCode(email, code) {
    const db = admin.firestore();
    const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes from now
    await db
      .collection("verificationCodes")
      .doc(email)
      .set({ code, expiresAt });
  }

  /**
   * Validates a verification code against the stored code.
   * Ensures the code matches and has not expired.
   *
   * @param {string} email - The user's email address.
   * @param {string} code - The verification code entered by the user.
   * @returns {boolean} - True if the code is valid, otherwise false.
   */
  async validateCode(email, code) {
    const db = admin.firestore();
    const docRef = db.collection("verificationCodes").doc(email);
    const doc = await docRef.get();

    if (!doc.exists) {
      return false; // No code stored for this email
    }

    const { code: storedCode, expiresAt } = doc.data();
    if (storedCode !== code || Date.now() > expiresAt) {
      return false; // Code mismatch or expired
    }

    // Optionally delete the code after successful validation
    await docRef.delete();
    return true;
  }

  /**
   * Helper function to get userName by email.
   * Queries Firestore to retrieve the user's name based on their email address stored in the profile object.
   * @param {string} email - The email address of the user.
   * @returns {Promise<string>} The user's name, or a fallback if not available.
   */
  async getUserNameByEmail(email) {
    console.log("Fetching user's first name...");
    try {
      const db = admin.firestore();
      const userSnapshot = await db
        .collection("users")
        .where("profile.email", "==", email)
        .get();

      if (!userSnapshot.empty) {
        const userData = userSnapshot.docs[0].data();
        return userData?.profile?.firstName || "User";
      }

      return "User"; // Fallback if no user data is found
    } catch (error) {
      console.error("Error fetching user name by email:", error);
      return "User"; // Fallback in case of an error
    }
  }
}

module.exports = new AuthService();
