// Import the AuthService class to be tested.
const AuthService = require("../AuthService.js");

// Mock implementations of Firebase admin functionalities needed by AuthService.
const mockCreateUser = jest
  .fn()
  .mockResolvedValue({ uid: "123", email: "<EMAIL>" });
const mockCreateCustomToken = jest.fn().mockResolvedValue("fakeToken");
const mockVerifyIdToken = jest.fn().mockResolvedValue({ uid: "123" });
const mockGenerateEmailVerificationLink = jest
  .fn()
  .mockResolvedValue("verificationLink");
const mockGeneratePasswordResetLink = jest.fn().mockResolvedValue("resetLink");

// Mock the firebase-admin module, which is used internally by AuthService for user management and authentication.
jest.mock("../../config/firebase-admin", () => ({
  auth: () => ({
    createUser: mockCreateUser,
    createCustomToken: mockCreateCustomToken,
    verifyIdToken: mockVerifyIdToken,
    generateEmailVerificationLink: mockGenerateEmailVerificationLink,
    generatePasswordResetLink: mockGeneratePasswordResetLink,
    getUser: jest.fn().mockResolvedValue({ email: "<EMAIL>" }),
  }),
  firestore: () => ({
    doc: () => ({
      set: jest.fn().mockResolvedValue(true),
    }),
  }),
  initializeApp: jest.fn(),
}));

// Define a test suite for the AuthService class.
describe("AuthService", () => {
  let authService;

  // Initialize AuthService before running the tests.
  beforeAll(() => {
    authService = new AuthService();
  });

  // Test suite for the register function of AuthService.
  describe("register", () => {
    it("should create a new user, save default settings, send verification email, and return UID and token", async () => {
      const email = "<EMAIL>";
      const password = "password123";
      const defaultSettings = { theme: "dark" };

      const result = await authService.register(
        email,
        password,
        defaultSettings
      );

      // Assert that the register function returns the expected result.
      expect(result).toEqual({ userId: "123", idToken: "fakeToken" });
      // Check that createUser was called with correct parameters.
      expect(mockCreateUser).toHaveBeenCalledWith({ email, password });
      // Check that createCustomToken was called with the UID from createUser.
      expect(mockCreateCustomToken).toHaveBeenCalledWith("123");
      // Ensure that generateEmailVerificationLink was called with the user's email.
      expect(mockGenerateEmailVerificationLink).toHaveBeenCalledWith(email);
    });
  });

  // Test suite for the resetPassword function of AuthService.
  describe("resetPassword", () => {
    it("should generate a password reset link for the provided email", async () => {
      const email = "<EMAIL>";

      await authService.resetPassword(email);

      // Verify that generatePasswordResetLink was called with the correct email.
      expect(mockGeneratePasswordResetLink).toHaveBeenCalledWith(email);
    });
  });

  // Test suite for the verifyToken function of AuthService.
  describe("verifyToken", () => {
    it("should verify the token and return the UID", async () => {
      const idToken = "validToken123";

      const result = await authService.verifyToken(idToken);

      // Assert that the token verification returns the expected UID.
      expect(result).toEqual({ uid: "123" });
      // Ensure that verifyIdToken was called with the provided ID token.
      expect(mockVerifyIdToken).toHaveBeenCalledWith(idToken);
    });
  });
});
