const mockUserSettings = {
  physicalFitnessGoals: {
    bodyWeightGoal: 0,
  },
  nutritionalGoals: {
    macroGoals: {
      carb: {
        dailyPercentage: 0.4,
        dailyGrams: 300,
        dailyCalories: 1200,
      },
      protein: {
        dailyPercentage: 0.3,
        dailyGrams: 225,
        dailyCalories: 900,
      },
      fat: {
        dailyPercentage: 0.3,
        dailyGrams: 100,
        dailyCalories: 900,
      },
    },
    calorieGoal: 3000,
  },
  profile: {
    birthday: "April 25, 2003",
    firstName: "<PERSON><PERSON>",
    lastName: "Moro<PERSON>",
    sex: "Male",
    bodyWeight: "185 lbs",
    email: "<EMAIL>",
    age: 21,
    height: {
      inches: 70,
      centimeters: "177.80",
    },
    pictureUrl:
      "https://firebasestorage.googleapis.com/v0/b/nutracompass-individual.appspot.com/o/profilePictures%2FmjowxvME9neE06qS1JLrXYjW7Vq1%2FprofilePic.jpg?alt=media&token=6065038c-5577-4be9-a832-39ef6c7106a3",
  },
  appAppearance: {
    isDark: true,
    theme: "Default",
  },
};
