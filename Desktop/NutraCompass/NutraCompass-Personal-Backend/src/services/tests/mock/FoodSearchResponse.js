const eggsApiResponse = {
  text: "eggs",
  parsed: [
    {
      food: {
        foodId: "food_bhpradua77pk16aipcvzeayg732r",
        label: "Egg",
        knownAs: "egg",
        nutrients: {
          ENERC_KCAL: 143.0,
          PROCNT: 12.6,
          FAT: 9.51,
          CHOCDF: 0.72,
          FIBTG: 0.0,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/a7e/a7ec7c337cb47c6550b3b118e357f077.jpg",
      },
    },
  ],
  hints: [
    {
      food: {
        foodId: "food_bhpradua77pk16aipcvzeayg732r",
        label: "Egg",
        knownAs: "egg",
        nutrients: {
          ENERC_KCAL: 143.0,
          PROCNT: 12.6,
          FAT: 9.51,
          CHOCDF: 0.72,
          FIBTG: 0.0,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/a7e/a7ec7c337cb47c6550b3b118e357f077.jpg",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 50.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 43.0,
          qualified: [
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_extra_large",
                  label: "extra large",
                },
              ],
              weight: 56.0,
            },
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_large",
                  label: "large",
                },
              ],
              weight: 50.0,
            },
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_small",
                  label: "small",
                },
              ],
              weight: 38.0,
            },
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_medium",
                  label: "medium",
                },
              ],
              weight: 44.0,
            },
          ],
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_jumbo",
          label: "Jumbo",
          weight: 63.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 243.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_liter",
          label: "Liter",
          weight: 1027.10093956848,
        },
      ],
    },
    {
      food: {
        foodId: "food_bhpradua77pk16aipcvzeayg732r",
        label: "Free Range & Organic Egg",
        knownAs: "egg",
        nutrients: {
          ENERC_KCAL: 143.0,
          PROCNT: 12.6,
          FAT: 9.51,
          CHOCDF: 0.72,
          FIBTG: 0.0,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/a7e/a7ec7c337cb47c6550b3b118e357f077.jpg",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 50.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 43.0,
          qualified: [
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_extra_large",
                  label: "extra large",
                },
              ],
              weight: 56.0,
            },
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_large",
                  label: "large",
                },
              ],
              weight: 50.0,
            },
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_small",
                  label: "small",
                },
              ],
              weight: 38.0,
            },
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_medium",
                  label: "medium",
                },
              ],
              weight: 44.0,
            },
          ],
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_jumbo",
          label: "Jumbo",
          weight: 63.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 243.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_liter",
          label: "Liter",
          weight: 1027.10093956848,
        },
      ],
    },
    {
      food: {
        foodId: "food_b0tmrglbmacqs7b0ikcorakgg0e4",
        label: "Fried Egg",
        knownAs: "fried egg",
        nutrients: {
          ENERC_KCAL: 196.0,
          PROCNT: 13.6,
          FAT: 14.8,
          CHOCDF: 0.83,
          FIBTG: 0.0,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/f8b/f8b60f2c6e9b015c5a5e692be56784dc.jpg",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 37.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 36.8,
          qualified: [
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_large",
                  label: "large",
                },
              ],
              weight: 46.0,
            },
          ],
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_bxox5rcbk6q86xbr5e10wbdnog4m",
        label: "Scrambled Eggs",
        knownAs: "scrambled eggs",
        nutrients: {
          ENERC_KCAL: 212.0,
          PROCNT: 13.8,
          FAT: 16.2,
          CHOCDF: 2.08,
          FIBTG: 0.0,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/258/2585852444c17fcf41c702cf6e4f396b.png",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 48.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
          label: "Egg",
          weight: 48.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 48.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_a2y52zfbr22uq1ah5thnqac607ft",
        label: "Hard-Boiled Eggs",
        knownAs: "hard-boiled eggs",
        nutrients: {
          ENERC_KCAL: 155.0,
          PROCNT: 12.6,
          FAT: 10.6,
          CHOCDF: 1.12,
          FIBTG: 0.0,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/e54/e54c012fabed0f9cf211a817d1e23c5c.jpg",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 50.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 40.0,
          qualified: [
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_large",
                  label: "large",
                },
              ],
              weight: 50.0,
            },
          ],
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_tablespoon",
          label: "Tablespoon",
          weight: 8.5,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 136.0,
          qualified: [
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_chopped",
                  label: "chopped",
                },
              ],
              weight: 136.0,
            },
          ],
        },
      ],
    },
    {
      food: {
        foodId: "food_btjcdjya6y5nqcbzn4npebp0krdt",
        label: "Poached Egg",
        knownAs: "poached egg",
        nutrients: {
          ENERC_KCAL: 143.0,
          PROCNT: 12.5,
          FAT: 9.47,
          CHOCDF: 0.71,
          FIBTG: 0.0,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/d4e/d4eb4e406ef6211dc314c00e724513ba.jpg",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 40.0,
          qualified: [
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_large",
                  label: "large",
                },
              ],
              weight: 50.0,
            },
          ],
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 40.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_bmkbi94an1olz0azsm5weag1kfa1",
        label: "Scrambled Egg",
        knownAs: "scrambled egg",
        nutrients: {
          ENERC_KCAL: 149.0,
          PROCNT: 9.99,
          FAT: 11.0,
          CHOCDF: 1.61,
          FIBTG: 0.0,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/6ba/6ba06ffdd40d166ee04f0590843b86de.jpg",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 48.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 48.8,
          qualified: [
            {
              qualifiers: [
                {
                  uri: "http://www.edamam.com/ontologies/edamam.owl#Qualifier_large",
                  label: "large",
                },
              ],
              weight: 61.0,
            },
          ],
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_tablespoon",
          label: "Tablespoon",
          weight: 13.7,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 220.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_awouizcadpjp6pboc6ea0blbwa85",
        label: "Egg Salad, Egg",
        knownAs: "EGG SALAD, EGG",
        nutrients: {
          ENERC_KCAL: 336.0,
          PROCNT: 9.350000381469727,
          FAT: 32.709999084472656,
          CHOCDF: 1.8700000047683716,
          FIBTG: 0.0,
        },
        brand: "SALAD",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel:
          "INGREDIENTS: EGGS; SOYBEAN OIL; CONTAINS LESS THAN 2% OF WATER; EGG YOLKS (EGG YOLKS; SALT); SUGAR; MUSTARD (DISTILLED VINEGAR; MUSTARD SEED; SALT; TURMERIC; SPICES); WHITE DISTILLED VINEGAR; SALT; LEMON JUICE CONCENTRATE; SPICE; PAPRIKA (COLOR).",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
            label: "Cup",
            quantity: 0.5,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 107.0,
          },
        ],
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 107.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 214.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_b0tr49zaphrncubxo3vuzbsfo8m3",
        label: "Eggs",
        knownAs: "Eggs",
        nutrients: {
          ENERC_KCAL: 152.9635780160468,
          PROCNT: 6.603578736624046,
          FAT: 9.546876491989666,
          CHOCDF: 10.818705648743176,
          FIBTG: 1.558267049265168,
        },
        category: "Generic meals",
        categoryLabel: "meal",
        foodContentsLabel:
          "eggs; whipping cream; garlic; cream cheese; parmesan; butter; pepper",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 145.32482226851866,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 145.32482226851866,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_milliliter",
          label: "Milliliter",
          weight: 1.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_bkhpmh3akh7l10a7ndfgebmdcg8y",
        label: "Egg Salad, Egg",
        knownAs: "EGG SALAD, EGG",
        nutrients: {
          ENERC_KCAL: 336.0,
          PROCNT: 0.0,
          FAT: 32.709999084472656,
          CHOCDF: 1.8700000047683716,
          FIBTG: 0.0,
        },
        brand: "SALAD",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel:
          "INGREDIENTS: EGGS; SOYBEAN OIL; CONTAINS LESS THAN 2% OF WATER; EGG YOLKS (EGG YOLKS; SALT); SUGAR; MUSTARD (DISTILLED VINEGAR; MUSTARD SEED; SALT; TURMERIC; SPICES); WHITE DISTILLED VINEGAR; SALT; LEMON JUICE CONCENTRATE; SPICE; PAPRIKA (COLOR).",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
            label: "Cup",
            quantity: 0.5,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 107.0,
          },
        ],
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 107.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 214.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_aer1rbbaqb1xu4aqicbe6b6iddyh",
        label: "Egg Salad, Egg",
        knownAs: "EGG SALAD, EGG",
        nutrients: {
          ENERC_KCAL: 230.0,
          PROCNT: 10.0,
          FAT: 19.0,
          CHOCDF: 5.0,
        },
        brand: "SALAD",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel:
          "HARD COOKED EGGS; MAYONNAISE (SOYBEAN OIL; WATER; EGG YOLKS; VINEGAR; SALT); CELERY; CRACKER MEAL (BLEACHED WHEAT FLOUR); MUSTARD (VINEGAR; WATER; MUSTARD SEEDS; SALT; TURMERIC; PAPRIKA; SPICE; GARLIC POWDER); SUGAR; SALT; WATER; VINEGAR; MODIFIED CORN STARCH; BLACK PEPPER; SODIUM BENZOATE (PRESERVATIVE); POTASSIUM SORBATE (PRESERVATIVE); LEMON JUICE CONCENTRATE; XANTHAN GUM.",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 100.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
            label: "Cup",
            quantity: 0.3333333333333333,
          },
        ],
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 100.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 300.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_budu8jqaufljzmbq0cuyzadpy6aw",
        label: "Egg Beaters  Egg Product  Egg Whites",
        knownAs: "Egg Beaters  Egg Product  Egg Whites",
        nutrients: {
          ENERC_KCAL: 49.18032786885246,
          PROCNT: 9.836065573770492,
          FAT: 0.0,
          CHOCDF: 1.639344262295082,
          FIBTG: 0.0,
        },
        brand: "Egg Beaters",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel: "Egg Whites",
        image:
          "https://www.edamam.com/food-img/e52/e522611330ccd8828976241b71425aca.jpg",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 61.0,
          },
        ],
        servingsPerContainer: 2.0,
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 61.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_package",
          label: "Package",
          weight: 122.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_agoq89xahqlvl1b2tcrsya9wo5mt",
        label: "Bread, Egg",
        knownAs: "Bread, egg",
        nutrients: {
          ENERC_KCAL: 287.0,
          PROCNT: 9.5,
          FAT: 6.0,
          CHOCDF: 47.8,
          FIBTG: 2.3,
        },
        category: "Generic foods",
        categoryLabel: "food",
        image:
          "https://www.edamam.com/food-img/5f6/5f64cc5bd5c3ddc3013ebe9a2e21b0c5.jpg",
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 82.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_slice",
          label: "Slice",
          weight: 40.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_loaf",
          label: "Loaf",
          weight: 480.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 60.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_unit",
          label: "Whole",
          weight: 480.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_roll",
          label: "Roll",
          weight: 57.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_arn3j5hbylpxh5a32cyecbr8calu",
        label: "Eggs",
        knownAs: "EGGS",
        nutrients: {
          ENERC_KCAL: 158.0,
          PROCNT: 13.15999984741211,
          FAT: 10.529999732971191,
          CHOCDF: 2.630000114440918,
        },
        brand: "Mr. Beverages Old Time Cocktail Mixes",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel: "EGGS.",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
            label: "Egg",
            quantity: 1.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 38.0,
          },
        ],
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
          label: "Egg",
          weight: 38.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 38.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_as4m80vbni0k9iacegkdebbvyhmn",
        label: "Eggs",
        knownAs: "EGGS",
        nutrients: {
          ENERC_KCAL: 143.0,
          PROCNT: 12.5,
          FAT: 8.930000305175781,
          CHOCDF: 0.0,
        },
        brand: "Costco Companies Inc.",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel: "EGGS.",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 56.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
            label: "Egg",
            quantity: 1.0,
          },
        ],
        servingsPerContainer: 24.0,
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
          label: "Egg",
          weight: 56.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 56.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_package",
          label: "Package",
          weight: 1344.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_ao5zke5aq85e1oavdi4kla1cms6g",
        label: "Eggs",
        knownAs: "Eggs",
        nutrients: {
          ENERC_KCAL: 123.45886682353144,
          PROCNT: 10.582188584874125,
          FAT: 8.818490487395103,
          CHOCDF: 0.0,
          FIBTG: 0.0,
        },
        brand: "Pete And Gerry's Organics, Llc.",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel: "Eggs",
        image:
          "https://www.edamam.com/food-img/7bc/********************************.png",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 50.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
            label: "Ounce",
            quantity: 2.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
            label: "Egg",
            quantity: 1.0,
          },
        ],
        servingsPerContainer: 12.000000000000002,
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
          label: "Egg",
          weight: 56.69904625,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 56.69904625,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_package",
          label: "Package",
          weight: 680.3885550000001,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_bewy3imbmidf13aaqzt1faog1mb5",
        label: "Eggs",
        knownAs: "Eggs",
        nutrients: {
          ENERC_KCAL: 140.0,
          PROCNT: 12.0,
          FAT: 10.0,
          CHOCDF: 0.0,
        },
        brand: "Safeway Inc.",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel: "Eggs",
        image:
          "https://www.edamam.com/food-img/910/********************************.jpg",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 50.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
            label: "Egg",
            quantity: 1.0,
          },
        ],
        servingsPerContainer: 6.0,
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
          label: "Egg",
          weight: 50.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 50.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_package",
          label: "Package",
          weight: 300.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_b7nqvlxag1olezb7sdclma30yvfv",
        label: "Eggs",
        knownAs: "Eggs",
        nutrients: {
          ENERC_KCAL: 120.0,
          PROCNT: 12.0,
          FAT: 8.0,
          CHOCDF: 0.0,
        },
        brand: "Eggland's Best, Inc.",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel: "Eggs",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
            label: "Egg",
            quantity: 1.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 50.0,
          },
        ],
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
          label: "Egg",
          weight: 50.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 50.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_biliy3kb0gn5eibahwfp8bektyfm",
        label: "Eggs",
        knownAs: "EGGS",
        nutrients: {
          ENERC_KCAL: 143.0,
          PROCNT: 12.5,
          FAT: 8.930000305175781,
          CHOCDF: 0.0,
        },
        brand: "Pete and Gerry's Organics",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel: "FRESH BROWN EGGS",
        image:
          "https://www.edamam.com/food-img/b86/********************************.jpg",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 56.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
            label: "Egg",
            quantity: 1.0,
          },
        ],
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_egg",
          label: "Egg",
          weight: 56.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 56.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
    {
      food: {
        foodId: "food_bfksec1bjp5s8zbbrj38dal0m1dv",
        label: "Streit's Egg Flakes Egg",
        knownAs: "Streit's Egg Flakes Egg",
        nutrients: {
          ENERC_KCAL: 381.8181818181818,
          PROCNT: 14.545454545454545,
          FAT: 3.6363636363636362,
          CHOCDF: 70.9090909090909,
          FIBTG: 3.6363636363636362,
        },
        brand: "Streit's",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel:
          "DURUM FLOUR (WHEAT); EGG YOLKS OR EGGS; NIACIN; IRON (FERROUS SULFATE); THIAMINE MONONITRATE (VITAMIN B1); RIBOFLAVIN (VITAMIN B2) AND FOLIC ACID.",
        image:
          "https://www.edamam.com/food-img/19c/********************************.jpg",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 55.0,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
            label: "Cup",
            quantity: 0.33000001311302185,
          },
        ],
        servingsPerContainer: 4.0,
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 55.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_package",
          label: "Package",
          weight: 220.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_cup",
          label: "Cup",
          weight: 166.66666004392863,
        },
      ],
    },
    {
      food: {
        foodId: "food_b0aq1myb7dnbmrbcxcmema477j44",
        label: "Egg Noodles, Tagliatelle, Egg",
        knownAs: "EGG NOODLES, TAGLIATELLE, EGG",
        nutrients: {
          ENERC_KCAL: 365.0,
          PROCNT: 12.699999809265137,
          FAT: 3.9700000286102295,
          CHOCDF: 71.43000030517578,
          FIBTG: 3.200000047683716,
        },
        brand: "LUCIANA MOSCONI",
        category: "Packaged foods",
        categoryLabel: "food",
        foodContentsLabel: "DURUM WHEAT SEMOLINA; FRESH EGG 28%",
        servingSizes: [
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
            label: "Ounce",
            quantity: 2.200000047683716,
          },
          {
            uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
            label: "Gram",
            quantity: 63.0,
          },
        ],
      },
      measures: [
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_serving",
          label: "Serving",
          weight: 62.368952226810606,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_gram",
          label: "Gram",
          weight: 1.0,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_ounce",
          label: "Ounce",
          weight: 28.349523125,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_pound",
          label: "Pound",
          weight: 453.59237,
        },
        {
          uri: "http://www.edamam.com/ontologies/edamam.owl#Measure_kilogram",
          label: "Kilogram",
          weight: 1000.0,
        },
      ],
    },
  ],
  _links: {
    next: {
      title: "Next page",
      href: "https://v1.edamam.com/v1/food-database/v2/parser?session=42&ingr=eggs&app_id=b0413aad&app_key=8179d530ea996907b35c3323f4b89c8e",
    },
  },
};

module.exports = {
  eggsApiResponse,
};
