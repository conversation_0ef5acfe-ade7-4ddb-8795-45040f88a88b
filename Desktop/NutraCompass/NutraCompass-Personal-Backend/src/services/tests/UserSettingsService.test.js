const UserSettingsService = require("../UserSettingsService.js");
const admin = require("../../config/firebase-admin.js");
const { mockUserSettings } = require("./mock/GetUserSettingsResponse.js"); // Adjust path as necessary
jest.mock("../../config/firebase-admin.js");

describe("UserSettingsService", () => {
  let service;

  beforeEach(() => {
    service = new UserSettingsService();
    admin.firestore = jest.fn().mockReturnValue({
      doc: jest.fn().mockReturnValue({
        get: jest.fn(),
        set: jest.fn(),
      }),
    });
  });

  describe("getSettings", () => {
    it("should fetch user settings successfully", async () => {
      admin
        .firestore()
        .doc()
        .get.mockResolvedValue({
          exists: true,
          data: () => mockUserSettings,
        });

      const settings = await service.getSettings("user123");
      expect(settings).toEqual(mockUserSettings);
    });

    it("should throw an error if settings not found", async () => {
      admin.firestore().doc().get.mockResolvedValue({ exists: false });
      await expect(service.getSettings("user123")).rejects.toThrow(
        "Settings not found"
      );
    });
  });

  describe("updateSettings", () => {
    it("should update user settings successfully", async () => {
      admin.firestore().doc().set.mockResolvedValue();
      await expect(
        service.updateSettings("user123", mockUserSettings)
      ).resolves.toBeUndefined();
    });

    it("should throw an error when update fails", async () => {
      admin
        .firestore()
        .doc()
        .set.mockRejectedValue(new Error("Failed to update"));
      await expect(
        service.updateSettings("user123", mockUserSettings)
      ).rejects.toThrow("Failed to update settings");
    });
  });
});
