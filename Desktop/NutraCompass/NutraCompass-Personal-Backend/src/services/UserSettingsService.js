const admin = require("../config/firebase-admin");

class UserSettingsService {
  static instance = null;

  constructor() {
    if (!UserSettingsService.instance) {
      UserSettingsService.instance = this;
    }
    return UserSettingsService.instance;
  }

  /**
   * Fetches user settings from Firestore using the user's UID with retry logic
   * to handle Firestore's eventual consistency model.
   * @param {string} uid - User's UID
   * @returns {Promise<Object>} User settings data
   * @throws {Error} If settings cannot be retrieved after max attempts
   */
  async getSettings(uid) {
    const MAX_ATTEMPTS = 5;
    const BASE_DELAY_MS = 300;
    let attempts = 0;

    while (attempts < MAX_ATTEMPTS) {
      try {
        const userSettingsDocRef = admin.firestore().doc(`users/${uid}`);
        const userSettingsDoc = await userSettingsDocRef.get();

        if (userSettingsDoc.exists) {
          //console.log(`Settings found for ${uid} on attempt ${attempts + 1}`);
          return userSettingsDoc.data();
        }

        // Only throw on final attempt
        if (attempts === MAX_ATTEMPTS - 1) {
          throw new Error(`Settings not found after ${MAX_ATTEMPTS} attempts`);
        }

        // Exponential backoff with jitter
        const delay =
          BASE_DELAY_MS * Math.pow(2, attempts) + Math.random() * 100;
        console.log(`Retry ${attempts + 1} for ${uid}, waiting ${delay}ms`);
        await new Promise((resolve) => setTimeout(resolve, delay));

        attempts++;
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed:`, error.message);
        if (attempts === MAX_ATTEMPTS - 1) {
          throw new Error(`Failed to fetch settings: ${error.message}`);
        }
      }
    }

    // Fallback error if all attempts fail
    throw new Error(
      `Settings fetch failed for ${uid} after ${MAX_ATTEMPTS} retries`
    );
  }

  /**
   * Updates user settings in Firestore using the user's UID and the provided settings object.
   * @param {string} uid - User's UID.
   * @param {Object} settings - New settings to be saved.
   * @returns {Promise<void>}
   */
  async updateSettings(uid, settings) {
    try {
      const userSettingsDocRef = admin.firestore().doc(`users/${uid}`);
      await userSettingsDocRef.set(settings, { merge: true });
    } catch (error) {
      console.error("Settings Update Error: ", error);
      throw new Error("Failed to update settings: " + error.message);
    }
  }
}

module.exports = new UserSettingsService(); // Automatically create and export a singleton instance
