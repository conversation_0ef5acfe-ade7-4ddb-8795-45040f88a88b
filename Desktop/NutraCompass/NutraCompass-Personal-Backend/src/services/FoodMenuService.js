const admin = require("../config/firebase-admin");
const db = admin.firestore();
const bucket = admin.storage().bucket();
const uuid = require("uuid");
const fs = require("fs");

class FoodMenuService {
  constructor() {
    this.db = db;
    this.bucket = bucket;
  }

  /**
   * Retrieves all custom meals for a given user. Validates the user ID before querying the database.
   * @param {string} userId - User's unique identifier.
   * @returns {Promise<Array>} A list of custom meals.
   */
  async getCustomMeals(userId) {
    if (!userId) {
      throw new Error("Invalid user ID provided.");
    }
    try {
      const collectionRef = this.db.collection(`users/${userId}/customMeals`);
      const snapshot = await collectionRef.get();
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error("Failed to fetch custom meals:", error);
      throw new Error("Error retrieving custom meals.");
    }
  }

  /**
   * Adds or updates a custom meal in the database. Optionally handles image file upload if provided.
   * @param {string} userId - User's unique identifier.
   * @param {Object} customMeal - The custom meal data. Must not include the meal ID for new meals.
   * @param {Object} imageFile - Optional image file associated with the custom meal.
   * @returns {Promise<Object>} A promise that resolves with the operation result.
   */
  async saveCustomMeal(userId, customMeal, imageFile) {
    if (!userId || !customMeal) {
      throw new Error(
        "Missing required parameters to add or update custom meal."
      );
    }
    try {
      const collectionRef = this.db.collection(`users/${userId}/customMeals`);
      if (!customMeal.id) {
        customMeal.id = uuid.v4(); // Assign a new UUID if not provided
      }

      const docRef = collectionRef.doc(customMeal.id);
      const doc = await docRef.get();

      if (doc.exists) {
        const existingData = doc.data();
        const existingImageUrl = existingData.mealImageUrl;

        // If there's an existing image but no new image file is provided, delete the old image
        if (existingImageUrl && !imageFile) {
          const oldImagePath = `customMeals/${userId}/${customMeal.id}`;
          await this.deleteImage(oldImagePath);
          // Remove the imageUrl from the customMeal object to reflect the deletion
          customMeal.mealImageUrl = null;
        }
      }

      if (imageFile) {
        // Upload the new image and update the customMeal with the new image URL
        const imagePath = `customMeals/${userId}/${customMeal.id}`;
        const imageUrl = await this.uploadImage(imageFile, imagePath);
        customMeal.mealImageUrl = imageUrl;
      }

      // Merge the new data with existing data in Firestore to support partial updates
      await docRef.set(customMeal, { merge: true });
      return {
        message: "Custom meal saved successfully",
        id: customMeal.id,
        ...customMeal,
      };
    } catch (error) {
      console.error("Failed to save custom meal:", error);
      throw new Error("Error saving custom meal.");
    }
  }

  /**
   * Deletes a specific custom meal, ensuring that the user ID and meal ID are valid.
   * @param {string} userId - User's unique identifier.
   * @param {string} mealId - The specific meal's ID to delete.
   * @returns {Promise<void>}
   */
  async deleteCustomMeal(userId, mealId) {
    if (!userId || !mealId) {
      throw new Error("Missing required parameters to delete custom meal.");
    }
    try {
      const docRef = this.db.doc(`users/${userId}/customMeals/${mealId}`);
      const doc = await docRef.get();

      if (doc.exists && doc.data().mealImageUrl) {
        // Delete the associated image from storage
        const imagePath = `customMeals/${userId}/${mealId}`;
        await this.deleteImage(imagePath);
      }

      await docRef.delete();
    } catch (error) {
      console.error("Failed to delete custom meal:", error);
      throw new Error("Error deleting custom meal.");
    }
  }

  /**
   * Uploads an image file to Google Cloud Storage and makes it publicly accessible.
   * @param {Object} imageFile - The image file to upload.
   * @param {string} path - The storage path within the bucket.
   * @returns {Promise<string>} A promise that resolves with the public URL of the uploaded image.
   */
  async uploadImage(imageFile, path) {
    const blob = this.bucket.file(path);
    const blobStream = blob.createWriteStream({
      metadata: {
        contentType: imageFile.mimetype, // Ensure correct content type is set for image
      },
    });

    return new Promise((resolve, reject) => {
      blobStream.on("error", reject); // Handle upload errors
      blobStream.on("finish", async () => {
        await blob.makePublic(); // Make the image publicly accessible
        // Construct the public URL for the image
        resolve(
          `https://storage.googleapis.com/${
            this.bucket.name
          }/${encodeURIComponent(blob.name)}`
        );
      });
      blobStream.end(imageFile.buffer); // Start the upload
    });
  }

  async deleteImage(imagePath) {
    const file = bucket.file(imagePath);
    await file.delete();
  }
}

module.exports = new FoodMenuService();
