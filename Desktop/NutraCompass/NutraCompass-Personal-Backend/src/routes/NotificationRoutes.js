const express = require("express");
const router = express.Router();
const NotificationController = require("../controllers/NotificationController.js");
const { checkAuthAndOwnership } = require("../middleware/AuthMiddleware");

// =============================================
// Core Notification Settings Routes
// =============================================

router.put(
  "/v1/notification-settings/:userId",
  checkAuthAndOwnership,
  NotificationController.updateNotificationSettings
);

router.get(
  "/v1/notification-settings/:userId",
  checkAuthAndOwnership,
  NotificationController.getNotificationSettings
);

// =============================================
// Device Registration Subsystem
// =============================================

router.post(
  "/v1/notification-settings/:userId/push-token",
  checkAuthAndOwnership,
  NotificationController.registerPushToken
);

// =============================================
// Notification Lifecycle Management
// =============================================

router.post(
  "/v1/notification-settings/:userId/cancel-meal-reminders",
  checkAuthAndOwnership,
  NotificationController.cancelMealReminders
);

// =============================================
// Security Header Enforcement
// =============================================

// Recommended: Add security headers middleware here
// router.use(helmet());

module.exports = router;
