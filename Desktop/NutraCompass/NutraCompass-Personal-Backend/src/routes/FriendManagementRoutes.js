const express = require("express");
const router = express.Router();
const FriendManagementController = require("../controllers/FriendManagementController");

/**
 * GET: Fetch recommended users for the logged-in user.
 * Excludes the requester to provide a list of potential new connections.
 */
router.get(
  "/v1/users/recommended",
  FriendManagementController.getRecommendedUsers
);

/**
 * GET: Search users based on a text query.
 * This route provides a search functionality for finding users by names or usernames.
 */
router.get("/v1/users/search", FriendManagementController.searchUsers);

/**
 * POST: Send a friend request to another user.
 * This route handles the initiation of a friendship request from one user to another.
 */
router.post(
  "/v1/:receiverId/add",
  FriendManagementController.sendFriendRequest
);

/**
 * POST: Cancel a friend request to another user.
 * This route handles the cancellation of a friendship request from one user to another.
 */
router.post(
  "/v1/:receiverId/cancel",
  FriendManagementController.cancelFriendRequest
);

/**
 * POST: Accept a friend request.
 * This route allows a user to accept a pending friend request.
 */
router.post(
  "/v1/:senderId/accept",
  FriendManagementController.acceptFriendRequest
);

/**
 * POST: Reject a friend request.
 * This route allows a user to reject a pending friend request.
 */
router.post(
  "/v1/:senderId/reject",
  FriendManagementController.rejectFriendRequest
);

/**
 * DELETE: Remove a friend connection.
 * This route allows a user to remove an existing friend from their friends list.
 */
router.delete("/v1/:friendId/remove", FriendManagementController.removeFriend);

/**
 * POST: Block a user.
 * This route enables a user to block another user, preventing them from making contact.
 */
router.post("/v1/:blockedUserId/block", FriendManagementController.blockUser);

/**
 * DELETE: Unblock a user.
 * This route enables a user to unblock another user, allowing them to make contact again.
 */
router.delete(
  "/v1/:blockedUserId/unblock",
  FriendManagementController.unblockUser
);

/**
 * GET: Retrieve the blocked list of a user.
 * This route provides the list of blocked users associated with the logged-in user.
 */
router.get("/v1/blocked", FriendManagementController.getBlockedUsersList);

/**
 * GET: Retrieve the friends list of a user.
 * This route provides the list of friends associated with the logged-in user.
 */
router.get("/v1/friends", FriendManagementController.getFriendsList);

/**
 * GET: Fetch incoming friend requests.
 * This route is used to retrieve all pending friend requests directed to the logged-in user.
 */
router.get(
  "/v1/friend-requests/incoming",
  FriendManagementController.getIncomingRequests
);

/**
 * GET: Fetch outgoing friend requests.
 * This route is used to retrieve all pending friend requests directed from the logged-in user.
 */
router.get(
  "/v1/friend-requests/outgoing",
  FriendManagementController.getOutgoingRequests
);

module.exports = router;
