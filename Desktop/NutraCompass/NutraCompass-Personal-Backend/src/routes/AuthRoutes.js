const express = require("express");
const AuthController = require("../controllers/AuthController"); // Import the AuthController to handle route logic.
const router = express.Router();

/**
 * Route to verify the authentication token provided by the user.
 * This is used to authenticate and potentially reauthorize the client.
 * POST: /verify-token
 * Receives an ID token from the client and verifies its validity, returning the associated user's UID if valid.
 * This endpoint is typically used to confirm the user's current session validity.
 */
router.post(
  "/v1/authentication/verify-token",
  AuthController.verifyUserSession
);

/**
 * Route to register a new user with email, password, and default settings.
 * Upon successful registration, it returns a token for immediate authentication and sends a verification email.
 * POST: /register
 * Handles user registration using Firebase, stores default settings in Firestore, and returns a custom token for authentication.
 */
router.post("/v1/authentication/register", AuthController.registerUser);

/**
 * POST route to handle the bulk registration of users.
 * This endpoint accepts an array of user data and passes it to the AuthController to process multiple registrations.
 * Each user's data should include necessary details such as email, password, and default settings.
 */
router.post(
  "/v1/authentication/register-multiple-users",
  AuthController.registerMultipleUsers
);

/**
 * Route to validate an existing user's authentication token.
 * This route does not handle traditional sign-ins but verifies a Firebase ID token provided by the client.
 * POST: /validate-session
 * Validates the token and returns the user's UID if the token is valid.
 * This route name is updated to better reflect that it's used for session validation rather than a traditional sign-in.
 */
router.post(
  "/v1/authentication/validate-session",
  AuthController.verifyUserSession
);

/**
 * Route to initiate a password reset or change process.
 * POST: /password-reset
 * Accepts an email address and sends a password reset link to that email.
 */
router.post("/v1/authentication/password-reset", AuthController.resetPassword);

/**
 * DELETE route to handle account deletion.
 * Deletes all user data and authentication records.
 */
router.delete(
  "/v1/authentication/delete-account",
  AuthController.deleteAccount
);

/**
 * Route to check if an email already exists in Firebase Authentication.
 * POST: /v1/authentication/check-email
 * Receives an email address and checks if it exists in Firebase Authentication.
 * Returns a boolean indicating the existence of the email.
 */
router.post("/v1/authentication/check-email", AuthController.checkEmailExists);

/**
 * Route to send a verification code to the user's email.
 * This is used for email verification during the registration process.
 * POST: /send-verification
 * Accepts the user's email and sends a one-time verification code.
 */
router.post(
  "/v1/authentication/send-verification",
  AuthController.sendVerificationCode
);

/**
 * Route to verify the code entered by the user.
 * POST: /verify-code
 * Accepts the user's email and the verification code, validating its correctness.
 */
router.post("/v1/authentication/verify-code", AuthController.verifyCode);

/**
 * Route to resend the verification code to the user's email.
 * POST: /resend-code
 * Accepts the user's email and sends another one-time verification code.
 */
router.post("/v1/authentication/resend-code", AuthController.resendCode);

// Export the router to be mounted by the main application.
module.exports = router;
