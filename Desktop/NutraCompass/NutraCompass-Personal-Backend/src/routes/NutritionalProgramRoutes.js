// NutritionalProgramRoutes.js
const express = require("express");
const NutritionalProgramController = require("../controllers/NutritionalProgramController.js");
const upload = require("../middleware/MulterMiddlewareForImages.js"); // Using the same multer middleware for image uploads

const router = express.Router();

/**
 * @route POST /api/v1/calculate-nutritional-goals
 * @desc Calculate the daily nutritional goals based on the user's metrics and goal.
 * @access Public or Authenticated (Depending on your setup)
 */
router.post(
  "/v1/calculate-nutritional-goals",
  NutritionalProgramController.calculateDailyNutritionalGoals
);

/**
 * @route POST /api/v1/generate-nutritional-program
 * @desc Generate a complete nutritional program, including checkpoints and goals.
 * @access Public or Authenticated (Depending on your setup)
 */
router.post(
  "/v1/generate-nutritional-program",
  NutritionalProgramController.generateNutritionalProgram
);

/**
 * @route POST /api/v1/activate-nutritional-program
 * @desc Activates the nutritional program for a user, marking it as active and saving it in the 'activeNutritionalPrograms' collection.
 * @access Public or Authenticated (Depending on your setup)
 */
router.post(
  "/v1/activate-nutritional-program",
  NutritionalProgramController.activateNutritionalProgram
);

/**
 * @route POST /api/v1/deactivate-nutritional-program
 * @desc Deactivates the nutritional program for a user, moving it from the 'activeNutritionalPrograms' collection to the 'inactiveNutritionalPrograms' collection.
 * @access Public or Authenticated (Depending on your setup)
 */
router.post(
  "/v1/deactivate-nutritional-program",
  NutritionalProgramController.deactivateNutritionalProgram
);

/**
 * @route GET /api/v1/active-nutritional-program
 * @desc Fetch the currently active nutritional program for a given user.
 * @access Authenticated
 */
router.get(
  "/v1/active-nutritional-program",
  NutritionalProgramController.getActiveProgram
);

/**
 * @route GET /api/v1/inactive-nutritional-programs
 * @desc Fetch all inactive nutritional programs for a given user.
 * @access Authenticated
 */
router.get(
  "/v1/inactive-nutritional-programs",
  NutritionalProgramController.getInactivePrograms
);

/**
 * @route DELETE /api/v1/users/:userId/nutritional-programs/:programId
 * @desc Delete a single inactive nutritional program
 * @access Protected
 */
router.delete(
  "/v1/users/:userId/nutritional-programs/:programId",
  NutritionalProgramController.deleteNutritionalProgram
);

/**
 * @route DELETE /api/v1/users/:userId/nutritional-programs
 * @desc Batch delete multiple inactive nutritional programs
 * @access Protected
 * @body    { programIds: [ "programId1", "programId2", ... ] }
 */
router.delete(
  "/v1/users/:userId/nutritional-programs",
  NutritionalProgramController.deleteNutritionalProgramsBatch
);

/**
 * @route POST /api/v1/active-nutritional-program/checkpoint/:checkpointId/log-weight
 * @desc Log the user's weight for a specific checkpoint in an active nutritional program.
 * @access Authenticated
 */
router.post(
  "/v1/active-nutritional-program/checkpoint/:checkpointId/log-weight",
  NutritionalProgramController.logCheckpointWeight
);

/**
 * @route POST /api/v1/active-nutritional-program/checkpoint/:checkpointId/upload-progress-picture
 * @desc Upload a progress picture for a specific checkpoint in an active nutritional program.
 * @access Authenticated
 */
router.post(
  "/v1/active-nutritional-program/checkpoint/:checkpointId/upload-progress-picture",
  upload.single("progressPicture"), // 'progressPicture' is the field name in the form
  NutritionalProgramController.uploadProgressPicture
);

/**
 * @route DELETE /api/v1/active-nutritional-program/checkpoint/:checkpointId/delete-progress-picture
 * @desc Delete a progress picture for a specific checkpoint in an active nutritional program.
 * @access Authenticated
 */
router.delete(
  "/v1/active-nutritional-program/checkpoint/:checkpointId/delete-progress-picture",
  NutritionalProgramController.deleteProgressPicture
);

module.exports = router;
