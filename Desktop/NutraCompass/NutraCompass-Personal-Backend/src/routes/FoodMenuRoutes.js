const express = require("express");
const router = express.Router();
const upload = require("../middleware/MulterMiddlewareForImages.js"); // Import your multer configuration
const FoodMenuController = require("../controllers/FoodMenuController");

// Handling custom meals retrieval
router.get(
  "/v1/food/menu/:userId/custom-meals",
  FoodMenuController.getCustomMeals
);

// Route to add or update custom meals using POST. The presence of an ID in the body determines add or update.
// Route to upload a custom meal image
router.post(
  "/v1/food/menu/:userId/custom-meals",
  upload.single("mealImage"), // 'mealImage' is the name of the field in the form
  FoodMenuController.saveCustomMeal
);

// Delete a custom meal
router.delete(
  "/v1/food/menu/:userId/custom-meals/:mealId",
  FoodMenuController.deleteCustomMeal
);

module.exports = router;
