const admin = require("firebase-admin");
const { adminConfig, storageBucket } = require("../../configs.js");

// Unified initialization for all environments
if (!admin.apps?.length) {
  // Auto-config for Firebase
  if (process.env.FIREBASE_CONFIG) {
    admin.initializeApp();
    admin.app().storage().bucket(storageBucket);
  }
  // Manual config (Heroku/local)
  else {
    const serviceAccount = JSON.parse(adminConfig || "{}"); // Firebase service account file
    if (!serviceAccount.project_id || !serviceAccount.private_key) {
      throw new Error(
        "Missing Firebase Admin credentials. Verify FIREBASE_ADMIN_CREDENTIALS_JSON is set."
      );
    }
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      storageBucket: storageBucket,
    });
  }
}

module.exports = admin;
