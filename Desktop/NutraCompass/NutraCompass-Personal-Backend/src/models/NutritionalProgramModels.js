const Joi = require("joi");

/**
 * Schema for the "Basic" nutritional program fields.
 * - Using `convert: true` in the controller ensures strings like "5" become numbers automatically.
 */
const BasicProgramSchema = Joi.object({
  programType: Joi.string().valid("Basic").required(),
  sex: Joi.string().valid("Male", "Female").required(),
  heightFeet: Joi.number().precision(1).required(),
  heightInches: Joi.number().precision(1).required(),
  weight: Joi.number().precision(1).required(),
  age: Joi.number().required(),
  activityLevel: Joi.string().required(),
  goal: Joi.string().required(),
  goalWeight: Joi.number().required(),
  startDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      "string.pattern.base": "startDate must be in YYYY-MM-DD format.",
    }),

  endDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      "string.pattern.base": "startDate must be in YYYY-MM-DD format.",
    }),
});

module.exports = {
  BasicProgramSchema,
  // Future: AIMealPlanSchema, VoiceAssistedSchema, etc.
};
