/**
 * NutrientUtils.js
 *
 * Provides utility functions for processing nutrient data for food items.
 * This includes transforming raw nutrient data from external API responses into
 * a structured format, handling specific nutrient calculations for different types of food items,
 * and supporting the management of custom meals by processing aggregate nutrient data.
 */

class NutrientUtils {
  // Process raw API nutrient data into a structured format
  static processNutrientData(nutrientData) {
    if (!nutrientData || !nutrientData.totalNutrients) return {};
    const processedNutrients = {};
    Object.entries(nutrientData.totalNutrients).forEach(([key, value]) => {
      processedNutrients[key] = {
        label: NutrientUtils.getNutrientLabel(key),
        quantity: parseFloat(value.quantity).toFixed(2),
        unit: value.unit,
      };
    });
    return processedNutrients;
  }

  // Process nutrients into core, vitamin, and mineral groups based on predefined keys
  static processCoreNutrients(nutrientData) {
    return NutrientUtils.processSpecificNutrients(nutrientData, [
      "ENERC_KCAL",
      "CHOCDF",
      "PROCNT",
      "FAT",
      "FASAT",
      "FATRN",
      "FAPU",
      "FAMS",
      "CHOLE",
      "FIBTG",
      "SUGAR",
    ]);
  }

  static processVitamins(nutrientData) {
    return NutrientUtils.processSpecificNutrients(nutrientData, [
      "VITA_RAE",
      "VITC",
      "VITD",
      "TOCPHA",
      "VITK1",
      "THIA",
      "RIBF",
      "NIA",
      "VITB6A",
      "FOLDFE",
      "VITB12",
    ]);
  }

  static processMinerals(nutrientData) {
    return NutrientUtils.processSpecificNutrients(nutrientData, [
      "CA",
      "FE",
      "MG",
      "P",
      "K",
      "NA",
      "ZN",
    ]);
  }

  // Helper to process specific nutrients from a set of keys
  static processSpecificNutrients(nutrientData, keys) {
    const nutrients = {};
    keys.forEach((key) => {
      const nutrient = nutrientData.totalNutrients[key];
      if (nutrient) {
        nutrients[key] = {
          label: NutrientUtils.getNutrientLabel(key),
          quantity: parseFloat(nutrient.quantity).toFixed(2),
          unit: nutrient.unit,
        };
      }
    });
    return nutrients;
  }

  // Map nutrient keys to human-readable labels
  static getNutrientLabel(nutrientKey) {
    const labels = {
      ENERC_KCAL: "Calories",
      CHOCDF: "Carbohydrates",
      PROCNT: "Protein",
      FAT: "Fat",
      FASAT: "Saturated Fat",
      FATRN: "Trans Fat",
      FAPU: "Polyunsaturated Fat",
      FAMS: "Monounsaturated Fat",
      CHOLE: "Cholesterol",
      FIBTG: "Fiber",
      SUGAR: "Sugars",
      VITA_RAE: "Vitamin A",
      VITC: "Vitamin C",
      VITD: "Vitamin D",
      TOCPHA: "Vitamin E",
      VITK1: "Vitamin K1",
      THIA: "Thiamine (B1)",
      RIBF: "Riboflavin (B2)",
      NIA: "Niacin (B3)",
      VITB6A: "Vitamin B6",
      FOLDFE: "Folate (B9)",
      VITB12: "Vitamin B12",
      CA: "Calcium",
      FE: "Iron",
      MG: "Magnesium",
      P: "Phosphorus",
      K: "Potassium",
      NA: "Sodium",
      ZN: "Zinc",
    };
    return labels[nutrientKey] || "Unknown Nutrient";
  }
}

module.exports = NutrientUtils;
