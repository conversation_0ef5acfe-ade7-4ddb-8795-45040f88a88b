const { zonedTimeToUtc, utcToZonedTime, format } = require("date-fns-tz");
const { addDays, parseISO } = require("date-fns");

class TimezoneError extends Error {
  constructor(message) {
    super(message);
    this.name = "TimezoneError";
  }
}

/**
 * Converts local time string to UTC components
 * @param {string} localTime - Format "HH:mm" (e.g., "09:00")
 * @param {string} timezone - IANA timezone (e.g., "America/New_York")
 * @returns {Object} { utcHours: number, utcMinutes: number }
 */
function parseLocalTime(localTime, timezone) {
  try {
    // Create date object for next occurrence of this local time
    const [hours, minutes] = localTime.split(":").map(Number);
    const now = new Date();
    const localDate = utcToZonedTime(now, timezone);

    // Create base time in target timezone
    let targetDate = new Date(localDate);
    targetDate.setHours(hours, minutes, 0, 0);

    // If time already passed today, use tomorrow
    if (targetDate < localDate) {
      targetDate = addDays(targetDate, 1);
    }

    // Convert to UTC equivalent
    const utcDate = zonedTimeToUtc(targetDate, timezone);

    return {
      utcHours: utcDate.getUTCHours(),
      utcMinutes: utcDate.getUTCMinutes(),
      isoString: utcDate.toISOString(),
      originalLocalTime: localTime,
      timezone,
    };
  } catch (error) {
    throw new TimezoneError(
      `Failed parsing ${localTime} in ${timezone}: ${error.message}`
    );
  }
}

/**
 * Validates timezone against IANA database
 */
function validateTimezone(timezone) {
  try {
    format(new Date(), "zzzz", { timeZone: timezone });
    return true;
  } catch {
    return false;
  }
}

module.exports = {
  parseLocalTime,
  validateTimezone,
  TimezoneError,
};
