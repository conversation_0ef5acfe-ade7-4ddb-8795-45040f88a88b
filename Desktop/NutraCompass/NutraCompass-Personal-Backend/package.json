{"name": "Backend", "scripts": {"start": "cross-env NODE_ENV=production node src/server.js", "dev": "cross-env NODE_ENV=development nodemon src/server.js", "test": "cross-env NODE_ENV=test jest", "build": "echo 'Build completed'", "monitor": "heroku logs --tail -a nutracompass-personal-dev | grep -E 'Redis|LIMIT'"}, "engines": {"node": "20.x", "npm": "10.x"}, "dependencies": {"@google-cloud/tasks": "^5.5.0", "@mailchimp/mailchimp_transactional": "^1.0.59", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto": "^1.0.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "expo-server-sdk": "^3.14.0", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2", "joi": "^17.13.3", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.9", "openai": "^4.63.0", "rate-limit-redis": "^4.2.0", "redis": "^4.7.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0"}}