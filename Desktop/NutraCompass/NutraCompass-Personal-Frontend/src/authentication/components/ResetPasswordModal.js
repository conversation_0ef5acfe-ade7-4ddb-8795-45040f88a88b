import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { Card, Button } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../../context/ThemeContext.js";
import { useAuth } from "../context/AuthContext.js";

const ResetPasswordModal = ({ visible, onClose }) => {
  const { theme } = useThemeContext();
  const [email, setEmail] = useState("");
  const { resetPassword } = useAuth();

  const handleForgotPassword = () => {
    if (!email) {
      alert("Please enter your email address.");
      return;
    }
    // Calling reset password without knowing the firstName so backend will fetch the firstName instead of passing through this function
    resetPassword(email, "");
    onClose(); // Optionally close modal after submission
  };

  return (
    <Modal transparent={true} visible={visible} animationType="slide">
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0, 0, 0, 0.8)",
          }}
        >
          <Card
            style={{
              width: "80%",
              paddingVertical: 20,
              borderRadius: 14,
            }}
          >
            <Card.Title title="Reset Password" />
            <Card.Content>
              <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <View style={{ alignSelf: "center", width: "80%" }}>
                  <TextInput
                    style={{
                      height: 40,
                      borderColor: "gray",
                      borderWidth: 1,
                      borderRadius: 8,
                      paddingHorizontal: 10,
                      color: theme.colors.primaryTextColor,
                      marginBottom: 20,
                    }}
                    keyboardType="email-address"
                    placeholder="Enter your email"
                    placeholderTextColor={theme.colors.subTextColor}
                    value={email}
                    onChangeText={setEmail}
                    autoCapitalize="none"
                  />
                </View>
              </TouchableWithoutFeedback>
            </Card.Content>
            <Card.Actions>
              <Button onPress={onClose}>CANCEL</Button>
              <Button onPress={handleForgotPassword}>RESET</Button>
            </Card.Actions>
          </Card>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default ResetPasswordModal;
