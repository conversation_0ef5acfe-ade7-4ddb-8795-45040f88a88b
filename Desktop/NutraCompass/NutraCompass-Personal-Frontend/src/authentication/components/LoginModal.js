// LoginModal.js
import React from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Dimensions,
  Image,
} from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import { useThemeContext } from "../../context/ThemeContext.js";

const screenHeight = Dimensions.get("window").height;

const LoginModal = ({
  isVisible,
  onClose,
  handleGoogleSignIn,
  handleEmailLogin,
}) => {
  const { theme } = useThemeContext();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={{ flex: 1 }}
        activeOpacity={1}
        onPressOut={onClose}
      >
        <View
          style={{
            position: "absolute",
            bottom: 0,
            height: screenHeight * 0.35,
            width: "100%",
            backgroundColor: theme.colors.surface,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            padding: 20,
            alignItems: "center",
          }}
        >
          {/* Drag Indicator */}
          <View
            style={{
              height: 4,
              width: 40,
              backgroundColor: "gray",
              borderRadius: 2,
              marginBottom: 20,
              position: "absolute",
            }}
          />

          {/* Title and Subtitle */}
          <Text
            style={{
              paddingTop: 12,
              fontSize: 24,
              fontWeight: "bold",
              textAlign: "center",
              color: theme.colors.primaryTextColor,
            }}
          >
            Welcome back
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "gray",
              textAlign: "center",
              marginTop: 8,
              marginBottom: 12,
              color: theme.colors.subTextColor,
            }}
          >
            Select method to log in
          </Text>

          {/* Google Sign-In Button */}
          <TouchableOpacity
            style={{
              flexDirection: "row",
              backgroundColor: theme.colors.surface,
              padding: 12,
              borderRadius: 10,
              width: "90%",
              alignItems: "center",
              justifyContent: "center",
              borderWidth: 1,
              borderColor: theme.colors.primaryTextColor,
              marginVertical: 10,
            }}
            onPress={handleGoogleSignIn}
          >
            <Image
              source={require("../../../assets/google.png")}
              style={{ width: 20, height: 20, marginRight: 10 }}
            />
            <Text
              style={{ color: theme.colors.primaryTextColor, fontSize: 16 }}
            >
              Continue with Google
            </Text>
          </TouchableOpacity>

          {/* Email Sign-In Button */}
          <TouchableOpacity
            style={{
              flexDirection: "row",
              backgroundColor: theme.colors.surface,
              padding: 12,
              borderRadius: 10,
              width: "90%",
              alignItems: "center",
              justifyContent: "center",
              borderWidth: 1,
              borderColor: theme.colors.primaryTextColor,
            }}
            onPress={handleEmailLogin}
          >
            <FontAwesome
              name="envelope"
              size={20}
              color={theme.colors.primaryTextColor}
              style={{ marginRight: 10 }}
            />
            <Text
              style={{ color: theme.colors.primaryTextColor, fontSize: 16 }}
            >
              Continue with Email
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

export default LoginModal;
