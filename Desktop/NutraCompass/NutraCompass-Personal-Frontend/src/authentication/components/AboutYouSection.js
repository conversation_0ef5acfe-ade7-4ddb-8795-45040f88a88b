import React, { useState, useEffect } from "react";
import { TouchableWithoutFeedback, Text, View } from "react-native";
import { <PERSON><PERSON>, Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import Feather from "react-native-vector-icons/Feather";
import signupScreenStyles from "../../screens/styles/signupScreenStyles.js";
import { useThemeContext } from "../../context/ThemeContext.js";
import CustomSexPickerModal from "./CustomSexPickerModal.js";
import CustomDatePickerModal from "./CustomDatePickerModal.js";
import CustomHeightPickerModal from "./CustomHeightPickerModal.js";
import CustomWeightInputModal from "./CustomWeightInputModal.js";

const SelectInput = ({ onPress, selectedValue, placeholder }) => {
  const { theme } = useThemeContext();
  const styles = signupScreenStyles();

  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <View style={styles.selectInput}>
        <View style={{ flexDirection: "row", gap: 20 }}>
          <Feather
            name="arrow-right"
            color={
              selectedValue ? theme.colors.primary : "rgba(169, 169, 169, 0.7)"
            }
            size={18}
          />
          <Text
            style={{
              fontSize: 18,
              color: selectedValue
                ? theme.colors.primary
                : "rgba(169, 169, 169, 0.7)",
            }}
          >
            {selectedValue || placeholder}
          </Text>
        </View>
        <Feather
          name="arrow-right"
          color={theme.colors.primaryTextColor}
          size={18}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default function AboutYouSection({ value, setValue, onNext }) {
  const styles = signupScreenStyles();
  const { theme } = useThemeContext();

  const [isSexModalVisible, setSexModalVisible] = useState(false);
  const [isBirthdayModalVisible, setBirthdayModalVisible] = useState(false);
  const [isHeightModalVisible, setHeightModalVisible] = useState(false);
  const [isWeightModalVisible, setWeightModalVisible] = useState(false);

  const formatHeight = (height) => {
    if (height?.unit === "in" && height?.inches) {
      const feet = Math.floor(height.inches / 12);
      const inches = Math.round(height.inches % 12);
      return `${feet}'${inches}"`;
    }
    if (height?.unit === "cm" && height?.centimeters) {
      return `${height.centimeters} cm`;
    }
    return "";
  };

  const [localState, setLocalState] = useState({
    sex: value.sex || "",
    birthday: value.birthday || null,
    height: formatHeight(value.height),
    weight: value.weight || "",
  });

  useEffect(() => {
    setLocalState({
      sex: value.sex || "",
      birthday: value.birthday || null,
      height: formatHeight(value.height),
      weight: value.weight || "",
    });
  }, [value]);

  const calculateAge = (birthday) => {
    const currentDate = new Date();
    const birthDate = new Date(birthday);
    let age = currentDate.getFullYear() - birthDate.getFullYear();
    const monthDifference = currentDate.getMonth() - birthDate.getMonth();
    if (
      monthDifference < 0 ||
      (monthDifference === 0 && currentDate.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  const handleSelect = (field, inputValue) => {
    switch (field) {
      case "sex":
        setLocalState((prev) => ({ ...prev, sex: inputValue }));
        setValue((prev) => ({ ...prev, sex: inputValue }));
        break;
      case "birthday":
        const formattedDate = inputValue.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
        const age = calculateAge(inputValue);
        setLocalState((prev) => ({ ...prev, birthday: formattedDate }));
        setValue((prev) => ({ ...prev, birthday: formattedDate, age }));
        break;
      case "height":
        const formattedHeight = formatHeight(inputValue);
        setLocalState((prev) => ({ ...prev, height: formattedHeight }));
        setValue((prev) => ({ ...prev, height: inputValue }));
        break;
      case "weight":
        setLocalState((prev) => ({ ...prev, weight: inputValue }));
        setValue((prev) => ({ ...prev, weight: inputValue }));
        break;
      default:
        break;
    }
  };

  const handleNext = () => {
    if (
      localState.sex &&
      localState.birthday &&
      localState.height &&
      localState.weight
    ) {
      onNext();
    } else {
      console.log("Please fill out all profile details.");
    }
  };

  return (
    <View style={{ flex: 1, width: "100%" }}>
      <View style={{ alignItems: "center", paddingTop: 10, gap: 5 }}>
        <Text
          style={{
            fontSize: 28,
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
          }}
        >
          About You
        </Text>
        <Text
          style={{
            paddingHorizontal: 10,
            fontSize: 16,
            color: "black",
            textAlign: "center",
          }}
        >
          Enter your details so NutraCompass can customize your targets.
        </Text>
        <Card
          style={{
            width: "100%",
            marginTop: 10,
            borderWidth: 1,
            borderColor:
              localState.sex &&
              localState.birthday &&
              localState.height &&
              localState.weight
                ? theme.colors.primary
                : theme.colors.cardBorderColor,
          }}
        >
          <Card.Content>
            <View style={{ justifyContent: "center", width: "100%", gap: 10 }}>
              <SelectInput
                onPress={() => setSexModalVisible(true)}
                selectedValue={localState.sex}
                placeholder="Your sex"
              />
              <SelectInput
                onPress={() => setBirthdayModalVisible(true)}
                selectedValue={localState.birthday}
                placeholder="Your birthday"
              />
              <SelectInput
                onPress={() => setHeightModalVisible(true)}
                selectedValue={localState.height}
                placeholder="Your height"
              />
              <SelectInput
                onPress={() => setWeightModalVisible(true)}
                selectedValue={localState.weight}
                placeholder="Your weight"
              />
            </View>
          </Card.Content>
        </Card>
      </View>
      <CustomSexPickerModal
        title="Select Sex"
        visible={isSexModalVisible}
        onClose={() => setSexModalVisible(false)}
        onSelect={(sex) => handleSelect("sex", sex)}
      />
      <CustomDatePickerModal
        title="Select Birthday"
        selectedDate={localState.birthday}
        onSelect={(date) => handleSelect("birthday", date)}
        visible={isBirthdayModalVisible}
        onClose={() => setBirthdayModalVisible(false)}
      />
      <CustomHeightPickerModal
        title="Select Height"
        selectedHeight={value.height} // Pass the height object directly
        onSelect={(height) => handleSelect("height", height)}
        visible={isHeightModalVisible}
        onClose={() => setHeightModalVisible(false)}
      />
      <CustomWeightInputModal
        title="Enter Weight"
        selectedWeight={localState.weight}
        onSelect={(weight) => handleSelect("weight", weight)}
        visible={isWeightModalVisible}
        onClose={() => setWeightModalVisible(false)}
      />
      <View
        style={{
          flex: 1,
          alignItems: "center",
          justifyContent: "flex-end",
          paddingBottom: 20,
          gap: 20,
        }}
      >
        <Text style={{ fontSize: 14, color: "white", textAlign: "center" }}>
          We use this information to calculate and provide you with daily
          personalized recommendations.
        </Text>
        <Button
          mode="contained"
          labelStyle={{ color: "black", fontSize: 18, fontWeight: "bold" }}
          style={{
            backgroundColor:
              localState.sex &&
              localState.birthday &&
              localState.height &&
              localState.weight
                ? "white"
                : "gray",
            borderRadius: 8,
            width: "60%",
          }}
          disabled={
            !(
              localState.sex &&
              localState.birthday &&
              localState.height &&
              localState.weight
            )
          }
          onPress={handleNext}
        >
          Next
        </Button>
      </View>
    </View>
  );
}
