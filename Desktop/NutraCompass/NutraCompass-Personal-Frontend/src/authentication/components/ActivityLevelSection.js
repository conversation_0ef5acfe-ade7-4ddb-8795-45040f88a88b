import React, { useState, useEffect } from "react";
import { Text, View } from "react-native";
import { TextInput, <PERSON><PERSON>, Card, Icon } from "react-native-paper";
import Slider from "@react-native-community/slider";
import * as Haptics from "expo-haptics";
import signupScreenStyles from "../../screens/styles/signupScreenStyles.js";
import { useThemeContext } from "../../context/ThemeContext.js";

const ActivityLevelSection = ({ value, setValue, onNext }) => {
  const styles = signupScreenStyles();
  const { theme } = useThemeContext();

  const [sliderValue, setSliderValue] = useState(40);
  const [customKcal, setCustomKcal] = useState("");

  useEffect(() => {
    const levelToSliderValue = {
      None: 0,
      "Sedentary (BMR x 0.2)": 20,
      "Lightly Active (BMR x 0.375)": 40,
      "Moderately Active (BMR x 0.5)": 60,
      "Very Active (BMR x 0.9)": 80,
    };

    // Update sliderValue only if activityLevel changes
    if (
      value.activityLevel &&
      sliderValue !== levelToSliderValue[value.activityLevel]
    ) {
      setSliderValue(levelToSliderValue[value.activityLevel]);
    }

    // Update customKcal only if maintenanceCalories changes
    const newCustomKcal = value.maintenanceCalories
      ? String(value.maintenanceCalories)
      : "";
    if (customKcal !== newCustomKcal && sliderValue === 100) {
      setCustomKcal(newCustomKcal);
    }
  }, [value]);

  const getActivityLevel = (sliderValue) => {
    if (sliderValue === 0) return "None";
    else if (sliderValue === 20) return "Sedentary (BMR x 0.2)";
    else if (sliderValue === 40) return "Lightly Active (BMR x 0.375)";
    else if (sliderValue === 60) return "Moderately Active (BMR x 0.5)";
    else if (sliderValue === 80) return "Very Active (BMR x 0.9)";
    return "";
  };

  const handleSliderChange = (sliderValue) => {
    setSliderValue(sliderValue);
    if (sliderValue !== 100) {
      setCustomKcal(""); // Clear custom calories for non-custom levels
      setValue((prevValue) => ({
        ...prevValue, // Retain all other properties
        activityLevel: getActivityLevel(sliderValue),
        maintenanceCalories: null,
      }));
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleCustomKcalChange = (text) => {
    setCustomKcal(text);
    if (sliderValue === 100) {
      setValue((prevValue) => ({
        ...prevValue, // Retain all other properties
        maintenanceCalories: parseFloat(text) || null,
        activityLevel: "",
      }));
    }
  };

  const handleNext = () => {
    onNext();
  };

  const handleSkip = () => {
    onNext();
  };

  return (
    <View style={{ flex: 1, width: "100%" }}>
      <View
        style={{
          alignItems: "center",
          paddingTop: 10,
          gap: 5,
        }}
      >
        <Text
          style={{
            fontSize: 28,
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
          }}
        >
          Activity Level
        </Text>
        <Text
          style={{
            paddingHorizontal: 10,
            fontSize: 16,
            color: "black",
            textAlign: "center",
          }}
        >
          Based on your activity level, NutraCompass can estimate the amount of
          energy you burn each day.
        </Text>

        <Card
          style={{
            width: "100%",
            marginTop: 10,
            borderRadius: 14,
            borderWidth: 1,
            borderColor: theme.colors.primary,
          }}
        >
          <Card.Content>
            <View style={{ justifyContent: "center", width: "100%", gap: 10 }}>
              <View style={{ alignSelf: "center" }}>
                <Icon source="run-fast" color={"white"} size={80} />
              </View>

              {sliderValue !== 100 && (
                <Text
                  style={{
                    fontSize: 18,
                    color: "white",
                    textAlign: "center",
                    fontWeight: "500",
                  }}
                >
                  {getActivityLevel(sliderValue)}
                </Text>
              )}

              {sliderValue === 100 && (
                <View
                  style={{
                    flexDirection: "row",
                    gap: 10,
                    alignItems: "center",
                    justifyContent: "center",
                    alignSelf: "center",
                  }}
                >
                  <Text
                    style={{
                      fontSize: 18,
                      color: "white",
                      textAlign: "center",
                      fontWeight: "500",
                    }}
                  >
                    Custom
                  </Text>
                  <TextInput
                    style={{
                      alignSelf: "center",
                      height: 40,
                      borderColor: "gray",
                      backgroundColor: "transparent",
                      borderWidth: 1,
                      borderRadius: 8,
                      borderBottomLeftRadius: 0,
                      borderBottomRightRadius: 0,
                      textAlign: "center",
                      color: "white",
                    }}
                    underlineColor="transparent"
                    activeOutlineColor="red"
                    activeUnderlineColor={theme.colors.primary}
                    keyboardType="number-pad"
                    value={customKcal}
                    onChangeText={handleCustomKcalChange}
                    placeholder="2000"
                  />
                  <Text
                    style={{
                      fontSize: 18,
                      color: "white",
                      textAlign: "center",
                      fontWeight: "400",
                    }}
                  >
                    kcal
                  </Text>
                </View>
              )}
              <Slider
                style={{ width: "100%" }}
                step={20}
                minimumValue={0}
                maximumValue={100}
                minimumTrackTintColor={theme.colors.primary}
                maximumTrackTintColor="rgba(169, 169, 169, 0.7)"
                value={sliderValue}
                onValueChange={handleSliderChange}
              />
              <Text
                style={{
                  fontSize: 14,
                  color: "white",
                  textAlign: "center",
                }}
              >
                {sliderValue === 0 && "No activity."}
                {sliderValue === 20 &&
                  "Little or no exercise or daily activity."}
                {sliderValue === 40 &&
                  "Basic daily living and/or light exercise."}
                {sliderValue === 60 && "Moderate exercise 3-5 days/week."}
                {sliderValue === 80 && "Intense activity throughout the day."}
                {sliderValue === 100 && "Set your own fixed daily value."}
              </Text>
            </View>
          </Card.Content>
        </Card>
      </View>
      <View
        style={{
          flex: 1,
          alignItems: "center",
          justifyContent: "flex-end",
          gap: 20,
        }}
      >
        <Button
          mode="contained"
          labelStyle={{
            color: "black",
            fontSize: 18,
            fontWeight: "bold",
          }}
          style={{
            backgroundColor: "white",
            borderRadius: 8,
            width: "60%",
          }}
          onPress={handleNext}
        >
          Next
        </Button>
        <Button
          mode="text"
          labelStyle={{
            color: "white",
            fontSize: 18,
            fontWeight: "bold",
          }}
          style={{
            borderRadius: 8,
            width: "60%",
          }}
          onPress={handleSkip}
        >
          Skip
        </Button>
      </View>
    </View>
  );
};

export default ActivityLevelSection;
