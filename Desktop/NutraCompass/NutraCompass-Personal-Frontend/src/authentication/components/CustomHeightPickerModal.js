import React, { useState, useEffect } from "react";
import { Modal, View } from "react-native";
import { <PERSON>, Button } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { Picker } from "@react-native-picker/picker";
import { useThemeContext } from "../../context/ThemeContext.js";

const CustomHeightPickerModal = ({
  title,
  selectedHeight,
  onSelect,
  visible,
  onClose,
}) => {
  const { theme } = useThemeContext();

  // Parse initial selected height into state
  const [selectedUnit, setSelectedUnit] = useState("in");
  const [selectedFeet, setSelectedFeet] = useState("5");
  const [selectedInches, setSelectedInches] = useState("7");
  const [selectedCentimeters, setSelectedCentimeters] = useState("170");

  useEffect(() => {
    if (selectedHeight) {
      setSelectedUnit(selectedHeight.unit || "in");
      if (selectedHeight.unit === "in" && selectedHeight.inches) {
        setSelectedFeet(Math.floor(selectedHeight.inches / 12).toString());
        setSelectedInches((selectedHeight.inches % 12).toString());
      } else if (selectedHeight.unit === "cm" && selectedHeight.centimeters) {
        setSelectedCentimeters(selectedHeight.centimeters.toString());
      }
    }
  }, [selectedHeight]);

  const handleSave = () => {
    let height = {};
    if (selectedUnit === "cm") {
      const cm = parseFloat(selectedCentimeters);
      height = {
        inches: parseFloat((cm / 2.54).toFixed(2)),
        centimeters: cm,
        unit: "cm",
      };
    } else {
      const feet = parseInt(selectedFeet, 10);
      const inches = parseInt(selectedInches, 10);
      const totalInches = feet * 12 + inches;
      height = {
        inches: totalInches,
        centimeters: parseFloat((totalInches * 2.54).toFixed(2)),
        unit: "in",
      };
    }

    onSelect(height); // Pass the structured height object
    onClose();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const renderFeetPicker = () => {
    return Array.from({ length: 8 }, (_, i) => i + 4).map((value) => (
      <Picker.Item
        key={value}
        label={value.toString()}
        value={value.toString()}
        color={theme.colors.primaryTextColor}
      />
    ));
  };

  const renderInchesPicker = () => {
    return Array.from({ length: 12 }, (_, i) => i).map((value) => (
      <Picker.Item
        key={value}
        label={value.toString()}
        value={value.toString()}
        color={theme.colors.primaryTextColor}
      />
    ));
  };

  const renderCentimetersPicker = () => {
    return Array.from({ length: 221 }, (_, i) => i + 30).map((value) => (
      <Picker.Item
        key={value}
        label={value.toString()}
        value={value.toString()}
        color={theme.colors.primaryTextColor}
      />
    ));
  };

  const heightPickerStyle = { flex: 1 / 2 };

  return (
    <Modal transparent={true} visible={visible} animationType="slide">
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0, 0, 0, 0.7)",
        }}
      >
        <Card
          style={{
            width: "80%",
            borderRadius: 14,
          }}
        >
          <Card.Title title={title} />
          <Card.Content>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                marginHorizontal: 10,
              }}
            >
              {selectedUnit === "in" ? (
                <>
                  <Picker
                    selectedValue={selectedFeet}
                    onValueChange={(itemValue) => setSelectedFeet(itemValue)}
                    style={heightPickerStyle}
                  >
                    {renderFeetPicker()}
                  </Picker>
                  <Picker
                    selectedValue={selectedInches}
                    onValueChange={(itemValue) => setSelectedInches(itemValue)}
                    style={heightPickerStyle}
                  >
                    {renderInchesPicker()}
                  </Picker>
                </>
              ) : (
                <Picker
                  selectedValue={selectedCentimeters}
                  onValueChange={(itemValue) =>
                    setSelectedCentimeters(itemValue)
                  }
                  style={heightPickerStyle}
                >
                  {renderCentimetersPicker()}
                </Picker>
              )}
              <Picker
                selectedValue={selectedUnit}
                onValueChange={(itemValue) => setSelectedUnit(itemValue)}
                style={heightPickerStyle}
              >
                <Picker.Item
                  label="cm"
                  value="cm"
                  color={theme.colors.primaryTextColor}
                />
                <Picker.Item
                  label="in"
                  value="in"
                  color={theme.colors.primaryTextColor}
                />
              </Picker>
            </View>
          </Card.Content>
          <Card.Actions>
            <Button onPress={onClose}>CANCEL</Button>
            <Button onPress={handleSave}>SAVE</Button>
          </Card.Actions>
        </Card>
      </View>
    </Modal>
  );
};

export default CustomHeightPickerModal;
