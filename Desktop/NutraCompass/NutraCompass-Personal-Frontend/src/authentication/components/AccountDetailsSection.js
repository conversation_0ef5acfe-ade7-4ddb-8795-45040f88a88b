import React, { useState, useEffect } from "react";
import {
  ScrollView,
  Text,
  View,
  TouchableOpacity,
  Alert,
  Modal,
} from "react-native";
import { TextIn<PERSON>, <PERSON><PERSON>, Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import signupScreenStyles from "../../screens/styles/signupScreenStyles.js";
import { useThemeContext } from "../../context/ThemeContext.js";
import { MaterialIcons } from "@expo/vector-icons";

// Validation utilities
const validateEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
const validatePassword = (password) =>
  password && password.length >= 8 && /\d/.test(password);
const validateName = (name) => (name ? name.trim().length > 0 : false);

export default function AccountDetailsSection({
  value,
  setValue,
  handleSignUp,
  checkEmailExists,
  handleVerifyCode,
  handleResendCode,
}) {
  const styles = signupScreenStyles();
  const { theme } = useThemeContext();

  // State management for input fields and modal
  const [isSecureTextEntry, setIsSecureTextEntry] = useState(true); // Toggles password visibility
  const [errors, setErrors] = useState({}); // Stores validation errors
  const [touched, setTouched] = useState({}); // Tracks which fields the user has interacted with
  const [isFormValid, setIsFormValid] = useState(false); // Determines if the form is valid
  const [debounceTimeout, setDebounceTimeout] = useState(null); // Debounce for email validation
  const [isEmailCheckComplete, setIsEmailCheckComplete] = useState(false); // Tracks email validation status
  const [verificationModalVisible, setVerificationModalVisible] =
    useState(false); // Controls modal visibility
  const [verificationCode, setVerificationCode] = useState(""); // Stores the verification code input

  const [visited, setVisited] = useState(false); // Tracks if the section has been visited

  useEffect(() => {
    if (!visited) {
      setVisited(true);
    }
  }, []);

  /**
   * Validates the form in real-time whenever the value or error states change.
   * Ensures that all fields meet validation criteria before enabling the Sign-Up button.
   */
  useEffect(() => {
    const isValid =
      validateName(value?.firstName) &&
      validateName(value?.lastName) &&
      validateName(value?.userName) &&
      validateEmail(value?.email) &&
      validatePassword(value?.password) &&
      value?.password === value?.confirmPassword &&
      isEmailCheckComplete &&
      !errors.email;

    setIsFormValid(isValid);
  }, [value, errors, isEmailCheckComplete]);

  /**
   * Handles text input changes and validates the input.
   * Updates the error state dynamically based on validation results.
   */
  const handleInputChange = (field, text) => {
    setValue({ ...value, [field]: text });

    const fieldLabels = {
      firstName: "First name",
      lastName: "Last name",
      userName: "Username",
      email: "Email",
      password: "Password",
      confirmPassword: "Confirm password",
    };

    let fieldError = "";
    if (
      (field === "firstName" || field === "lastName" || field === "userName") &&
      !validateName(text)
    ) {
      fieldError = `${fieldLabels[field]} cannot be empty.`;
    } else if (field === "email") {
      setIsEmailCheckComplete(false);
      if (!validateEmail(text)) {
        fieldError = `${fieldLabels[field]} is invalid.`;
      } else {
        // Debounce email validation to minimize backend calls
        if (debounceTimeout) {
          clearTimeout(debounceTimeout);
        }
        const newTimeout = setTimeout(async () => {
          const emailExists = await checkEmailExists(text);
          if (emailExists) {
            setErrors((prevErrors) => ({
              ...prevErrors,
              email: `${fieldLabels[field]} is already associated with an existing account.`,
            }));
          } else {
            setErrors((prevErrors) => ({
              ...prevErrors,
              email: "",
            }));
          }
          setIsEmailCheckComplete(true);
        }, 500);
        setDebounceTimeout(newTimeout);
      }
    } else if (field === "password" && !validatePassword(text)) {
      fieldError = `${fieldLabels[field]} must be at least 8 characters and contain a number.`;
    } else if (field === "confirmPassword" && text !== value?.password) {
      fieldError = `${fieldLabels[field]} does not match Password.`;
    }

    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: fieldError,
    }));
  };

  /**
   * Tracks when a user interacts with an input field.
   * Used to control when error messages should be shown.
   */
  const handleBlur = (field) => {
    setTouched((prevTouched) => ({ ...prevTouched, [field]: true }));
  };

  /**
   * Handles the Sign-Up button click.
   * Initiates the sign-up process and opens the verification modal on success.
   */
  const onSignUpPress = async () => {
    if (isFormValid) {
      try {
        const isSignUpSuccessful = await handleSignUp();

        if (isSignUpSuccessful) {
          setVerificationModalVisible(true); // Show verification modal if sign-up succeeds
        }
      } catch (error) {
        console.error("Error during sign-up:", error);
        Alert.alert("Error", "An error occurred during sign-up.");
      }
    } else {
      Alert.alert("Error", "Please fix the errors before proceeding.");
    }
  };

  /**
   * Handles verification code submission.
   * Verifies the user's email address using the entered code.
   */
  const onVerifyPress = async () => {
    try {
      await handleVerifyCode(verificationCode);
      setVerificationModalVisible(false); // Close the modal on success
      //Alert.alert("Success", "Your email has been verified!");
    } catch (error) {
      console.error("Verification failed:", error);
      Alert.alert("Error", "Invalid verification code.");
    }
  };

  /**
   * Resends the verification code to the user's email.
   * Provides feedback if the operation fails.
   */
  const onResendPress = async () => {
    try {
      await handleResendCode();
      // Alert.alert(
      //   "Success",
      //   "A new verification code has been sent to your email."
      // );
    } catch (error) {
      console.error("Resend failed:", error);
      Alert.alert("Error", "Failed to resend the verification code.");
    }
  };

  return (
    <>
      <ScrollView style={{ flex: 1, width: "100%" }}>
        <TouchableOpacity
          activeOpacity={1}
          style={{
            alignItems: "center",
            paddingTop: 10,
            gap: 5,
          }}
        >
          <Text
            style={{
              fontSize: 28,
              fontWeight: "bold",
              color: "black",
              textAlign: "center",
            }}
          >
            Account Details
          </Text>
          <Text
            style={{
              fontSize: 18,
              color: "black",
              textAlign: "center",
            }}
          >
            Enter your account details to complete the signup process.
          </Text>

          <Card style={{ width: "100%", marginTop: 10 }}>
            <Card.Content>
              <View
                style={{ justifyContent: "center", width: "100%", gap: 10 }}
              >
                <TextInput
                  label="First Name"
                  style={styles.input}
                  value={value?.firstName || ""}
                  onChangeText={(text) => handleInputChange("firstName", text)}
                  onBlur={() => handleBlur("firstName")}
                  mode="outlined"
                  autoCapitalize="none"
                  error={
                    (!!errors.firstName && touched.firstName) ||
                    (!!errors.firstName && visited)
                  }
                />
                {((errors.firstName && touched.firstName) ||
                  (errors.firstName && visited)) && (
                  <Text style={{ color: "red", fontSize: 12 }}>
                    {errors.firstName}
                  </Text>
                )}

                <TextInput
                  label="Last Name"
                  style={styles.input}
                  value={value?.lastName || ""}
                  onChangeText={(text) => handleInputChange("lastName", text)}
                  onBlur={() => handleBlur("lastName")}
                  mode="outlined"
                  autoCapitalize="none"
                  error={!!errors.lastName && (touched.lastName || visited)}
                />
                {errors.lastName && (touched.lastName || visited) && (
                  <Text style={{ color: "red", fontSize: 12 }}>
                    {errors.lastName}
                  </Text>
                )}

                <TextInput
                  label="User Name"
                  style={styles.input}
                  value={value?.userName || ""}
                  onChangeText={(text) => handleInputChange("userName", text)}
                  onBlur={() => handleBlur("userName")}
                  mode="outlined"
                  autoCapitalize="none"
                  error={!!errors.userName && (touched.userName || visited)}
                />
                {errors.userName && (touched.userName || visited) && (
                  <Text style={{ color: "red", fontSize: 12 }}>
                    {errors.userName}
                  </Text>
                )}

                <TextInput
                  label="Email"
                  style={styles.input}
                  value={value?.email || ""}
                  onChangeText={(text) => handleInputChange("email", text)}
                  onBlur={() => handleBlur("email")}
                  mode="outlined"
                  autoCapitalize="none"
                  keyboardType="email-address"
                  error={!!errors.email && (touched.email || visited)}
                />
                {errors.email && (touched.email || visited) && (
                  <Text style={{ color: "red", fontSize: 12 }}>
                    {errors.email}
                  </Text>
                )}

                <TextInput
                  label="Password"
                  style={styles.input}
                  value={value?.password || ""}
                  onChangeText={(text) => handleInputChange("password", text)}
                  onBlur={() => handleBlur("password")}
                  secureTextEntry={isSecureTextEntry}
                  mode="outlined"
                  autoCapitalize="none"
                  error={!!errors.password && (touched.password || visited)}
                  right={
                    <TextInput.Icon
                      icon="eye"
                      onPress={() => setIsSecureTextEntry(!isSecureTextEntry)}
                    />
                  }
                />
                {errors.password && (touched.password || visited) && (
                  <Text style={{ color: "red", fontSize: 12 }}>
                    {errors.password}
                  </Text>
                )}

                <TextInput
                  label="Confirm Password"
                  style={styles.input}
                  value={value?.confirmPassword || ""}
                  onChangeText={(text) =>
                    handleInputChange("confirmPassword", text)
                  }
                  onBlur={() => handleBlur("confirmPassword")}
                  secureTextEntry={isSecureTextEntry}
                  mode="outlined"
                  autoCapitalize="none"
                  error={
                    !!errors.confirmPassword &&
                    (touched.confirmPassword || visited)
                  }
                  right={
                    <TextInput.Icon
                      icon="eye"
                      onPress={() => setIsSecureTextEntry(!isSecureTextEntry)}
                    />
                  }
                />
                {errors.confirmPassword &&
                  (touched.confirmPassword || visited) && (
                    <Text style={{ color: "red", fontSize: 12 }}>
                      {errors.confirmPassword}
                    </Text>
                  )}
              </View>
            </Card.Content>
          </Card>
        </TouchableOpacity>

        <View
          style={{
            marginTop: 10,
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
            gap: 20,
          }}
        >
          <Button
            mode="contained"
            labelStyle={{
              color: theme.colors.primaryTextColor,
              fontSize: 18,
              fontWeight: "bold",
            }}
            style={{
              backgroundColor: isFormValid ? theme.colors.primary : "gray",
              borderRadius: 8,
              width: "60%",
            }}
            onPress={onSignUpPress}
            disabled={!isFormValid}
          >
            Sign Up
          </Button>
        </View>
      </ScrollView>

      {/* Verification Code Modal */}
      <Modal
        visible={verificationModalVisible}
        transparent={true}
        animationType="slide"
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0, 0, 0, 0.9)",
          }}
        >
          <Card style={{ width: "80%", padding: 20, position: "relative" }}>
            {/* Close Button */}
            <TouchableOpacity
              onPress={() => {
                if (verificationModalVisible === true) {
                  setVerificationModalVisible(false);
                }
              }}
              style={{
                position: "absolute",
                top: 0,
                left: 10,
                zIndex: 2,
              }}
            >
              <MaterialIcons
                name="close"
                size={24}
                color={theme.colors.primaryTextColor}
              />
            </TouchableOpacity>

            {/* Modal Content */}
            <Text
              style={{
                fontSize: 20,
                textAlign: "center",
                marginBottom: 10,
                color: theme.colors.primaryTextColor,
              }}
            >
              Verify Your Email
            </Text>
            <TextInput
              label="Verification Code"
              value={verificationCode}
              onChangeText={(text) => setVerificationCode(text)}
              style={{ marginBottom: 20 }}
            />
            <Button mode="contained" onPress={onVerifyPress}>
              Verify
            </Button>
            <Button
              mode="text"
              onPress={onResendPress}
              style={{ marginTop: 10 }}
            >
              Resend Code
            </Button>
          </Card>
        </View>
      </Modal>
    </>
  );
}
