import React, { createContext, useContext, useState, useEffect } from "react";
import { Alert } from "react-native";
import { useTime } from "../../context/TimeContext.js";
import {
  getAuth,
  onAuthStateChanged,
  signOut,
  signInWithCustomToken,
  signInWithEmailAndPassword,
  GoogleAuthProvider,
  signInWithCredential,
} from "firebase/auth";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { db } from "../../config/firebase.js";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import Configs from "../../../configs.js";
import * as Notifications from "expo-notifications";

// Creates a context for managing authentication state across the application.
const AuthContext = createContext();

// Provides a custom hook for easy consumption of authentication context in components.
export function useAuth() {
  return useContext(AuthContext);
}

// Provider component that manages authentication state and interfaces with Firebase.
export function AuthProvider({ children }) {
  const { deviceTimezone, deviceRegion } = useTime();
  const [user, setUser] = useState(null); // Stores the current user object
  const [loading, setLoading] = useState(true); // Manages the loading state for authentication processes
  const [token, setToken] = useState(""); // Holds the Firebase token for the authenticated user
  const auth = getAuth(); // Initializes Firebase authentication service
  const apiUrl = Configs.NutraCompass_API_URL; // Base URL for API, should move to environment variables for production

  // Subscribe to authentication state changes and manage user state
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      if (currentUser) {
        setUser(currentUser); // Set the user state if authenticated
        const idToken = await currentUser.getIdToken(true);
        setToken(idToken); // Store the token for use in authenticated requests
      } else {
        setUser(null); // Clear user state if not authenticated
        setToken(""); // Clear token
      }
      setLoading(false); // Set loading to false after handling user state
    });

    // Cleanup the subscription on component unmount
    return () => unsubscribe();
  }, []);

  // Configure Google Sign-In
  useEffect(() => {
    GoogleSignin.configure({
      iosClientId: Configs.GOOGLE_AUTH_IOS_CLIENT_ID,
      webClientId: Configs.GOOGLE_AUTH_WEB_CLIENT_ID,
    });
  }, []);

  const defaultUserSettings = {
    profile: {
      firstName: "", // To be populated from Google data if available
      lastName: "", // To be populated from Google data if available
      userName: "", // To be set or generated based on Google info if needed
      email: "", // To be populated from Google data
      birthday: "", // Placeholder, only set if Google provides it
      age: null, // Placeholder, calculated if Google provides birthday
      sex: "", // Placeholder, set if Google provides it
      bodyWeight: "", // Placeholder, user-provided if Google doesn’t provide it
      height: { inches: null, centimeters: null }, // Placeholder, user-provided if unavailable
      pictureUrl: "", // Set if Google provides a profile picture
    },
    location: {
      timezone: deviceTimezone,
      region: deviceRegion,
    },
    appAppearance: {
      theme: "Default",
      isDark: true,
    },
    nutritionalGoals: {
      calorieGoal: 2000, // Default daily calorie goal
      waterGoal: { amount: 64, unit: "fl oz" },
      macroGoals: {
        carb: { dailyPercentage: 0.4, dailyCalories: 800, dailyGrams: 200 },
        protein: { dailyPercentage: 0.3, dailyCalories: 600, dailyGrams: 150 },
        fat: { dailyPercentage: 0.3, dailyCalories: 600, dailyGrams: 67 },
      },
    },
    physicalFitnessGoals: {
      stepsGoal: 10000,
      distanceGoal: 5,
      distanceUnit: "mi",
    },
  };

  // Ensures that the Firestore document exists for the user
  async function ensureUserDocument(user, settings = {}) {
    //console.log("User: ", JSON.stringify(user, null, 1));
    const userDocRef = doc(db, "users", user.uid);
    const userDoc = await getDoc(userDocRef);

    if (!userDoc.exists()) {
      // Extract Google profile information
      const googleProfile = {
        email: user.email,
        firstName: user.displayName?.split(" ")[0] || "",
        lastName: user.displayName?.split(" ").slice(1).join(" ") || "",
      };

      // Merge settings with priority: form data > google data > defaults
      const mergedSettings = {
        ...defaultUserSettings,
        ...settings,
        profile: {
          ...defaultUserSettings.profile,
          ...settings.profile,
          // Use form data if available, otherwise fallback to Google data
          firstName: settings.profile?.firstName || googleProfile.firstName,
          lastName: settings.profile?.lastName || googleProfile.lastName,
          email: googleProfile.email, // Always use Google's verified email
          userName:
            settings.profile?.userName || generateUsername(googleProfile),
          userNameLower: (
            settings.profile?.userName || generateUsername(googleProfile)
          ).toLowerCase(),
          pictureUrl: "", // Explicit empty string
          // Preserve form-based metrics
          birthday: settings.profile?.birthday,
          age: settings.profile?.age,
          sex: settings.profile?.sex,
          bodyWeight: settings.profile?.bodyWeight,
          height: settings.profile?.height,
        },
      };
      //console.log("Merged Settings: ", JSON.stringify(mergedSettings, null, 1));
      try {
        await setDoc(userDocRef, mergedSettings);
        console.log("User document created with merged data.");
        return true;
      } catch (error) {
        console.error("Document creation error:", error);
        throw error;
      }
    }
    return false;
  }

  // Helper function to generate username from Google data
  function generateUsername(googleProfile) {
    const base =
      googleProfile.email.split("@")[0] ||
      `${googleProfile.firstName}${googleProfile.lastName}`.toLowerCase();
    return `${base}_${Math.floor(1000 + Math.random() * 9000)}`;
  }

  // Google Sign-In Function
  async function signInWithGoogle(settings = {}) {
    try {
      await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true,
      });
      const userInfo = await GoogleSignin.signIn();

      const credential = GoogleAuthProvider.credential(userInfo.data.idToken);
      // Authenticate with Firebase using the Google credential
      const userCredential = await signInWithCredential(auth, credential);

      // Ensure Firestore document for the users
      await ensureUserDocument(userCredential.user, settings);

      // Explicitly set user in case onAuthStateChanged has delay issues
      //setUser(userCredential.user);
      console.log("User signed in via Google.");
    } catch (error) {
      console.error("Google Sign-In Error:", error);
      throw error;
    }
  }

  // Handles new user registration by interacting with the backend
  async function registration(email, password, defaultSettings) {
    try {
      const response = await fetch(
        `${apiUrl}/v1/authentication/register`, // Updated endpoint to match AuthRoutes
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email, password, defaultSettings }),
        }
      );

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Unable to register");
      }

      await signInWithCustomToken(auth, data.idToken); // Sign in the user with the received Firebase token
    } catch (err) {
      console.log("Registration Failed", err.message);
    }
  }

  // Handles user session validation by verifying credentials via the backend
  async function signIn(email, password) {
    try {
      // Attempt to sign in with Firebase Authentication
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );

      // Obtain the ID token for backend validation
      const idToken = await userCredential.user.getIdToken();

      // Validate the session with the backend
      const response = await fetch(
        `${apiUrl}/v1/authentication/validate-session`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ idToken }),
        }
      );

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Backend validation failed");
      }

      console.log("Session validated with UID:", data.uid); // Debugging log
    } catch (err) {
      console.error("Sign-In Error:", err.message);
      throw err; // Propagate the error to the SignInScreen
    }
  }

  // Handles logging out the current user
  async function loggingOut() {
    try {
      // Safe notification cleanup
      await Notifications.cancelAllScheduledNotificationsAsync();

      // Auth cleanup
      await signOut(auth);

      // State reset
      setUser(null);
      setToken("");
    } catch (err) {
      console.error("Signout error:", err.message);
      // Consider adding error analytics here
    }
  }

  // Handles sending a password reset link
  const resetPassword = async (emailParam, firstName) => {
    try {
      // Use the provided email or fallback to the signed-in user's email
      const emailToUse = emailParam || user?.email;

      if (!emailToUse) {
        Alert.alert(
          "Error",
          "Please provide a valid email address to reset your password."
        );
        return;
      }

      const response = await fetch(
        `${apiUrl}/v1/authentication/password-reset`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email: emailToUse, firstName: firstName }),
        }
      );

      if (response.ok) {
        Alert.alert(
          "Password Reset",
          "A password reset link has been sent to your email. Please check your inbox."
        );
      } else {
        const error = await response.json();
        Alert.alert(
          "Error",
          error.message || "Failed to process password reset."
        );
      }
    } catch (error) {
      console.error("Error initiating password reset:", error);
    }
  };

  // Handles deleting the user account
  async function deleteAccount(uid, firstName) {
    try {
      const response = await fetch(
        `${apiUrl}/v1/authentication/delete-account`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ userId: uid, firstName: firstName }),
        }
      );

      if (response.ok) {
        // Redirect user to the welcome screen or logout
        loggingOut();
      } else {
        const error = await response.json();
        console.error("Error deleting account:", error.message);
        alert("Failed to delete account. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting account:", error);
    }
  }

  const checkEmailExists = async (email) => {
    try {
      const response = await fetch(`${apiUrl}/v1/authentication/check-email`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      // Check if the response is OK and parseable as JSON
      if (!response.ok) {
        console.error(
          "Non-200 response:",
          response.status,
          await response.text()
        );
        return false;
      }

      const data = await response.json();
      return data.exists; // Assuming your backend sends `{ exists: true/false }`
    } catch (error) {
      console.error("Error checking email existence:", error);
      return false; // Treat as not existing in case of error
    }
  };

  // Handles sending the verification code to the user's email
  const sendVerificationCode = async (email, firstName) => {
    try {
      const response = await fetch(
        `${apiUrl}/v1/authentication/send-verification`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email: email, firstName: firstName }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to send verification code.");
      }

      // Alert.alert(
      //   "Verification Email Sent",
      //   "Please check your email for the code."
      // );
    } catch (error) {
      console.error("Error sending verification code:", error);
      Alert.alert(
        "Error",
        error.message || "Unable to send verification code."
      );
    }
  };

  // Handles verifying the code entered by the user
  const verifyCode = async (email, code) => {
    try {
      const response = await fetch(`${apiUrl}/v1/authentication/verify-code`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, code }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Invalid verification code.");
      }

      //Alert.alert("Verification Successful", "Your email has been verified.");
      return true; // Indicate successful verification
    } catch (error) {
      console.error("Error verifying code:", error);
      Alert.alert("Error", error.message || "Unable to verify code.");
      return false; // Indicate failed verification
    }
  };

  // Handles resending the verification code
  const resendCode = async (email, firstName) => {
    try {
      const response = await fetch(`${apiUrl}/v1/authentication/resend-code`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: email, firstName: firstName }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to resend verification code.");
      }

      //Alert.alert("Verification Code Resent", "Please check your email.");
    } catch (error) {
      console.error("Error resending verification code:", error);
      Alert.alert(
        "Error",
        error.message || "Unable to resend verification code."
      );
    }
  };

  // Context provider passing down authentication state and control methods to child components
  const value = {
    user,
    token,
    loading,
    registration,
    signIn,
    signInWithGoogle,
    loggingOut,
    resetPassword,
    deleteAccount,
    checkEmailExists,
    sendVerificationCode,
    verifyCode,
    resendCode,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
