import React from "react";
import { View, Text, Switch, SafeAreaView } from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import ThemeDisplay from "../components/ThemeDisplay.js";
import ThemeSelector from "../components/ThemeSelector.js";

const ThemeScreen = () => {
  const { toggleDarkMode, theme, mode } = useThemeContext();

  const handleToggleDarkMode = () => {
    toggleDarkMode();
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
      }}
    >
      {/* Screen Header */}
      <View
        style={{
          flexDirection: "row",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "baseline",
          padding: 10,
          paddingTop: 30,
        }}
      >
        <Text
          style={{
            color: theme.colors.primaryTextColor,
            fontSize: 26,
            alignSelf: "center",
          }}
        >
          Theme Palette
        </Text>

        {/* Toggle for switching between dark and light mode */}
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <Text
            style={{ color: theme.colors.primaryTextColor, marginRight: 10 }}
          >
            {mode.charAt(0).toUpperCase() + mode.slice(1)} Mode
          </Text>
          <Switch
            trackColor={{ false: "#767577", true: "#81b0ff" }}
            thumbColor={mode === "dark" ? "#f5dd4b" : "#f4f3f4"}
            ios_backgroundColor="#3e3e3e"
            onValueChange={handleToggleDarkMode}
            value={mode === "dark"}
          />
        </View>
      </View>

      <View
        style={{
          flex: 1,
          backgroundColor: theme.colors.surface,
          padding: 5,
          paddingTop: 20,
        }}
      >
        <Text
          style={{
            fontSize: 18,
            color: theme.colors.primaryTextColor,
            alignSelf: "center",
          }}
        >
          Current Theme
        </Text>
        <View style={{ flex: 1 }}>
          <ThemeDisplay />
        </View>
      </View>

      <View
        style={{
          flex: 4,
          paddingBottom: "20%",
          alignItems: "center",
        }}
      >
        <ThemeSelector renderStructure="structure1" />
      </View>
    </SafeAreaView>
  );
};

export default ThemeScreen;
