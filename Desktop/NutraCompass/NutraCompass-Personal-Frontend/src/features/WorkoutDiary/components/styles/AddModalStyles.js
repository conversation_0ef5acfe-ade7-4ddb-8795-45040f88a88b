import { StyleSheet } from 'react-native';

const useModalStyles = (theme) => StyleSheet.create({
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        width: '80%',
        padding: 20,
        backgroundColor: theme.colors.cardBackgroundColor,
        borderRadius: 10,
    },
    modalTitle: {
        fontSize: 20,
        color: theme.colors.primaryTextColor,
        marginBottom: 20,
        textAlign: 'center',
    },
    modalInput: {
        borderWidth: 1,
        borderColor: theme.colors.cardBorderColor,
        borderRadius: 4,
        padding: 10,
        marginBottom: 20,
        color: theme.colors.primaryTextColor,
    },
    modalButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    modalButton: {
        padding: 10,
        borderRadius: 4,
        backgroundColor: theme.colors.primary,
    },
    modalButtonText: {
        color: theme.colors.primaryTextColor,
    },
});

export default useModalStyles;