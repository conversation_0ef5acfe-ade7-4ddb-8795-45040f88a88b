import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Modal }  from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useThemeContext } from "../../../context/ThemeContext";
import useModalStyles from './styles/AddModalStyles';

const AddWorkoutModal = ({ isVisible, onClose, onAddWorkout }) => {
    const { theme } = useThemeContext();
    const styles = useModalStyles(theme);
    const [searchQuery, setSearchQuery] = useState('');

    useEffect(() => {
        if (isVisible) {
            setSearchQuery('');
        }
    }, [isVisible]);

    return (
        <Modal
            visible={isVisible}
            animationType="slide"
            transparent={true}
            onRequestClose={onClose}
        >
            <View style={styles.modalContainer}>
                <View style={styles.modalContent}>
                    <Text style={styles.modalTitle}>Add a Workout</Text>
                    <TextInput
                        style={styles.modalInput}
                        placeholder="Search or Enter Workout Name"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                    />
                    <View style={styles.modalButtons}>
                        <TouchableOpacity onPress={() => onAddWorkout(searchQuery)} style={styles.modalButton}>
                            <Text style={styles.modalButtonText}>Add Workout</Text>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={onClose} style={styles.modalButton}>
                            <Text style={styles.modalButtonText}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

export default AddWorkoutModal;