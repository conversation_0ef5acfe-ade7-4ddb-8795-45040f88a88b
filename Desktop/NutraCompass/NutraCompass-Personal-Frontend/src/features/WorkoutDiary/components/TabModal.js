import React from "react";
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons"; // Ensure this is imported
import { useTheme } from "react-native-paper"; // Assuming you are using styled components from react-native-paper

const { height } = Dimensions.get("window"); // Get the height of the device screen

const TabModal = ({ isVisible, onClose, renderContent }) => {
  const theme = useTheme(); // Using theme for styling

  return (
    <Modal
      animationType="slide"
      transparent={false} // Full-screen and non-transparent
      visible={isVisible}
      onRequestClose={onClose}
    >
      <View
        style={[
          styles.fullScreenView,
          {
            backgroundColor: theme.colors.background,
            paddingTop: height / 16,
          },
        ]}
      >
        <TouchableOpacity onPress={onClose} style={styles.closeIcon}>
          <Icon name="chevron-down" size={30} color={theme.colors.primary} />
        </TouchableOpacity>
        <View style={styles.contentContainer}>{renderContent()}</View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  fullScreenView: {
    flex: 1,
    justifyContent: "flex-start", // Changed to flex-start to allow content to be at the top
    alignItems: "center",
  },
  closeIcon: {
    alignSelf: "center", // Center the icon horizontally
  },
  contentContainer: {
    flex: 1,
    width: "100%",
    paddingVertical: 20, // Extra padding to accommodate the close icon
  },
});

export default TabModal;
