const dummyData = {
    history: [
        {
            date: "2024-06-15",
            workoutName: "Full Body Workout",
            duration: "60 minutes",
            caloriesBurned: 500,
            exercises: [
                { name: "Push-ups", sets: 3, reps: [15, 15, 15], weight: [null, null, null] },
                { name: "Squats", sets: 3, reps: [20, 20, 20], weight: [null, null, null] },
                { name: "<PERSON><PERSON><PERSON><PERSON>", sets: 3, reps: [10, 10, 10], weight: [null, null, null] }
            ]
        }
    ],
    presetPrograms: [
        {
            id: '1',
            name: "Beginner Plan",
            days: ["Monday", "Wednesday", "Friday"],
            exercises: [
                {
                    day: "Monday",
                    workouts: [
                        { name: "Push-ups", description: "Push-ups description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] },
                        { name: "Squats", description: "Squats description", sets: 3, reps: [20, 20, 20], weight: [null, null, null] }
                    ]
                },
                {
                    day: "Wednesday",
                    workouts: [
                        { name: "Burpees", description: "Burpees description", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                        { name: "Lunges", description: "Lunges description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
                    ]
                },
                {
                    day: "Friday",
                    workouts: [
                        { name: "Plank", description: "Plank description", sets: 3, reps: [60, 60, 60], weight: [null, null, null] }, // seconds for plank
                        { name: "Mountain Climbers", description: "Mountain Climbers description", sets: 3, reps: [20, 20, 20], weight: [null, null, null] }
                    ]
                }
            ]
        },
        {
            id: '2',
            name: "Intermediate Plan",
            days: ["Tuesday", "Thursday", "Saturday"],
            exercises: [
                {
                    day: "Tuesday",
                    workouts: [
                        { name: "Deadlift", description: "Deadlift description", sets: 4, reps: [10, 10, 10, 10], weight: [150, 150, 150, 150] },
                        { name: "Bench Press", description: "Bench Press description", sets: 4, reps: [12, 12, 12, 12], weight: [100, 100, 100, 100] }
                    ]
                },
                {
                    day: "Thursday",
                    workouts: [
                        { name: "Pull-ups", description: "Pull-ups description", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                        { name: "Dips", description: "Dips description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
                    ]
                },
                {
                    day: "Saturday",
                    workouts: [
                        { name: "Squats", description: "Squats description", sets: 4, reps: [15, 15, 15, 15], weight: [120, 120, 120, 120] },
                        { name: "Overhead Press", description: "Overhead Press description", sets: 4, reps: [10, 10, 10, 10], weight: [60, 60, 60, 60] }
                    ]
                }
            ]
        }
    ],
    customPrograms: [
        {
            id: '3',
            name: "Custom Plan 1",
            days: ["Tuesday", "Thursday"],
            exercises: [
                {
                    day: "Tuesday",
                    workouts: [
                        { name: "Deadlift", description: "Deadlift description", sets: 4, reps: [10, 10, 10, 10], weight: [150, 150, 150, 150] },
                        { name: "Bench Press", description: "Bench Press description", sets: 4, reps: [12, 12, 12, 12], weight: [100, 100, 100, 100] }
                    ]
                },
                {
                    day: "Thursday",
                    workouts: [
                        { name: "Pull-ups", description: "Pull-ups description", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                        { name: "Dips", description: "Dips description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
                    ]
                }
            ]
        },
        {
            id: '4',
            name: "Custom Plan 2",
            days: ["Monday", "Wednesday"],
            exercises: [
                {
                    day: "Monday",
                    workouts: [
                        { name: "Push-ups", description: "Push-ups description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] },
                        { name: "Sit-ups", description: "Sit-ups description", sets: 3, reps: [20, 20, 20], weight: [null, null, null] }
                    ]
                },
                {
                    day: "Wednesday",
                    workouts: [
                        { name: "Squats", description: "Squats description", sets: 3, reps: [20, 20, 20], weight: [null, null, null] },
                        { name: "Lunges", description: "Lunges description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
                    ]
                }
            ]
        },
        {
            id: '5',
            name: "Custom Plan 3",
            days: ["Tuesday", "Friday"],
            exercises: [
                {
                    day: "Tuesday",
                    workouts: [
                        { name: "Bicep Curls", description: "Bicep Curls description", sets: 3, reps: [12, 12, 12], weight: [15, 15, 15] },
                        { name: "Tricep Dips", description: "Tricep Dips description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
                    ]
                },
                {
                    day: "Friday",
                    workouts: [
                        { name: "Lunges", description: "Lunges description", sets: 3, reps: [20, 20, 20], weight: [null, null, null] },
                        { name: "Plank", description: "Plank description", sets: 3, reps: [60, 60, 60], weight: [null, null, null] } // seconds for plank
                    ]
                }
            ]
        },
        {
            id: '6',
            name: "Custom Plan 4",
            days: ["Monday", "Thursday"],
            exercises: [
                {
                    day: "Monday",
                    workouts: [
                        { name: "Bench Press", description: "Bench Press description", sets: 4, reps: [12, 12, 12, 12], weight: [100, 100, 100, 100] },
                        { name: "Deadlift", description: "Deadlift description", sets: 4, reps: [10, 10, 10, 10], weight: [150, 150, 150, 150] }
                    ]
                },
                {
                    day: "Thursday",
                    workouts: [
                        { name: "Pull-ups", description: "Pull-ups description", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                        { name: "Dips", description: "Dips description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
                    ]
                }
            ]
        }
    ],
    presetWorkouts: [
        {
            id: '7',
            name: "Workout 1",
            exercises: [
                { name: "Push-ups", sets: 3, reps: [15, 15, 15], weight: [null, null, null] },
                { name: "Squats", sets: 3, reps: [20, 20, 20], weight: [null, null, null] }
            ]
        },
        {
            id: '8',
            name: "Workout 2",
            exercises: [
                { name: "Burpees", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                { name: "Lunges", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
            ]
        },
        {
            id: '9',
            name: "Workout 3",
            exercises: [
                { name: "Deadlift", sets: 4, reps: [10, 10, 10, 10], weight: [150, 150, 150, 150] },
                { name: "Bench Press", sets: 4, reps: [12, 12, 12, 12], weight: [100, 100, 100, 100] }
            ]
        },
        {
            id: '10',
            name: "Workout 4",
            exercises: [
                { name: "Pull-ups", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                { name: "Dips", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
            ]
        },
        {
            id: '11',
            name: "Workout 5",
            exercises: [
                { name: "Squats", sets: 4, reps: [15, 15, 15, 15], weight: [120, 120, 120, 120] },
                { name: "Overhead Press", sets: 4, reps: [10, 10, 10, 10], weight: [60, 60, 60, 60] }
            ]
        },
        {
            id: '12',
            name: "Workout 6",
            exercises: [
                { name: "Push-ups", sets: 3, reps: [15, 15, 15], weight: [null, null, null] },
                { name: "Sit-ups", sets: 3, reps: [20, 20, 20], weight: [null, null, null] }
            ]
        }
    ],
    customWorkouts: [
        {
            id: '13',
            name: "Custom Workout 1",
            exercises: [
                { name: "Push-ups", sets: 3, reps: [15, 15, 15], weight: [null, null, null] },
                { name: "Squats", sets: 3, reps: [20, 20, 20], weight: [null, null, null] }
            ]
        },
        {
            id: '14',
            name: "Custom Workout 2",
            exercises: [
                { name: "Burpees", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                { name: "Lunges", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
            ]
        },
        {
            id: '15',
            name: "Custom Workout 3",
            exercises: [
                { name: "Deadlift", sets: 4, reps: [10, 10, 10, 10], weight: [150, 150, 150, 150] },
                { name: "Bench Press", sets: 4, reps: [12, 12, 12, 12], weight: [100, 100, 100, 100] }
            ]
        },
        {
            id: '16',
            name: "Custom Workout 4",
            exercises: [
                { name: "Pull-ups", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                { name: "Dips", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
            ]
        },
        {
            id: '17',
            name: "Custom Workout 5",
            exercises: [
                { name: "Squats", sets: 4, reps: [15, 15, 15, 15], weight: [120, 120, 120, 120] },
                { name: "Overhead Press", sets: 4, reps: [10, 10, 10, 10], weight: [60, 60, 60, 60] }
            ]
        },
        {
            id: '18',
            name: "Custom Workout 6",
            exercises: [
                { name: "Push-ups", sets: 3, reps: [15, 15, 15], weight: [null, null, null] },
                { name: "Sit-ups", sets: 3, reps: [20, 20, 20], weight: [null, null, null] }
            ]
        }
    ],
    deletedPlans: [
        {
            id: '19',
            name: "Deleted Plan 1",
            days: ["Wednesday", "Friday"],
            exercises: [
                {
                    day: "Wednesday",
                    workouts: [
                        { name: "Pull-ups", description: "Pull-ups description", sets: 3, reps: [10, 10, 10], weight: [null, null, null] },
                        { name: "Dips", description: "Dips description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
                    ]
                },
                {
                    day: "Friday",
                    workouts: [
                        { name: "Squats", description: "Squats description", sets: 3, reps: [20, 20, 20], weight: [null, null, null] },
                        { name: "Lunges", description: "Lunges description", sets: 3, reps: [15, 15, 15], weight: [null, null, null] }
                    ]
                }
            ]
        }
    ],
    deletedWorkouts: [
        {
            id: '20',
            name: "Deleted Workout 1",
            exercises: [
                { name: "Push-ups", sets: 3, reps: [15, 15, 15], weight: [null, null, null] },
                { name: "Squats", sets: 3, reps: [20, 20, 20], weight: [null, null, null] }
            ]
        }
    ]
};

export default dummyData;