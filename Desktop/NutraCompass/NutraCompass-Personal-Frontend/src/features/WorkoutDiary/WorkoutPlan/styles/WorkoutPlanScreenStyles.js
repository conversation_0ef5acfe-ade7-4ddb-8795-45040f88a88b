import { StyleSheet } from 'react-native';

const useWorkoutPlanScreenStyles = (theme) => {
    return StyleSheet.create({
        container: {
            flex: 1,
        },
        headerContainer: {
            padding: 10,
            backgroundColor: theme.colors.sectionBackgroundColor,
            alignItems: 'center',
        },
        header: {
            fontSize: 24,
            fontWeight: 'bold',
        },
        bodyContainer: {
            flex: 1,
            paddingHorizontal: 10,
        },
        sectionHeader: {
            fontSize: 18,
            fontWeight: 'bold',
            marginVertical: 10,
        },
        horizontalScroll: {
            marginVertical: 10,
        },
        horizontalContainer: {
            flexDirection: 'row',
        },
        planItem: {
            width: 150, // Adjust this size to make it a square
            height: 150, // Ensure height matches width for square shape
            marginHorizontal: 5,
            padding: 15,
            borderRadius: 10,
            justifyContent: 'flex-end', // Align content to the bottom
            alignItems: 'flex-start', // Align content to the left
            backgroundColor: theme.colors.cardBackgroundColor,
        },
        planText: {
            fontSize: 16,
            color: theme.colors.primaryTextColor,
        },
    });
};

export default useWorkoutPlanScreenStyles;