import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useThemeContext } from "../../../context/ThemeContext";
import useWorkoutPlanScreenStyles from './styles/WorkoutPlanScreenStyles';
import PlanDetailScreen from './PlanDetails/PlanDetailsScreen';
import dummyData from "../WorkoutPlan/dummydata"; // Ensure correct path

const WorkoutPlanScreen = () => {
    const { theme } = useThemeContext();
    const styles = useWorkoutPlanScreenStyles(theme);
    const [selectedPlan, setSelectedPlan] = useState(null);
    const [presetPrograms, setPresetPrograms] = useState([]);
    const [customPrograms, setCustomPrograms] = useState([]);
    const [presetWorkouts, setPresetWorkouts] = useState([]);
    const [customWorkouts, setCustomWorkouts] = useState([]);

    useEffect(() => {
        setPresetPrograms(dummyData.presetPrograms);
        setCustomPrograms(dummyData.customPrograms);
        setPresetWorkouts(dummyData.presetWorkouts);
        setCustomWorkouts(dummyData.customWorkouts);
    }, []);

    const renderPlan = (item, type) => (
        <TouchableOpacity
            style={[styles.planItem, { backgroundColor: theme.colors.cardBackgroundColor }]}
            onPress={() => setSelectedPlan(item)}
            key={item.id}
        >
            <Text style={[styles.planText, { color: theme.colors.primaryTextColor }]}>{item.name}</Text>
        </TouchableOpacity>
    );

    if (selectedPlan) {
        return <PlanDetailScreen plan={selectedPlan} onBack={() => setSelectedPlan(null)} />;
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.colors.sectionBackgroundColor }]}>
            <View style={styles.headerContainer}>
                <Text style={[styles.header, { color: theme.colors.primaryTextColor }]}>Workout Plans</Text>
            </View>
            <ScrollView style={styles.bodyContainer}>
                <View>
                    <Text style={[styles.sectionHeader, { color: theme.colors.primaryTextColor }]}>Preset Programs</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
                        <View style={styles.horizontalContainer}>
                            {presetPrograms.map(item => renderPlan(item, 'presetProgram'))}
                        </View>
                    </ScrollView>
                </View>
                <View>
                    <Text style={[styles.sectionHeader, { color: theme.colors.primaryTextColor }]}>Custom Programs</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
                        <View style={styles.horizontalContainer}>
                            {customPrograms.map(item => renderPlan(item, 'customProgram'))}
                        </View>
                    </ScrollView>
                </View>
                <View>
                    <Text style={[styles.sectionHeader, { color: theme.colors.primaryTextColor }]}>Preset Workouts</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
                        <View style={styles.horizontalContainer}>
                            {presetWorkouts.map(item => renderPlan(item, 'presetWorkout'))}
                        </View>
                    </ScrollView>
                </View>
                <View>
                    <Text style={[styles.sectionHeader, { color: theme.colors.primaryTextColor }]}>Custom Workouts</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
                        <View style={styles.horizontalContainer}>
                            {customWorkouts.map(item => renderPlan(item, 'customWorkout'))}
                        </View>
                    </ScrollView>
                </View>
            </ScrollView>
        </View>
    );
};

export default WorkoutPlanScreen;