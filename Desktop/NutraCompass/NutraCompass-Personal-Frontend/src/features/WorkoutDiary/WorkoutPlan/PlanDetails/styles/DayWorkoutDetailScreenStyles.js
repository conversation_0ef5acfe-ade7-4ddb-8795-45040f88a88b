import { StyleSheet } from 'react-native';

const styles = (theme) => StyleSheet.create({
    container: {
        flex: 1,
        padding: 0,
        backgroundColor: theme.colors.screenBackground,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
        justifyContent:'space-between'
    },
    backButton: {
        padding: 0, // Increased padding for larger button area
    },
    headerText: {
        fontSize: 24,
        color: theme.colors.primaryTextColor,
        alignItems: 'center',
    },
    placeholder: {
        width: 30, // Adjust based on the size of the icon
    },
    listContent: {
        paddingBottom: 20,
        padding:20,
    },
    workoutButton: {
    backgroundColor: theme.colors.cardBackgroundColor,
    padding: 15,
    marginVertical: 10,
    borderRadius: 5,
    alignItems: 'center',
    },
    workoutButtonText: {
    fontSize: 18,
    color: theme.colors.primaryTextColor,
    },
    startButton: {
        marginTop: 20,
        paddingVertical: 10,
        paddingHorizontal: 50,
        backgroundColor: theme.colors.primary,
        borderRadius: 20, // Large border radius for rounded rectangular bubble
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center', // Center the button
        marginBottom:20,
    },
    startButtonText: {
    fontSize: 18,
    color: theme.colors.secondaryTextColor,
    },
});

export default styles;