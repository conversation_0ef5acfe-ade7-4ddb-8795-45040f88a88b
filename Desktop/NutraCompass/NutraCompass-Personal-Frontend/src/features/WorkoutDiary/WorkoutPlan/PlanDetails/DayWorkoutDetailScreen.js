import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons'; // Ensure you have this installed
import { useThemeContext } from "../../../../context/ThemeContext"; // Ensure correct path
import useDayWorkoutDetailScreenStyles from './styles/DayWorkoutDetailScreenStyles'; // Ensure correct path
import WorkoutDetailScreen from "../PlanDetails/WorkoutDetailScreen"; // Ensure correct path

const DayWorkoutDetailScreen = ({ day, exercises, onBack }) => {
    const { theme } = useThemeContext();
    const styles = useDayWorkoutDetailScreenStyles(theme);

    const [selectedWorkout, setSelectedWorkout] = useState(null);

    const startWorkout = () => {
        setSelectedWorkout(exercises[0]);
    };

    const completeWorkout = () => {
        const currentIndex = exercises.findIndex(ex => ex.name === selectedWorkout.name);
        if (currentIndex < exercises.length - 1) {
            setSelectedWorkout(exercises[currentIndex + 1]);
        } else {
            setSelectedWorkout(null);
            onBack();
        }
    };

    if (selectedWorkout) {
        return (
            <WorkoutDetailScreen
                workout={selectedWorkout}
                day={day}
                onBack={() => setSelectedWorkout(null)}
                onComplete={completeWorkout}
            />
        );
    }

    const renderWorkoutButton = ({ item }) => (
        <View style={styles.workoutButton}>
            <Text style={styles.workoutButtonText}>{item.name}</Text>
        </View>
    );

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={onBack} style={styles.backButton}>
                    <Icon name="chevron-left" size={45} color={theme.colors.primaryTextColor} />
                </TouchableOpacity>
                <Text style={styles.headerText}>{day}</Text>
                <View style={styles.placeholder} />
            </View>
            <FlatList
                data={exercises}
                renderItem={renderWorkoutButton}
                keyExtractor={(item, index) => `${item.name}-${index}`}
                contentContainerStyle={styles.listContent}
            />
            <TouchableOpacity style={styles.startButton} onPress={startWorkout}>
                <Text style={styles.startButtonText}>Start</Text>
            </TouchableOpacity>
        </View>
    );
};

export default DayWorkoutDetailScreen;