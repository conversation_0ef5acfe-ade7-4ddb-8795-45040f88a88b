import { StyleSheet } from 'react-native';

const styles = (theme) => StyleSheet.create({
    container: {
        flex: 1,
        padding: 0,
        backgroundColor: theme.colors.screenBackground,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
    },
    backButton: {
        padding: 0,
    },
    headerText: {
        fontSize: 24,
        color: theme.colors.primaryTextColor,
        textAlign: 'center',
        flex: 1,
    },
    placeholder: {
        width: 24, // Adjust based on the size of the icon
        padding:20,
    },
    dayButton: {
        padding: 15,
        marginVertical: 10,
        backgroundColor: theme.colors.primary,
        borderRadius: 5,
    },
    dayButtonText: {
        color: theme.colors.secondaryTextColor,
        textAlign: 'center',
    },
    listContent: {
        paddingBottom: 20,
        padding:20,
    },
});

export default styles;