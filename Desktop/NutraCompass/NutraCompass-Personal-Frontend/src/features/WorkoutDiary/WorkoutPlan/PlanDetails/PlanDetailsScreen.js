import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons'; // Ensure you have this installed
import { useThemeContext } from "../../../../context/ThemeContext"; // Ensure correct path
import usePlanDetailScreenStyles from './styles/PlanDetailScreenStyles'; // Ensure correct path
import DayWorkoutDetailScreen from "../PlanDetails/DayWorkoutDetailScreen"; // Ensure correct path
import dummyData from '../dummydata'; // Ensure correct path

const PlanDetailScreen = ({ plan, onBack }) => {
    const { theme } = useThemeContext();
    const styles = usePlanDetailScreenStyles(theme);

    const [selectedDay, setSelectedDay] = useState(null);

    const renderDayButton = ({ item }) => (
        <TouchableOpacity style={styles.dayButton} onPress={() => setSelectedDay(item)}>
            <Text style={styles.dayButtonText}>{item}</Text>
        </TouchableOpacity>
    );

    if (selectedDay) {
        const dayExercises = plan.exercises.find(ex => ex.day === selectedDay)?.workouts || [];
        return <DayWorkoutDetailScreen day={selectedDay} exercises={dayExercises} onBack={() => setSelectedDay(null)} />;
    }

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={onBack} style={styles.backButton}>
                    <Icon name="chevron-left" size={45} color={theme.colors.primaryTextColor} />
                </TouchableOpacity>
                <Text style={styles.headerText}>{plan.name}</Text>
                <View style={styles.placeholder} />
            </View>
            <FlatList
                data={plan.days}
                renderItem={renderDayButton}
                keyExtractor={(item) => item}
                contentContainerStyle={styles.listContent}
            />
        </View>
    );
};

export default PlanDetailScreen;