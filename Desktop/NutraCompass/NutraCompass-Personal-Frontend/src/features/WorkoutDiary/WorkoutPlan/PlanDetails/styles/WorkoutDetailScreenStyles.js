import { StyleSheet } from 'react-native';

const styles = (theme) => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingTop: 10,
    },
    backButton: {
        padding: 0,
    },
    headerText: {
        fontSize: 24,
        color: theme.colors.primaryTextColor,
        marginLeft: 10,
    },
    workoutName: {
        fontSize: 22,
        color: theme.colors.primaryTextColor,
        marginVertical: 10,
        textAlign: 'center',
    },
    scrollContainer: {
        flex: 1,
    },
    content: {
        alignItems: 'center',
        padding: 20,
    },
    gifPlaceholder: {
        height: 400,
        width: '100%',
        backgroundColor: theme.colors.cardBackgroundColor,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 20,
    },
    gifText: {
        color: theme.colors.subTextColor,
    },
    description: {
        fontSize: 16,
        color: theme.colors.primaryTextColor,
        textAlign: 'center',
        marginVertical: 10,
    },
    fixedContainer: {
        padding: 10,
        backgroundColor: theme.colors.screenBackground,
    },
    sectionTitle: {
        fontSize: 15,
        fontWeight: 'bold',
        color: theme.colors.primaryTextColor,
        marginBottom: 8,
    },
    logContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    picker: {
        width: 130,
        height: 50,
    },
    logInput: {
        width: '30%',
        padding: 8,
        borderWidth: 1,
        borderColor: theme.colors.borderColor,
        borderRadius: 4,
        textAlign: 'center',
        color: theme.colors.primaryTextColor,
        backgroundColor: theme.colors.inputBackgroundColor,
    },
    completeButton: {
        paddingVertical: 10,
        paddingHorizontal: 50,
        backgroundColor: theme.colors.primary,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        marginBottom: 20,
    },
    completeButtonText: {
        color: theme.colors.secondaryTextColor,
    },
});

export default styles;