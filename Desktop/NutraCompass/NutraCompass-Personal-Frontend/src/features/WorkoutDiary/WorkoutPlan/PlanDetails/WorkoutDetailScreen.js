import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, TextInput } from 'react-native';
import { useThemeContext } from "../../../../context/ThemeContext";
import styles from './styles/WorkoutDetailScreenStyles';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Picker } from '@react-native-picker/picker';

const WorkoutDetailScreen = ({ workout, onBack, onComplete }) => {
    const { theme } = useThemeContext();
    const themedStyles = styles(theme);

    const [sets, setSets] = useState(workout.reps.length);
    const [currentSet, setCurrentSet] = useState(0);
    const [reps, setReps] = useState(workout.reps);
    const [weights, setWeights] = useState(workout.weight);

    const handleSetChange = (index) => {
        if (index === sets) {
            addNewSet();
        } else {
            setCurrentSet(index);
        }
    };

    const addNewSet = () => {
        setReps([...reps, 0]);
        setWeights([...weights, 0]);
        setSets(sets + 1);
        setCurrentSet(sets); // Set the new set as the current set
    };

    return (
        <View style={themedStyles.container}>
            <View style={themedStyles.header}>
                <TouchableOpacity onPress={onBack} style={themedStyles.backButton}>
                    <Icon name="chevron-left" size={45} color={theme.colors.primaryTextColor} />
                </TouchableOpacity>
                <Text style={themedStyles.headerText}>{workout.day}</Text>
            </View>
            <Text style={themedStyles.workoutName}>{workout.name}</Text>
            <ScrollView style={themedStyles.scrollContainer}>
                <View style={themedStyles.content}>
                    <View style={themedStyles.gifPlaceholder}>
                        <Text style={themedStyles.gifText}>GIF</Text>
                    </View>
                    <Text style={themedStyles.description}>{workout.description}</Text>
                </View>
            </ScrollView>
            <View style={themedStyles.fixedContainer}>
                <Text style={themedStyles.sectionTitle}>Target</Text>
                <View style={themedStyles.logContainer}>
                    <Picker
                        selectedValue={currentSet}
                        style={themedStyles.picker}
                        onValueChange={(itemValue) => handleSetChange(itemValue)}
                    >
                        {reps.map((_, index) => (
                            <Picker.Item key={index} label={`Set ${index + 1}`} value={index} />
                        ))}
                        <Picker.Item label="Add Set" value={sets} />
                    </Picker>
                    <TextInput
                        style={themedStyles.logInput}
                        placeholder="Reps"
                        keyboardType="numeric"
                        value={reps[currentSet].toString()}
                        onChangeText={(value) => {
                            const newReps = [...reps];
                            newReps[currentSet] = parseInt(value, 10);
                            setReps(newReps);
                        }}
                    />
                    <TextInput
                        style={themedStyles.logInput}
                        placeholder="Weight"
                        keyboardType="numeric"
                        value={weights[currentSet] ? weights[currentSet].toString() : ''}
                        onChangeText={(value) => {
                            const newWeights = [...weights];
                            newWeights[currentSet] = parseInt(value, 10) || 0;
                            setWeights(newWeights);
                        }}
                    />
                </View>
                <Text style={themedStyles.sectionTitle}>Log</Text>
                <View style={themedStyles.logContainer}>
                    <TextInput style={themedStyles.logInput} placeholder="Sets" keyboardType="numeric" />
                    <TextInput style={themedStyles.logInput} placeholder="Reps" keyboardType="numeric" />
                    <TextInput style={themedStyles.logInput} placeholder="Weight" keyboardType="numeric" />
                </View>
                <TouchableOpacity onPress={onComplete} style={themedStyles.completeButton}>
                    <Text style={themedStyles.completeButtonText}>Complete</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default WorkoutDetailScreen;