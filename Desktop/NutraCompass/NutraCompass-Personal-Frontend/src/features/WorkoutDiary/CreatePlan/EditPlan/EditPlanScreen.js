import React, { useState } from 'react';
import { View, Text, TouchableOpacity, TextInput, FlatList, Modal, Button } from 'react-native';
import { useThemeContext } from "../../../../context/ThemeContext";
import useEditPlanScreenStyles from "../EditPlan/styles/EditPlanScreenStyles";
import Icon from 'react-native-vector-icons/MaterialIcons';
import EditDaysScreen from "../EditPlan/EditDaysScreen";

const EditPlanScreen = ({ onBack, plan }) => {
    const { theme } = useThemeContext();
    const styles = useEditPlanScreenStyles(theme);
    const [selectedDay, setSelectedDay] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [days, setDays] = useState(plan.days);

    const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    const availableDays = allDays.filter(day => !days.includes(day));

    const dayOrder = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

    const handleAddDay = (day) => {
        const updatedDays = [...days, day];
        updatedDays.sort((a, b) => dayOrder.indexOf(a) - dayOrder.indexOf(b));
        setDays(updatedDays);
        plan.exercises.push({ day, workouts: [] });
        setModalVisible(false);
    };

    const renderDayButton = ({ item }) => (
        <TouchableOpacity style={styles.dayButton} onPress={() => setSelectedDay(item)}>
            <Text style={styles.dayButtonText}>{item}</Text>
        </TouchableOpacity>
    );

    if (selectedDay) {
        const dayExercises = plan.exercises.find(ex => ex.day === selectedDay)?.workouts || [];
        return <EditDaysScreen day={selectedDay} exercises={dayExercises} onBack={() => setSelectedDay(null)} />;
    }

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={onBack} style={styles.backButton}>
                    <Icon name="chevron-left" size={30} color={theme.colors.primaryTextColor} />
                </TouchableOpacity>
                <TextInput
                    style={styles.input}
                    placeholder="Plan Name"
                    placeholderTextColor={theme.colors.subTextColor}
                    value={plan.name}
                    editable={false}
                />
                <TouchableOpacity>
                    <Text style={styles.saveButton}>Save</Text>
                </TouchableOpacity>
            </View>
            <FlatList
                data={days}
                renderItem={renderDayButton}
                keyExtractor={(item, index) => `${item}-${index}`}
                contentContainerStyle={styles.listContent}
            />
            <TouchableOpacity style={styles.addButton} onPress={() => setModalVisible(true)}>
                <Icon name="add" size={24} color={theme.colors.primaryTextColor} />
                <Text style={styles.addButtonText}>Add Day</Text>
            </TouchableOpacity>

            <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Select a Day</Text>
                        {availableDays.map((day, index) => (
                            <TouchableOpacity key={index} style={styles.modalButton} onPress={() => handleAddDay(day)}>
                                <Text style={styles.modalButtonText}>{day}</Text>
                            </TouchableOpacity>
                        ))}
                        <Button title="Close" onPress={() => setModalVisible(false)} />
                    </View>
                </View>
            </Modal>
        </View>
    );
};

export default EditPlanScreen;