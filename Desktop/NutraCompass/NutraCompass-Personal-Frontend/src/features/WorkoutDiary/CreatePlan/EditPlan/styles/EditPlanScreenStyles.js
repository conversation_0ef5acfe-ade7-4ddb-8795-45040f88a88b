import { StyleSheet } from 'react-native';

const styles = (theme) => StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: theme.colors.screenBackground,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    input: {
        height: 40,
        borderColor: theme.colors.cardBorderColor,
        borderWidth: 1,
        marginBottom: 20,
        paddingHorizontal: 10,
        color: theme.colors.primaryTextColor,
        backgroundColor: theme.colors.cardBackgroundColor,
    },
    dayButton: {
        padding: 15,
        marginVertical: 10,
        backgroundColor: theme.colors.primary,
        borderRadius: 5,
    },
    dayButtonText: {
        color: theme.colors.secondaryTextColor,
        textAlign: 'center',
    },
    backButton: {
        padding: 0,
    },
    backButtonText: {
        color: theme.colors.secondaryTextColor,
    },
    saveButton: {
        color: theme.colors.primary,
        fontSize: 16,
    },
    addButton: {
        marginTop: 20,
        padding: 15,
        backgroundColor: theme.colors.primary,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        flexDirection: 'row',
    },
    addButtonText: {
        color: theme.colors.secondaryTextColor,
        marginLeft: 10,
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContent: {
        width: 300,
        padding: 20,
        backgroundColor: theme.colors.screenBackground,
        borderRadius: 10,
        alignItems: 'center',
        padding: 15,
    },
    modalTitle: {
        fontSize: 18,
        marginBottom: 10,
        color: theme.colors.primaryTextColor,
    },
    modalButton: {
        padding: 10,
        backgroundColor: theme.colors.primary,
        borderRadius: 5,
        marginTop: 10,
        width: '100%',
        alignItems: 'center',
        marginBottom:10,
    },
    modalButtonText: {
        color: theme.colors.secondaryTextColor,
    },
});

export default styles;