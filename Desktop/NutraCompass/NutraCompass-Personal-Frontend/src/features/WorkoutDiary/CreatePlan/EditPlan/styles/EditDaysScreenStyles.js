import { StyleSheet } from 'react-native';

const styles = (theme) => StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: theme.colors.screenBackground,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 20,
    },
    backButton: {
        padding: 0,
    },
    headerText: {
        fontSize: 24,
        color: theme.colors.primaryTextColor,
        textAlign: 'center',
        flex: 1,
    },
    addButton: {
        padding: 10,
    },
    listContent: {
        paddingBottom: 20,
    },
    workoutItem: {
        backgroundColor: theme.colors.cardBackgroundColor,
        padding: 15,
        marginVertical: 10,
        borderRadius: 5,
        justifyContent: 'center',
    },
    workoutName: {
        fontSize: 18,
        color: theme.colors.primaryTextColor,
    },
    workoutDetails: {
        marginTop: 10,
    },
    gifContainer: {
        width: '100%',
        height: 200,
        backgroundColor: theme.colors.cardBackgroundColor,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 20,
    },
    gifPlaceholder: {
        fontSize: 16,
        color: theme.colors.subTextColor,
    },
    workoutDescription: {
        fontSize: 16,
        color: theme.colors.primaryTextColor,
        textAlign: 'center',
        marginVertical: 10,
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContent: {
        width: '80%',
        padding: 20,
        backgroundColor: theme.colors.screenBackground,
        borderRadius: 10,
        alignItems: 'center',
    },
    modalTitle: {
        fontSize: 20,
        marginBottom: 20,
        color: theme.colors.primaryTextColor,
    },
});

export default styles;
