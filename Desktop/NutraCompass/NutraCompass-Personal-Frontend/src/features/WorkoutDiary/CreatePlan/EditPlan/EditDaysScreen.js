import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, Modal, Button } from 'react-native';
import { useThemeContext } from "../../../../context/ThemeContext";
import useEditDaysScreenStyles from './styles/EditDaysScreenStyles';
import Icon from 'react-native-vector-icons/MaterialIcons';

const EditDaysScreen = ({ day, onBack, exercises }) => {
    const { theme } = useThemeContext();
    const styles = useEditDaysScreenStyles(theme);
    const [expandedWorkout, setExpandedWorkout] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);

    const handleWorkoutPress = (workoutName) => {
        setExpandedWorkout(expandedWorkout === workoutName ? null : workoutName);
    };

    const renderWorkout = ({ item }) => (
        <TouchableOpacity style={styles.workoutItem} onPress={() => handleWorkoutPress(item.name)}>
            <Text style={styles.workoutName}>{item.name}</Text>
            {expandedWorkout === item.name && (
                <View style={styles.workoutDetails}>
                    <View style={styles.gifContainer}>
                        <Text style={styles.gifPlaceholder}>GIF</Text>
                        {/* Replace with an actual image tag or component to show the gif */}
                        {/* <Image source={{ uri: item.gif }} style={styles.gif} /> */}
                    </View>
                    <Text style={styles.workoutDescription}>{item.description}</Text>
                </View>
            )}
        </TouchableOpacity>
    );

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={onBack} style={styles.backButton}>
                    <Icon name="chevron-left" size={45} color={theme.colors.primaryTextColor} />
                </TouchableOpacity>
                <Text style={styles.headerText}>{day}</Text>
                <TouchableOpacity style={styles.addButton} onPress={() => setModalVisible(true)}>
                    <Icon name="add" size={30} color={theme.colors.primaryTextColor} />
                </TouchableOpacity>
            </View>
            <FlatList
                data={exercises}
                renderItem={renderWorkout}
                keyExtractor={(item, index) => `${item.name}-${index}`}
                contentContainerStyle={styles.listContent}
            />

            <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Add Workout</Text>
                        {/* Add form or inputs to add workout */}
                        <Button title="Close" onPress={() => setModalVisible(false)} />
                    </View>
                </View>
            </Modal>
        </View>
    );
};

export default EditDaysScreen;