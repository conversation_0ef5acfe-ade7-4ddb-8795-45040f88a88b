import { StyleSheet } from 'react-native';

const useNewProgramScreenStyles = (theme) => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        paddingVertical: 10,
    },
    backButton: {
        padding: 0,
    },
    inputContainer: {
        borderWidth: 1,
        borderColor: theme.colors.cardBorderColor,
        borderRadius: 4,
        marginHorizontal: 10,
        height: 50,
        justifyContent: 'center',
    },
    input: {
        fontSize: 18,
        color: theme.colors.primaryTextColor,
        textAlign: 'center',
        padding: 10,
    },
    saveButton: {
        paddingHorizontal: 10,
    },
    saveButtonText: {
        fontSize: 18,
        color: theme.colors.primaryTextColor,
    },
    thumbnailContainer: {
        alignItems: 'center',
        paddingVertical: 20,
    },
    thumbnailButton: {
        height: 200,
        width: '90%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.cardBackgroundColor,
        borderWidth: 1,
        borderColor: theme.colors.cardBorderColor,
        borderRadius: 4,
    },
    thumbnailButtonText: {
        color: theme.colors.primaryTextColor,
        fontSize: 18,
    },
    addButton: {
        alignSelf: 'flex-end',
        backgroundColor: theme.colors.primary,
        borderRadius: 50,
        padding: 10,
        marginRight: 20,
        marginBottom: 0, // Adjust this value to place it correctly below the image
        zIndex: 1,
    },
    calendar: {
        marginHorizontal: 10,
        borderWidth: 1,
        borderColor: theme.colors.cardBorderColor,
        borderRadius: 4,
        marginTop: 10,
    },
    customDay: {
        alignItems: 'center',
        justifyContent: 'center',
        width: 36,
        height: 30,
        borderRadius: 18,
    },
    durationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 10,
    },
    durationLabel: {
        fontSize: 16,
        color: theme.colors.primaryTextColor,
    },
    durationInput: {
        flex: 1,
        borderWidth: 1,
        borderColor: theme.colors.cardBorderColor,
        borderRadius: 4,
        padding: 10,
        marginLeft: 10,
        color: theme.colors.primaryTextColor,
    },
});

export default useNewProgramScreenStyles;