﻿import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { Calendar } from 'react-native-calendars';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useThemeContext } from "../../../../context/ThemeContext";
import useNewProgramScreenStyles from './styles/NewProgramScreenStyles';
import NewProgramWorkoutScreen from '../NewPlan/DayWorkouts/NewProgramWorkoutScreen';
import TabModal from '../../components/TabModal';

const NewProgramScreen = ({ onBack }) => {
    const { theme } = useThemeContext();
    const styles = useNewProgramScreenStyles(theme);
    const [programName, setProgramName] = useState('');
    const [duration, setDuration] = useState('');
    const [markedDates, setMarkedDates] = useState({});
    const [startDate, setStartDate] = useState(null);
    const [selectedDay, setSelectedDay] = useState(null);
    const [isWorkoutModalVisible, setIsWorkoutModalVisible] = useState(false);
    const lastPress = useRef(0);

    useEffect(() => {
        highlightCurrentDay();
    }, []);

    const highlightCurrentDay = () => {
        const today = new Date().toISOString().split('T')[0];
        setMarkedDates(prevDates => ({
            ...prevDates,
            [today]: { selected: true, selectedColor: theme.colors.primary, isCurrentDay: true }
        }));
    };

    const handleDayPress = day => {
        const dateString = day.dateString;
        const currentTime = new Date().getTime();
        const timeDelta = currentTime - lastPress.current;

        if (timeDelta < 300) {
            // Double click detected
            setStartDate(dateString);
            const newMarkedDates = { ...markedDates };
            Object.keys(newMarkedDates).forEach(key => {
                if (newMarkedDates[key].isDuration) {
                    delete newMarkedDates[key];
                }
            });
            if (duration) {
                calculateEndDate(dateString, duration, newMarkedDates);
            }
        } else {
            // Single click detected
            setSelectedDay(prevSelectedDay => (prevSelectedDay === dateString ? null : dateString));
        }

        lastPress.current = currentTime;
    };

    const handleDurationChange = text => {
        setDuration(text);
        if (text.trim() === '') {
            const today = new Date().toISOString().split('T')[0];
            const newMarkedDates = {};
            Object.keys(markedDates).forEach(key => {
                if (!markedDates[key].isDuration) {
                    newMarkedDates[key] = markedDates[key];
                }
            });
            newMarkedDates[today] = { selected: true, selectedColor: theme.colors.primary, isCurrentDay: true };
            setMarkedDates(newMarkedDates);
            return;
        }
        if (startDate) {
            const newMarkedDates = { ...markedDates };
            calculateEndDate(startDate, text, newMarkedDates);
        }
    };

    const calculateEndDate = (start, duration, markedDates) => {
        const parts = duration.split(' ');
        if (parts.length < 2) {
            return;
        }
        const [value, unit] = parts;
        const date = new Date(start);
        if (unit.includes('week')) {
            date.setDate(date.getDate() + parseInt(value) * 7);
        } else if (unit.includes('month')) {
            date.setMonth(date.getMonth() + parseInt(value));
        } else {
            return;
        }
        const endDate = date.toISOString().split('T')[0];
        markDatesInRange(start, endDate, markedDates);
    };

    const markDatesInRange = (start, end, markedDates) => {
        const startDate = new Date(start);
        const endDate = new Date(end);
        const newMarkedDates = { ...markedDates };
        for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
            const dateString = date.toISOString().split('T')[0];
            newMarkedDates[dateString] = { selected: true, selectedColor: theme.colors.primaryLightOpacity, isDuration: true };
        }
        setMarkedDates(newMarkedDates);
        highlightCurrentDay();
    };

    const handleAddWorkout = () => {
        if (selectedDay) {
            setIsWorkoutModalVisible(true);
        } else {
            alert('Please select a day to add a workout.');
        }
    };

    const handleSaveWorkout = workoutData => {
        // Save workout data logic
        console.log('Workout saved:', workoutData);
        markWorkoutDate(selectedDay, workoutData.repeatEOP);
        setIsWorkoutModalVisible(false); // Close the modal after saving
    };

    const markWorkoutDate = (startDay, repeatEOP) => {
        const newMarkedDates = { ...markedDates };
        const startDate = new Date(startDay);
        newMarkedDates[startDay] = { ...newMarkedDates[startDay], marked: true, dotColor: theme.colors.secondary };
        if (repeatEOP && duration) {
            const endDate = new Date(startDate);
            if (duration.includes('week')) {
                const [value] = duration.split(' ');
                endDate.setDate(startDate.getDate() + parseInt(value) * 7);
            } else if (duration.includes('month')) {
                const [value] = duration.split(' ');
                endDate.setMonth(startDate.getMonth() + parseInt(value));
            }
            for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 7)) {
                const dateString = date.toISOString().split('T')[0];
                newMarkedDates[dateString] = { ...newMarkedDates[dateString], marked: true, dotColor: theme.colors.secondary };
            }
        }
        setMarkedDates(newMarkedDates);
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={onBack} style={styles.backButton}>
                    <Icon name="chevron-left" size={40} color={theme.colors.primaryTextColor} />
                </TouchableOpacity>
                <View style={styles.inputContainer}>
                    <TextInput
                        style={styles.input}
                        placeholder="Program Name"
                        value={programName}
                        onChangeText={setProgramName}
                        placeholderTextColor={theme.colors.subTextColor}
                    />
                </View>
                <TouchableOpacity onPress={() => console.log('Save')} style={styles.saveButton}>
                    <Text style={styles.saveButtonText}>Save</Text>
                </TouchableOpacity>
            </View>
            <View style={styles.thumbnailContainer}>
                <TouchableOpacity style={styles.thumbnailButton}>
                    <Text style={styles.thumbnailButtonText}>Upload Thumbnail</Text>
                </TouchableOpacity>
            </View>
            <TouchableOpacity style={styles.addButton} onPress={handleAddWorkout}>
                <Icon name="add" size={24} color={theme.colors.primaryTextColor} />
            </TouchableOpacity>
            <Calendar
                style={styles.calendar}
                onDayPress={handleDayPress}
                markedDates={{
                    ...markedDates,
                    ...(selectedDay ? { [selectedDay]: { selected: true, customStyles: { container: { borderWidth: 2, borderColor: theme.colors.secondary, borderRadius: 18 } } } } : {})
                }}
                markingType={'custom'}
                theme={{
                    calendarBackground: theme.colors.screenBackground,
                    dayTextColor: theme.colors.primaryTextColor,
                    textDisabledColor: theme.colors.subTextColor,
                    monthTextColor: theme.colors.primaryTextColor,
                    arrowColor: theme.colors.primaryTextColor,
                    selectedDayBackgroundColor: theme.colors.secondary,
                    selectedDayTextColor: theme.colors.primaryTextColor,
                }}
            />
            <View style={styles.durationContainer}>
                <Text style={styles.durationLabel}>Duration:</Text>
                <TextInput
                    style={styles.durationInput}
                    placeholder="Enter duration (e.g., 8 weeks)"
                    value={duration}
                    onChangeText={handleDurationChange}
                    placeholderTextColor={theme.colors.subTextColor}
                />
            </View>
            <TabModal
                isVisible={isWorkoutModalVisible}
                onClose={() => setIsWorkoutModalVisible(false)}
                renderContent={() => (
                    <NewProgramWorkoutScreen
                        onClose={() => setIsWorkoutModalVisible(false)}
                        onSave={handleSaveWorkout}
                    />
                )}
            />
        </View>
    );
};

export default NewProgramScreen;