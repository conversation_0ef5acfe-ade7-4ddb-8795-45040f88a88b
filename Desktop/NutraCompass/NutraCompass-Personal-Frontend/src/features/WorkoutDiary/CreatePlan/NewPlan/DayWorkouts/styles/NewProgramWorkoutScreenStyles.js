import { StyleSheet } from "react-native";

const useNewProgramWorkoutScreenStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: 10,
      paddingVertical: 10,
      backgroundColor: theme.colors.headerBackground,
    },
    backButton: {
      padding: 0,
    },
    inputContainer: {
      marginHorizontal: 10,
      borderWidth: 1,
      borderColor: theme.colors.cardBorderColor,
      borderRadius: 4,
      justifyContent: "center",
    },
    input: {
      fontSize: 18,
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      padding: 10,
    },
    saveButton: {
      paddingHorizontal: 10,
      marginBottom: 10,
    },
    saveButtonText: {
      fontSize: 18,
      color: theme.colors.primaryTextColor,
    },
    addWorkoutIcon: {
      marginTop: 10,
      right: 5,
      backgroundColor: theme.colors.primary,
      borderRadius: 50,
      padding: 10,
    },
    bodyContainer: {
      padding: 20,
    },
    bodyContent: {
      paddingBottom: 20,
    },
    imageContainer: {
      alignItems: "center",
      marginBottom: 20,
    },
    image: {
      width: "100%",
      height: 200,
      contentFit: "cover",
      marginBottom: 10,
    },
    imageUploadButton: {
      width: "100%",
      height: 200,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: theme.colors.cardBackgroundColor,
      borderWidth: 1,
      borderColor: theme.colors.cardBorderColor,
      borderRadius: 4,
      marginBottom: 10,
    },
    imageUploadButtonText: {
      color: theme.colors.primaryTextColor,
      fontSize: 18,
    },
    workoutsHeader: {
      fontSize: 22,
      color: theme.colors.primaryTextColor,
      position: "absolute",
      bottom: 10,
      left: 10,
    },
    workoutsContainer: {
      marginTop: 10,
    },
    workoutItem: {
      padding: 15,
      marginBottom: 10,
      backgroundColor: theme.colors.cardBackgroundColor,
      borderRadius: 4,
      width: "100%",
    },
    workoutText: {
      fontSize: 18,
      color: theme.colors.primaryTextColor,
    },
    workoutDetailsContainer: {
      padding: 10,
      backgroundColor: theme.colors.cardBackgroundColor,
      borderRadius: 4,
      marginTop: 0,
      marginBottom: 15,
    },
    targetsContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 10,
    },
    targetText: {
      fontSize: 16,
      color: theme.colors.primaryTextColor,
    },
    targetInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: theme.colors.cardBorderColor,
      borderRadius: 4,
      padding: 10,
      marginLeft: 10,
      color: theme.colors.primaryTextColor,
    },
    picker: {
      height: 50,
      width: 120,
    },
    restContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    restText: {
      fontSize: 16,
      color: theme.colors.primaryTextColor,
    },
    restInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: theme.colors.cardBorderColor,
      borderRadius: 4,
      padding: 10,
      marginLeft: 10,
      color: theme.colors.primaryTextColor,
    },
    saveSetButton: {
      marginLeft: 10,
      backgroundColor: theme.colors.primary,
      borderRadius: 4,
      padding: 10,
    },
    saveSetButtonText: {
      color: theme.colors.primaryTextColor,
    },
    repeatContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 10,
      borderTopWidth: 1,
      borderColor: theme.colors.cardBorderColor,
    },
    repeatText: {
      fontSize: 18,
      color: theme.colors.primaryTextColor,
    },
  });

export default useNewProgramWorkoutScreenStyles;
