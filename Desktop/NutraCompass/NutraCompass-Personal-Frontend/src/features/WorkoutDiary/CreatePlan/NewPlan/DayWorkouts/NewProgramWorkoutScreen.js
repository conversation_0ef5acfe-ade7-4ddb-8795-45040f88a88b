import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
} from "react-native";
import { Image } from "expo-image";
import { Picker } from "@react-native-picker/picker";
import Icon from "react-native-vector-icons/MaterialIcons";
import * as ImagePicker from "expo-image-picker";
import { useThemeContext } from "../../../../../context/ThemeContext";
import useNewProgramWorkoutScreenStyles from "./styles/NewProgramWorkoutScreenStyles";
import AddWorkoutModal from "../../../components/AddWorkoutModal";

const NewProgramWorkoutScreen = ({ onClose, onSave }) => {
  const { theme } = useThemeContext();
  const styles = useNewProgramWorkoutScreenStyles(theme);
  const [workoutName, setWorkoutName] = useState("");
  const [image, setImage] = useState(null);
  const [workouts, setWorkouts] = useState([{ id: 1, name: "Workout 1" }]);
  const [expandedWorkoutId, setExpandedWorkoutId] = useState(null);
  const [selectedSet, setSelectedSet] = useState("1");
  const [setsData, setSetsData] = useState({
    1: { reps: "", weight: "", rest: "" },
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [repeatEOP, setRepeatEOP] = useState(false);

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.uri);
    }
  };

  const addWorkout = (workoutName) => {
    const nextId = workouts.length ? workouts[workouts.length - 1].id + 1 : 1;
    setWorkouts([...workouts, { id: nextId, name: workoutName }]);
    setIsModalVisible(false);
  };

  const toggleWorkoutDetails = (workoutId) => {
    if (expandedWorkoutId === workoutId) {
      setExpandedWorkoutId(null);
    } else {
      setExpandedWorkoutId(workoutId);
    }
  };

  const saveSet = () => {
    Alert.alert(
      "Set Saved",
      `Set: ${selectedSet}, Reps: ${setsData[selectedSet].reps}, Weight: ${setsData[selectedSet].weight}, Rest: ${setsData[selectedSet].rest}`
    );
  };

  const handleAddMoreSets = () => {
    const nextSet = Object.keys(setsData).length + 1;
    setSetsData({ ...setsData, [nextSet]: { reps: "", weight: "", rest: "" } });
    setSelectedSet(nextSet.toString());
  };

  const handleSetChange = (setNumber) => {
    if (setNumber === "add") {
      handleAddMoreSets();
    } else {
      setSelectedSet(setNumber);
    }
  };

  const handleInputChange = (field, value) => {
    setSetsData({
      ...setsData,
      [selectedSet]: {
        ...setsData[selectedSet],
        [field]: value,
      },
    });
  };

  const handleSave = () => {
    const workoutData = {
      workoutName,
      exercises: workouts,
      image,
      repeatEOP,
    };
    onSave(workoutData);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.backButton}>
          <Icon
            name="chevron-left"
            size={40}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Workout Name"
            value={workoutName}
            onChangeText={setWorkoutName}
            placeholderTextColor={theme.colors.subTextColor}
            color={theme.colors.primaryTextColor}
          />
        </View>
        <View style={{ flexDirection: "column", alignItems: "flex-end" }}>
          <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setIsModalVisible(true)}
            style={styles.addWorkoutIcon}
          >
            <Icon name="add" size={28} color={theme.colors.primaryTextColor} />
          </TouchableOpacity>
        </View>
      </View>
      <ScrollView
        style={styles.bodyContainer}
        contentContainerStyle={styles.bodyContent}
      >
        <View style={styles.imageContainer}>
          <TouchableOpacity
            onPress={pickImage}
            style={styles.imageUploadButton}
          >
            {image ? (
              <Image source={{ uri: image }} style={styles.image} />
            ) : (
              <Text style={styles.imageUploadButtonText}>Upload Image</Text>
            )}
          </TouchableOpacity>
          <Text style={styles.workoutsHeader}>Exercises</Text>
        </View>
        <View style={styles.workoutsContainer}>
          {workouts.map((workout) => (
            <View key={workout.id}>
              <TouchableOpacity
                style={styles.workoutItem}
                onPress={() => toggleWorkoutDetails(workout.id)}
              >
                <Text style={styles.workoutText}>{workout.name}</Text>
              </TouchableOpacity>
              {expandedWorkoutId === workout.id && (
                <View style={styles.workoutDetailsContainer}>
                  <TouchableOpacity style={styles.imageUploadButton}>
                    <Text style={styles.imageUploadButtonText}>
                      Upload GIF/Video
                    </Text>
                  </TouchableOpacity>
                  <View style={styles.targetsContainer}>
                    <Text style={styles.targetText}>Targets: </Text>
                    <Picker
                      selectedValue={selectedSet}
                      style={styles.picker}
                      onValueChange={handleSetChange}
                    >
                      {Object.keys(setsData).map((n) => (
                        <Picker.Item key={n} label={`Set ${n}`} value={n} />
                      ))}
                      <Picker.Item label="Add More Sets" value="add" />
                    </Picker>
                    <TextInput
                      style={styles.targetInput}
                      placeholder="Reps"
                      value={setsData[selectedSet]?.reps || ""}
                      onChangeText={(value) => handleInputChange("reps", value)}
                      keyboardType="numeric"
                      placeholderTextColor={theme.colors.primaryTextColor}
                      color={theme.colors.primaryTextColor}
                    />
                    <TextInput
                      style={styles.targetInput}
                      placeholder="Weight"
                      value={setsData[selectedSet]?.weight || ""}
                      onChangeText={(value) =>
                        handleInputChange("weight", value)
                      }
                      keyboardType="numeric"
                      placeholderTextColor={theme.colors.primaryTextColor}
                      color={theme.colors.primaryTextColor}
                    />
                  </View>
                  <View style={styles.restContainer}>
                    <Text style={styles.restText}>Rest: </Text>
                    <TextInput
                      style={styles.restInput}
                      placeholder="Seconds"
                      value={setsData[selectedSet]?.rest || ""}
                      onChangeText={(value) => handleInputChange("rest", value)}
                      keyboardType="numeric"
                      placeholderTextColor={theme.colors.primaryTextColor}
                      color={theme.colors.primaryTextColor}
                    />
                    <TouchableOpacity
                      onPress={saveSet}
                      style={styles.saveSetButton}
                    >
                      <Text style={styles.saveSetButtonText}>Add</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          ))}
        </View>
      </ScrollView>
      <View style={styles.repeatContainer}>
        <Text style={styles.repeatText}>Repeat until EOP</Text>
        <Switch
          value={repeatEOP}
          onValueChange={setRepeatEOP}
          trackColor={{
            false: theme.colors.subTextColor,
            true: theme.colors.primary,
          }}
          thumbColor={
            repeatEOP ? theme.colors.primary : theme.colors.cardBackgroundColor
          }
        />
      </View>
      <AddWorkoutModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onAddWorkout={addWorkout}
      />
    </View>
  );
};

export default NewProgramWorkoutScreen;
