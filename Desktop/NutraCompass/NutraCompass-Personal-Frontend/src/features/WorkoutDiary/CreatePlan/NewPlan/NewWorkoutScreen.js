import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native";
import { Image } from "expo-image";
import { Picker } from "@react-native-picker/picker";
import * as ImagePicker from "expo-image-picker";
import Icon from "react-native-vector-icons/MaterialIcons";
import { useThemeContext } from "../../../../context/ThemeContext";
import useNewWorkoutScreenStyles from "./styles/NewWorkoutScreenStyles";
import AddWorkoutModal from "../../components/AddWorkoutModal";

const NewWorkoutScreen = ({ onBack }) => {
  const { theme } = useThemeContext();
  const styles = useNewWorkoutScreenStyles(theme);
  const [workoutName, setWorkoutName] = useState("");
  const [image, setImage] = useState(null);
  const [workouts, setWorkouts] = useState([{ id: 1, name: "Workout 1" }]);
  const [expandedWorkoutId, setExpandedWorkoutId] = useState(null);
  const [selectedSet, setSelectedSet] = useState("1");
  const [setsData, setSetsData] = useState({
    1: { reps: "", weight: "", rest: "" },
  });
  const [isModalVisible, setIsModalVisible] = useState(false);

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.cancelled) {
      setImage(result.uri);
    }
  };

  const addWorkout = (workoutName) => {
    const nextId = workouts.length ? workouts[workouts.length - 1].id + 1 : 1;
    setWorkouts([...workouts, { id: nextId, name: workoutName }]);
    setIsModalVisible(false);
  };

  const toggleWorkoutDetails = (workoutId) => {
    if (expandedWorkoutId === workoutId) {
      setExpandedWorkoutId(null);
    } else {
      setExpandedWorkoutId(workoutId);
    }
  };

  const saveSet = () => {
    Alert.alert(
      "Set Saved",
      `Set: ${selectedSet}, Reps: ${setsData[selectedSet].reps}, Weight: ${setsData[selectedSet].weight}, Rest: ${setsData[selectedSet].rest}`
    );
  };

  const handleAddMoreSets = () => {
    const nextSet = Object.keys(setsData).length + 1;
    setSetsData({ ...setsData, [nextSet]: { reps: "", weight: "", rest: "" } });
    setSelectedSet(nextSet.toString());
  };

  const handleSetChange = (setNumber) => {
    if (setNumber === "add") {
      handleAddMoreSets();
    } else {
      setSelectedSet(setNumber);
    }
  };

  const handleInputChange = (field, value) => {
    setSetsData({
      ...setsData,
      [selectedSet]: {
        ...setsData[selectedSet],
        [field]: value,
      },
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Icon
            name="chevron-left"
            size={40}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Workout Name"
            value={workoutName}
            onChangeText={setWorkoutName}
            placeholderTextColor={theme.colors.subTextColor}
          />
        </View>
        <View style={{ flexDirection: "column", alignItems: "flex-end" }}>
          <TouchableOpacity
            onPress={() => console.log("Save")}
            style={styles.saveButton}
          >
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setIsModalVisible(true)}
            style={styles.addWorkoutIcon}
          >
            <Icon name="add" size={28} color={theme.colors.primaryTextColor} />
          </TouchableOpacity>
        </View>
      </View>
      <ScrollView
        style={styles.bodyContainer}
        contentContainerStyle={styles.bodyContent}
      >
        <View style={styles.imageContainer}>
          <TouchableOpacity
            onPress={pickImage}
            style={styles.imageUploadButton}
          >
            {image ? (
              <Image source={{ uri: image }} style={styles.image} />
            ) : (
              <Text style={styles.imageUploadButtonText}>Upload Image</Text>
            )}
          </TouchableOpacity>
          <Text style={styles.workoutsHeader}>Exercises</Text>
        </View>
        <View style={styles.workoutsContainer}>
          {workouts.map((workout) => (
            <View key={workout.id}>
              <TouchableOpacity
                style={styles.workoutItem}
                onPress={() => toggleWorkoutDetails(workout.id)}
              >
                <Text style={styles.workoutText}>{workout.name}</Text>
              </TouchableOpacity>
              {expandedWorkoutId === workout.id && (
                <View style={styles.workoutDetailsContainer}>
                  <TouchableOpacity style={styles.imageUploadButton}>
                    <Text style={styles.imageUploadButtonText}>
                      Upload GIF/Video
                    </Text>
                  </TouchableOpacity>
                  <View style={styles.targetsContainer}>
                    <Text style={styles.targetText}>Targets: </Text>
                    <Picker
                      selectedValue={selectedSet}
                      style={styles.picker}
                      onValueChange={handleSetChange}
                    >
                      {Object.keys(setsData).map((n) => (
                        <Picker.Item key={n} label={`Set ${n}`} value={n} />
                      ))}
                      <Picker.Item label="Add More Sets" value="add" />
                    </Picker>
                    <TextInput
                      style={styles.targetInput}
                      placeholder="Reps"
                      value={setsData[selectedSet]?.reps || ""}
                      onChangeText={(value) => handleInputChange("reps", value)}
                      keyboardType="numeric"
                    />
                    <TextInput
                      style={styles.targetInput}
                      placeholder="Weight"
                      value={setsData[selectedSet]?.weight || ""}
                      onChangeText={(value) =>
                        handleInputChange("weight", value)
                      }
                      keyboardType="numeric"
                    />
                  </View>
                  <View style={styles.restContainer}>
                    <Text style={styles.restText}>Rest: </Text>
                    <TextInput
                      style={styles.restInput}
                      placeholder="Seconds"
                      value={setsData[selectedSet]?.rest || ""}
                      onChangeText={(value) => handleInputChange("rest", value)}
                      keyboardType="numeric"
                    />
                    <TouchableOpacity
                      onPress={saveSet}
                      style={styles.saveSetButton}
                    >
                      <Text style={styles.saveSetButtonText}>Add</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          ))}
        </View>
      </ScrollView>
      <AddWorkoutModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onAddWorkout={addWorkout}
      />
    </View>
  );
};

export default NewWorkoutScreen;
