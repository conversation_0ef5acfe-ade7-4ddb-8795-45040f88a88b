import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { useThemeContext } from "../../../context/ThemeContext"; // Ensure correct path
import useCreateEditContentStyles from './styles/CreateEditContentStyles'; // Import the custom hook for styles
import NewProgramScreen from "../CreatePlan/NewPlan/NewProgramScreen";
import NewWorkoutScreen from "../CreatePlan/NewPlan/NewWorkoutScreen"; // Ensure correct path
import dummyData from '../WorkoutPlan/dummydata'; // Ensure correct path
import EditPlanScreen from "../CreatePlan/EditPlan/EditPlanScreen"; // Ensure correct path

const CreateEditContent = ({ onClose }) => {
    const { theme } = useThemeContext();
    const styles = useCreateEditContentStyles(theme);
    const [isCreatingNewProgram, setIsCreatingNewProgram] = useState(false);
    const [isCreatingNewWorkout, setIsCreatingNewWorkout] = useState(false);
    const [plans, setPlans] = useState([]);
    const [workouts, setWorkouts] = useState([]);
    const [deletedPlans, setDeletedPlans] = useState([]);
    const [deletedWorkouts, setDeletedWorkouts] = useState([]);
    const [selectedPlan, setSelectedPlan] = useState(null);

    useEffect(() => {
        console.log('Initializing plans and workouts from dummyData');
        setPlans(dummyData.customPrograms || []);
        setWorkouts(dummyData.customWorkouts || []);
        setDeletedPlans(dummyData.deletedPlans || []);
        setDeletedWorkouts(dummyData.deletedWorkouts || []);
    }, []);

    const handleLongPress = (item, type) => {
        const isPlan = type === 'plan';
        const items = isPlan ? plans : workouts;
        const setItems = isPlan ? setPlans : setWorkouts;
        const deletedItems = isPlan ? deletedPlans : deletedWorkouts;
        const setDeletedItems = isPlan ? setDeletedPlans : setDeletedWorkouts;

        const isInDeleted = deletedItems.some(element => element.id === item.id);

        if (isInDeleted) {
            Alert.alert(
                `Restore ${isPlan ? 'Plan' : 'Workout'}`,
                `Do you want to restore this ${isPlan ? 'plan' : 'workout'}?`,
                [
                    { text: "Cancel", style: "cancel" },
                    {
                        text: "Restore",
                        onPress: () => {
                            setDeletedItems(prevDeleted => prevDeleted.filter(element => element.id !== item.id));
                            setItems(prevItems => [...prevItems, item]);
                        }
                    }
                ]
            );
        } else {
            Alert.alert(
                `Delete ${isPlan ? 'Plan' : 'Workout'}`,
                `Are you sure you want to delete this ${isPlan ? 'plan' : 'workout'}?`,
                [
                    { text: "Cancel", style: "cancel" },
                    {
                        text: "Delete",
                        onPress: () => {
                            setItems(prevItems => prevItems.filter(element => element.id !== item.id));
                            setDeletedItems(prevDeleted => [...prevDeleted, item]);
                        }
                    }
                ]
            );
        }
    };

    const handlePress = (item, type) => {
        if (!deletedPlans.find(plan => plan.id === item.id) && !deletedWorkouts.find(workout => workout.id === item.id)) {
            setSelectedPlan(item);
        }
    };

    const renderItem = (item, type) => (
        <TouchableOpacity
            style={[styles.planItem, { backgroundColor: theme.colors.cardBackgroundColor }]}
            onPress={() => handlePress(item, type)}
            onLongPress={() => handleLongPress(item, type)}
            key={item.id}
        >
            <Text style={[styles.planText, { color: theme.colors.primaryTextColor }]}>{item.name}</Text>
        </TouchableOpacity>
    );

    if (isCreatingNewProgram) {
        return <NewProgramScreen onBack={() => setIsCreatingNewProgram(false)} />;
    }

    if (isCreatingNewWorkout) {
        return <NewWorkoutScreen onBack={() => setIsCreatingNewWorkout(false)} />;
    }

    if (selectedPlan) {
        return <EditPlanScreen onBack={() => setSelectedPlan(null)} plan={selectedPlan} />;
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.colors.sectionBackgroundColor }]}>
            <View style={styles.headerContainer}>
                <Text style={[styles.header, { color: theme.colors.primaryTextColor }]}>Create/Edit Plan</Text>
            </View>
            <ScrollView style={styles.bodyContainer}>
                <View>
                    <Text style={[styles.sectionHeader, { color: theme.colors.primaryTextColor }]}>My Programs</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
                        <View style={styles.horizontalContainer}>
                            {plans.map(item => renderItem(item, 'plan'))}
                        </View>
                    </ScrollView>
                </View>
                <View>
                    <Text style={[styles.sectionHeader, { color: theme.colors.primaryTextColor }]}>My Workouts</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
                        <View style={styles.horizontalContainer}>
                            {workouts.map(item => renderItem(item, 'workout'))}
                        </View>
                    </ScrollView>
                </View>
                <View>
                    <Text style={[styles.sectionHeader, { color: theme.colors.primaryTextColor }]}>Deleted Programs</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
                        <View style={styles.horizontalContainer}>
                            {deletedPlans.map(item => renderItem(item, 'plan'))}
                        </View>
                    </ScrollView>
                </View>
                <View>
                    <Text style={[styles.sectionHeader, { color: theme.colors.primaryTextColor }]}>Deleted Workouts</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
                        <View style={styles.horizontalContainer}>
                            {deletedWorkouts.map(item => renderItem(item, 'workout'))}
                        </View>
                    </ScrollView>
                </View>
            </ScrollView>
            <View style={styles.footerContainer}>
                <TouchableOpacity
                    style={[styles.newPlanButton, { backgroundColor: theme.colors.primary }]}
                    onPress={() => setIsCreatingNewProgram(true)}
                >
                    <Text style={[styles.newPlanButtonText, { color: theme.colors.primaryTextColor }]}>+ New Program</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.newPlanButton, { backgroundColor: theme.colors.primary }]}
                    onPress={() => setIsCreatingNewWorkout(true)}
                >
                    <Text style={[styles.newPlanButtonText, { color: theme.colors.primaryTextColor }]}>+ New Workout</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default CreateEditContent;