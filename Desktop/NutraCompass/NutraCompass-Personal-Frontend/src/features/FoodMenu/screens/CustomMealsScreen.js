import React, { useState, useMemo } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Keyboard,
  TextInput,
} from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import {
  Searchbar,
  IconButton,
  Dialog,
  Portal,
  Button,
} from "react-native-paper";
import * as Haptics from "expo-haptics";
import Feather from "react-native-vector-icons/Feather";
import Ionicons from "@expo/vector-icons/Ionicons";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodMenu } from "../context/FoodMenuContext.js";
import CreateCustomMealModal from "../components/CreateCustomMealModal.js";

export default function CustomMealsScreen({ navigation }) {
  const { theme } = useThemeContext();
  const {
    customMeals,
    deleteCustomMeal,
    setTempCustomMeal,
    clearTempCustomMeal,
  } = useFoodMenu();
  const [searchQuery, setSearchQuery] = useState("");
  const onChangeSearch = (query) => setSearchQuery(query.toLowerCase());

  // Use useMemo to avoid unnecessary recalculations
  const sortedMeals = useMemo(() => {
    if (!searchQuery) {
      return customMeals;
    }
    return [...customMeals].sort((a, b) => {
      const nameA = a.foodLabel.toLowerCase();
      const nameB = b.foodLabel.toLowerCase();
      const query = searchQuery.toLowerCase();

      const matchA = nameA.includes(query);
      const matchB = nameB.includes(query);

      if (matchA && !matchB) {
        return -1;
      } else if (!matchA && matchB) {
        return 1;
      } else {
        return 0;
      }
    });
  }, [customMeals, searchQuery]);

  const [isCreateCustomMealModalVisible, setIsCreateCustomMealModalVisible] =
    useState(false);
  const [showDeleteCustomMealDialog, setShowDeleteCustomMealDialog] =
    useState(false);
  const [customMealToDelete, setCustomMealToDelete] = useState("");

  const handleDeleteCustomMealConfirm = async () => {
    try {
      await deleteCustomMeal(customMealToDelete);
      setShowDeleteCustomMealDialog(false);
      setCustomMealToDelete("");
    } catch (error) {
      console.log("Error deleting custom meal.");
    }
  };

  const handleOpenDeleteCustomMealDialog = (mealId) => {
    setCustomMealToDelete(mealId);
    setShowDeleteCustomMealDialog(true);
  };

  const handleDeleteCustomMealCancel = () => {
    setShowDeleteCustomMealDialog(false);
    setCustomMealToDelete("");
  };

  const renderMealItem = ({ item }) => (
    <TouchableOpacity
      onPress={() => {
        setTempCustomMeal(item);
        setIsCreateCustomMealModalVisible(true);
      }}
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: theme.colors.surface,
        borderWidth: 1,
        borderColor: theme.colors.cardBorderColor,
        padding: 4,
        marginVertical: 5,
        borderRadius: 60,
      }}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: 10,
        }}
      >
        <View
          style={{
            width: 60,
            height: 60,
            borderRadius: 60,
            backgroundColor: theme.colors.surface,
            alignItems: "center",
            justifyContent: "center",
            borderWidth: 1,
            borderColor: theme.colors.primaryTextColor,
          }}
        >
          {item?.mealImageUrl ? (
            <Image
              source={{ uri: item.mealImageUrl }}
              contentFit="cover"
              style={{
                width: 60,
                height: 60,
                borderRadius: 60,
              }}
            />
          ) : (
            <Ionicons
              name="image-outline"
              size={24}
              color={theme.colors.subTextColor}
            />
          )}
        </View>

        <View style={{ gap: 2 }}>
          <Text style={{ fontSize: 14, color: theme.colors.primaryTextColor }}>
            {item.foodLabel}
          </Text>
          <Text style={{ fontSize: 12, color: theme.colors.subTextColor }}>
            {Math.round(parseFloat(item?.nutrients?.ENERC_KCAL?.quantity))} cal
          </Text>
        </View>
      </View>

      <IconButton
        onPress={() => handleOpenDeleteCustomMealDialog(item.id)}
        icon="trash-can-outline"
        iconColor={"red"}
        size={18}
      />
    </TouchableOpacity>
  );

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={Keyboard.dismiss}
      style={{
        flex: 1,
        paddingBottom: "20%",
        backgroundColor: theme.colors.screenBackground,
        paddingHorizontal: 8,
      }}
    >
      <LinearGradient
        style={{
          height: "8%",
          justifyContent: "center",
          backgroundColor: theme.colors.surface,
          borderRadius: 10,
          paddingVertical: 4,
          paddingHorizontal: 4,
        }}
        colors={[`${theme.colors.surface}99`, `${theme.colors.surface}99`]}
        start={{ x: 0, y: 1.5 }}
        end={{ x: 1, y: 2 }}
      >
        <View>
          <View style={{ paddingHorizontal: "4%" }}>
            <View
              style={{
                height: "100%",
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "row",
              }}
            >
              <TouchableOpacity
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                  position: "absolute",
                  left: 0,
                }}
                onPress={() => {
                  navigation.navigate("Food Options");
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <Feather
                  name="chevron-left"
                  color={theme.colors.primaryTextColor}
                  size={24}
                />
              </TouchableOpacity>

              <Text
                style={{
                  fontSize: 20,
                  fontWeight: "500",
                  color: theme.colors.primaryTextColor,
                }}
              >
                Custom Meals
              </Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      <View style={{ flex: 1 }}>
        <View
          style={{
            flexDirection: "row",
            padding: 8,
            gap: 8,
          }}
        >
          <Searchbar
            placeholder="Search Meals"
            value={searchQuery}
            onChangeText={onChangeSearch}
            onSubmitEditing={onChangeSearch}
            placeholderTextColor={theme.colors.subTextColor}
            icon={() => (
              <TouchableOpacity
                disabled={true}
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                  width: 40,
                  height: 40,
                }}
              >
                <Ionicons
                  name="search-outline"
                  size={20}
                  color={theme.colors.primaryTextColor}
                />
              </TouchableOpacity>
            )}
            style={{
              flex: 2,
              height: 40,
              borderRadius: 60,
              backgroundColor: theme.colors.surface,
              elevation: 0, // Disable shadow/elevation
            }}
            inputStyle={{
              fontSize: 14,
              color: theme.colors.primaryTextColor,
              alignSelf: "center", // Center text vertically
            }}
          />

          <View
            style={{
              flex: 1,
              flexDirection: "row",
              alignItems: "center",
              gap: 4,
            }}
          >
            <TouchableOpacity
              onPress={() => {
                setIsCreateCustomMealModalVisible(true);
              }}
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: theme.colors.primary,
                padding: 8,
                borderRadius: 60,
              }}
            >
              <Ionicons
                name="add"
                size={18}
                color={theme.colors.primaryTextColor}
              />
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: 12,
                  fontWeight: "500",
                }}
              >
                Create Meal
              </Text>
            </TouchableOpacity>

            <Ionicons
              name="filter-outline"
              size={24}
              color={theme.colors.primaryTextColor}
            />
          </View>
        </View>

        <View style={{ flex: 1, padding: 4 }}>
          <FlatList
            data={sortedMeals}
            renderItem={renderMealItem}
            keyExtractor={(item) => item?.id.toString()}
            style={{ flex: 1 }}
            contentContainerStyle={{ paddingBottom: 150 }}
          />
        </View>
      </View>
      <CreateCustomMealModal
        isVisible={isCreateCustomMealModalVisible}
        closeModal={() => {
          setIsCreateCustomMealModalVisible(!isCreateCustomMealModalVisible);
          clearTempCustomMeal();
        }}
      />

      <Portal>
        <Dialog
          visible={showDeleteCustomMealDialog}
          onDismiss={handleDeleteCustomMealCancel}
        >
          <Dialog.Title>Delete Custom Meal</Dialog.Title>
          <Dialog.Content>
            <Text>Are you sure you want to delete this custom meal?</Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={handleDeleteCustomMealCancel}>Cancel</Button>
            <Button onPress={handleDeleteCustomMealConfirm}>Delete</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </TouchableOpacity>
  );
}
