import React, {
  useState,
  useEffect,
  useMemo,
  createContext,
  useContext,
} from "react";
import axios from "axios"; // Preferred HTTP client for browser-based applications
import { useAuth } from "../../../authentication/context/AuthContext.js"; // Importing context for user authentication
import uuid from "react-native-uuid"; // UUID generator for client-side unique identifiers
import Configs from "../../../../configs.js";

const FoodMenuContext = createContext();

export function useFoodMenu() {
  return useContext(FoodMenuContext);
}

export function FoodMenuProvider({ children }) {
  const { user } = useAuth();
  const userId = user?.uid;
  const apiUrl = Configs.NutraCompass_API_URL; // Base URL for API, should move to environment variables for production

  const [customMeals, setCustomMeals] = useState([]);
  const [tempCustomMeal, setTempCustomMeal] = useState({
    mealItems: [],
    foodLabel: "",
    mealImageUrl: "",
  });

  useEffect(() => {
    const loadData = async () => {
      await loadCustomMeals();
    };

    if (user) {
      loadData();
    }
  }, [user]);

  const loadCustomMeals = async () => {
    try {
      const response = await axios.get(
        `${apiUrl}/v1/food/menu/${userId}/custom-meals`
      );

      const updatedCustomMeals = response.data.map((meal) => {
        if (meal.mealImageUrl) {
          meal.mealImageUrl += `?timestamp=${new Date().getTime()}`;
        }
        return meal;
      });

      setCustomMeals(updatedCustomMeals || []);
    } catch (error) {
      console.error("Error loading custom meals:", error);
    }
  };

  const saveOrUpdateCustomMeal = async (customMeal) => {
    try {
      const url = `${apiUrl}/v1/food/menu/${userId}/custom-meals`;
      const formData = new FormData();

      // Serialize and append mealItems as a JSON string if it exists
      if (customMeal.mealItems) {
        formData.append("mealItems", JSON.stringify(customMeal.mealItems));
      }

      // Serialize and append nutrients as a JSON string if it exists
      if (
        customMeal.nutrients &&
        Object.keys(customMeal.nutrients).length > 0
      ) {
        formData.append("nutrients", JSON.stringify(customMeal.nutrients));
      }

      // Append other regular fields except mealImageUrl and already handled complex fields
      Object.keys(customMeal).forEach((key) => {
        if (
          key !== "mealImageUrl" &&
          key !== "mealItems" &&
          key !== "nutrients"
        ) {
          formData.append(key, customMeal[key]);
        }
      });

      // Append image if it exists
      if (customMeal.mealImageUrl) {
        formData.append("mealImage", {
          uri: customMeal.mealImageUrl,
          type: "image/jpeg", // Adjust based on actual image type
          name: "mealImage.jpg", // Adjust based on actual filename or preference
        });
      }

      // Send the form data with the correct headers
      await axios.post(url, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      // Reload the custom meals from the server to reflect the changes
      loadCustomMeals();
      setTempCustomMeal({
        mealItems: [],
        foodLabel: "",
        mealImageUrl: "",
      });
    } catch (error) {
      console.error("Error saving or updating custom meal:", error);
    }
  };

  const deleteCustomMeal = async (mealId) => {
    try {
      await axios.delete(
        `${apiUrl}/v1/food/menu/${userId}/custom-meals/${mealId}`
      );
      loadCustomMeals();
    } catch (error) {
      console.error("Error deleting custom meal:", error);
    }
  };

  // Temp Data Management
  const setTempCustomMealName = (newMealName) => {
    setTempCustomMeal({ ...tempCustomMeal, foodLabel: newMealName });
  };

  const saveFoodToTempCustomMeal = (selectedFoodItem) => {
    const itemExists = tempCustomMeal.mealItems.some(
      (item) => item.foodId === selectedFoodItem.foodId
    );
    if (itemExists) {
      setTempCustomMeal((prevMeal) => {
        const updatedMealItems = prevMeal.mealItems.map((item) =>
          item.foodId === selectedFoodItem.foodId
            ? { ...selectedFoodItem, foodId: item.foodId }
            : item
        );
        return { ...prevMeal, mealItems: updatedMealItems };
      });
    } else {
      setTempCustomMeal((prevMeal) => ({
        ...prevMeal,
        mealItems: [
          ...prevMeal.mealItems,
          { ...selectedFoodItem, foodId: selectedFoodItem.foodId || uuid.v4() },
        ],
      }));
    }
  };

  const addLoggedFoodItemsToTempCustomMeal = (foodItems) => {
    const formattedItems = foodItems.map((item) => ({
      foodId: item.foodId,
      foodLabel: item.foodLabel,
      foodCategory: item?.foodCategory || "",
      foodBrand: item?.foodBrand || "",
      numberOfServings: item.numberOfServings,
      activeMeasure: { ...item.activeMeasure },
      nutrients: { ...item.nutrients },
      measures: item.measures,
    }));

    setTempCustomMeal((prev) => ({
      ...prev,
      mealItems: [...prev.mealItems, ...formattedItems],
    }));
  };

  const deleteFoodFromTempCustomMeal = (index) => {
    setTempCustomMeal((prevMeal) => {
      const updatedMealItems = [...prevMeal.mealItems];
      updatedMealItems.splice(index, 1);
      return { ...prevMeal, mealItems: updatedMealItems };
    });
  };

  const clearTempCustomMeal = () => {
    setTempCustomMeal({ mealItems: [], foodLabel: "", mealImageUrl: "" });
  };

  const contextValue = useMemo(
    () => ({
      customMeals,
      setCustomMeals,
      tempCustomMeal,
      setTempCustomMeal,
      loadCustomMeals,
      saveOrUpdateCustomMeal,
      deleteCustomMeal,
      setTempCustomMealName,
      saveFoodToTempCustomMeal,
      deleteFoodFromTempCustomMeal,
      clearTempCustomMeal,
      addLoggedFoodItemsToTempCustomMeal,
    }),
    [
      customMeals,
      setCustomMeals,
      tempCustomMeal,
      setTempCustomMeal,
      loadCustomMeals,
      saveOrUpdateCustomMeal,
      deleteCustomMeal,
      setTempCustomMealName,
      saveFoodToTempCustomMeal,
      deleteFoodFromTempCustomMeal,
      clearTempCustomMeal,
      addLoggedFoodItemsToTempCustomMeal,
    ]
  );

  return (
    <FoodMenuContext.Provider value={contextValue}>
      {children}
    </FoodMenuContext.Provider>
  );
}
