// CreateCustomMealModal.js
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  FlatList,
  Alert,
} from "react-native";
import { Image } from "expo-image";
import Modal from "react-native-modal";
import { LinearGradient } from "expo-linear-gradient";
import { Card, Button, TextInput, TouchableRipple } from "react-native-paper";
import * as Haptics from "expo-haptics";
import Feather from "react-native-vector-icons/Feather";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodMenu } from "../context/FoodMenuContext.js";
import FoodEntryModal from "../../FoodDiary/components/FoodEntryModal.js";
import FoodNutrientModal from "../../FoodDiary/components/FoodNutrientModal.js";
import * as ImagePicker from "expo-image-picker";
import { CustomImageUploadAlert } from "../../../components/CustomImageUploadAlert.js";

export default function CreateCustomMealModal({ isVisible, closeModal }) {
  const { theme } = useThemeContext();
  const {
    saveFoodToTempCustomMeal,
    deleteFoodFromTempCustomMeal,
    tempCustomMeal,
    setTempCustomMeal,
    setTempCustomMealName,
  } = useFoodMenu();

  const [activeFoodItem, setActiveFoodItem] = useState({});
  const [isFoodEntryModalVisible, setIsFoodEntryModalVisible] = useState(false);
  const [isFoodNutrientModalVisible, setIsFoodNutrientModalVisible] =
    useState(false);
  const [activeCustomMeal, setActiveCustomMeal] = useState(null);
  const [showCustomImageUploadAlert, setShowCustomImageUploadAlert] =
    useState(false);

  useEffect(() => {
    // Update custom meal total nutrients when tempCustomMeal changes
    updateActiveCustomMeal();
  }, [tempCustomMeal]);

  const updateActiveCustomMeal = () => {
    if (tempCustomMeal.mealItems.length > 0) {
      // Initialize an object to store active custom meal data
      const activeCustomMealObject = {
        id: tempCustomMeal?.id || "",
        foodLabel: tempCustomMeal?.foodLabel, // Add foodLabel property with meal name
        foodCategory: "Custom Meal",
        isCustomMeal: true,
        numberOfServings: 1,
        mealImageUrl: tempCustomMeal?.mealImageUrl || "",
        mealItems: [], // Array to hold all the items in the meal
        nutrients: {}, // Initialize nutrients property to store processed nutrients
      };
      // Iterate through each meal item
      tempCustomMeal.mealItems.forEach((item) => {
        // Construct new item object based on the item in tempCustomMeal
        const newItem = {
          foodId: item.foodId,
          foodLabel: item.foodLabel,
          foodCategory: item?.foodCategory || "",
          foodBrand: item?.foodBrand || "",
          numberOfServings: item.numberOfServings,
          activeMeasure: { ...item.activeMeasure },
          nutrients: { ...item.nutrients },
          measures: item.measures,
        };

        // Push the new item into the items array of activeCustomMealObject
        activeCustomMealObject.mealItems.push(newItem);

        // Iterate over the keys in item.nutrients
        for (const key in item.nutrients) {
          if (item.nutrients.hasOwnProperty(key)) {
            if (key === "vitamins" || key === "minerals") {
              // If the nutrient is "vitamins" or "minerals", process each item inside
              if (!activeCustomMealObject.nutrients[key]) {
                activeCustomMealObject.nutrients[key] = {};
              }
              for (const nestedKey in item.nutrients[key]) {
                if (item.nutrients[key].hasOwnProperty(nestedKey)) {
                  // If the nested nutrient exists in nutrients, add the quantity
                  if (activeCustomMealObject.nutrients[key][nestedKey]) {
                    activeCustomMealObject.nutrients[key][nestedKey].quantity =
                      (
                        parseFloat(
                          activeCustomMealObject.nutrients[key][nestedKey]
                            .quantity
                        ) + parseFloat(item.nutrients[key][nestedKey].quantity)
                      ).toFixed(2);
                  } else {
                    // If the nested nutrient doesn't exist in nutrients, initialize it
                    activeCustomMealObject.nutrients[key][nestedKey] = {
                      ...item.nutrients[key][nestedKey],
                    };
                  }
                }
              }
            } else {
              // Process individual nutrient
              if (activeCustomMealObject.nutrients[key]) {
                // If the nutrient already exists in nutrients, add the quantity
                activeCustomMealObject.nutrients[key].quantity = (
                  parseFloat(activeCustomMealObject.nutrients[key].quantity) +
                  parseFloat(item.nutrients[key].quantity)
                ).toFixed(2);
              } else {
                // If the nutrient doesn't exist in nutrients, initialize it
                activeCustomMealObject.nutrients[key] = {
                  ...item.nutrients[key],
                };
              }
            }
          }
        }
      });

      // Set the active custom meal data
      setActiveCustomMeal(activeCustomMealObject);
    } else {
      // If no meal items, reset active custom meal data
      setActiveCustomMeal(null);
    }
  };

  const handleOpenFoodEntryModal = () => {
    setIsFoodEntryModalVisible(true);
  };

  const handleCloseFoodNutrientModal = () => {
    setIsFoodNutrientModalVisible(false);
  };

  const handleOpenFoodNutrientModal = (activeItem) => {
    setActiveFoodItem(activeItem);
    // setShowTotalNutrients(false); // Reset to show the nutrient info of the active food item
    setIsFoodNutrientModalVisible(true);
  };

  const handleSaveFoodToTempMeal = (selectedFoodItem) => {
    // Save the selected food item to the temp meal
    saveFoodToTempCustomMeal(selectedFoodItem);
  };

  const handleDeleteFoodFromTempMeal = (index) => {
    // Delete the selected food item from the temp meal
    deleteFoodFromTempCustomMeal(index);
  };

  const takePhoto = async () => {
    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    // Using the "assets" array to access the selected image
    if (!result.canceled && result.assets) {
      const imageUri = result.assets[0].uri; // Accessing the first selected image URI
      setTempCustomMeal((currentMeal) => ({
        ...currentMeal,
        mealImageUrl: imageUri,
      }));
    }

    setShowCustomImageUploadAlert(false);
  };

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    // Using the "assets" array to access the selected image
    if (!result.canceled && result.assets) {
      const imageUri = result.assets[0].uri; // Accessing the first selected image URI
      setTempCustomMeal((currentMeal) => ({
        ...currentMeal,
        mealImageUrl: imageUri,
      }));
    }

    setShowCustomImageUploadAlert(false);
  };

  const handleDeleteImageConfirmation = () => {
    // Check if there is an image to delete
    if (tempCustomMeal.mealImageUrl) {
      Alert.alert(
        "Delete Image",
        "Are you sure you want to delete this image?",
        [
          {
            text: "Cancel",
            style: "cancel",
          },
          {
            text: "Delete",
            onPress: () => handleDeleteImage(),
            style: "destructive",
          },
        ]
      );
    }
  };

  const handleDeleteImage = () => {
    // Delete the image from tempCustomMeal state
    setTempCustomMeal((currentMeal) => ({
      ...currentMeal,
      mealImageUrl: "",
    }));

    setShowCustomImageUploadAlert(false);
  };

  const handleNextButtonPress = () => {
    if (!tempCustomMeal.foodLabel || tempCustomMeal.mealItems.length === 0) {
      // Check if tempCustomMeal.mealName is not provided
      // Display an alert with the required message
      Alert.alert(
        "Enter Details",
        "Please input a meal name and a meal item.",
        [{ text: "OK", onPress: () => console.log("OK pressed") }] // Add an "OK" button to close the alert
      );
    } else {
      // Show the total nutrient information when the "Next" button is clicked
      handleOpenFoodNutrientModal(activeCustomMeal);
    }
  };

  const handlePickImage = pickImage;
  const handleTakePhoto = takePhoto;

  const getFoodNutrientModalTitle = () => {
    if (activeFoodItem?.isCustomMeal === true && activeFoodItem?.id) {
      return "Edit Meal";
    } else if (activeFoodItem?.isCustomMeal === true) {
      return "Create Meal";
    } else if (activeFoodItem?.foodId) {
      return "Edit Meal Item";
    }
  };

  return (
    <Modal
      isVisible={isVisible}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      style={{
        flex: 1,
        height: "100%",
        width: "100%",
        margin: 0,
        backgroundColor: theme.colors.screenBackground,
      }}
    >
      <TouchableOpacity
        style={{ flex: 1 }}
        onPress={Keyboard.dismiss}
        activeOpacity={1}
      >
        {/* Header */}
        <LinearGradient
          style={{
            height: "12%",
            justifyContent: "flex-end",
            borderBottomLeftRadius: 24,
            borderBottomRightRadius: 24,
            borderColor: theme.colors.sectionBorderColor,
            borderLeftWidth: 1,
            borderRightWidth: 1,
            borderBottomWidth: 1,
            backgroundColor: theme.colors.screenBackground,
            elevation: 4,
          }}
          colors={[
            `${theme.colors.primary}99`, // Adding "99" for 0.99 opacity
            `${theme.colors.secondary}99`, // Adding "99" for 0.99 opacity
          ]}
          start={{ x: 0, y: 1.5 }} // Top left corner
          end={{ x: 1, y: 2 }} // Bottom right corner
        >
          <View>
            <View style={{ paddingHorizontal: "5%", paddingVertical: 5 }}>
              <View
                style={{
                  height: "100%",
                  justifyContent: "center",
                  alignItems: "flex-end",
                  flexDirection: "row",
                }}
              >
                <TouchableOpacity
                  style={{
                    justifyContent: "center",
                    alignItems: "center",
                    position: "absolute",
                    left: 0,
                  }}
                  onPress={() => {
                    closeModal();
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                >
                  <Feather
                    name="x"
                    color={theme.colors.primaryTextColor}
                    size={28}
                  />
                </TouchableOpacity>

                <Text
                  style={{
                    fontSize: 28,
                    fontWeight: "500",
                    color: theme.colors.primaryTextColor,
                  }}
                >
                  {tempCustomMeal?.id ? "Edit Meal" : "Create Meal"}
                </Text>
              </View>
            </View>
          </View>
        </LinearGradient>
        {/* Body */}
        <View
          style={{
            backgroundColor: theme.colors.screenBackground,
            flex: 1,
            padding: 10,
          }}
        >
          <TextInput
            mode="outlined"
            label="Meal Name (Optional)"
            value={tempCustomMeal?.foodLabel}
            onChangeText={(mealName) => setTempCustomMealName(mealName)}
            outlineStyle={{ borderRadius: 12 }}
          />

          <TouchableRipple
            onPress={() => setShowCustomImageUploadAlert(true)}
            onLongPress={handleDeleteImageConfirmation}
            style={{
              width: "100%",
              alignItems: "center",
              marginVertical: 10,
              borderLeftWidth: 1,
              borderRightWidth: 1,
              borderColor: theme.colors.cardBorderColor,
              backgroundColor: theme.colors.surface,
            }}
          >
            {tempCustomMeal?.mealImageUrl ? (
              <Image
                source={{ uri: tempCustomMeal?.mealImageUrl }}
                style={{
                  width: 150,
                  height: 150,
                  borderRadius: 10,
                  borderWidth: 1,
                }}
              />
            ) : (
              <View
                style={{
                  width: "100%",
                  height: 150,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text style={{ color: theme.colors.primary, fontSize: 16 }}>
                  Upload an image for your custom meal
                </Text>
              </View>
            )}
          </TouchableRipple>

          <View
            style={{
              flex: 1,
              backgroundColor: theme.colors.surface,
              borderRadius: 12,
              borderWidth: 1,
              borderColor: theme.colors.cardBorderColor,
            }}
          >
            {/* Button to open FoodEntryModal */}
            <Button
              icon={"plus"}
              style={{
                borderBottomWidth: 1,
                borderColor: theme.colors.cardBorderColor,
                borderRadius: 0,
                padding: 4,
              }}
              onPress={handleOpenFoodEntryModal} // Call handleOpenFoodEntryModal on press
            >
              ADD MEAL ITEM
            </Button>
            {/** Display Food Items In Custom Meal */}
            <FlatList
              style={{ flex: 1 }}
              data={tempCustomMeal.mealItems} // Use the mealItems from tempCustomMeal
              keyExtractor={(item, index) => index.toString()}
              renderItem={({ item, index }) => (
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    padding: 10,
                    borderBottomWidth: 1,
                    borderBottomColor: theme.colors.cardBorderColor,
                  }}
                  onPress={() => {
                    if (item.foodCategory === "Quick Add") {
                      console.log(
                        "Item is a Quick Add, handle this entry differently."
                      );
                    } else {
                      handleOpenFoodNutrientModal(item); // Pass item directly instead of activeItem
                    }
                  }}
                >
                  <View style={{ gap: 5 }}>
                    <Text
                      style={{
                        fontSize: 16,
                        color: theme.colors.primaryTextColor,
                        maxWidth: "90%",
                      }}
                    >
                      {item.foodCategory === "Quick Add" && "Quick Add - "}
                      {item.foodLabel}
                    </Text>
                    {item.foodCategory === "Quick Add" ? (
                      <Text
                        style={{
                          fontSize: 14,
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {Math.round(item.nutrients.ENERC_KCAL?.quantity)} cal
                      </Text>
                    ) : (
                      <Text
                        style={{
                          fontSize: 14,
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {Math.round(item.nutrients.ENERC_KCAL.quantity)} cal,{" "}
                        {Math.round(item.activeMeasure.weight) *
                          Math.round(item.numberOfServings)}{" "}
                        {item.activeMeasure.label}
                      </Text>
                    )}
                  </View>

                  <TouchableOpacity
                    style={{
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    onPress={() => handleDeleteFoodFromTempMeal(index)}
                  >
                    <Feather
                      style={{ padding: 10 }}
                      name="x"
                      size={18}
                      color={theme.colors.primaryTextColor}
                    />
                  </TouchableOpacity>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
        <View
          style={{
            height: "10%",
            padding: 10,
            flexDirection: "row",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          {/* <Button
            mode="contained"
            style={{ width: "48%" }}
            onPress={handleSaveMeal}
          >
            SAVE
          </Button> */}
          <Button
            mode="contained"
            style={{ width: "48%" }}
            onPress={handleNextButtonPress}
          >
            NEXT
          </Button>
        </View>

        {/* Render FoodEntryModal */}
        <FoodEntryModal
          isVisible={isFoodEntryModalVisible}
          onSave={handleSaveFoodToTempMeal}
          onCancel={() => setIsFoodEntryModalVisible(false)}
          activeFoodItem={activeFoodItem}
          setActiveFoodItem={setActiveFoodItem}
          isBuildingMeal={true}
        />

        {/* Food Nutrient Modal */}
        <FoodNutrientModal
          isVisible={isFoodNutrientModalVisible}
          closeModal={handleCloseFoodNutrientModal}
          activeFoodItem={activeFoodItem} // Show totalNutrients if showTotalNutrients is true, otherwise show activeFoodItem
          setActiveFoodItem={setActiveFoodItem}
          foodNutrientModalType={getFoodNutrientModalTitle()}
          isBuildingMeal={true}
          closeCreateCustomMealModal={closeModal}
        />
      </TouchableOpacity>
      <CustomImageUploadAlert
        isVisible={showCustomImageUploadAlert}
        onClose={() => setShowCustomImageUploadAlert(false)}
        onPickImage={handlePickImage}
        onTakePhoto={handleTakePhoto}
        onRemoveImage={handleDeleteImageConfirmation}
        hasImage={!!tempCustomMeal?.mealImageUrl}
      />
    </Modal>
  );
}
