import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { Pedometer } from "expo-sensors"; // Import Pedometer from expo-sensors
import { useTime } from "../../../context/TimeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";

const StepsLogContext = createContext();
export const useStepsLog = () => useContext(StepsLogContext);

export const StepsLogProvider = ({ children }) => {
  const { selectedDate } = useTime();
  const { getPhysicalFitnessGoals } = useUserSettings();

  // Retrieve the user's step goal from their settings
  const { stepsGoal, distanceGoal, distanceUnit } = getPhysicalFitnessGoals();

  const [caloriesBurned, setCaloriesBurned] = useState(0);
  const [steps, setSteps] = useState(0);
  const [distance, setDistance] = useState(0);
  const [loading, setLoading] = useState(true);

  /**
   * Keep track of the last real device date (YYYY-MM-DD) on which
   * we have already sent the "Step Goal Achieved!" notification.
   */
  const [lastNotifiedDate, setLastNotifiedDate] = useState("");

  const calculateDistanceFromSteps = useCallback((steps) => {
    const AVERAGE_STEP_LENGTH_METERS = 0.762;
    return (steps * AVERAGE_STEP_LENGTH_METERS) / 1000; // kilometers
  }, []);

  const calculateCaloriesBurned = useCallback((steps) => {
    return steps * 0.04;
  }, []);

  // Fetch step count for the selected date
  useEffect(() => {
    const fetchStepDataForSelectedDate = async () => {
      setLoading(true);

      try {
        const pedometerAvailable = await Pedometer.isAvailableAsync();
        if (!pedometerAvailable) {
          console.warn("Pedometer is not available on this device.");
          setSteps(0);
          setDistance(0);
          setCaloriesBurned(0);
          setLoading(false);
          return;
        }

        const start = new Date(selectedDate);
        start.setHours(0, 0, 0, 0); // Start of the selected day
        const end = new Date(selectedDate);
        end.setHours(23, 59, 59, 999); // End of the selected day

        const result = await Pedometer.getStepCountAsync(start, end);

        if (result && result.steps != null) {
          setSteps(result.steps);
          setDistance(calculateDistanceFromSteps(result.steps));
          setCaloriesBurned(calculateCaloriesBurned(result.steps));
        } else {
          console.warn("No step data available for the selected date.");
          setSteps(0);
          setDistance(0);
          setCaloriesBurned(0);
        }
      } catch (error) {
        console.error("Error fetching step count:", error);
        setSteps(0);
        setDistance(0);
        setCaloriesBurned(0);
      } finally {
        setLoading(false);
      }
    };

    fetchStepDataForSelectedDate();
  }, [selectedDate]);

  /**
   * Send "Step Goal Achieved" notification exactly once per real (device) day.
   * - We track today's date as `todayIsoString`.
   * - If `steps >= stepsGoal` and it's a new day (lastNotifiedDate !== todayIsoString),
   *   we send the notification and set lastNotifiedDate to todayIsoString.
   */
  // useEffect(() => {
  //   // Get today's local date in YYYY-MM-DD
  //   const todayIsoString = new Date().toISOString().split("T")[0];

  //   if (
  //     stepGoalNotification && // Notifications are enabled
  //     stepsGoal > 0 && // There's an actual goal
  //     steps >= stepsGoal && // Goal is reached or exceeded
  //     lastNotifiedDate !== todayIsoString // Not yet notified today
  //   ) {
  //     sendStepGoalNotification();
  //     setLastNotifiedDate(todayIsoString);
  //   }
  // }, [
  //   stepGoalNotification,
  //   steps,
  //   stepsGoal,
  //   lastNotifiedDate,
  //   sendStepGoalNotification,
  // ]);

  // Prepare the context value
  const contextValue = useMemo(
    () => ({
      steps,
      distance,
      caloriesBurned,
      loading,
      calculateCaloriesBurned,
      calculateDistanceFromSteps,
      stepsGoal,
      distanceGoal,
      distanceUnit,
    }),
    [steps, distance, caloriesBurned, loading]
  );

  return (
    <StepsLogContext.Provider value={contextValue}>
      {children}
    </StepsLogContext.Provider>
  );
};
