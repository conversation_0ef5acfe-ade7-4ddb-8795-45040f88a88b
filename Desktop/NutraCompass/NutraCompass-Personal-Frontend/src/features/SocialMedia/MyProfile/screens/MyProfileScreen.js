import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  FlatList,
  Modal,
  TextInput,
} from "react-native";
import { Image } from "expo-image";
import { useNavigation } from "@react-navigation/native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import OpenDrawerToggle from "../../components/OpenDrawerToggle.js";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import LinearGradientCard from "../../../../components/LinearGradientCard.js";
import { useUserSettings } from "../../../Settings/context/UserSettingsContext.js";
import AddFriendModal from "../../Messages/components/AddFriendModal.js";
import ProfilePicture from "../../components/ProfilePicture";

const MyProfileScreen = () => {
  const { theme } = useThemeContext();
  const styles = getStyles(theme);

  const { getUserProfile } = useUserSettings();
  const userProfile = getUserProfile();

  const navigation = useNavigation();

  const [isAddFriendModalVisible, setIsAddFriendModalVisible] = useState(false);
  const [expandedAlbums, setExpandedAlbums] = useState({});

  const [profile, setProfile] = useState({
    name: `${userProfile.firstName} ${userProfile.lastName}`,
    profilePictureUrl:
      "https://via.placeholder.com/150/FFFFFF/000000?Text=Digital.com",
    username: userProfile.userName,
    bio: "Hey!, This is my NutraProfile.",
    followers: "#",
    following: "#",
    albums: [
      {
        name: "Reels",
        content: Array(4)
          .fill()
          .map(() => ({
            type: "image",
            url: "https://via.placeholder.com/150/FFFFFF/000000?Text=Digital.com",
            description: "Placeholder Image",
          })),
      },
      {
        name: "Physique Updates",
        content: Array(4)
          .fill()
          .map(() => ({
            type: "image",
            url: "https://via.placeholder.com/150/FFFFFF/000000?Text=Digital.com",
            description: "Placeholder Image",
          })),
      },
      {
        name: "Food Recipes",
        content: Array(4)
          .fill()
          .map(() => ({
            type: "image",
            url: "https://via.placeholder.com/150/FFFFFF/000000?Text=Digital.com",
            description: "Placeholder Image",
          })),
      },
      {
        name: "Workout Plans",
        content: Array(4)
          .fill()
          .map(() => ({
            type: "image",
            url: "https://via.placeholder.com/150/FFFFFF/000000?Text=Digital.com",
            description: "Placeholder Image",
          })),
      },
    ],
  });

  const toggleAlbum = (name) => {
    setExpandedAlbums((prevState) => ({
      ...prevState,
      [name]: !prevState[name],
    }));
  };

  const ProfileHeaderComponent = () => (
    <View>
      <ImageBackground
        source={{
          uri: "https://via.placeholder.com/150/000000/FFFFFF?Text=Digital.com",
        }}
        contentFit="cover"
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => console.log("Edit profile pressed")}>
            <Icon
              name="pencil"
              size={24}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
        </View>
      </ImageBackground>

      <View style={styles.profileInfoContainer}>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <ProfilePicture size={110} style={styles.profilePic} />
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.stat}>{profile.followers}</Text>
              <Text style={styles.statLabel}>Followers</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.stat}>{profile.following}</Text>
              <Text style={styles.statLabel}>Following</Text>
            </View>
          </View>
        </View>

        <View style={styles.profileTextContainer}>
          <Text style={styles.fullName}>{profile.name}</Text>
          <View style={styles.usernameContainer}>
            <Text style={styles.username}>@{profile.username}</Text>
            <TouchableOpacity
              onPress={() => console.log("Edit username pressed")}
            >
              <Icon
                name="pencil"
                size={16}
                color={theme.colors.primaryTextColor}
              />
            </TouchableOpacity>
          </View>
          <Text style={styles.bio}>{profile.bio}</Text>
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={() => setIsAddFriendModalVisible(true)}
        >
          <Icon
            name="account-plus"
            size={24}
            color={theme.colors.primaryTextColor}
          />
          <Text style={styles.buttonText}>Add Friend</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.button}>
          <Icon
            name="share-variant"
            size={24}
            color={theme.colors.primaryTextColor}
          />
          <Text style={styles.buttonText}>Share Me</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.albumHeader}>
        <Text style={styles.albumTitle}>Social Side Coming Soon</Text>
      </View>
    </View>
  );

  const renderAlbumContent = (content) => {
    // Render up to 3 items
    return content
      .slice(0, 3)
      .map((item, index) => (
        <Image
          key={index}
          style={styles.albumImage}
          source={{ uri: item.url }}
        />
      ));
  };

  const renderAlbum = ({ item }) => (
    <View style={styles.albumContainer}>
      <View style={styles.albumHeader}>
        <Text style={styles.albumName}>{item.name}</Text>
        <TouchableOpacity
          onPress={() => toggleAlbum(item.name)}
          style={styles.expandIcon}
        >
          <Icon
            name={expandedAlbums[item.name] ? "chevron-up" : "chevron-down"}
            size={24}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
      </View>
      {expandedAlbums[item.name] && (
        <View style={styles.albumContent}>
          {renderAlbumContent(item.content)}
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        ListHeaderComponent={ProfileHeaderComponent}
        data={[]}
        renderItem={renderAlbum}
        keyExtractor={(item, index) => index.toString()}
        contentContainerStyle={styles.flatListContent}
      />

      <AddFriendModal
        isVisible={isAddFriendModalVisible}
        onClose={() => setIsAddFriendModalVisible(false)}
      />
    </View>
  );
};

const getStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
      paddingHorizontal: 10,
    },
    header: {
      width: "100%",
      height: 100,
    },
    headerContent: {
      flexDirection: "row",
      justifyContent: "flex-end",
      padding: 10,
    },
    profileInfoContainer: {
      alignItems: "center",
      padding: 60,
      marginTop: -45,
    },
    profilePic: {
      borderRadius: 55,
      borderWidth: 3,
      borderColor: theme.colors.primary,
      marginBottom: 10,
      marginRight: 15,
    },
    statsContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      width: "100%",
      marginTop: 10,
    },
    statItem: {
      alignItems: "center",
      marginHorizontal: 15,
    },
    stat: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    statLabel: {
      fontSize: 14,
      color: theme.colors.subTextColor,
    },
    profileTextContainer: {
      alignItems: "center",
      marginTop: 10,
    },
    fullName: {
      fontSize: 24,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    usernameContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 5,
    },
    username: {
      fontSize: 16,
      color: theme.colors.primary,
      marginRight: 5,
    },
    bio: {
      fontSize: 14,
      color: theme.colors.subTextColor,
      textAlign: "center",
      marginTop: 10,
      paddingHorizontal: 20,
    },
    buttonContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      width: "100%",
      paddingVertical: 0,
    },
    button: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.primary,
      padding: 10,
      borderRadius: 20,
      paddingHorizontal: 20,
      marginHorizontal: 5,
      marginTop: -40,
    },
    buttonText: {
      marginLeft: 10,
      color: theme.colors.primaryTextColor,
    },
    albumHeader: {
      alignSelf: "center",
      paddingHorizontal: 20,
      paddingVertical: 40,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    albumTitle: {
      fontSize: 20,
      color: theme.colors.primaryTextColor,
      marginTop: 10,
    },
    albumContainer: {
      padding: 20,
    },
    albumName: {
      fontSize: 18,
      color: theme.colors.primaryTextColor,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    albumContent: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 10,
    },
    albumImage: {
      width: 100,
      height: 100,
      borderRadius: 10,
      marginRight: 10,
    },
    flatListContent: {
      paddingBottom: 100,
    },
    modalOverlay: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    modalContainer: {
      width: 300,
      padding: 20,
      borderRadius: 10,
      backgroundColor: "#fff",
      alignItems: "center",
    },
    input: {
      width: "100%",
      padding: 10,
      borderWidth: 1,
      borderRadius: 5,
      marginVertical: 10,
    },
    modalButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
    },
    modalButton: {
      padding: 10,
      marginHorizontal: 10,
      borderRadius: 5,
      alignItems: "center",
      backgroundColor: theme.colors.primary,
    },
    expandIcon: {
      paddingHorizontal: 10,
      marginTop: -20,
      marginLeft: "auto",
    },
  });

export default MyProfileScreen;
