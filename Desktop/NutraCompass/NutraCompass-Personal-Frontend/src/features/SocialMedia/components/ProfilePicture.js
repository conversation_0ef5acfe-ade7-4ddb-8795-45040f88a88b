import React, { useState } from "react";
import { View, TouchableOpacity, Text, Alert, Linking } from "react-native";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { CustomImageUploadAlert } from "../../../components/CustomImageUploadAlert";

export default function ProfilePicture({ size }) {
  const { uploadProfilePicture, removeProfilePicture, getUserProfile } =
    useUserSettings();
  const { theme } = useThemeContext();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const userProfile = getUserProfile();
  const profilePictureUrl = userProfile.pictureUrl;

  const handlePickImage = async () => {
    setIsModalVisible(true);
  };

  const handleImagePick = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Alert.alert(
        "Permission Required",
        "You've refused to allow this app to access your photos. Please enable access in your settings.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Settings", onPress: () => Linking.openSettings() },
        ]
      );
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync();
    if (pickerResult.canceled) {
      console.log("User canceled image picker");
      return;
    }

    if (pickerResult.assets && pickerResult.assets.length > 0) {
      const image = pickerResult.assets[0];
      try {
        await uploadProfilePicture({
          uri: image.uri,
          type: image.type,
          name: image.fileName || "profile-pic.jpg",
        });
        setIsModalVisible(false); // Close modal after successful upload
      } catch (error) {
        console.error("Error uploading profile picture:", error);
        Alert.alert("Upload failed", "Unable to upload profile picture.");
      }
    }
  };

  const handleTakePhoto = async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
    if (!permissionResult.granted) {
      Alert.alert(
        "Permission Required",
        "You've refused to allow this app to access your camera. Please enable access in your settings.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Settings", onPress: () => Linking.openSettings() },
        ]
      );
      return;
    }

    const pickerResult = await ImagePicker.launchCameraAsync();
    if (pickerResult.canceled) {
      console.log("User canceled camera picker");
      return;
    }

    if (pickerResult.assets && pickerResult.assets.length > 0) {
      const image = pickerResult.assets[0];
      try {
        await uploadProfilePicture({
          uri: image.uri,
          type: image.type,
          name: image.fileName || "profile-pic.jpg",
        });
        setIsModalVisible(false); // Close modal after successful upload
      } catch (error) {
        console.error("Error uploading profile picture:", error);
        Alert.alert("Upload failed", "Unable to upload profile picture.");
      }
    }
  };

  const handleRemovePhoto = async () => {
    Alert.alert(
      "Remove Profile Picture",
      "Are you sure you want to remove your profile picture?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          onPress: async () => {
            try {
              await removeProfilePicture();
              setIsModalVisible(false); // Close modal after successful removal
            } catch (error) {
              console.error("Error removing profile picture:", error);
              Alert.alert(
                "Removal failed",
                "Unable to remove profile picture."
              );
            }
          },
          style: "destructive",
        },
      ]
    );
  };

  return (
    <View>
      <TouchableOpacity
        style={{
          height: size,
          width: size,
          borderRadius: size / 2,
          borderWidth: 1,
          borderColor: theme.colors.primary,
          overflow: "hidden",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#cccccc",
        }}
        onPress={handlePickImage}
      >
        {profilePictureUrl ? (
          <Image
            source={{ uri: profilePictureUrl }}
            style={{ width: size, height: size, borderRadius: size / 2 }}
          />
        ) : (
          <View
            style={{
              width: size,
              height: size,
              borderRadius: size / 2,
              backgroundColor: "#cccccc",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text>Upload Photo</Text>
          </View>
        )}
      </TouchableOpacity>
      <CustomImageUploadAlert
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onPickImage={handleImagePick}
        onTakePhoto={handleTakePhoto}
        onRemoveImage={handleRemovePhoto}
        hasImage={!!profilePictureUrl}
      />
    </View>
  );
}
