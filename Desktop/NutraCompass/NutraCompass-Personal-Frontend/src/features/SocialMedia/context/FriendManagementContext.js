import React, {
  createContext,
  useContext,
  useState,
  useMemo,
  useEffect,
} from "react";
import axios from "axios";
import Configs from "../../../../configs.js";
import * as Contacts from "expo-contacts";
import { useAuth } from "../../../authentication/context/AuthContext.js";

const FriendManagementContext = createContext();

export const useFriendManagement = () => useContext(FriendManagementContext);

export const FriendManagementProvider = ({ children }) => {
  const { user } = useAuth(); // Destructure authenticated user from context
  const userId = user?.uid;
  const apiUrl = Configs.NutraCompass_API_URL; // Base URL for API, should move to environment variables for production

  const [initialUsers, setInitialUsers] = useState([]); // Store initially loaded users
  const [searchedUsers, setSearchedUsers] = useState([]);
  const [phoneContacts, setPhoneContacts] = useState([]);
  const [incomingRequests, setIncomingRequests] = useState([]);
  const [outgoingRequests, setOutgoingRequests] = useState([]);
  const [friends, setFriends] = useState([]);
  const [blockedUsers, setBlockedUsers] = useState([]);

  useEffect(() => {
    (async () => {
      // Request permissions and fetch phone contacts
      const { status } = await Contacts.requestPermissionsAsync();
      if (status === "granted") {
        const { data } = await Contacts.getContactsAsync({
          fields: [Contacts.Fields.Emails, Contacts.Fields.PhoneNumbers],
        });

        if (data.length > 0) {
          setPhoneContacts(data);
        }
      }

      fetchInitialUsers(); // Fetch initial users to populate the search sections Add Friends and Quick Add with recommended users
      fetchIncomingRequests(); // Fetch incoming requests such as a friend request
      fetchOutgoingRequests(); // Fetch outgoing requests such as a friend request
      fetchFriendsList(); // Fetch the user's friends list
      fetchBlockedUsersList(); // Fetch the user's blocked users list
    })();
  }, []); // This effect should only run once on mount

  const fetchInitialUsers = async () => {
    try {
      const response = await axios.get(`${apiUrl}/v1/users/recommended`, {
        params: { userId: userId },
      });
      setInitialUsers(response.data);
      setSearchedUsers(response.data);
    } catch (error) {
      console.error("Error fetching initial user list:", error);
    }
  };

  // Fetches the list of incoming friend requests for the logged-in user
  const fetchIncomingRequests = async () => {
    try {
      const response = await axios.get(
        `${apiUrl}/v1/friend-requests/incoming`,
        {
          params: { userId: userId },
        }
      );

      setIncomingRequests(response.data);
    } catch (error) {
      console.error("Error fetching incoming friend requests:", error);
    }
  };

  // Fetches the list of outgoing friend requests from the logged-in user
  const fetchOutgoingRequests = async () => {
    try {
      const response = await axios.get(
        `${apiUrl}/v1/friend-requests/outgoing`,
        {
          params: { userId: userId },
        }
      );

      setOutgoingRequests(response.data);
    } catch (error) {
      console.error("Error fetching outgoing friend requests:", error);
    }
  };

  // Filters users to exclude any who are already connected with or blocked by the user
  const filterUsers = (users) => {
    const activeRequestIds = new Set([
      ...incomingRequests.map((req) => req.id),
      ...outgoingRequests.map((req) => req.id),
      ...friends.map((friend) => friend.id),
      ...blockedUsers.map((blocked) => blocked.id),
    ]);
    return users.filter((user) => !activeRequestIds.has(user.id));
  };

  /**
   * Searches for users based on the provided query string, filtering out users involved in
   * incoming or outgoing friend requests. It prioritizes local matches from the already loaded
   * searchedUsers before making a backend call to fetch additional matches.
   *
   * @param {string} searchQuery - The user input used to filter users by name or username.
   */

  const searchUsers = async (searchQuery) => {
    // Immediately clear the search results and return to initial users if the query is empty
    if (!searchQuery) {
      setSearchedUsers(filterUsers(initialUsers)); // Apply filter when clearing the search query
      console.log("Cleared searched users.");
      return;
    }

    // Normalize the search query to improve the matching process
    const normalizedQuery = searchQuery.trim().toLowerCase();
    let matches = initialUsers.filter((user) =>
      `${user.profile?.firstName} ${user.profile?.lastName} ${user.profile?.userName}`
        .toLowerCase()
        .includes(normalizedQuery)
    );

    matches = filterUsers(matches); // Apply dynamic filtering

    if (matches.length > 0) {
      setSearchedUsers(matches);
      console.log("Local users found:", matches);
      return;
    }

    // Fetch from backend if no local matches found
    try {
      const response = await axios.get(`${apiUrl}/v1/users/search`, {
        params: { searchQuery: searchQuery, userId: userId },
      });
      const filteredUsers = filterUsers(response.data);
      console.log("Filtered Searched Users from backend:", filteredUsers);
      setSearchedUsers(filteredUsers);
    } catch (error) {
      console.error("Error searching users in backend:", error);
      setSearchedUsers([]); // Clear the results on error as well
    }
  };

  // Send friend request and update outgoing requests, searchedUsers, and initialUsers state
  const sendFriendRequest = async (receiverId) => {
    try {
      const response = await axios.post(
        `${apiUrl}/v1/${receiverId}/add`,
        {},
        {
          params: { senderId: userId },
        }
      );

      if (response.data.success) {
        console.log("Friend request sent successfully.");
        const receiverProfile = response.data.requestDetails;

        // Update outgoingRequests state with new outgoing request
        setOutgoingRequests((prev) => [...prev, receiverProfile]);

        // Also hide the receiver profile from searchedUsers and initialUsers
        setSearchedUsers((prevUsers) =>
          prevUsers.filter((user) => user.id !== receiverId)
        );
        setInitialUsers((prevUsers) =>
          prevUsers.filter((user) => user.id !== receiverId)
        );
      } else {
        console.error("Failed to send friend request:", response.data.message);
      }
    } catch (error) {
      console.error("Error adding friend:", error);
    }
  };

  // Cancel a friend request
  const cancelFriendRequest = async (receiverId) => {
    try {
      const response = await axios.post(
        `${apiUrl}/v1/${receiverId}/cancel`,
        {},
        {
          params: { senderId: userId },
        }
      );
      if (response.data.success) {
        console.log("Friend request cancellation successful.");
        // Update the outgoingRequests state to reflect this change
        setOutgoingRequests((prev) =>
          prev.filter((request) => request.id !== receiverId)
        );

        // Re-add user to initialUsers and searchedUsers if they are not already present
        const userToAdd = response.data.receiverProfile;

        setInitialUsers((prevUsers) => {
          const exists = prevUsers.some((user) => user.id === receiverId);
          if (!exists) {
            return [...prevUsers, userToAdd];
          }
          return prevUsers;
        });

        setSearchedUsers((prevUsers) => {
          const exists = prevUsers.some((user) => user.id === receiverId);
          if (!exists) {
            return [...prevUsers, userToAdd];
          }
          return prevUsers;
        });
      } else {
        console.error(
          "Failed to cancel friend request:",
          response.data.message
        );
      }
    } catch (error) {
      console.error("Error cancelling friend request:", error);
    }
  };

  // Accept friend request and update friends and incomingRequests state
  const acceptFriendRequest = async (senderId) => {
    try {
      const response = await axios.post(
        `${apiUrl}/v1/${senderId}/accept`,
        {},
        {
          params: { receiverId: userId },
        }
      );

      if (response.data.success) {
        console.log("Friend request accepted successfully.");
        setFriends((prevFriends) => [
          ...prevFriends,
          response.data.senderFriendshipDetails,
        ]); // Add new friend to friends list
        setIncomingRequests((prevIncoming) =>
          prevIncoming.filter((request) => request.id !== senderId)
        ); // Remove from incoming requests
      } else {
        console.error(
          "Failed to accept friend request:",
          response.data.message
        );
      }
    } catch (error) {
      console.error("Error accepting friend request:", error);
    }
  };

  // Reject friend request and update incomingRequests state, re-add to initialUsers
  const rejectFriendRequest = async (senderId) => {
    try {
      const response = await axios.post(
        `${apiUrl}/v1/${senderId}/reject`,
        {},
        {
          params: { receiverId: userId },
        }
      );

      if (response.data.success) {
        console.log("Friend request rejected successfully.");
        setIncomingRequests((prevIncoming) =>
          prevIncoming.filter((request) => request.id !== senderId)
        ); // Remove from incoming requests

        setInitialUsers((prevInitial) => {
          // Check if the user is already in the initial users to prevent duplicates
          const exists = prevInitial.some((user) => user.id === senderId);
          if (!exists) {
            // Assuming response.data.senderProfile includes necessary user data
            return [...prevInitial, response.data.senderProfile];
          }
          return prevInitial;
        }); // Optionally re-add user to initial users if not already present
      } else {
        console.error(
          "Failed to reject friend request:",
          response.data.message
        );
      }
    } catch (error) {
      console.error("Error rejecting friend request:", error);
    }
  };

  // Removes a friend connection and updates the state
  const removeFriend = async (friendId) => {
    try {
      const response = await axios.delete(`${apiUrl}/v1/${friendId}/remove`, {
        params: { userId: userId },
      });
      if (response.data.success) {
        console.log("Friend removed successfully.");
        // Update the friends state to remove the friend
        setFriends((prevFriends) =>
          prevFriends.filter((friend) => friend.id !== friendId)
        );
        // fetchFriendsList(); // Optionally refresh the list from the backend
      } else {
        console.error("Failed to remove friend:", response.data.message);
      }
    } catch (error) {
      console.error("Error removing friend:", error);
    }
  };

  // Blocks a user and updates the state accordingly
  const blockUser = async (blockedUserId) => {
    try {
      const response = await axios.post(
        `${apiUrl}/v1/${blockedUserId}/block`,
        {},
        {
          params: { userId: userId },
        }
      );
      if (response.data.success) {
        console.log("User blocked successfully.");
        // Add this user to the blocked users state
        setBlockedUsers((prevBlocked) => [
          ...prevBlocked,
          response.data.blockedUser,
        ]);

        // Remove the blocked user from the searched users state
        setSearchedUsers((prevSearched) =>
          prevSearched.filter((user) => user.id !== blockedUserId)
        );
      } else {
        console.error("Failed to block user:", response.data.message);
      }
    } catch (error) {
      console.error("Error blocking user:", error);
    }
  };

  // Unblocks a user and updates the state accordingly
  const unblockUser = async (blockedUserId) => {
    try {
      const response = await axios.delete(
        `${apiUrl}/v1/${blockedUserId}/unblock`,
        {
          params: { userId: userId },
        }
      );
      if (response.data.success) {
        console.log("User unblocked successfully.");
        // Remove this user from the blocked users state
        setBlockedUsers((prevBlocked) =>
          prevBlocked.filter((user) => user.id !== blockedUserId)
        );
      } else {
        console.error("Failed to unblock user:", response.data.message);
      }
    } catch (error) {
      console.error("Error unblocking user:", error);
    }
  };

  // Fetches the list of blocked users for the logged-in user
  const fetchBlockedUsersList = async () => {
    try {
      const response = await axios.get(`${apiUrl}/v1/blocked`, {
        params: { userId },
      });
      setBlockedUsers(response.data);
    } catch (error) {
      console.error("Error retrieving friends list:", error);
    }
  };

  // Fetches the list of friend users for the logged-in user
  const fetchFriendsList = async () => {
    try {
      const response = await axios.get(`${apiUrl}/v1/friends`, {
        params: { userId },
      });
      setFriends(response.data);
    } catch (error) {
      console.error("Error retrieving friends list:", error);
    }
  };

  // Function to remove a user from the "Quick Add" section
  const ignoreFriendRecommendation = (id) => {
    setSearchedUsers((prevUsers) => prevUsers.filter((user) => user.id !== id));
    console.log("User removed from Quick Add section with ID:", id);
  };

  const contextValue = useMemo(
    () => ({
      initialUsers,
      friends,
      blockedUsers,
      searchedUsers,
      setSearchedUsers,
      phoneContacts,
      incomingRequests,
      outgoingRequests,
      searchUsers,
      sendFriendRequest,
      cancelFriendRequest,
      acceptFriendRequest,
      rejectFriendRequest,
      removeFriend,
      blockUser,
      unblockUser,
      ignoreFriendRecommendation,
    }),
    [
      initialUsers,
      friends,
      blockedUsers,
      searchedUsers,
      setSearchedUsers,
      phoneContacts,
      incomingRequests,
      outgoingRequests,
      searchUsers,
      sendFriendRequest,
      cancelFriendRequest,
      acceptFriendRequest,
      rejectFriendRequest,
      removeFriend,
      blockUser,
      unblockUser,
      ignoreFriendRecommendation,
    ]
  );

  return (
    <FriendManagementContext.Provider value={contextValue}>
      {children}
    </FriendManagementContext.Provider>
  );
};
