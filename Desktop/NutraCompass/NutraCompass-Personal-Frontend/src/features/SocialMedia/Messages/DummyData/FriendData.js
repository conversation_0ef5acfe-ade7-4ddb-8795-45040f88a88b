// Expanded dummy data for all potential users in the app
export const allUsersList = [
  {
    id: "1",
    name: "<PERSON>",
    username: "alice2817",
    status: "Active 12m ago",
    image: "https://via.placeholder.com/150/0000FF/808080?Text=Alice",
  },
  {
    id: "2",
    name: "<PERSON>",
    username: "bobby<PERSON><PERSON><PERSON><PERSON>",
    status: "Active 1h ago",
    image: "https://via.placeholder.com/150/FF0000/FFFFFF?Text=Bob",
  },
  {
    id: "3",
    name: "<PERSON>",
    username: "charlieschocolatefamily00",
    status: "Active now",
    image: "https://via.placeholder.com/150/FFFF00/000000?Text=Charlie",
  },
  {
    id: "4",
    name: "<PERSON>",
    username: "<PERSON><PERSON><PERSON>",
    status: "Active yesterday",
    image: "https://via.placeholder.com/150/00FF00/000000?Text=Dana",
  },
  {
    id: "5",
    name: "<PERSON>",
    username: "goatmilk2000",
    status: "Active 2h ago",
    image: "https://via.placeholder.com/150/FF00FF/FFFFFF?Text=<PERSON>",
  },
  {
    id: "6",
    name: "<PERSON>",
    username: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    status: "Offline",
    image: "https://via.placeholder.com/150/00FFFF/000000?Text=Frank",
  },
  {
    id: "7",
    name: "Grace",
    username: "Grace100",
    status: "Active last week",
    image: "https://via.placeholder.com/150/FFFFFF/000000?Text=Grace",
  },
  {
    id: "8",
    name: "Hannah",
    username: "HannahMontana",
    status: "Active 5m ago",
    image: "https://via.placeholder.com/150/000000/FFFFFF?Text=Hannah",
  },
  {
    id: "9",
    name: "Ivan",
    username: "KingIvan:>",
    status: "Active now",
    image: "https://via.placeholder.com/150/0000FF/FFFFFF?Text=Ivan",
  },
  {
    id: "10",
    name: "Judy",
    username: "BBL_Judy",
    status: "Active yesterday",
    image: "https://via.placeholder.com/150/FF0000/000000?Text=Judy",
  },
];

// Expanded dummy data for the current friends of the user
export const currentFriendsList = [
  allUsersList[0], // Alice
  allUsersList[1], // Bob
  allUsersList[4], // Eve
  allUsersList[7], // Hannah
];

// Expanded dummy data for messages in the chat
export const messagesList = [
  {
    messageId: "m1",
    userId: "1",
    text: "Hey, how are you?",
    timestamp: "5m",
    isMe: false,
  },
  {
    messageId: "m2",
    userId: "2",
    text: "See you soon!",
    timestamp: "1h",
    isMe: true,
  },
  {
    messageId: "m3",
    userId: "5",
    text: "That sounds great",
    timestamp: "2h",
    isMe: false,
  },
  {
    messageId: "m4",
    userId: "8",
    text: "Can't wait for the weekend!",
    timestamp: "10m",
    isMe: true,
  },
];

// Expanded dummy data for conversations list to show in the chat overview screen
export const conversationsList = [
  {
    conversationId: "c1",
    participants: ["1"],
    lastMessage: "Hey, how are you?",
    lastMessageTimestamp: "5m",
  },
  {
    conversationId: "c2",
    participants: ["2"],
    lastMessage: "See you soon!",
    lastMessageTimestamp: "1h",
  },
  {
    conversationId: "c3",
    participants: ["5"],
    lastMessage: "That sounds great",
    lastMessageTimestamp: "2h",
  },
  {
    conversationId: "c4",
    participants: ["8"],
    lastMessage: "Can't wait for the weekend!",
    lastMessageTimestamp: "10m",
  },
];

// Expanded dummy data for groups and group messages
export const groupsList = [
  {
    groupId: "g1",
    groupName: "Gym Community Chat",
    members: ["1", "2", "5", "8"], // Including Alice, Bob, Eve, Hannah
    messages: [
      {
        messageId: "gm1",
        userId: "1",
        text: "Are we still meeting up tomorrow?",
        timestamp: "5m",
        isMe: false,
      },
      {
        messageId: "gm2",
        userId: "2",
        text: "Yes, see you there!",
        timestamp: "4m",
        isMe: false,
      },
      {
        messageId: "gm3",
        userId: "5",
        text: "Can�t make it, sorry guys.",
        timestamp: "3m",
        isMe: false,
      },
    ],
  },
  {
    groupId: "g2",
    groupName: "Family Group",
    members: ["3", "6", "9"], // Including Charlie, Frank, Ivan
    messages: [
      {
        messageId: "gm4",
        userId: "3",
        text: "Don't forget grandma's birthday party on Sunday.",
        timestamp: "1d",
        isMe: false,
      },
      {
        messageId: "gm5",
        userId: "6",
        text: "Got the cake!",
        timestamp: "20h",
        isMe: false,
      },
    ],
  },
];

// Updated non-friend users list to filter out current friends from all users
export const nonFriendUsersList = allUsersList.filter(
  (user) => !currentFriendsList.some((friend) => friend.id === user.id)
);

export const getUserOrGroupNameById = (id) => {
  const user = allUsersList.find((user) => user.id === id);
  if (user) return user.name;

  const group = groupsList.find((group) => group.groupId === id);
  if (group) return group.groupName;

  return "Unknown";
};
