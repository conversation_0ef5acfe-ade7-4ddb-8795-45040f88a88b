import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  Keyboard,
  Alert,
  SafeAreaView,
} from "react-native";
import { Image } from "expo-image";
import * as Haptics from "expo-haptics";
import Ionicons from "@expo/vector-icons/Ionicons";
import AntDesign from "@expo/vector-icons/AntDesign";
import useAddFriendStyles from "./styles/addFriendModalStyles.js";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { useFriendManagement } from "../../context/FriendManagementContext.js";
import debounce from "lodash.debounce";
import UserOptionsModal from "./UserOptionsModal.js";

const INITIAL_USER_DISPLAY_COUNT = 3;
const MAX_ADDITIONAL_USERS = 6;

const AddFriendModal = ({ isVisible, onClose }) => {
  const { theme } = useThemeContext();
  const styles = useAddFriendStyles();
  const {
    friends,
    phoneContacts,
    incomingRequests,
    outgoingRequests,
    blockedUsers,
    searchUsers,
    setSearchedUsers,
    searchedUsers,
    initialUsers,
    sendFriendRequest,
    cancelFriendRequest,
    acceptFriendRequest,
    rejectFriendRequest,
    removeFriend,
    blockUser,
    unblockUser,
    ignoreFriendRecommendation,
  } = useFriendManagement();
  const [searchQuery, setSearchQuery] = useState("");
  const [displayCounts, setDisplayCounts] = useState({
    "Added Me": INITIAL_USER_DISPLAY_COUNT,
    "Invite To NutraCompass": INITIAL_USER_DISPLAY_COUNT,
    "Add Friends": INITIAL_USER_DISPLAY_COUNT,
    "My Friends": INITIAL_USER_DISPLAY_COUNT,
    "Pending Friend Requests": INITIAL_USER_DISPLAY_COUNT,
  });

  const [isUserOptionsModalVisible, setIsUserOptionsModalVisible] =
    useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  useEffect(() => {
    if (!isVisible) {
      setSearchQuery("");
      setDisplayCounts({
        "Added Me": INITIAL_USER_DISPLAY_COUNT,
        "Invite To NutraCompass": INITIAL_USER_DISPLAY_COUNT,
        "Add Friends": INITIAL_USER_DISPLAY_COUNT,
        "My Friends": INITIAL_USER_DISPLAY_COUNT,
        "Pending Friend Requests": INITIAL_USER_DISPLAY_COUNT,
      });
    }
  }, [isVisible]);

  useEffect(() => {
    // Debouncing could be added here to optimize performance
    if (searchQuery.trim()) {
      const normalizedQuery = searchQuery.toLowerCase();
      debouncedSearch(normalizedQuery);
    } else {
      searchUsers(""); // This clears the search when query is empty
    }
  }, [searchQuery, debouncedSearch]); // Reacts to changes in searchQuery automatically

  const debouncedSearch = useCallback(
    debounce((query) => {
      searchUsers(query);
    }, 300),
    [searchUsers]
  ); // Debounce calling searchUsers

  const handleOpenisUserOptionsModalVisible = (user) => {
    setSelectedUser(user);
    setIsUserOptionsModalVisible(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleViewMoreLess = (sectionTitle) => {
    setDisplayCounts((prevCounts) => {
      const currentCount = prevCounts[sectionTitle];
      const isMaxedOut =
        currentCount >= MAX_ADDITIONAL_USERS + INITIAL_USER_DISPLAY_COUNT;
      return {
        ...prevCounts,
        [sectionTitle]: isMaxedOut
          ? INITIAL_USER_DISPLAY_COUNT
          : currentCount + MAX_ADDITIONAL_USERS,
      };
    });
  };

  const handleAcceptFriend = (id) => {
    acceptFriendRequest(id);
  };

  const handleSendFriendRequest = (id) => {
    sendFriendRequest(id);
  };

  const handleCancelFriendRequest = (id) => {
    cancelFriendRequest(id);
  };

  // Function to reject a friend request
  const handleRejectFriendRequest = (id) => {
    rejectFriendRequest(id);
  };

  // Combined handler function that decides which action to take based on the section title
  const handleIgnoreUser = (id, title) => {
    if (title === "Added Me") {
      handleRejectFriendRequest(id);
    } else if (title === "Quick Add") {
      ignoreFriendRecommendation(id);
    }
  };

  const handleInviteUser = (user) => {
    Alert.alert("Invite Sent", `An SMS invite was sent to ${user.username}!`);
  };

  const sectionsBasedOnSearch = useMemo(() => {
    const normalizedQuery = searchQuery.trim().toLowerCase();

    if (normalizedQuery) {
      // Phone Contacts Sorting Logic
      const scoredPhoneContacts = phoneContacts.map((contact) => {
        let score = 0;
        const nameLower = contact.name.toLowerCase();

        // Highest priority: starts with the query
        if (nameLower.startsWith(normalizedQuery)) {
          score = 3;
        }
        // Secondary priority: contains the query
        else if (nameLower.includes(normalizedQuery)) {
          score = 2;
        }
        // Tertiary priority: matches part of the query
        else if (
          normalizedQuery.split(" ").some((part) => nameLower.includes(part))
        ) {
          score = 1;
        }

        return { ...contact, score };
      });

      // Sort contacts first by score in descending order, then alphabetically
      scoredPhoneContacts.sort((a, b) => {
        if (b.score !== a.score) {
          return b.score - a.score;
        }
        return a.name.localeCompare(b.name);
      });

      // After sorting, slice only for display purposes if needed
      const sortedPhoneContacts = scoredPhoneContacts;

      // User Sorting Logic for "Add Friends" and "Quick Add"

      // Score and sort users based on multiple attributes
      const scoredUsers = searchedUsers.map((user) => {
        let score = 0;
        const firstName = user.profile?.firstName.toLowerCase();
        const lastName = user.profile?.lastName.toLowerCase();
        const userNameLower = user.profile?.userNameLower;

        // Check for starts with the query
        if (
          firstName.startsWith(normalizedQuery) ||
          lastName.startsWith(normalizedQuery) ||
          userNameLower.startsWith(normalizedQuery)
        ) {
          score += 3;
        }
        // Check for contains the query
        if (
          firstName.includes(normalizedQuery) ||
          lastName.includes(normalizedQuery) ||
          userNameLower.includes(normalizedQuery)
        ) {
          score += 2;
        }
        // Check for partial matches with any part of the query
        normalizedQuery.split(" ").forEach((part) => {
          if (
            firstName.includes(part) ||
            lastName.includes(part) ||
            userNameLower.includes(part)
          ) {
            score += 1;
          }
        });

        return { ...user, score };
      });

      // Sort users first by score in descending order, then alphabetically by first name, last name
      scoredUsers.sort((a, b) => {
        if (b.score !== a.score) {
          return b.score - a.score; // Descending order of score
        }
        return `${a.profile?.firstName} ${a.profile?.lastName}`.localeCompare(
          `${b.profile?.firstName} ${b.profile?.lastName}`
        );
      });

      const sortedUsers = scoredUsers;

      // Sections when there's a search query
      const sectionsWhenThereIsSearchQuery = [
        {
          title: "Invite To NutraCompass",
          data:
            sortedPhoneContacts.slice(
              0,
              displayCounts["Invite To NutraCompass"]
            ) || [],
        },
        {
          title: "Add Friends",
          data: sortedUsers.slice(0, displayCounts["Add Friends"]) || [],
        },
        {
          title: "My Friends",
          data: friends.slice(0, displayCounts["My Friends"]) || [],
        },
      ];

      // Conditionally add "Added Me" section if there are incoming requests
      if (incomingRequests && incomingRequests.length > 0) {
        sectionsWhenThereIsSearchQuery.unshift({
          // Unshift adds the element at the start of the array
          title: "Added Me",
          data: incomingRequests.slice(0, displayCounts["Added Me"] || []),
        });
      }

      // Conditionally add "Pending Friend Requests" section if there are outgoing requests
      if (outgoingRequests && outgoingRequests.length > 0) {
        sectionsWhenThereIsSearchQuery.unshift({
          // Unshift adds the element at the start of the array
          title: "Pending Friend Requests",
          data: outgoingRequests.slice(
            0,
            displayCounts["Pending Friend Requests"] || []
          ),
        });
      }

      return sectionsWhenThereIsSearchQuery;
    } else {
      // Default sections when there's no search query
      const defaultSections = [
        {
          title: "Invite Your Friends",
          data: phoneContacts || [],
        },
        {
          title: "Quick Add",
          data: searchedUsers || [],
        },
        {
          title: "Invite To NutraCompass",
          data: phoneContacts || [],
        },
      ];

      // Conditionally add "Added Me" section if there are incoming requests
      if (incomingRequests && incomingRequests.length > 0) {
        defaultSections.splice(1, 0, {
          title: "Added Me",
          data: incomingRequests || [],
        });
      }

      // Conditionally add "Pending Friend Requests" section if there are outgoing requests
      if (outgoingRequests && outgoingRequests.length > 0) {
        defaultSections.splice(1, 0, {
          title: "Pending Friend Requests",
          data: outgoingRequests || [],
        });
      }

      return defaultSections;
    }
  }, [
    searchQuery,
    searchedUsers,
    initialUsers,
    phoneContacts,
    incomingRequests,
    outgoingRequests,
    blockedUsers,
    friends,
    displayCounts,
  ]);

  const renderSection = ({ section }) => {
    // Customize your section rendering as needed
    if (section.title === "Invite Your Friends") {
      return (
        <TouchableOpacity style={styles.inviteFriendsButton}>
          <AntDesign
            name="contacts"
            size={26}
            color={theme.colors.primaryTextColor}
          />
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              fontWeight: "500",
              fontSize: 16,
            }}
          >
            Invite your friends!
          </Text>
          <Ionicons
            name="chevron-forward"
            size={26}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
      );
    } else {
      return (
        <View style={{ width: "95%" }}>
          {section.data.map((user, index) => {
            return (
              <TouchableOpacity
                key={user.id || index}
                onLongPress={() => {
                  if (
                    section.title === "Quick Add" ||
                    section.title === "Add Friends"
                  ) {
                    handleOpenisUserOptionsModalVisible(user);
                  }
                }}
                activeOpacity={0.8}
                style={{
                  ...styles.friendCard,
                  borderTopLeftRadius: index === 0 ? 12 : 0,
                  borderTopRightRadius: index === 0 ? 12 : 0,
                  borderBottomLeftRadius:
                    index === section.data.length - 1 && searchQuery === ""
                      ? 12
                      : 0,
                  borderBottomRightRadius:
                    index === section.data.length - 1 && searchQuery === ""
                      ? 12
                      : 0,
                  borderBottomWidth:
                    index === section.data.length - 1 && searchQuery === ""
                      ? 0
                      : 1,
                  borderBottomColor:
                    index === section.data.length - 1 && searchQuery === ""
                      ? null
                      : theme.colors.sectionBorderColor,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: 10,
                  }}
                >
                  <View
                    style={{
                      backgroundColor: theme.colors.shadow,
                      borderRadius: 90, // Half of your View container's height and width to make it perfectly circular
                      padding: 0, // Adjust padding as needed, or remove if not necessary
                      overflow: "hidden", // Ensures the image does not bleed outside the borderRadius
                      height: 60, // Set height, adjust as needed
                      width: 60, // Set width, adjust as needed
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Image
                      source={getImageSource(user, section.title)}
                      style={{ height: "100%", width: "100%" }}
                    />
                  </View>

                  <View style={{ gap: 4 }}>
                    <Text style={styles.friendName}>
                      {getUserName(user, section.title)}
                    </Text>
                    <Text
                      style={{
                        color: theme.colors.subTextColor,
                        fontSize: 12,
                      }}
                    >
                      {getUserSubtitle(user, section.title)}
                    </Text>
                  </View>
                </View>

                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {section.title === "Invite To NutraCompass" && (
                    <TouchableOpacity
                      style={styles.addButton}
                      onPress={() => handleInviteUser(user)}
                    >
                      <Ionicons
                        name="person-add"
                        size={16}
                        color={theme.colors.primaryTextColor}
                      />

                      <Text style={styles.addButtonText}>Invite</Text>
                    </TouchableOpacity>
                  )}
                  {section.title === "Added Me" && (
                    <TouchableOpacity
                      style={styles.addButton}
                      onPress={() => handleAcceptFriend(user.id)}
                    >
                      <Ionicons
                        name="person-add"
                        size={16}
                        color={theme.colors.primaryTextColor}
                      />

                      <Text style={styles.addButtonText}>Accept</Text>
                    </TouchableOpacity>
                  )}
                  {(section.title === "Quick Add" ||
                    section.title === "Add Friends") && (
                    <TouchableOpacity
                      style={styles.addButton}
                      onPress={() => handleSendFriendRequest(user.id)}
                    >
                      <Ionicons
                        name="person-add"
                        size={16}
                        color={theme.colors.primaryTextColor}
                      />

                      <Text style={styles.addButtonText}>Add</Text>
                    </TouchableOpacity>
                  )}
                  {(section.title === "Quick Add" ||
                    section.title === "Added Me") && (
                    <TouchableOpacity
                      onPress={() => handleIgnoreUser(user.id, section.title)}
                    >
                      <Ionicons
                        name="close"
                        size={18}
                        style={{ paddingHorizontal: 10 }}
                        color={theme.colors.primaryTextColor}
                      />
                    </TouchableOpacity>
                  )}
                  {section.title === "Pending Friend Requests" && (
                    <TouchableOpacity
                      style={{ ...styles.addButton, margin: 16 }}
                      onPress={() => handleCancelFriendRequest(user.id)}
                    >
                      <Ionicons
                        name="person-add"
                        size={16}
                        color={theme.colors.primaryTextColor}
                      />

                      <Text style={styles.addButtonText}>Added</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
          {searchQuery !== "" &&
            (section.title === "Invite To NutraCompass" ||
              section.title === "Add Friends" ||
              section.title === "My Friends") && (
              <TouchableOpacity
                onPress={() => handleViewMoreLess(section.title)}
                style={{
                  backgroundColor: theme.colors.cardBackgroundColor,
                  alignItems: "center",
                  borderBottomLeftRadius: 12,
                  borderBottomRightRadius: 12,
                }}
              >
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    fontSize: 14,
                    fontWeight: "400",
                    padding: 12,
                  }}
                >
                  {displayCounts[section.title] > INITIAL_USER_DISPLAY_COUNT
                    ? "View Less"
                    : "View More"}
                </Text>
              </TouchableOpacity>
            )}
        </View>
      );
    }
  };

  // Helper function to get image source based on the section
  const getImageSource = (user, title) => {
    if (title === "Invite To NutraCompass") {
      return { uri: user?.imageUrl || "https://via.placeholder.com/150" };
    } else if (title === "Added Me") {
      return {
        uri:
          user.senderProfile?.pictureUrl || "https://via.placeholder.com/150",
      };
    } else if (title === "Pending Friend Requests") {
      return {
        uri:
          user.receiverProfile?.pictureUrl || "https://via.placeholder.com/150",
      };
    } else if (title === "My Friends") {
      return {
        uri:
          user.friendProfile?.pictureUrl || "https://via.placeholder.com/150",
      };
    }
    return {
      uri: user.profile?.pictureUrl || "https://via.placeholder.com/150",
    };
  };

  // Helper function to get user full name based on the section
  const getUserName = (user, title) => {
    if (title === "Invite To NutraCompass") {
      return user.name;
    } else if (title === "Added Me") {
      return `${user.senderProfile?.firstName} ${user.senderProfile?.lastName}`;
    } else if (title === "Pending Friend Requests") {
      return `${user.receiverProfile?.firstName} ${user.receiverProfile?.lastName}`;
    } else if (title === "My Friends") {
      return `${user.friendProfile?.firstName} ${user.friendProfile?.lastName}`;
    }
    return `${user.profile?.firstName} ${user.profile?.lastName}`;
  };

  // Helper function to get user subtitle aka username
  const getUserSubtitle = (user, title) => {
    if (title === "Added Me") {
      return user.senderProfile?.userName;
    } else if (title === "Pending Friend Requests") {
      return user.receiverProfile?.userName;
    } else if (title === "My Friends") {
      return user.friendProfile?.userName;
    }
    return user.profile?.userName;
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        activeOpacity={1}
        onPress={Keyboard.dismiss}
        style={styles.centeredView}
      >
        <SafeAreaView style={styles.modalView}>
          <View style={styles.modalHeader}>
            <Ionicons
              name="chevron-down"
              size={26}
              color={theme.colors.primaryTextColor}
              onPress={onClose}
            />
            <Text style={styles.modalTitle}>Add Friend</Text>
            <Ionicons
              name="ellipsis-horizontal"
              size={26}
              color={theme.colors.primaryTextColor}
            />
          </View>
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search for friends..."
              placeholderTextColor={theme.colors.primaryTextColor}
              returnKeyType="search"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                onPress={() => setSearchQuery("")} // Clear the search query
                style={styles.clearButton}
              >
                <Ionicons name="md-close-circle" size={20} color="#ccc" />
              </TouchableOpacity>
            )}
          </View>
          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={{
              flexGrow: 1,
              paddingBottom: 100,
            }}
          >
            {sectionsBasedOnSearch.map((section, index) => (
              <View key={index} style={{ width: "100%", alignItems: "center" }}>
                {section.title !== "Invite Your Friends" && (
                  <View
                    style={{
                      width: "100%",
                      paddingVertical: 16,
                      paddingLeft: 15,
                      gap: 5,
                    }}
                  >
                    <Text
                      style={{
                        alignSelf: "flex-start",
                        color: theme.colors.primaryTextColor,
                        fontWeight: "500",
                        fontSize: 18,
                      }}
                    >
                      {section.title}
                    </Text>
                    {section.title === "Invite To NutraCompass" && (
                      <Text
                        style={{
                          alignSelf: "flex-start",
                          color: theme.colors.subTextColor,
                          fontWeight: "500",
                          fontSize: 10,
                        }}
                      >
                        NutraCompass will send a SMS invite with a friend
                        request for you!
                      </Text>
                    )}
                  </View>
                )}
                {renderSection({ section })}
              </View>
            ))}
          </ScrollView>
        </SafeAreaView>
      </TouchableOpacity>
      <UserOptionsModal
        isVisible={isUserOptionsModalVisible}
        onClose={() => setIsUserOptionsModalVisible(false)}
        user={selectedUser}
        sendFriendRequest={sendFriendRequest}
        blockUser={blockUser}
        ignoreFriendRecommendation={ignoreFriendRecommendation}
      />
    </Modal>
  );
};

export default AddFriendModal;
