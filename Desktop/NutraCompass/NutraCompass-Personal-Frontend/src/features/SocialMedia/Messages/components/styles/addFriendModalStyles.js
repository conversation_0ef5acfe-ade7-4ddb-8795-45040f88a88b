// Import necessary hooks and modules
import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../../context/ThemeContext.js";
const useAddFriendStyles = () => {
  // Access your custom theme context
  const { theme } = useThemeContext();

  // Create and return the styles object
  return StyleSheet.create({
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      width: "100%",
      padding: 10, // Adjust padding as needed
      backgroundColor: theme.colors.screenBackground, // Header background color
    },
    modalTitle: {
      flex: 1,
      textAlign: "center",
      fontSize: 22,
      color: theme.colors.primaryTextColor, // Title text color
      // Ensure title is centered by applying flex to surrounding icons if not explicitly set
    },
    centeredView: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      marginTop: 22,
    },
    modalView: {
      backgroundColor: theme.colors.screenBackground,
      height: "100%",
    },
    closeButton: {
      backgroundColor: "#2196F3",
      borderRadius: 20,
      padding: 10,
      elevation: 2,
      marginTop: 15,
    },
    textStyle: {
      color: theme.colors.primaryTextColor,
      fontWeight: "bold",
      textAlign: "center",
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    searchContainer: {
      flexDirection: "row",
      alignItems: "center",
      padding: 10,
      backgroundColor: theme.colors.cardBackgroundColor,
      borderBottomWidth: 1,
      borderColor: theme.colors.sectionBorderColor,
    },
    searchInput: {
      flex: 1,
      fontSize: 16,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.cardBorderColor,
      paddingLeft: 15,
      height: 40,
      backgroundColor: theme.colors.cardBackgroundColor,
      color: theme.colors.primaryTextColor,
    },
    clearButton: {
      marginLeft: 10,
    },
    inviteFriendsButton: {
      width: "90%",
      paddingVertical: 10,
      paddingHorizontal: 10,
      backgroundColor: theme.colors.cardBackgroundColor,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 15,
      marginBottom: 10,
      borderRadius: 12,
    },
    friendCard: {
      backgroundColor: theme.colors.cardBackgroundColor,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 8,
    },
    friendName: {
      fontSize: 16,
      color: theme.colors.primaryTextColor,
    },
    addButton: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      gap: 5,
      paddingVertical: 6,
      paddingHorizontal: 10,
      backgroundColor: theme.colors.primary,
      borderRadius: 15,
    },
    addButtonText: {
      color: theme.colors.primaryTextColor,
      fontWeight: "500",
      fontSize: 16,
    },
    // Add other styles as needed
  });
};

export default useAddFriendStyles;
