import React from "react";
import { Modal, View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Image } from "expo-image";
import { Button } from "react-native-paper";
import Ionicons from "@expo/vector-icons/Ionicons";
import {
  ThemeContext,
  useThemeContext,
} from "../../../../context/ThemeContext"; // Ensure the path is correct

const UserOptionsModal = ({
  isVisible,
  onClose,
  user,
  sendFriendRequest,
  blockUser,
  ignoreFriendRecommendation,
}) => {
  const { theme } = useThemeContext(); // Using theme context

  if (!user) return null;

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={[
          styles.modalBackground,
          { backgroundColor: "rgba(0, 0, 0, 0.7)" },
        ]}
        activeOpacity={1}
        onPressOut={onClose}
      >
        <View style={[styles.modalContainer]}>
          {/* Top Section */}
          <View
            style={[styles.header, { backgroundColor: theme.colors.surface }]}
          >
            <Image
              source={{
                uri:
                  user.profile.pictureUrl || "https://via.placeholder.com/150",
              }}
              style={styles.profilePic}
            />
            <View style={styles.userInfo}>
              <Text
                style={[
                  styles.userName,
                  { color: theme.colors.primaryTextColor },
                ]}
              >
                {user.profile.firstName} {user.profile.lastName}
              </Text>
              <Text
                style={[
                  styles.viewProfileText,
                  { color: theme.colors.subTextColor },
                ]}
              >
                View Profile
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                sendFriendRequest(user.id);
                onClose();
              }}
              style={{
                ...styles.addButton,
                backgroundColor: theme.colors.primary,
              }}
            >
              <Ionicons
                name="add"
                size={20}
                color={theme.colors.primaryTextColor}
              />

              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "500",
                  fontSize: 16,
                }}
              >
                Add
              </Text>
            </TouchableOpacity>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={theme.colors.subTextColor}
            />
          </View>

          {/* Middle Section */}
          <View
            style={[
              styles.middleSection,
              { backgroundColor: theme.colors.surface },
            ]}
          >
            <TouchableOpacity
              disabled={true}
              onPress={() => {
                blockUser(user.id);
                onClose();
              }}
              style={{
                padding: 12,
                justifyContent: "center",
                borderBottomWidth: 1,
                borderBottomColor: "rgba(0, 0, 0, 0.3)",
                opacity: 0.3,
              }}
            >
              <Text style={{ color: theme.colors.primary, fontSize: 18 }}>
                Block
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              disabled={true}
              style={{
                padding: 12,
                justifyContent: "center",
                borderBottomWidth: 1,
                borderBottomColor: "rgba(0, 0, 0, 0.3)",
                opacity: 0.3,
              }}
            >
              <Text style={{ color: theme.colors.primary, fontSize: 18 }}>
                Report
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                ignoreFriendRecommendation(user.id);
                onClose();
              }}
              style={{
                padding: 12,
                justifyContent: "center",
                borderBottomWidth: 1,
                borderBottomColor: "rgba(0, 0, 0, 0.3)",
              }}
            >
              <Text
                style={{ color: theme.colors.primaryTextColor, fontSize: 18 }}
              >
                Ignore Friend Recommendation
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              disabled={true}
              style={{
                padding: 12,
                justifyContent: "center",
                borderBottomWidth: 1,
                borderBottomColor: "rgba(0, 0, 0, 0.3)",
                opacity: 0.3,
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                <Text
                  style={{ color: theme.colors.primaryTextColor, fontSize: 18 }}
                >
                  Send Profile To...
                </Text>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={theme.colors.subTextColor}
                />
              </View>
            </TouchableOpacity>
          </View>

          {/* Bottom Section */}
          <View
            style={[
              styles.bottomSection,
              { backgroundColor: theme.colors.surface },
            ]}
          >
            <TouchableOpacity
              style={{
                padding: 12,
                justifyContent: "center",
                alignItems: "center",
              }}
              onPress={onClose}
            >
              <Text
                style={{ color: theme.colors.primaryTextColor, fontSize: 18 }}
              >
                Done
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "center",
    paddingBottom: 10,
  },
  modalContainer: {
    width: "95%",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
    padding: 12,
    borderRadius: 12,
  },
  profilePic: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  userInfo: {
    flex: 1,
    marginLeft: 10,
    gap: 6,
  },
  userName: {
    fontSize: 18,
    fontWeight: "bold",
  },
  viewProfileText: {
    fontSize: 14,
  },
  addButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 15,
    marginRight: 4,
  },
  middleSection: {
    marginBottom: 20,
    gap: 6,
    borderRadius: 12,
  },
  bottomSection: {
    marginBottom: 20,
    gap: 6,
    borderRadius: 12,
  },
  sendProfileContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 10,
  },
});

export default UserOptionsModal;
