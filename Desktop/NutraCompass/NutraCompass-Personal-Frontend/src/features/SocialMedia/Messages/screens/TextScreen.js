import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
} from "react-native";
import { Image } from "expo-image";
import { useTheme } from "react-native-paper";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  Ionicons,
  MaterialCommunityIcons,
  FontAwesome5,
} from "@expo/vector-icons";
import {
  messagesList,
  allUsersList,
  conversationsList,
  getUserOrGroupNameById,
  groupsList,
} from "../DummyData/FriendData";

const TextScreen = ({ route, navigation }) => {
  const { conversationId, friendIds, isGroupChat } = route.params;
  const [newMessage, setNewMessage] = useState("");
  const [messages, setMessages] = useState([]);
  const theme = useTheme();
  const styles = getStyles(theme);

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const storedMessages = await AsyncStorage.getItem(
          `messages_${conversationId || friendIds[0]}`
        );
        if (storedMessages) {
          setMessages(JSON.parse(storedMessages));
        } else {
          let chatMessages = [];
          if (conversationId) {
            const conversation = conversationsList.find(
              (c) => c.conversationId === conversationId
            );
            chatMessages = messagesList.filter((message) =>
              conversation.participants.includes(message.userId)
            );
          } else if (friendIds) {
            chatMessages = isGroupChat
              ? groupsList.find((group) => group.groupId === friendIds[0])
                  ?.messages
              : messagesList.filter((message) =>
                  friendIds.includes(message.userId)
                );
          }
          setMessages(chatMessages);
        }
      } catch (error) {
        console.error("Failed to load messages", error);
      }
    };
    fetchMessages();
  }, [conversationId, friendIds, isGroupChat]);

  useEffect(() => {
    const saveMessages = async () => {
      try {
        await AsyncStorage.setItem(
          `messages_${conversationId || friendIds[0]}`,
          JSON.stringify(messages)
        );
      } catch (error) {
        console.error("Failed to save messages", error);
      }
    };
    saveMessages();
  }, [messages, conversationId, friendIds]);

  let chatTitle, chatStatus;
  if (conversationId) {
    const conversation = conversationsList.find(
      (c) => c.conversationId === conversationId
    );
    chatTitle = getUserOrGroupNameById(conversation.participants[0]);
    chatStatus = allUsersList.find(
      (user) => user.id === conversation.participants[0]
    )?.status;
  } else if (friendIds) {
    chatTitle = isGroupChat
      ? groupsList.find((group) => group.groupId === friendIds[0])?.groupName
      : getUserOrGroupNameById(friendIds[0]);
    chatStatus = isGroupChat
      ? "Group chat"
      : allUsersList.find((user) => user.id === friendIds[0])?.status;
  }

  const sendMessage = () => {
    if (newMessage.trim() !== "") {
      const newMsg = {
        messageId: `${messages.length + 1}`,
        userId: "1", // Assuming '1' is the ID of the current user
        text: newMessage,
        timestamp: Date.now(),
        isMe: true,
        avatar: "https://via.placeholder.com/150",
      };

      setMessages([newMsg, ...messages]);
      setNewMessage("");
    }
  };

  const handlePickImage = () => {
    console.log("Pick an image");
  };

  const handlePickGIF = () => {
    console.log("Pick a GIF");
  };

  const handleStartVoiceCall = () => {
    console.log("Start voice call");
  };

  const handleStartVideoCall = () => {
    console.log("Start video call");
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
        <View style={{ flex: 1 }}>
          <Text style={styles.headerTitle}>
            {chatTitle || "Conversation Name"}
          </Text>
          <Text style={styles.headerSubtitle}>
            {chatStatus || "Unavailable"}
          </Text>
        </View>
        <View style={styles.callActions}>
          <TouchableOpacity
            onPress={handleStartVoiceCall}
            style={styles.callButton}
          >
            <FontAwesome5 name="phone" size={22} color={theme.colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleStartVideoCall}
            style={styles.callButton}
          >
            <FontAwesome5 name="video" size={22} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>
      <FlatList
        data={messages}
        keyExtractor={(item) => item.messageId}
        renderItem={({ item }) => (
          <View
            style={[
              styles.messageContainer,
              item.isMe
                ? styles.myMessageContainer
                : styles.theirMessageContainer,
            ]}
          >
            {!item.isMe && (
              <Image source={{ uri: item.avatar }} style={styles.avatar} />
            )}
            <View
              style={[
                styles.messageBubble,
                item.isMe ? styles.myMessageBubble : styles.theirMessageBubble,
              ]}
            >
              <Text style={styles.messageText}>{item.text}</Text>
              <Text style={styles.messageTimestamp}>
                {new Date(item.timestamp).toLocaleTimeString()}
              </Text>
            </View>
          </View>
        )}
        inverted
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.inputArea}
      >
        <TouchableOpacity onPress={handlePickImage} style={styles.actionButton}>
          <MaterialCommunityIcons
            name="camera"
            size={24}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={handlePickGIF} style={styles.actionButton}>
          <MaterialCommunityIcons
            name="image-multiple"
            size={24}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
        <TextInput
          style={styles.input}
          value={newMessage}
          onChangeText={setNewMessage}
          placeholder="Type a message..."
          placeholderTextColor="white"
        />
        <TouchableOpacity onPress={sendMessage} style={styles.sendButton}>
          <Ionicons name="send" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const getStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      padding: 10,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      marginRight: 10,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme.colors.primary,
    },
    headerSubtitle: {
      fontSize: 14,
      color: "#FFF", // White text color for status
    },
    callActions: {
      flexDirection: "row",
    },
    callButton: {
      marginLeft: 10,
    },
    messageContainer: {
      flexDirection: "row",
      alignItems: "flex-end",
      marginVertical: 5,
    },
    myMessageContainer: {
      justifyContent: "flex-end",
    },
    theirMessageContainer: {
      justifyContent: "flex-start",
      marginLeft: -45,
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: 10,
    },
    messageBubble: {
      maxWidth: "70%",
      padding: 10,
      borderRadius: 20,
      backgroundColor: "green",
    },
    myMessageBubble: {
      borderBottomRightRadius: 0,
      backgroundColor: "green",
    },
    theirMessageBubble: {
      borderBottomLeftRadius: 0,
      backgroundColor: "green",
    },
    messageText: {
      color: "white",
    },
    messageTimestamp: {
      fontSize: 10,
      color: "white",
      textAlign: "right",
      marginTop: 5,
    },
    inputArea: {
      flexDirection: "row",
      alignItems: "center",
      padding: 10,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    actionButton: {
      marginHorizontal: 5,
    },
    input: {
      flex: 1,
      padding: 10,
      backgroundColor: theme.colors.surface,
      borderRadius: 20,
      marginHorizontal: 5,
      color: "white", // Ensure the text color is white
    },
    sendButton: {
      marginHorizontal: 5,
    },
  });

export default TextScreen;
