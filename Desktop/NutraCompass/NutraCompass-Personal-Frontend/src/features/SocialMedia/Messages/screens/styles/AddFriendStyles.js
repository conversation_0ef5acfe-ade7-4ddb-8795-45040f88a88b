import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FAFAFA',
    },
    searchContainer: {
        flexDirection: 'row', // Set the direction of children to be in a row
        alignItems: 'center', // Align children vertically in the center
        padding: 10,
        backgroundColor: '#FFFFFF',
        borderBottomWidth: 1,
        borderColor: '#ECECEC',
    },
    searchInput: {
        flex: 1, // Take up as much space as possible
        fontSize: 16,
        borderRadius: 20,
        borderWidth: 1,
        borderColor: '#CDCDCD',
        paddingLeft: 15,
        height: 40,
        backgroundColor: '#FFFFFF',
    },
    clearButton: {
        marginLeft: 10, // Add a little space between the input and clear button
    },
    friendCard: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#EEE',
    },
    friendName: {
        fontSize: 16,
        color: '#333', // Dark text color for names
    },
    addButton: {
        paddingVertical: 6,
        paddingHorizontal: 20,
        backgroundColor: '#FFFC00', // Snapchat's yellow color for the Add button
        borderRadius: 15,
    },
    addButtonText: {
        color: '#000', // Black text color for the button text
        fontWeight: 'bold',
    },
    // Add other styles as needed
    });
export default styles;