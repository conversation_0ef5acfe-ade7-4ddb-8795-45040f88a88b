import { StyleSheet } from "react-native";

const getTextScreenStyles = (theme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingTop: 12,
      paddingHorizontal: 12,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    callActions: {
      flexDirection: "row",
      color: theme.primaryTextColor,
    },
    callButton: {
      marginLeft: 16,
    },
    backButton: {
      marginRight: 16,
    },
    headerContent: {
      justifyContent: "center",
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    headerSubtitle: {
      fontSize: 14,
      color: "#FFF", // Change to white for better visibility
      paddingTop: 4,
    },
    inputArea: {
      flexDirection: "row",
      paddingHorizontal: 9,
      paddingVertical: 5,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: Platform.select({ ios: 15, android: 20 }),
      padding: 5,
    },
    input: {
      flex: 1,
      height: 40,
      borderRadius: 20,
      paddingHorizontal: 10,
      marginRight: 10,
      backgroundColor: theme.colors.surface,
      color: "white", // Ensure the text color is white
    },
    sendIcon: {
      // Add styles for send icon if needed
    },
    messageContainer: {
      marginVertical: 5,
      marginBottom: 10,
      maxWidth: "80%",
    },
    myMessageContainer: {
      alignSelf: "flex-end",
      flexDirection: "row-reverse",
    },
    theirMessageContainer: {
      alignSelf: "flex-start",
      flexDirection: "row",
      
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginHorizontal: 10,
    },
    messageBubble: {
      padding: 10,
      borderRadius: 20,
      backgroundColor: "green",
      marginBottom: 5,
    },
    messageText: {
      fontSize: 16,
      color: theme.colors.primaryTextColor,
    },
    timestamp: {
      alignSelf: "flex-end",
      fontSize: 12,
      color: theme.colors.subtext,
      paddingHorizontal: 4,
      paddingTop: 2,
    },
    actionButton: {
      marginHorizontal: 3,
    },
  });
};

export default getTextScreenStyles;