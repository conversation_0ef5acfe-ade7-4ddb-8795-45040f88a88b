import React, { useState } from 'react';
import { View, TextInput, FlatList, Text, TouchableOpacity, Platform, Keyboard } from 'react-native';
import styles from "../screens/styles/AddFriendStyles";
import { nonFriendUsersList } from '../DummyData/FriendData';
import Ionicons from '@expo/vector-icons/Ionicons'; // Make sure you have @expo/vector-icons installed

const AddFriend = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);

    const handleSearch = (query) => {
        setSearchQuery(query);
        if (query.trim()) {
            const filteredResults = nonFriendUsersList.filter(user =>
                user.name.toLowerCase().includes(query.toLowerCase())
            );
            setSearchResults(filteredResults);
        } else {
            setSearchResults([]);
        }
    };

    const clearSearch = () => {
        setSearchQuery('');
        setSearchResults([]);
        Keyboard.dismiss(); // Dismiss the keyboard if open
    };

    const sendFriendRequest = (userId) => {
        // Implement friend request logic here
        setSearchResults(currentResults =>
            currentResults.filter(user => user.id !== userId)
        );
    };

    return (
        <View style={styles.container}>
            <View style={styles.searchContainer}>
                <TextInput
                    style={styles.searchInput}
                    value={searchQuery}
                    onChangeText={handleSearch}
                    placeholder="Search for friends..."
                    returnKeyType="search"
                    onSubmitEditing={() => handleSearch(searchQuery)} // When user submits the search
                />
                {searchQuery.length > 0 && (
                    <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                        <Ionicons name="md-close-circle" size={20} color="#ccc" />
                    </TouchableOpacity>
                )}
            </View>
            <FlatList
                data={searchResults}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                    <View style={styles.friendCard}>
                        <Text style={styles.friendName}>{item.name}</Text>
                        <TouchableOpacity
                            style={styles.addButton}
                            onPress={() => sendFriendRequest(item.id)}
                        >
                            <Text style={styles.addButtonText}>Add</Text>
                        </TouchableOpacity>
                    </View>
                )}
              
            />
        </View>
    );
};

export default AddFriend;