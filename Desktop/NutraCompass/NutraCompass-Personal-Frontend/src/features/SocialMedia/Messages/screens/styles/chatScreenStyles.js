import { StyleSheet } from "react-native";

const chatScreenStyles = (theme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },

    backButton: {
      marginRight: 5,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: 10,
      paddingTop: 10, // Add padding to account for status bar height
    },
    title: {
      fontSize: 20,
      fontWeight: "bold",
      padding: 10,
      color: theme.colors.primaryTextColor,
      alignSelf: "center", // Center title
      textAlign: "center", // Center text
      flex: 1, // Allow title to take up available space
    },
    plusIcon: {
      position: "right", // Position absolutely within the parent view
      color: theme.colors.primaryTextColor,
    },
    searchBar: {
      fontSize: 16,
      paddingVertical: 8,
      paddingHorizontal: 15,
      borderWidth: 1,
      borderColor: theme.colors.subTextColor,
      borderRadius: 30, // Increased border radius for an oval shape
      color: "black",
      backgroundColor: theme.colors.subTextColor,
      marginHorizontal: 10,
      marginBottom: 10,
      marginTop: 0, // Add some space between the header and search bar
    },
    chatItem: {
      flexDirection: "row",
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: theme.colors.border,
      alignItems: "center", // Align items in a row
      justifyContent: "center",
    },
    profileImage: {
      width: 50, // Set the width of the image
      height: 50, // Set the height of the image
      borderRadius: 30, // Make it round
      marginRight: 14, // Add some margin between the image and the text
    },
    chatInfo: {
      flexDirection: "column",
      justifyContent: "center",
      flex: 1, // Take the remaining space
    },
    nameMessageContainer: {
      flexDirection: "column",
      flex: 1, // Take available space
    },
    chatName: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    lastMessage: {
      fontSize: 14,
      color: theme.colors.subTextColor,
      maxWidth: "90%",
    },
    timestamp: {
      fontSize: 12,
      color: theme.colors.subTextColor,
      alignSelf: "flex-end", // Align timestamp to the bottom-right
    },
    unreadIndicator: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.primary,
      marginLeft: 10,
    },

    // Additional styles can be added here
  });
};

export default chatScreenStyles;
