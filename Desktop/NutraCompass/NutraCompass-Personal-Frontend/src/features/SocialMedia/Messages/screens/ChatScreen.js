import React, { useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  TextInput,
  StyleSheet,
} from "react-native";
import { Image } from "expo-image";
import { useTheme } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import chatScreenStyles from "../screens/styles/chatScreenStyles";
import {
  conversationsList,
  messagesList,
  getUserOrGroupNameById,
  groupsList,
} from "../DummyData/FriendData";
import SelectFriend from "./SelectFriend";
import Ionicons from "react-native-vector-icons/Ionicons";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";

// Assuming you are passing navigation prop to ChatScreen component
export default function ChatScreen({ navigation, route }) {
  const theme = useTheme();
  const styles = chatScreenStyles(theme);
  const [searchQuery, setSearchQuery] = useState("");

  const handlePressConversation = (conversation) => {
    // Navigate to the TextScreen with conversation details
    navigation.navigate("TextScreen", {
      conversationId: conversation.conversationId,
    });
  };

  // Function to handle search input
  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  //Function to Handle back arrow

  const { originStack } = route.params || {};

  const handleBackPress = () => {
    if (originStack === "SocialBottomTabs") {
      // Assumes "Social" is the route name for SocialBottomTabs
      navigation.navigate("Social");
    } else {
      // Assumes "Personal" is the route name for PersonalBottomTabs
      navigation.navigate("Personal");
    }
  };

  // Function to handle new chat creation
  const handleNewChat = () => {
    navigation.navigate("SelectFriend"); // Navigate to SelectFriend screen
  };

  // Function to get the last message details from messagesList
  const getLastMessageDetails = (conversation) => {
    let lastMessage, lastMessageTimestamp;

    if (conversation.type === "individual") {
      // ... existing logic for individual chats
    } else if (conversation.type === "group") {
      const groupMessages =
        groupsList.find((group) => group.groupId === conversation.groupId)
          ?.messages || [];
      lastMessage = groupMessages[groupMessages.length - 1]?.text || "";
      lastMessageTimestamp =
        groupMessages[groupMessages.length - 1]?.timestamp || "";
    }
    return { text: lastMessage, timestamp: lastMessageTimestamp };
  };

  const unifiedConversations = [
    ...conversationsList.map((conversation) => ({
      ...conversation,
      type: "individual",
      name: getUserOrGroupNameById(conversation.participants[0]),
      lastMessage:
        messagesList
          .filter((message) => message.userId === conversation.participants[0])
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0]
          ?.text || "No messages yet",
      lastMessageTimestamp:
        messagesList
          .filter((message) => message.userId === conversation.participants[0])
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0]
          ?.timestamp || "",
    })),
    ...groupsList.map((group) => ({
      ...group,
      type: "group",
      name: group.groupName,
      lastMessage:
        group.messages.sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        )[0]?.text || "No messages yet",
      lastMessageTimestamp:
        group.messages.sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        )[0]?.timestamp || "",
    })),
  ];

  // Now sort the unifiedConversations by lastMessageTimestamp
  unifiedConversations.sort(
    (a, b) =>
      new Date(b.lastMessageTimestamp) - new Date(a.lastMessageTimestamp)
  );

  // Use getUserOrGroupNameById for filtering based on the search query
  const filteredConversations = unifiedConversations.filter((conversation) => {
    let chatName = "";
    if (conversation.type === "individual") {
      chatName = getUserOrGroupNameById(conversation.participants[0]);
    } else if (conversation.type === "group") {
      const group = groupsList.find(
        (group) => group.groupId === conversation.id
      );
      if (group) {
        chatName = group.groupName;
      }
    }
    return chatName.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const renderChatItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => handlePressConversation(item)}
      >
        <Image
          source={{ uri: "https://via.placeholder.com/150/000000/FFFFFF" }}
          style={styles.profileImage}
        />
        <View style={styles.chatInfo}>
          <View style={styles.nameMessageContainer}>
            <Text style={styles.chatName}>{item.name}</Text>
            <Text style={styles.lastMessage}>{item.lastMessage}</Text>
          </View>
          <Text style={styles.timestamp}>{item.lastMessageTimestamp}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const overlayStyles = StyleSheet.create({
    overlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    text: {
      color: "white",
      fontSize: 20,
      marginBottom: 10, // Space above the icon
    },
    icon: {
      fontSize: 50,
      color: "white",
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Ionicons
            name="arrow-back"
            size={24}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>

        <Text style={styles.title}>Messages</Text>

        <TouchableOpacity style={styles.plusIcon} onPress={handleNewChat}>
          <MaterialCommunityIcons
            name="plus-circle-outline"
            size={24}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
      </View>
      <TextInput
        style={styles.searchBar}
        placeholder="Search"
        placeholderTextColor={theme.colors.text}
        onChangeText={handleSearch}
        value={searchQuery}
      />
      <FlatList
        data={filteredConversations}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.conversationId || item.groupId} // Use a unique key
      />

      {/* Semi-transparent overlay with a message and icon */}
      <View style={overlayStyles.overlay}>
        <Text style={overlayStyles.text}>Social Side Coming Soon</Text>
        <Icon name="lock" style={overlayStyles.icon} />
      </View>
    </View>
  );
}
