import { StyleSheet, StatusBar, Platform } from "react-native";
const statusBarHeight = Platform.OS === "android" ? StatusBar.currentHeight : 0;

const selectFriendStyles = (theme) => {
  // Use StatusBar.currentHeight to get the height of the status bar
  const statusBarHeight = StatusBar.currentHeight ? StatusBar.currentHeight : 0;

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerSection: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 5,
      paddingTop: 10,
      backgroundColor: theme.colors.cardDarkGrayBackgroundColor,
      // Ensure that the header section height is enough to contain its children
      height: 50 + statusBarHeight,
    },
    headerActionButton: {
      // Adjust the button padding if needed
      paddingVertical: 5,
      paddingHorizontal: 5,
    },
    headerActionText: {
      color: theme.colors.primary,
    },
    backButton: {
      marginRight: 5, // Right margin for spacing between back button and title
    },
    headerTitle: {
      color: theme.colors.primaryTextColor,
      fontSize: 20,
      fontWeight: "bold",
      flex: 1, // This will push the action button to the edge of the container
      textAlign: "center", // Center the title text
    },
    searchBarSection: {
      paddingHorizontal: 10,
      paddingVertical: 16,
      backgroundColor: theme.colors.sectionBackgroundColor,
    },
    searchBar: {
      flex: 1,
      fontSize: 16,
      borderRadius: theme.dimensions.sectionBorderRadius,
      backgroundColor: theme.colors.cardBackgroundColor,
      color: theme.colors.primaryTextColor,
      borderWidth: 1,
      borderColor: theme.colors.sectionBorderColor,
      paddingHorizontal: 10,
      marginRight: 8, // Space before the create group button
      height: 40,
    },
    createGroupButton: {
      backgroundColor: theme.colors.cardBackgroundColor,
      borderRadius: theme.dimensions.cardBorderRadius,
      borderColor: theme.colors.cardBorderColor,
      borderWidth: 1,
      padding: 15,
      marginVertical: 8,
    },
    createGroupText: {
      color: theme.colors.primary,
      fontWeight: "bold",
      textAlign: "center",
    },
    friendItem: {
      flexDirection: "row",
      alignItems: "center",
      padding: 14,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
      backgroundColor: theme.colors.cardDarkGrayBackgroundColor,
    },
    friendName: {
      color: theme.colors.primaryTextColor,
      fontSize: 20,
      marginLeft: 10,
    },
    selectionBox: {
      // Add style for the selection box if necessary
    },
    // Add any additional styles you need
  });
};

export default selectFriendStyles;
