import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StatusBar,
} from "react-native";
import { useTheme } from "react-native-paper";
import selectFriendStyles from "../screens/styles/selectFriendStyles";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { currentFriendsList } from "../DummyData/FriendData";

const SelectFriend = ({ navigation }) => {
  const theme = useTheme();
  const styles = selectFriendStyles(theme);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFriends, setSelectedFriends] = useState(new Set());

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const filteredFriends = currentFriendsList.filter((friend) =>
    friend.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelectFriend = (friend) => {
    setSelectedFriends((prevSelected) => {
      const newSelected = new Set(prevSelected);
      if (newSelected.has(friend.id)) {
        newSelected.delete(friend.id);
      } else {
        newSelected.add(friend.id);
      }
      return newSelected;
    });
  };

  const executeAction = () => {
    // Logic based on the number of selected friends
      // Use navigation to move to the chat or group creation screen
      const selectedIds = [...selectedFriends];
      if (selectedFriends.size === 1) {
          // Navigate to the TextScreen for a single chat
          navigation.navigate('TextScreen', { friendIds: selectedIds, isGroupChat: false });
      } else if (selectedFriends.size > 1) {
          // Navigate to the TextScreen for a group chat
          navigation.navigate('TextScreen', { friendIds: selectedIds, isGroupChat: true });
      }
  };

  const renderFriendItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.friendItem,
        selectedFriends.has(item.id) && styles.friendItemSelected,
      ]}
      onPress={() => handleSelectFriend(item)}
    >
      <View style={styles.selectionBox}>
        {selectedFriends.has(item.id) ? (
          <MaterialCommunityIcons name="checkbox-marked" size={24} color={theme.colors.primary} />
        ) : (
          <MaterialCommunityIcons name="checkbox-blank-outline" size={24} color={theme.colors.subTextColor} />
        )}
      </View>
      <Text style={styles.friendName}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        {/* Back Button */}
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.primaryTextColor} />
        </TouchableOpacity>

        {/* Search Bar */}
        <TextInput
          style={styles.searchBar}
          placeholder="Search friends"
          placeholderTextColor={theme.colors.subTextColor}
          onChangeText={handleSearch}
          value={searchQuery}
        />

        {/* Action Button */}
        {selectedFriends.size > 0 && (
          <TouchableOpacity onPress={executeAction} style={styles.headerActionButton}>
            <Text style={styles.headerActionText}>
              {selectedFriends.size === 1 ? "Select Friend" : "Create Group"}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Friends List */}
      <FlatList
        data={filteredFriends}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderFriendItem}
        style={styles.list}
      />
    </View>
  );
};

export default SelectFriend;