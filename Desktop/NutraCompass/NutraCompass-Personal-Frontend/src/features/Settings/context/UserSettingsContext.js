import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { storage } from "../../../config/firebase.js";
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject,
} from "firebase/storage";
import Configs from "../../../../configs.js";

const USER_SETTINGS_CACHE_KEY = (userId) => `@user_settings_${userId}`;
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hour

const UserSettingsContext = createContext();

export function useUserSettings() {
  return useContext(UserSettingsContext);
}

export function UserSettingsProvider({ children }) {
  const { user, token } = useAuth();
  const userId = user?.uid;
  const [userSettings, setUserSettings] = useState(null);
  const apiUrl = Configs.NutraCompass_API_URL;

  // AppLoadingScreen: Load user data only when userId and token are confirmed
  const fetchUserSettings = async (forceRefresh = false) => {
    if (!userId || !token) {
      console.log("Skipping fetch due to missing userId or token.");
      return;
    }

    const cacheKey = USER_SETTINGS_CACHE_KEY(userId);

    try {
      if (!forceRefresh) {
        const cachedData = await AsyncStorage.getItem(cacheKey);
        if (cachedData) {
          const { data, timestamp } = JSON.parse(cachedData);

          // Validate cache structure
          const isValidCache = data?.profile && data?.nutritionalGoals;
          //const isRecent = Date.now() - timestamp < CACHE_TTL;

          if (isValidCache) {
            console.log("[CACHE] Applying cached user settings");
            setUserSettings(data);
            return data;
          }
        }
      }

      console.log("[NETWORK] Fetching fresh user settings");
      const response = await fetch(`${apiUrl}/v1/settings/${userId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }

      const newData = await response.json();

      // Validate and normalize response
      const validatedData = {
        profile: newData.profile || {},
        nutritionalGoals: newData.nutritionalGoals || {},
        physicalFitnessGoals: newData.physicalFitnessGoals || {},
        appAppearance: newData.appAppearance || {},
        location: newData.location || {},
      };

      setUserSettings(validatedData);
      await AsyncStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: validatedData,
          timestamp: Date.now(),
        })
      );

      return validatedData;
    } catch (error) {
      console.error("Settings load failed:", error);

      // Fallback to cache if available
      const fallback = await AsyncStorage.getItem(cacheKey);
      if (fallback) {
        console.log("[FALLBACK] Using cached settings");
        const { data } = JSON.parse(fallback);
        setUserSettings(data);
        return data;
      }

      // Fallback to empty defaults
      const defaults = {
        profile: {},
        nutritionalGoals: {},
        physicalFitnessGoals: {},
        appAppearance: {},
        location: {},
      };
      setUserSettings(defaults);
      return defaults;
    }
  };

  // Update user settings in the backend and cache
  const updateUserSettings = async (updatedSettings) => {
    if (!userId) return;

    try {
      // Network update
      await fetch(`${apiUrl}/v1/settings/${userId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(updatedSettings),
      });

      // Update local state and cache
      setUserSettings((prev) => {
        const newSettings = { ...prev, ...updatedSettings };
        AsyncStorage.setItem(
          USER_SETTINGS_CACHE_KEY(userId),
          JSON.stringify({
            data: newSettings,
            timestamp: Date.now(),
          })
        );
        return newSettings;
      });
    } catch (error) {
      console.error("Update failed:", error);
      throw error;
    }
  };

  // Method to calculate total daily calorie carbs goal based on percentage of the total daily calories
  const calculateCarbDailyCalories = (totalDailyCalories, carbPercentage) => {
    return Math.round((totalDailyCalories * carbPercentage) / 100);
  };

  // Method to calculate total daily calorie protein goal based on percentage of the total daily calories
  const calculateProteinDailyCalories = (
    totalDailyCalories,
    proteinPercentage
  ) => {
    return Math.round((totalDailyCalories * proteinPercentage) / 100);
  };

  // Method to calculate total daily calorie fat goal based on percentage of the total daily calories
  const calculateFatDailyCalories = (totalDailyCalories, fatPercentage) => {
    return Math.round((totalDailyCalories * fatPercentage) / 100);
  };

  const calculateCarbDailyGrams = (totalDailyCalories, carbPercentage) => {
    const carbCaloriesPerGram = 4;
    return Math.round(
      (totalDailyCalories * (carbPercentage / 100)) / carbCaloriesPerGram
    );
  };

  const calculateProteinDailyGrams = (
    totalDailyCalories,
    proteinPercentage
  ) => {
    const proteinCaloriesPerGram = 4;
    return Math.round(
      (totalDailyCalories * (proteinPercentage / 100)) / proteinCaloriesPerGram
    );
  };

  const calculateFatDailyGrams = (totalDailyCalories, fatPercentage) => {
    const fatCaloriesPerGram = 9;
    return Math.round(
      (totalDailyCalories * (fatPercentage / 100)) / fatCaloriesPerGram
    );
  };

  // Function to upload profile picture
  const uploadProfilePicture = async ({ uri }) => {
    if (!userId || !uri) return null;

    try {
      console.log("Profile Pic Uri: ", uri);
      const response = await fetch(uri);
      const blob = await response.blob();
      // Use a fixed filename for the profile picture, e.g., "profilePic.jpg"
      const imageRef = ref(storage, `profilePictures/${userId}/profilePic.jpg`);
      await uploadBytes(imageRef, blob);
      const downloadURL = await getDownloadURL(imageRef);
      console.log("Profile Pic Url: ", downloadURL);
      setUserSettings((prev) => ({
        ...prev,
        profile: { ...prev.profile, pictureUrl: downloadURL },
      }));

      return downloadURL;
    } catch (error) {
      console.error("Error uploading profile picture:", error);
      return null; // Handle the error appropriately
    }
  };

  // Function to remove profile picture
  const removeProfilePicture = async () => {
    if (!userId) return null;

    try {
      // Use a fixed filename for the profile picture, e.g., "profilePic.jpg"
      const imageRef = ref(storage, `profilePictures/${userId}/profilePic.jpg`);
      await deleteObject(imageRef); // Use Firebase Storage delete function
      setUserSettings((prev) => ({
        ...prev,
        profile: { ...prev.profile, pictureUrl: null },
      }));
    } catch (error) {
      console.error("Error removing profile picture:", error);
      throw error;
    }
  };

  // Getter methods
  const getUserProfile = () => userSettings?.profile || {};
  const getUserLocation = () => userSettings?.location || {};
  const getAppAppearance = () => userSettings?.appAppearance || {};
  const getNutritionalGoals = () => userSettings?.nutritionalGoals || {};

  const getPhysicalFitnessGoals = () => {
    return {
      stepsGoal: userSettings?.physicalFitnessGoals?.stepsGoal ?? 0,
      distanceGoal: userSettings?.physicalFitnessGoals?.distanceGoal ?? 0,
      distanceUnit: userSettings?.physicalFitnessGoals?.distanceUnit ?? "km",
    };
  };

  // Setter methods with added error handling

  const setUserProfile = async (newProfile) => {
    try {
      await updateUserSettings({ profile: newProfile });
    } catch (error) {
      console.error("Failed to update profile:", error);
    }
  };

  const setUserLocation = async (newLocation) => {
    try {
      // Add final validation layer
      if (!newLocation?.timezone || typeof newLocation.timezone !== "string") {
        throw new Error("Invalid location format");
      }

      await updateUserSettings({ location: newLocation });
    } catch (error) {
      console.error("Failed to update user location:", error);
    }
  };

  const setAppAppearance = async (newAppearance) => {
    try {
      await updateUserSettings({ appAppearance: newAppearance });
    } catch (error) {
      console.error("Failed to update app appearance:", error);
    }
  };

  // Set calorie and macro goals that's in nutritional goals
  const setCalorieAndMacroGoals = async (newGoals) => {
    if (!userId) return; // Ensure there's a user logged in

    try {
      const currentGoals = userSettings.nutritionalGoals || {}; // Fetch existing goals

      // Calculate new macros based on the input
      const newMacroGoals = {
        carb: {
          dailyPercentage: newGoals.carbPercentage / 100,
          dailyCalories: calculateCarbDailyCalories(
            newGoals.calorieGoal,
            newGoals.carbPercentage
          ),
          dailyGrams: calculateCarbDailyGrams(
            newGoals.calorieGoal,
            newGoals.carbPercentage
          ),
        },
        protein: {
          dailyPercentage: newGoals.proteinPercentage / 100,
          dailyCalories: calculateProteinDailyCalories(
            newGoals.calorieGoal,
            newGoals.proteinPercentage
          ),
          dailyGrams: calculateProteinDailyGrams(
            newGoals.calorieGoal,
            newGoals.proteinPercentage
          ),
        },
        fat: {
          dailyPercentage: newGoals.fatPercentage / 100,
          dailyCalories: calculateFatDailyCalories(
            newGoals.calorieGoal,
            newGoals.fatPercentage
          ),
          dailyGrams: calculateFatDailyGrams(
            newGoals.calorieGoal,
            newGoals.fatPercentage
          ),
        },
      };

      // Merge new goals with existing ones
      const updatedGoals = {
        ...currentGoals,
        calorieGoal: newGoals.calorieGoal, // Overwrite calorie goal
        macroGoals: { ...currentGoals.macroGoals, ...newMacroGoals }, // Merge macro goals
      };

      // Update user settings with the merged goals
      await updateUserSettings({ nutritionalGoals: updatedGoals });
    } catch (error) {
      console.error("Failed to update nutritional goals:", error);
    }
  };

  // Set water consumption goal that's in nutritional goals
  const setWaterGoal = async (waterGoal) => {
    if (!userId) return; // Ensure there's a user logged in

    try {
      const updatedSettings = {
        nutritionalGoals: {
          ...userSettings.nutritionalGoals, // Preserve existing nutritional goals
          waterGoal: waterGoal, // Update only the water goal
        },
      };

      await updateUserSettings(updatedSettings);
    } catch (error) {
      console.error("Failed to update water goal:", error);
    }
  };

  const setPhysicalFitnessGoals = async (newGoals) => {
    try {
      await updateUserSettings({ physicalFitnessGoals: newGoals });
    } catch (error) {
      console.error("Failed to update physical fitness goals:", error);
    }
  };

  const contextValue = useMemo(() => {
    return {
      fetchUserSettings,
      userSettings,
      getUserProfile,
      getUserLocation,
      getAppAppearance,
      getNutritionalGoals,
      getPhysicalFitnessGoals,
      setUserProfile,
      setUserLocation,
      setAppAppearance,
      setCalorieAndMacroGoals,
      setPhysicalFitnessGoals,
      calculateProteinDailyGrams,
      calculateCarbDailyGrams,
      calculateFatDailyGrams,
      uploadProfilePicture,
      removeProfilePicture,
      setWaterGoal,
    };
  }, [
    fetchUserSettings,
    userSettings,
    getUserProfile,
    getUserLocation,
    getAppAppearance,
    getNutritionalGoals,
    getPhysicalFitnessGoals,
    setUserProfile,
    setUserLocation,
    setAppAppearance,
    setCalorieAndMacroGoals,
    setPhysicalFitnessGoals,
    calculateProteinDailyGrams,
    calculateCarbDailyGrams,
    calculateFatDailyGrams,
    uploadProfilePicture,
    removeProfilePicture,
    setWaterGoal,
  ]);

  return (
    <UserSettingsContext.Provider value={contextValue}>
      {children}
    </UserSettingsContext.Provider>
  );
}
