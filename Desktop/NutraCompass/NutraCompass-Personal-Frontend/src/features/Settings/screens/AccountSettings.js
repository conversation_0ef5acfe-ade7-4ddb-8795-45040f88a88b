import React, { useState } from "react";
import { View, Text, TouchableOpacity, Alert } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import accountSettingsStyles from "./styles/accountSettingsStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { useUserSettings } from "../context/UserSettingsContext.js";
import TimezonePickerModal from "../../../components/TimezonePickerModal.js";

const AccountSettings = ({ navigation }) => {
  const styles = accountSettingsStyles();
  const { theme } = useThemeContext();
  const { user, loggingOut, resetPassword, deleteAccount } = useAuth();
  const { userSettings, setUserLocation } = useUserSettings();
  const [showTimezoneModal, setShowTimezoneModal] = useState(false);

  const handleDeleteAccount = () => {
    Alert.alert(
      "Delete Account",
      "Are you sure you want to delete your account? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () =>
            deleteAccount(user?.uid, String(userSettings?.profile?.firstName)), // Trigger the deleteAccount function if confirmed
        },
      ],
      { cancelable: true }
    );
  };

  const handleChangePassword = () => {
    Alert.alert(
      "Change Password",
      "Are you sure you want to change your password?",
      [
        { text: "No", style: "cancel" },
        {
          text: "Yes",
          style: "destructive",
          onPress: () =>
            resetPassword(
              user?.email,
              String(userSettings?.profile?.firstName)
            ), // Trigger the resetPassword function if confirmed
        },
      ],
      { cancelable: true }
    );
  };

  const handleTimezoneSelect = async (selectedTimezone) => {
    // Add validation
    if (
      typeof selectedTimezone !== "string" ||
      !selectedTimezone.includes("/")
    ) {
      console.error("Invalid timezone format received:", selectedTimezone);
      return;
    }

    // Create new location object without spreading potential React references
    const newLocation = {
      region: "US", // Hardcode or use existing region
      timezone: selectedTimezone,
    };

    await setUserLocation(newLocation);
    setShowTimezoneModal(false);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialCommunityIcons
            name="chevron-left"
            size={28}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Account</Text>
      </View>

      {/* Name Row */}
      <View style={styles.row} onPress={() => console.log("Name Pressed")}>
        <Text style={styles.rowText}>Name</Text>
        <Text style={styles.subText}>
          {String(userSettings?.profile?.firstName || "")}{" "}
          {String(userSettings?.profile?.lastName || "")}
        </Text>
      </View>

      {/* Email Row */}
      <View style={styles.row} onPress={() => console.log("Email Pressed")}>
        <Text style={styles.rowText}>Email</Text>
        <Text style={styles.subText}>{String(user?.email || "")}</Text>
      </View>

      {/* Timezone Row */}
      <TouchableOpacity
        style={styles.row}
        onPress={() => setShowTimezoneModal(true)}
      >
        <Text style={styles.rowText}>Timezone</Text>
        <Text style={styles.subText}>
          {String(userSettings?.location?.timezone || "Not set")}
        </Text>
      </TouchableOpacity>

      {/* Change Password Row */}
      <TouchableOpacity style={styles.row} onPress={handleChangePassword}>
        <Text style={styles.rowText}>Change Password</Text>
      </TouchableOpacity>

      {/* Export Data Row */}
      {/* <TouchableOpacity
        style={styles.row}
        onPress={() => console.log("Export Data (Excel) Pressed")}
      >
        <Text style={styles.rowText}>Export Data (Excel)</Text>
      </TouchableOpacity> */}

      {/* Export Data Row Locked Up Version */}
      <View style={styles.row}>
        <Text style={styles.rowText}>Export Data (Excel)</Text>
        <Text
          style={{
            fontSize: 20,
            color: "white",
            alignSelf: "flex-end",
            paddingRight: 8,
          }}
        >
          🔒
        </Text>
      </View>

      {/* User ID Row */}
      <View style={styles.row}>
        <Text style={styles.rowText}>User ID</Text>
        <Text style={styles.subText}>{String(user?.uid || "")}</Text>
      </View>

      {/* Restore Subscription Row */}
      {/* <TouchableOpacity
        style={styles.row}
        onPress={() => console.log("Restore Subscription Pressed")}
      >
        <Text style={styles.rowText}>Restore Subscription</Text>
      </TouchableOpacity> */}

      {/* Restore Subscription Row Locked Up Version */}
      <View style={styles.row}>
        <Text style={styles.rowText}>Restore Subscription</Text>
        <Text
          style={{
            fontSize: 20,
            color: "white",
            alignSelf: "flex-end",
            paddingRight: 8,
          }}
        >
          🔒
        </Text>
      </View>

      {/* Terms of Service Row */}
      {/* <TouchableOpacity
        style={styles.row}
        onPress={() => console.log("Terms of Service Pressed")}
      >
        <Text style={styles.rowText}>Terms of Service</Text>
      </TouchableOpacity> */}

      {/* Terms of Service Row Locked Up Version */}
      <View style={styles.row}>
        <Text style={styles.rowText}>Terms of Service</Text>
        <Text
          style={{
            fontSize: 20,
            color: "white",
            alignSelf: "flex-end",
            paddingRight: 8,
          }}
        >
          🔒
        </Text>
      </View>

      {/* Scientific References Row */}
      {/* <TouchableOpacity
        style={styles.row}
        onPress={() => console.log("Scientific References Pressed")}
      >
        <Text style={styles.rowText}>Scientific References and Citations</Text>
      </TouchableOpacity> */}

      {/* Scientific References Row Locked Up Version */}
      <View style={styles.row}>
        <Text style={styles.rowText}>Scientific References and Citations</Text>
        {/* Overlay and Lock Icon */}
        <Text
          style={{
            fontSize: 20,
            color: "white",
            alignSelf: "flex-end",
            paddingRight: 8,
          }}
        >
          🔒
        </Text>
      </View>

      {/* Log Out Row */}
      <TouchableOpacity style={styles.row} onPress={() => loggingOut()}>
        <Text
          style={[styles.rowText, { color: theme.colors.primaryTextColor }]}
        >
          Log Out
        </Text>
      </TouchableOpacity>

      {/* Delete Account Row */}
      <TouchableOpacity style={styles.row} onPress={handleDeleteAccount}>
        <Text style={[styles.rowText, { color: "red" }]}>Delete Account</Text>
      </TouchableOpacity>

      {/* Timezone Picker Modal */}
      <TimezonePickerModal
        visible={showTimezoneModal}
        onClose={() => setShowTimezoneModal(false)}
        currentTimezone={userSettings?.location?.timezone}
        onSelect={handleTimezoneSelect}
      />
    </View>
  );
};

export default AccountSettings;
