import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import nutritionSettingsStyles from "./styles/nutritionSettingsStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";

const NutritionSettings = ({ navigation }) => {
  const styles = nutritionSettingsStyles();
  const { theme } = useThemeContext();

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialCommunityIcons
            name="chevron-left"
            size={28}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Nutrition</Text>
      </View>
      {/* Content */}
      <View>
        <Text style={{ textAlign: "center", marginTop: 20 }}>
          Nutrition settings content goes here.
        </Text>
      </View>
    </View>
  );
};

export default NutritionSettings;
