import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";

const profileSettingsStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center", // Centers the title in the header
      height: 60,
      backgroundColor: theme.colors.screenBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor, // Optional: Adds a bottom border for separation
    },
    backButton: {
      position: "absolute", // Keeps the back button on the left
      left: 20,
      zIndex: 1, // Ensures the button is always on top
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    card: {
      backgroundColor: theme.colors.surface,
      margin: 10,
      padding: 15,
      borderRadius: theme.dimensions.cardBorderRadius,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 4,
    },
    headerText: {
      fontSize: 14,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
      alignSelf: "center",
      paddingBottom: 18,
    },
    inputContainer: {
      marginBottom: 10,
      flexDirection: "row",
      alignItems: "center",
    },
    label: {
      flex: 1 / 2,
      fontSize: 12,
      color: theme.colors.subTextColor,
      marginBottom: 5,
    },
    input: {
      flex: 1,
      height: 40,
      backgroundColor: theme.colors.surface,
      borderColor: theme.colors.sectionBorderColor,
      borderWidth: 1,
      borderRadius: theme.dimensions.cardBorderRadius,
      paddingHorizontal: 10,
      fontSize: 12,
      color: theme.colors.primaryTextColor,
      justifyContent: "center",
    },
  });
};

export default profileSettingsStyles;
