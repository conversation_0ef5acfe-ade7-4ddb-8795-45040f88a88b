import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";

const accountSettingsStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center", // Centers the title in the header
      height: 60,
      backgroundColor: theme.colors.screenBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor, // Optional: Adds a bottom border for separation
    },
    backButton: {
      position: "absolute", // Keeps the back button on the left
      left: 20,
      zIndex: 1, // Ensures the button is always on top
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    rowText: {
      fontSize: 16,
      color: theme.colors.primaryTextColor,
    },
    subText: {
      fontSize: 14,
      color: theme.colors.subTextColor,
    },
  });
};

export default accountSettingsStyles;
