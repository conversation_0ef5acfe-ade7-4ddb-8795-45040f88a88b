import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";

const notificationSettingsStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center", // Centers the title in the header
      height: 60,
      backgroundColor: theme.colors.screenBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor, // Optional: Adds a bottom border for separation
    },
    backButton: {
      position: "absolute", // Keeps the back button on the left
      left: 20,
      zIndex: 1, // Ensures the button is always on top
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    rowContainer: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 8,
      paddingHorizontal: 15,
    },
    rowText: {
      fontSize: 16,
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
    },
    subText: {
      fontSize: 14,
      color: theme.colors.primaryTextColor,
    },
    disabledText: {
      color: theme.colors.subTextColor,
    },
  });
};

export default notificationSettingsStyles;
