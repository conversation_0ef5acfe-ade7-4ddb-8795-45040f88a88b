import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";

const nutritionSettingsStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: 60,
      backgroundColor: theme.colors.screenBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    backButton: {
      position: "absolute",
      left: 20,
      zIndex: 1,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
  });
};

export default nutritionSettingsStyles;
