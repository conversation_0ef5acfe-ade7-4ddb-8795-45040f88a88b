import React, { useState, useEffect } from "react";
import {
  Text,
  View,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { <PERSON>, Button } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import profileSettingsStyles from "./styles/profileSettingsStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../context/UserSettingsContext.js";
import CustomSexPickerModal from "../../../authentication/components/CustomSexPickerModal.js";
import CustomWeightInputModal from "../../../authentication/components/CustomWeightInputModal.js";
import CustomHeightPickerModal from "../../../authentication/components/CustomHeightPickerModal.js";
import CustomDatePickerModal from "../../../authentication/components/CustomDatePickerModal.js";

const ProfileSettings = ({ navigation }) => {
  const styles = profileSettingsStyles();
  const { theme } = useThemeContext();
  const { getUserProfile, setUserProfile } = useUserSettings();

  const [profile, setProfile] = useState({});
  const [originalProfile, setOriginalProfile] = useState({});
  const [isSexPickerVisible, setSexPickerVisible] = useState(false);
  const [isWeightPickerVisible, setWeightPickerVisible] = useState(false);
  const [isHeightPickerVisible, setHeightPickerVisible] = useState(false);
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);

  useEffect(() => {
    const loadUserProfile = async () => {
      const userProfile = await getUserProfile();
      setProfile(userProfile);
      setOriginalProfile(userProfile);
    };

    loadUserProfile();
  }, []);

  const handleSave = async () => {
    await setUserProfile(profile);
    setOriginalProfile(profile);
  };

  const handleChange = (key, value) => {
    setProfile((prev) => {
      if (key === "userName") {
        return { ...prev, userName: value, userNameLower: value.toLowerCase() };
      }
      return { ...prev, [key]: value };
    });
  };

  const hasChanges =
    JSON.stringify(originalProfile) !== JSON.stringify(profile);

  const handleSelectHeight = (height) => {
    if (height) {
      setProfile((prev) => ({ ...prev, height }));
    }
  };

  const formatHeight = (height) => {
    if (height?.unit === "in" && height?.inches) {
      const feet = Math.floor(height.inches / 12);
      const inches = Math.round(height.inches % 12);
      return `${feet}'${inches}"`;
    }
    if (height?.unit === "cm" && height?.centimeters) {
      return `${height.centimeters} cm`;
    }
    return "Select"; // Placeholder if height is not set
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <View style={styles.headerContainer}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <MaterialCommunityIcons
                name="chevron-left"
                size={28}
                color={theme.colors.primaryTextColor}
              />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Profile</Text>
          </View>

          <ScrollView contentContainerStyle={{ paddingBottom: 50 }}>
            <Card style={styles.card}>
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: 14,
                  fontWeight: "500",
                  paddingBottom: 12,
                }}
              >
                Personal Details
              </Text>
              {/* Username */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Username:</Text>
                <TextInput
                  style={styles.input}
                  value={profile?.userName}
                  onChangeText={(text) => handleChange("userName", text)}
                />
              </View>

              {/* First Name */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>First Name:</Text>
                <TextInput
                  style={styles.input}
                  value={profile?.firstName}
                  onChangeText={(text) => handleChange("firstName", text)}
                />
              </View>

              {/* Last Name */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Last Name:</Text>
                <TextInput
                  style={styles.input}
                  value={profile?.lastName}
                  onChangeText={(text) => handleChange("lastName", text)}
                />
              </View>

              {/* Birthday */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Birthday:</Text>
                <TouchableOpacity
                  style={styles.input}
                  onPress={() => setDatePickerVisible(true)}
                >
                  <Text style={{ color: theme.colors.primaryTextColor }}>
                    {profile?.birthday ? profile.birthday : "Select"}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Sex */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Sex:</Text>
                <TouchableOpacity
                  style={styles.input}
                  onPress={() => setSexPickerVisible(true)}
                >
                  <Text style={{ color: theme.colors.primaryTextColor }}>
                    {profile?.sex || "Select"}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Body Weight */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Body Weight:</Text>
                <TouchableOpacity
                  style={styles.input}
                  onPress={() => setWeightPickerVisible(true)}
                >
                  <Text style={{ color: theme.colors.primaryTextColor }}>
                    {profile.bodyWeight || "Enter weight"}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Height */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Height:</Text>
                <TouchableOpacity
                  style={styles.input}
                  onPress={() => setHeightPickerVisible(true)}
                >
                  <Text style={{ color: theme.colors.primaryTextColor }}>
                    {formatHeight(profile.height)}
                  </Text>
                </TouchableOpacity>
              </View>

              <Button
                onPress={handleSave}
                textColor={theme.colors.primary}
                mode="elevated"
                disabled={!hasChanges}
              >
                Save
              </Button>
            </Card>
          </ScrollView>

          {/* Modals */}
          <CustomSexPickerModal
            title="Select Sex"
            visible={isSexPickerVisible}
            onClose={() => setSexPickerVisible(false)}
            onSelect={(value) => {
              handleChange("sex", value);
              setSexPickerVisible(false);
            }}
          />

          <CustomWeightInputModal
            title="Enter Body Weight"
            visible={isWeightPickerVisible}
            onClose={() => setWeightPickerVisible(false)}
            onSelect={(value) => {
              handleChange("bodyWeight", value);
              setWeightPickerVisible(false);
            }}
          />

          <CustomHeightPickerModal
            title="Select Height"
            selectedHeight={profile.height} // Pass the height object directly
            onSelect={(height) => handleSelectHeight(height)}
            visible={isHeightPickerVisible}
            onClose={() => setHeightPickerVisible(false)}
          />

          <CustomDatePickerModal
            title="Select Birthday"
            visible={isDatePickerVisible}
            onClose={() => setDatePickerVisible(false)}
            onSelect={(value) => {
              handleChange(
                "birthday",
                value.toLocaleDateString("en-US", {
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                })
              );
              setDatePickerVisible(false);
            }}
          />
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default ProfileSettings;
