import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
} from "react";
import axios from "axios"; // Preferred HTTP client for browser-based applications
import { useAuth } from "../../../authentication/context/AuthContext.js"; // Importing context for user authentication
import { useTime } from "../../../context/TimeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";

import Configs from "../../../../constants.js";

const NutritionProgramContext = createContext();

export function useNutritionProgram() {
  // Custom hook for consuming context easily within components
  return useContext(NutritionProgramContext);
}

export function NutritionProgramProvider({ children }) {
  const { user } = useAuth(); // Destructure authenticated user from context
  const { getNutritionalGoals } = useUserSettings();
  const { calorieGoal, macroGoals } = getNutritionalGoals();
  const { getSelectedDateAsDate } = useTime();
  const userId = user?.uid;

  const apiUrl = Configs.NutraCompass_API_URL; // Base URL for API, should move to environment variables for production

  const [activeNutritionalProgram, setActiveNutritionalProgram] =
    useState(null);
  const [inactiveNutritionalPrograms, setInactiveNutritionalPrograms] =
    useState([]);
  const [generatedProgram, setGeneratedProgram] = useState(null);
  const [generatedNutritionalGoals, setGeneratedNutritionalGoals] =
    useState(null);
  const [actionState, setActionState] = useState(null); // Track current action (e.g., 'loading' or 'deactivating')
  const [error, setError] = useState(null);
  const [shouldShowAlignmentModal, setShouldShowAlignmentModal] =
    useState(false);

  useEffect(() => {
    if (
      !activeNutritionalProgram ||
      !activeNutritionalProgram.nutritionalGoals ||
      !activeNutritionalProgram.nutritionalGoals.macroGoals
    ) {
      setShouldShowAlignmentModal(false);
      return;
    }

    const currentDate = getSelectedDateAsDate();
    const programStartDate = new Date(activeNutritionalProgram.startDate);
    const programEndDate = new Date(activeNutritionalProgram.endDate);

    const isWithinProgramTimeline =
      currentDate >= programStartDate && currentDate <= programEndDate;

    const goalsAreDifferent =
      calorieGoal !== activeNutritionalProgram.nutritionalGoals.calorieGoal ||
      Math.round(macroGoals.protein.dailyPercentage * 100) !==
        Math.round(
          activeNutritionalProgram.nutritionalGoals.macroGoals.protein
            .dailyPercentage * 100
        ) ||
      Math.round(macroGoals.carb.dailyPercentage * 100) !==
        Math.round(
          activeNutritionalProgram.nutritionalGoals.macroGoals.carb
            .dailyPercentage * 100
        ) ||
      Math.round(macroGoals.fat.dailyPercentage * 100) !==
        Math.round(
          activeNutritionalProgram.nutritionalGoals.macroGoals.fat
            .dailyPercentage * 100
        );

    setShouldShowAlignmentModal(isWithinProgramTimeline && goalsAreDifferent);
  }, [activeNutritionalProgram, calorieGoal, macroGoals]);

  // Define loading message based on action state
  const getLoadingMessage = () => {
    switch (actionState) {
      case "loading":
        return "Loading Program...";
      case "deactivating":
        return "Deactivating Program...";
      default:
        return "Processing...";
    }
  };

  // Fetch active program
  const loadActiveNutritionalProgram = async (userId) => {
    setActionState("loading");
    try {
      const response = await axios.get(
        `${apiUrl}/v1/active-nutritional-program`,
        {
          params: { userId },
        }
      );
      // console.log(
      //   "Loaded Active Program: ",
      //   JSON.stringify(response.data, null, 1)
      // );
      setActiveNutritionalProgram(response.data);
    } catch (error) {
      console.error("Error fetching active program:", error);
      setError("Failed to load active program.");
    } finally {
      setActionState(null);
    }
  };

  // Fetch inactive programs
  const loadInactiveNutritionalPrograms = async (userId) => {
    setActionState("loading");
    try {
      const response = await axios.get(
        `${apiUrl}/v1/inactive-nutritional-programs`,
        {
          params: { userId },
        }
      );
      // console.log(
      //   "Loaded Inactive Programs: ",
      //   JSON.stringify(response.data, null, 1)
      // );
      setInactiveNutritionalPrograms(response.data);
    } catch (error) {
      console.error("Error fetching inactive programs:", error);
      setError("Failed to load inactive programs.");
    } finally {
      setActionState(null);
    }
  };

  // Function to activate a nutritional program
  const activateNutritionalProgram = async (program) => {
    if (!user || !program) return;

    try {
      setActionState("loading");
      const response = await axios.post(
        `${apiUrl}/v1/activate-nutritional-program`,
        {
          userId: userId,
          program,
        }
      );
      console.log(
        "Activated Program: ",
        JSON.stringify(response.data, null, 1)
      );
      setActiveNutritionalProgram(response.data.activatedProgram);
      return response.data.success;
    } catch (error) {
      console.error("Error activating nutritional program:", error);
      setError("Failed to activate nutritional program.");
    } finally {
      setActionState(null);
    }
  };

  // Function to deactivate a nutritional program
  const deactivateNutritionalProgram = async (programId) => {
    if (!user || !programId) return;

    try {
      setActionState("deactivating");
      const response = await axios.post(
        `${apiUrl}/v1/deactivate-nutritional-program`,
        {
          userId: userId,
          programId,
        }
      );

      // Move the deactivated program to the inactiveNutritionalPrograms state
      setInactiveNutritionalPrograms((prev) => [
        ...prev,
        activeNutritionalProgram,
      ]);
      setActiveNutritionalProgram(null); // Clear the active program
      return response.data.success;
    } catch (error) {
      console.error("Error deactivating nutritional program:", error);
      setError("Failed to deactivate nutritional program.");
    } finally {
      setActionState(null);
    }
  };

  // Fetch daily nutritional goals based on user data
  const calculateNutritionalGoals = async (
    sex,
    heightFeet,
    heightInches,
    weight,
    age,
    activityLevel,
    goal
  ) => {
    // Check for missing required fields
    if (
      !sex ||
      !heightFeet ||
      !heightInches ||
      !weight ||
      !age ||
      !activityLevel ||
      !goal
    ) {
      console.error("Missing or invalid input fields: ", {
        sex,
        heightFeet,
        heightInches,
        weight,
        age,
        activityLevel,
        goal,
      });
      return;
    }

    try {
      const response = await axios.post(
        `${apiUrl}/v1/calculate-nutritional-goals`,
        {
          sex,
          heightFeet,
          heightInches,
          weight,
          age,
          activityLevel,
          goal,
        }
      );
      console.log(
        "Generated Nutritional Goals: ",
        JSON.stringify(response.data, null, 1)
      );

      setGeneratedNutritionalGoals(response.data.nutritionalGoals);
    } catch (error) {
      console.error("Error calculating nutritional goals:", error);
      setError("Failed to calculate nutritional goals.");
    }
  };

  // Create a new nutritional program using the user's data
  const createNutritionalProgram = async (
    sex,
    heightFeet,
    heightInches,
    weight,
    age,
    activityLevel,
    goal,
    goalWeight,
    startDate,
    endDate
  ) => {
    // Check for missing required fields
    if (
      !sex ||
      !heightFeet ||
      !heightInches ||
      !weight ||
      !age ||
      !activityLevel ||
      !goal ||
      !goalWeight ||
      !startDate ||
      !endDate
    ) {
      console.error("Missing or invalid input fields: ", {
        sex,
        heightFeet,
        heightInches,
        weight,
        age,
        activityLevel,
        goal,
        goalWeight,
        startDate,
        endDate,
      });
      return;
    }

    try {
      setActionState("loading");
      const response = await axios.post(
        `${apiUrl}/v1/generate-nutritional-program`,
        {
          sex,
          heightFeet,
          heightInches,
          weight,
          age,
          activityLevel,
          goal,
          goalWeight,
          startDate,
          endDate,
        }
      );
      console.log(
        "Generated Nutritional Program: ",
        JSON.stringify(response.data, null, 1)
      );
      setGeneratedProgram(response.data.program);
    } catch (error) {
      console.error("Error creating nutritional program:", error);
      setError("Failed to create nutritional program.");
    } finally {
      setActionState(null);
    }
  };

  // User Program Checkpoint Logging Methods

  const submitWeight = async (checkpointId, newWeight) => {
    if (!userId || !checkpointId || newWeight == null) return;

    try {
      // Make the request to update the weight in the backend
      const response = await axios.post(
        `${apiUrl}/v1/active-nutritional-program/checkpoint/${checkpointId}/log-weight`,
        {
          userId, // Include userId as part of the request body
          userLoggedWeight: newWeight,
        }
      );

      // Check if the response contains the updated checkpoint info
      if (response.data && response.data.success && response.data.checkpoint) {
        const updatedCheckpoint = response.data.checkpoint;

        // Update the activeNutritionalProgram with the updated checkpoint data
        setActiveNutritionalProgram((prevProgram) => {
          if (!prevProgram || !prevProgram.checkpoints) return prevProgram;

          // Map over the checkpoints to find and update the specific checkpoint
          const updatedCheckpoints = prevProgram.checkpoints.map((checkpoint) =>
            checkpoint.week === checkpointId
              ? {
                  ...checkpoint,
                  userLoggedWeight: updatedCheckpoint.userLoggedWeight,
                  feedback: updatedCheckpoint.feedback,
                  grade: updatedCheckpoint.grade,
                }
              : checkpoint
          );

          // Return the updated program with modified checkpoints
          return {
            ...prevProgram,
            checkpoints: updatedCheckpoints,
          };
        });

        return updatedCheckpoint;
      } else {
        console.error("Failed to update checkpoint weight:", response.data);
      }
    } catch (error) {
      console.error("Error updating weight:", error);
    }
  };

  // Function to upload progress picture
  const uploadProgressPicture = async (uri, checkpointId) => {
    if (!userId || !uri) return null;

    try {
      const formData = new FormData();

      // Append the image file to formData (using the same method as custom meal image handling)
      formData.append("progressPicture", {
        uri,
        type: "image/jpeg", // Adjust based on actual image type
        name: "progressPicture.jpg", // Adjust based on actual filename or preference
      });

      // Append userId to formData
      formData.append("userId", userId);

      // Send the image to the backend API, which will handle uploading to Cloud Storage and Firestore
      const uploadResponse = await axios.post(
        `${apiUrl}/v1/active-nutritional-program/checkpoint/${checkpointId}/upload-progress-picture`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );

      const { progressPictureUrl } = uploadResponse.data; // Backend returns the image URL

      // Update the activeNutritionalProgram with the new picture URL
      setActiveNutritionalProgram((prevProgram) => {
        const updatedCheckpoints = prevProgram.checkpoints.map((checkpoint) =>
          checkpoint.week === checkpointId
            ? { ...checkpoint, progressPictureUrl } // Update URL with backend's response
            : checkpoint
        );
        return { ...prevProgram, checkpoints: updatedCheckpoints };
      });

      return progressPictureUrl;
    } catch (error) {
      console.error("Error uploading progress picture:", error);
      return null; // Handle the error appropriately
    }
  };

  // Function to remove progress picture
  const removeProgressPicture = async (checkpointId) => {
    if (!userId) return null;

    try {
      // Call the backend to delete the image both from Cloud Storage and Firestore
      await axios.delete(
        `${apiUrl}/v1/active-nutritional-program/checkpoint/${checkpointId}/delete-progress-picture`,
        {
          data: { userId }, // Send the userId in the request body
        }
      );

      // Update the activeNutritionalProgram to remove the picture URL
      setActiveNutritionalProgram((prevProgram) => {
        const updatedCheckpoints = prevProgram.checkpoints.map((checkpoint) =>
          checkpoint.week === checkpointId
            ? { ...checkpoint, progressPictureUrl: null }
            : checkpoint
        );
        return { ...prevProgram, checkpoints: updatedCheckpoints };
      });
    } catch (error) {
      console.error("Error removing progress picture:", error);
      throw error; // Handle the error appropriately
    }
  };

  const contextValue = useMemo(
    () => ({
      loadActiveNutritionalProgram,
      loadInactiveNutritionalPrograms,
      activeNutritionalProgram,
      inactiveNutritionalPrograms,
      activateNutritionalProgram,
      deactivateNutritionalProgram,
      generatedProgram,
      generatedNutritionalGoals,
      actionState,
      getLoadingMessage,
      error,
      shouldShowAlignmentModal,
      setShouldShowAlignmentModal,
      calculateNutritionalGoals,
      createNutritionalProgram,
      submitWeight,
      uploadProgressPicture,
      removeProgressPicture,
    }),
    [
      loadActiveNutritionalProgram,
      loadInactiveNutritionalPrograms,
      activeNutritionalProgram,
      inactiveNutritionalPrograms,
      activateNutritionalProgram,
      deactivateNutritionalProgram,
      generatedProgram,
      generatedNutritionalGoals,
      actionState,
      getLoadingMessage,
      error,
      shouldShowAlignmentModal,
      setShouldShowAlignmentModal,
      submitWeight,
      uploadProgressPicture,
      removeProgressPicture,
    ]
  );

  return (
    <NutritionProgramContext.Provider value={contextValue}>
      {children}
    </NutritionProgramContext.Provider>
  );
}
