import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
  useRef,
} from "react";
import axios from "axios"; // Preferred HTTP client for browser-based applications
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useAuth } from "../../../authentication/context/AuthContext.js"; // Importing context for user authentication
import Configs from "../../../../configs.js";
import dayjs from "dayjs";
import uuid from "react-native-uuid"; // UUID generator for client-side unique identifiers
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { useStepsLog } from "../../StepsLog/context/StepsLogContext.js";
import { usePushNotification } from "../../../context/PushNotificationContext.js";
import isEqual from "lodash.isequal";

const getMealSectionsCacheKey = (userId) => `@meal_sections_cache_${userId}`;
const CACHE_TTL = 60 * 60 * 1000; // 1 hour
const FoodLogContext = createContext();

export function useFoodLog() {
  // Custom hook for consuming context easily within components
  return useContext(FoodLogContext);
}

export function FoodLogProvider({ children }) {
  const { user } = useAuth(); // Destructure authenticated user from context
  const { selectedDate } = useTime();
  const { syncMealNames } = usePushNotification();

  const userId = user?.uid;
  const apiUrl = Configs.NutraCompass_API_URL; // Base URL for API

  const { getNutritionalGoals } = useUserSettings();
  const { calorieGoal, waterGoal, macroGoals } = getNutritionalGoals();
  const { caloriesBurned } = useStepsLog();

  // State management for various components of the food log
  const [mealSections, setMealSections] = useState([]);
  const [foodEntries, setFoodEntries] = useState({});
  const [foodHistory, setFoodHistory] = useState([]);
  const prevMealNamesRef = useRef({});

  // Function to extract required details from food entries
  const extractEntryDetails = (foodEntries) => {
    const simplifiedEntries = {};
    for (const mealType in foodEntries) {
      simplifiedEntries[mealType] = foodEntries[mealType].map((entry) => ({
        id: entry.id,
        mealType: entry.mealType,
        date: entry.date,
        foodLabel: entry.foodLabel,
      }));
    }
    return simplifiedEntries;
  };

  // Example of logging the simplified entries
  //console.log(JSON.stringify(extractEntryDetails(foodEntries), null, 2));

  // Load data dependent on meal sections
  useEffect(() => {
    const loadAndSync = async () => {
      if (mealSections.length === 0) return;

      try {
        // Always load dependent data first
        await Promise.all([loadFoodEntries(), loadFoodHistory()]);

        // Calculate name changes only when necessary
        const currentMealMap = mealSections.reduce((acc, meal) => {
          acc[meal.id] = meal.name;
          return acc;
        }, {});

        const previousMealMap = prevMealNamesRef.current;
        const mealSectionUpdates = {};

        // Detect actual changes
        mealSections.forEach((meal) => {
          if (previousMealMap[meal.id] !== meal.name) {
            mealSectionUpdates[meal.id] = meal.name;
          }
        });

        // Only sync if there are changes
        if (Object.keys(mealSectionUpdates).length > 0) {
          await syncMealNames(mealSectionUpdates);
        }

        // Update reference regardless of changes
        prevMealNamesRef.current = currentMealMap;
      } catch (error) {
        console.error("Meal data operations failed:", error);
      }
    };

    loadAndSync();
  }, [mealSections]);

  const loadMealSectionCustomizations = async (forceRefresh = false) => {
    const cacheKey = getMealSectionsCacheKey(userId);
    const DEFAULT_SECTIONS = [
      /* Your default meal sections here */
    ];

    try {
      if (!forceRefresh) {
        const cachedData = await AsyncStorage.getItem(cacheKey);
        if (cachedData) {
          const { data, timestamp } = JSON.parse(cachedData);
          const isCacheValid =
            Array.isArray(data) &&
            data.length > 0 &&
            data.every(
              (item) =>
                item?.id &&
                typeof item.id === "string" &&
                item.id !== "undefined" &&
                (item.id.startsWith("Meal") || item.id === "Water") &&
                typeof item.name === "string"
            );

          if (isCacheValid) {
            const hasChanges = !isEqual(data, mealSections);
            if (hasChanges) {
              console.log("[CACHE] Applying cached meal sections");
              setMealSections(data);
            }
            return data;
          } else {
            console.log("[CACHE] Discarding invalid/empty cache");
            await AsyncStorage.removeItem(cacheKey);
          }
        }
      }

      console.log("[NETWORK] Fetching fresh meal sections");
      const response = await axios.get(
        `${apiUrl}/v1/food/diary/${userId}/meal-sections`
      );

      // Validate and normalize response
      const newData = (response.data || [])
        .filter(
          (item) =>
            item?.id &&
            typeof item?.name === "string" &&
            item.id !== "undefined"
        )
        .concat(
          DEFAULT_SECTIONS.filter(
            (d) => !response.data?.some((r) => r.id === d.id)
          )
        );

      if (newData.length === 0) {
        console.warn("[NETWORK] Empty response, using defaults");
        setMealSections(DEFAULT_SECTIONS);
        return DEFAULT_SECTIONS;
      }

      console.log(
        "[STATE] Setting fresh sections:",
        newData.map((i) => `${i.id}:${i.name}`).join(", ")
      );

      setMealSections(newData);
      await AsyncStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: newData,
          timestamp: Date.now(),
        })
      );

      return newData;
    } catch (error) {
      console.error("[ERROR] Load failed:", error);

      const fallback =
        mealSections.length > 0 ? mealSections : DEFAULT_SECTIONS;
      console.log(
        "[FALLBACK] Using:",
        fallback.map((i) => i.id)
      );

      if (mealSections.length === 0) {
        setMealSections(fallback);
      }

      return fallback;
    }
  };

  const updateMealSectionNames = async (mealSectionUpdates) => {
    const cacheKey = getMealSectionsCacheKey(userId);
    const previousSections = [...mealSections];

    try {
      // 1. Server update (critical operation)
      const response = await axios.put(
        `${apiUrl}/v1/food/diary/${userId}/meal-sections/update-names`,
        mealSectionUpdates
      );

      // 2. Validate and process server response
      const newMealSections = (response.data.mealSections || [])
        .filter(
          (item) =>
            item?.id &&
            typeof item.id === "string" &&
            item.id !== "undefined" &&
            item.id.startsWith("Meal")
        )
        .concat(previousSections.filter((s) => s.id === "Water")) // Preserve Water
        .filter((v, i, a) => a.findIndex((t) => t.id === v.id) === i); // Deduplicate

      // 3. Update state with validated data
      setMealSections(newMealSections);

      // 4. Update cache with cleaned data
      await AsyncStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: newMealSections,
          timestamp: Date.now(),
        })
      );
    } catch (error) {
      if (!error.response || error.response.status >= 500) {
        console.log("Rolling back meal sections");
        setMealSections(previousSections);
        await AsyncStorage.setItem(
          cacheKey,
          JSON.stringify({
            data: previousSections,
            timestamp: Date.now(),
          })
        );
      }
      throw error;
    }
  };

  // Function to load user's food history from the backend
  const loadFoodHistory = async () => {
    try {
      const response = await axios.get(
        `${apiUrl}/v1/food/diary/${userId}/history`
      );
      const uniqueHistory = removeDuplicates(response.data || []);
      setFoodHistory(uniqueHistory);
    } catch (error) {
      console.error("Error loading food history:", error);
    }
  };

  // Ensure no duplicate entries in food history
  const removeDuplicates = (items) => {
    const seenMeals = new Set(); // Track meal IDs
    const seenEntries = new Set(); // Track individual entry IDs

    return items.filter((item) => {
      const isCustomMeal = item?.isCustomMeal === true;
      const uniqueId = isCustomMeal ? item.mealId : item.id;

      if (isCustomMeal) {
        if (seenMeals.has(uniqueId)) return false; // Duplicate meal
        seenMeals.add(uniqueId);
      } else {
        if (seenEntries.has(uniqueId)) return false; // Duplicate individual entry
        seenEntries.add(uniqueId);
      }

      return true; // Include non-duplicate items
    });
  };

  // Update food history when a new food item or custom meal is logged
  const updateFoodHistory = (newEntry) => {
    const isCustomMeal = newEntry?.isCustomMeal === true;

    setFoodHistory((prevHistory) => {
      const entryWithType = {
        ...newEntry,
        type: isCustomMeal ? "meal" : "food",
      };

      const updatedHistory = [entryWithType, ...prevHistory];
      return removeDuplicates(updatedHistory);
    });
  };

  // Load food entries for all meal sections
  const loadFoodEntries = async () => {
    // Check if there are valid meal sections
    if (mealSections.length === 0) {
      console.error("No meal sections available.");
      return;
    }

    try {
      const updatedEntries = {};

      for (const section of mealSections) {
        const mealType = section.id;

        const response = await axios.get(
          `${apiUrl}/v1/food/diary/${userId}/entries`,
          { params: { mealType: section.id } }
        );

        const entries = [];

        response.data.forEach((entry) => {
          entries.push({ id: entry.id, ...entry });
        });

        // Create a shallow copy of the previous state
        updatedEntries[mealType] = entries;
      }

      // Update the local state with the loaded entries for all meal sections
      setFoodEntries(updatedEntries);
    } catch (error) {
      console.error("Error loading food entries:", error);
      // Handle the error gracefully, e.g., show a message to the user
    }
  };

  const saveMealToFoodLog = async (mealType, selectedDate, selectedMeal) => {
    try {
      const response = await axios.post(
        `${apiUrl}/v1/food/diary/${userId}/save-meal`,
        {
          mealType,
          selectedDate,
          mealItems: selectedMeal.mealItems,
          mealId: selectedMeal.id,
        }
      );

      // Update state with new meal entries directly
      setFoodEntries((prevEntries) => {
        const updatedEntries = prevEntries[mealType]
          ? [...prevEntries[mealType]]
          : [];
        response.data.result.entryItems.forEach((entry) => {
          if (!updatedEntries.find((e) => e.id === entry.id)) {
            updatedEntries.push(entry);
          }
        });
        return {
          ...prevEntries,
          [mealType]: updatedEntries,
        };
      });

      // Update history with the custom meal object
      updateFoodHistory(selectedMeal);
    } catch (error) {
      console.error("Error saving meal to food log:", error);
    }
  };

  const saveOrUpdateSingleFoodItemToFoodLog = async (
    entryId,
    mealType,
    updatedEntry
  ) => {
    // Validate required fields
    if (!updatedEntry || !updatedEntry.foodId) {
      console.error("Missing required entry details: 'foodId'");
      throw new Error("Missing required entry details.");
    }

    try {
      const response = await axios.post(
        `${apiUrl}/v1/food/diary/${userId}/entries/${entryId || ""}`,
        {
          mealType,
          updatedEntry,
          selectedDate: selectedDate,
        }
      );
      if (entryId && response.data.result.entry) {
        console.log("Updated Food Entry and Updating Local state: ", entryId);
        // Update the local state
        setFoodEntries((prevEntries) => {
          const updatedEntries = { ...prevEntries };

          // Remove the entry from its current mealType
          for (const [currentMealType, currentMealTypeArray] of Object.entries(
            updatedEntries
          )) {
            const entryIndex = currentMealTypeArray.findIndex(
              (entry) => entry.id === entryId
            );

            if (entryIndex !== -1) {
              console.log(
                `Removing entry from ${currentMealType} meal type array.`
              );
              const updatedMealTypeArray = [...currentMealTypeArray];
              updatedMealTypeArray.splice(entryIndex, 1);
              updatedEntries[currentMealType] = updatedMealTypeArray;
              break; // Stop after removing from the first occurrence
            }
          }

          // Add the entry to the updated mealType, only if mealType exists
          if (mealType in updatedEntries) {
            updatedEntries[mealType] = [
              ...updatedEntries[mealType],
              { ...response.data.result.entry },
            ];
            console.log("Entry added to the meal type array.");
          } else {
            console.log("Meal type does not exist. Entry not updated.");
          }

          return updatedEntries;
        });

        // Update history with the individual food item
        //updateFoodHistory(response.data.result.entry);
      } else if (!entryId && response.data.result.entry) {
        // Update the local state
        setFoodEntries((prevEntries) => {
          const updatedEntries = { ...prevEntries };

          // Add the entry to the updated mealType
          if (mealType in updatedEntries) {
            updatedEntries[mealType] = [
              ...updatedEntries[mealType],
              { ...response.data.result.entry },
            ];
            console.log("Entry added to the meal type array.");
          } else {
            console.log("Meal type does not exist. Entry not added.");
          }

          return updatedEntries;
        });

        // Update history with the individual food item
        updateFoodHistory(response.data.result.entry);
      }
    } catch (error) {
      console.error("Error saving or updating food entry:", error.message);
    }
  };

  const deleteFoodEntry = async (mealType, entryId) => {
    try {
      await axios.delete(
        `${apiUrl}/v1/food/diary/${userId}/entries/${entryId}`
      );
      // Update the local state
      setFoodEntries((prevEntries) => {
        const updatedEntries = { ...prevEntries };

        // Find the meal section based on the id property
        const mealSection = mealSections.find(
          (section) => section.id === mealType
        );

        // Check if the meal section is found
        if (mealSection) {
          updatedEntries[mealSection.id] = updatedEntries[
            mealSection.id
          ].filter((entry) => entry.id !== entryId);
          console.log("Entry removed from the meal type array.");
        } else {
          console.warn(`Meal section with id ${mealType} not found.`);
        }

        return updatedEntries;
      });
    } catch (error) {
      console.error("Error deleting food entry:", error);
    }
  };

  // Function to handle deleting all entries from a specific meal section
  const deleteMealSectionEntries = async (mealType) => {
    try {
      await axios.delete(
        `${apiUrl}/v1/food/diary/${userId}/entries/meal-section/${mealType}/date/${selectedDate}`
      );

      // Update local state to reflect the deletion
      setFoodEntries((prevEntries) => {
        const updatedEntries = { ...prevEntries };
        // Ensure the entries for the specific meal section and date are cleared
        if (updatedEntries[mealType]) {
          updatedEntries[mealType] = updatedEntries[mealType].filter(
            (entry) => entry.date !== selectedDate
          );
        }
        return updatedEntries;
      });

      console.log("Entries deleted successfully.");
    } catch (error) {
      console.error("Failed to delete entries:", error);
    }
  };

  const copyEntriesBetweenMealSections = async (
    sourceMealType,
    destinationMealType,
    sourceDate,
    destinationDate
  ) => {
    try {
      // Retrieve entries from the source meal type and date
      let entries = foodEntries[sourceMealType] || [];

      entries = entries.filter((entry) => entry.date === sourceDate);

      if (entries.length > 0) {
        const response = await axios.post(
          `${apiUrl}/v1/food/diary/${userId}/copy-entries`,
          {
            entries,
            destinationMealType,
            destinationDate,
          }
        );
        console.log(response.data.message);

        // Update local state with copied entries for the destination meal type and date
        if (response.data.message.includes("successfully")) {
          setFoodEntries((prevEntries) => {
            const updatedEntries = { ...prevEntries };

            // Map entries to include new IDs and dates returned from the server
            const newEntries = response.data.entries.map((entry) => ({
              ...entry,
              mealType: destinationMealType,
            }));

            // Add or update the entries for the destination date and meal type
            updatedEntries[destinationMealType] = [
              ...(updatedEntries[destinationMealType] || []),
              ...newEntries,
            ];

            return updatedEntries;
          });
        }
      } else {
        console.log("No entries found to copy.");
      }
    } catch (error) {
      console.error("Error copying entries:", error);
    }
  };

  const addQuickFoodEntry = async (mealType, foodDetails) => {
    if (!mealType || !foodDetails) return;

    const payload = {
      mealType: mealType,
      entryDetails: foodDetails,
      selectedDate: selectedDate, // Assuming you have a utility to format dates
    };

    try {
      const response = await axios.post(
        `${apiUrl}/v1/food/diary/${userId}/quick-entry`,
        payload
      );

      // Assuming response.data.entry contains the new entry details
      const newEntry = response.data.entry;

      // Update local state
      setFoodEntries((prevEntries) => {
        const currentEntries = prevEntries[mealType] || [];
        if (foodDetails.id) {
          // Update the existing entry
          return {
            ...prevEntries,
            [mealType]: currentEntries.map((entry) =>
              entry.id === foodDetails.id ? { ...entry, ...newEntry } : entry
            ),
          };
        } else {
          // Add new entry
          return {
            ...prevEntries,
            [mealType]: [...currentEntries, newEntry],
          };
        }
      });

      console.log("Quick add successful:", response.data);
    } catch (error) {
      console.error("Failed to submit quick add:", error);
    }
  };

  const addWaterEntry = async (volume, unit, existingId) => {
    try {
      const response = await axios.post(
        `${apiUrl}/v1/food/diary/${userId}/water-log`,
        {
          volume: volume,
          unit: unit,
          date: selectedDate,
          id: existingId, // If it's an update, existingId will be used
        }
      );

      const newEntry = response.data.entry;

      // Update local state
      setFoodEntries((prevEntries) => {
        const currentEntries = prevEntries[newEntry.mealType] || [];
        if (existingId) {
          // Update the existing entry
          return {
            ...prevEntries,
            [newEntry.mealType]: currentEntries.map((entry) =>
              entry.id === existingId ? { ...entry, ...newEntry } : entry
            ),
          };
        } else {
          // Add new entry
          return {
            ...prevEntries,
            [newEntry.mealType]: [...currentEntries, newEntry],
          };
        }
      });

      console.log("Adding water entry successful:", response.data);
    } catch (error) {
      console.error("Failed to log water intake:", error);
    }
  };

  // Calculate the total daily water consumed
  const totalDailyWaterConsumed = React.useMemo(() => {
    const entries = foodEntries["Water"] || [];
    // Use dayjs to compare dates in "YYYY-MM-DD" format
    const todayEntries = entries.filter((entry) => {
      const entryDateFormatted = dayjs(entry.date).format("YYYY-MM-DD");
      const selectedDateFormatted = dayjs(selectedDate).format("YYYY-MM-DD");
      return entryDateFormatted === selectedDateFormatted;
    });
    //console.log("Today Water entries:", todayEntries);

    const total = todayEntries.reduce((total, entry) => {
      if (waterGoal?.unit === "fl oz") {
        // Convert entry.volume from ml to fluid ounces if necessary
        const volumeInOunces =
          entry.unit === "ml" ? entry.volume * 0.033814 : entry.volume;
        return total + parseFloat(volumeInOunces);
      } else {
        // Otherwise, assume the water goal is in milliliters
        const volumeInMilliliters =
          entry.unit === "fl oz" ? entry.volume * 29.5735 : entry.volume;
        return total + parseFloat(volumeInMilliliters);
      }
    }, 0);
    //console.log("Total Daily Water Consumed:", total);
    return total;
  }, [foodEntries, selectedDate, waterGoal]);

  // Calculate the water consumption percentage (0 to 100)
  const totalDailyWaterConsumedPercentage = React.useMemo(() => {
    //console.log("waterGoal:", waterGoal);
    // If waterGoal.amount is missing or zero, return 0
    if (!waterGoal?.amount || waterGoal.amount === 0) return 0;
    const rawPercentage = (totalDailyWaterConsumed / waterGoal.amount) * 100;
    //console.log("Raw Water Percentage:", rawPercentage);
    // Clamp percentage to 100
    const adjustedPercentage = Math.min(rawPercentage, 100);
    //console.log("Adjusted Water Percentage:", adjustedPercentage);
    return adjustedPercentage;
  }, [totalDailyWaterConsumed, waterGoal]);

  const filteredEntriesByMeal = useMemo(() => {
    // Filters entries by meal type and date, used for display and calculations
    return mealSections.reduce((acc, section) => {
      acc[section.id] =
        foodEntries[section.id]?.filter(
          (entry) => entry.date === selectedDate
        ) || [];
      return acc;
    }, {});
  }, [mealSections, foodEntries, selectedDate]);

  const findMealNameById = (mealId) => {
    const mealSection = mealSections.find((section) => section.id === mealId);
    return mealSection ? mealSection?.name : ""; // You can adjust the fallback if needed
  };

  const calculateTotalCaloriesAndMacros = (entries) => {
    // Calculates total calories and macros from food entries
    return entries.reduce(
      (total, entry) => {
        if (entry && entry.nutrients) {
          const { ENERC_KCAL, CHOCDF, PROCNT, FAT } = entry.nutrients;
          return {
            calories: total.calories + parseFloat(ENERC_KCAL?.quantity || 0),
            carbs: total.carbs + parseFloat(CHOCDF?.quantity || 0),
            protein: total.protein + parseFloat(PROCNT?.quantity || 0),
            fat: total.fat + parseFloat(FAT?.quantity || 0),
          };
        } else {
          return total;
        }
      },
      { calories: 0, carbs: 0, protein: 0, fat: 0 }
    );
  };

  const totalDailyCaloriesAndMacrosConsumed = useMemo(() => {
    // Computes daily totals of calories and macros consumed, used for nutritional tracking
    return calculateTotalCaloriesAndMacros(
      mealSections.flatMap((section) => filteredEntriesByMeal[section.id] || [])
    );
  }, [mealSections, filteredEntriesByMeal]);

  // Memoize data to prevent unnecessary recalculations on re-renders
  const calorieData = useMemo(() => {
    const totalCaloriesConsumed = totalDailyCaloriesAndMacrosConsumed.calories;

    // Left Half Circle: Percentage of calories consumed from food
    const foodConsumedPercentage = totalCaloriesConsumed / calorieGoal;

    // Center Full Circle: Progress (food consumed - calories burned) / calorieGoal
    const netCalorieProgressPercentage =
      (totalCaloriesConsumed - caloriesBurned) / calorieGoal;

    // Right Half Circle: Percentage of calories burned vs. goal
    const caloriesBurnedPercentage = caloriesBurned / calorieGoal;

    return {
      calorieGoal,
      totalCalories: totalCaloriesConsumed,
      caloriesRemaining: calorieGoal - totalCaloriesConsumed + caloriesBurned,
      foodConsumedPercentage: isNaN(foodConsumedPercentage)
        ? 0
        : foodConsumedPercentage,
      netCalorieProgressPercentage: isNaN(netCalorieProgressPercentage)
        ? 0
        : netCalorieProgressPercentage,
      caloriesBurnedPercentage: isNaN(caloriesBurnedPercentage)
        ? 0
        : caloriesBurnedPercentage,
    };
  }, [totalDailyCaloriesAndMacrosConsumed, calorieGoal, caloriesBurned]);

  const macroData = useMemo(() => {
    // Pre-calculate the percentage values
    const carbsPercentage =
      totalDailyCaloriesAndMacrosConsumed.carbs /
      (macroGoals?.carb?.dailyGrams || 1);

    const proteinPercentage =
      totalDailyCaloriesAndMacrosConsumed.protein /
      (macroGoals?.protein?.dailyGrams || 1);

    const fatPercentage =
      totalDailyCaloriesAndMacrosConsumed.fat /
      (macroGoals?.fat?.dailyGrams || 1);

    return {
      carbsData: {
        percentage: isNaN(carbsPercentage) ? 0 : carbsPercentage,
        consumedColor: "#FFA500", // Orange
        remainingColor: "#FFD580", // Light orange
        label: "Carbs",
        totalGramsGoal: macroGoals?.carb?.dailyGrams || 0,
        consumedGrams: totalDailyCaloriesAndMacrosConsumed.carbs || 0,
        iconName: "corn",
      },
      proteinData: {
        percentage: isNaN(proteinPercentage) ? 0 : proteinPercentage,
        consumedColor: "#32CD32", // Green
        remainingColor: "#8EDC8E", // Light green
        label: "Protein",
        totalGramsGoal: macroGoals?.protein?.dailyGrams || 0,
        consumedGrams: totalDailyCaloriesAndMacrosConsumed.protein || 0,
        iconName: "food-steak",
      },
      fatData: {
        percentage: isNaN(fatPercentage) ? 0 : fatPercentage,
        consumedColor: "#FF0000", // Red
        remainingColor: "#FF9999", // Light red
        label: "Fat",
        totalGramsGoal: macroGoals?.fat?.dailyGrams || 0,
        consumedGrams: totalDailyCaloriesAndMacrosConsumed.fat || 0,
        iconName: "sausage",
      },
    };
  }, [totalDailyCaloriesAndMacrosConsumed, macroGoals]);

  const contextValue = useMemo(
    () => ({
      loadMealSectionCustomizations,
      foodHistory,
      loadFoodEntries,
      mealSections,
      setMealSections,
      foodEntries,
      setFoodEntries,
      updateMealSectionNames,
      saveMealToFoodLog,
      deleteFoodEntry,
      deleteMealSectionEntries,
      saveOrUpdateSingleFoodItemToFoodLog,
      copyEntriesBetweenMealSections,
      calculateTotalCaloriesAndMacros,
      filteredEntriesByMeal,
      totalDailyCaloriesAndMacrosConsumed,
      calorieData,
      macroData,
      addQuickFoodEntry,
      addWaterEntry,
      totalDailyWaterConsumed,
      totalDailyWaterConsumedPercentage,
      findMealNameById,
    }),
    [
      loadMealSectionCustomizations,
      foodHistory,
      loadFoodEntries,
      mealSections,
      setMealSections,
      foodEntries,
      setFoodEntries,
      updateMealSectionNames,
      saveMealToFoodLog,
      deleteFoodEntry,
      deleteMealSectionEntries,
      saveOrUpdateSingleFoodItemToFoodLog,
      copyEntriesBetweenMealSections,
      calculateTotalCaloriesAndMacros,
      filteredEntriesByMeal,
      totalDailyCaloriesAndMacrosConsumed,
      calorieData,
      macroData,
      addQuickFoodEntry,
      addWaterEntry,
      totalDailyWaterConsumed,
      totalDailyWaterConsumedPercentage,
      findMealNameById,
    ]
  );

  return (
    <FoodLogContext.Provider value={contextValue}>
      {children}
    </FoodLogContext.Provider>
  );
}
