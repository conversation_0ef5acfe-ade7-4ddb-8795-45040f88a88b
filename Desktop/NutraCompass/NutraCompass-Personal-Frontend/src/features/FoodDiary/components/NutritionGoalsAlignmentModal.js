import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import Modal from "react-native-modal";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useNutritionProgram } from "../../NutritionalProgram/context/NutritionProgramContext.js";
import * as Haptics from "expo-haptics";
import { MaterialIcons } from "@expo/vector-icons";

const NutritionGoalsAlignmentModal = ({ isVisible, onClose }) => {
  const { theme } = useThemeContext();
  const { setCalorieAndMacroGoals } = useUserSettings();
  const { activeNutritionalProgram } = useNutritionProgram();

  const programNutritionalGoals =
    activeNutritionalProgram?.nutritionalGoals || {};
  const programMacroGoals = programNutritionalGoals.macroGoals || {};

  const handleAutoAlign = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      setCalorieAndMacroGoals({
        calorieGoal: programNutritionalGoals.calorieGoal || 0,
        proteinPercentage:
          (programMacroGoals.protein?.dailyPercentage || 0) * 100,
        carbPercentage: (programMacroGoals.carb?.dailyPercentage || 0) * 100,
        fatPercentage: (programMacroGoals.fat?.dailyPercentage || 0) * 100,
      });

      // Close the modal after updating nutritional goals
      onClose && onClose();
    } catch (error) {
      console.error("Error auto-aligning nutritional goals:", error);
    }
  };

  const handleIgnore = () => {
    // Close the modal without making changes
    onClose && onClose();
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Modal
      isVisible={isVisible}
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View
        style={{
          backgroundColor: theme.colors.surface,
          borderRadius: 10,
          padding: 20,
          width: "90%",
          alignItems: "center",
          position: "relative", // Enable absolute positioning for the icon
        }}
      >
        {/* Alert Icon */}
        <MaterialIcons
          name="error-outline" // Use an appropriate icon name
          size={26} // Adjust size as needed
          color="red" // Adjust color as needed
          style={{
            position: "absolute", // Position it absolutely
            top: 10, // Adjust position
            left: 10, // Adjust position
          }}
        />

        <Text
          style={{
            fontSize: 18,
            fontWeight: "bold",
            marginBottom: 15,
            color: theme.colors.primaryTextColor,
          }}
        >
          Align Nutritional Goals
        </Text>

        {/* Modal Content */}
        <View style={{ width: "100%", marginBottom: 15 }}>
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              marginBottom: 10,
            }}
          >
            Your current nutritional goals don't match your active program:
          </Text>

          <View
            style={{
              backgroundColor: theme.colors.screenBackground,
              borderRadius: 5,
              padding: 10,
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 8,
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: 14,
                }}
              >
                Calories:
              </Text>
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                }}
              >
                {programNutritionalGoals.calorieGoal || 0} cal
              </Text>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 8,
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: 14,
                }}
              >
                Protein:
              </Text>
              <Text style={{ color: theme.colors.primaryTextColor }}>
                {(
                  (programMacroGoals.protein?.dailyPercentage || 0) * 100
                ).toFixed(0)}
                % ({programMacroGoals.protein?.dailyGrams || 0}g)
              </Text>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 8,
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: 14,
                }}
              >
                Carbs:
              </Text>
              <Text style={{ color: theme.colors.primaryTextColor }}>
                {((programMacroGoals.carb?.dailyPercentage || 0) * 100).toFixed(
                  0
                )}
                % ({programMacroGoals.carb?.dailyGrams || 0}g)
              </Text>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 8,
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: 14,
                }}
              >
                Fat:
              </Text>
              <Text style={{ color: theme.colors.primaryTextColor }}>
                {((programMacroGoals.fat?.dailyPercentage || 0) * 100).toFixed(
                  0
                )}
                % ({programMacroGoals.fat?.dailyGrams || 0}g)
              </Text>
            </View>
          </View>
        </View>

        {/* Buttons */}
        <View
          style={{
            flexDirection: "row-reverse",
            justifyContent: "space-between",
            gap: 6,
          }}
        >
          <TouchableOpacity
            onPress={handleAutoAlign}
            style={{
              backgroundColor: theme.colors.primary,
              borderRadius: 5,
              flex: 2,
              alignItems: "center",
              justifyContent: "center",
              paddingHorizontal: 4,
              paddingVertical: 8,
            }}
          >
            <Text style={{ color: "white", fontWeight: "bold" }}>
              Update Nutritional Goals
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleIgnore}
            style={{
              backgroundColor: "red",
              borderRadius: 5,
              flex: 1,
              alignItems: "center",
              justifyContent: "center",
              paddingHorizontal: 4,
              paddingVertical: 8,
            }}
          >
            <Text style={{ color: "white", fontWeight: "bold" }}>Ignore</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default NutritionGoalsAlignmentModal;
