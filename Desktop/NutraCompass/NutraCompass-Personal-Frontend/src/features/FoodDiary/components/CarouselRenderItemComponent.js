import React, { useMemo } from "react";
import { View } from "react-native";
import CaloriesAndWaterProgressSection from "./CaloriesAndWaterProgressSection.js";
import MacroProgressSection from "./MacroProgressSection.js";
import DailyNutritionPerformanceSummary from "./DailyNutritionPerformanceSummary.js";

// Memoize the component to prevent unnecessary re-renders
const CarouselRenderItemComponent = React.memo(({ item }) => {
  const renderContent = useMemo(() => {
    switch (item.type) {
      case "CaloriesAndWaterProgressSection":
        return (
          <CaloriesAndWaterProgressSection
            calorieData={item.calorieData}
            caloriesBurned={item.caloriesBurned}
            waterProgressBarPercentage={item.waterProgressBarPercentage}
          />
        );
      case "MacroProgressSection":
        return <MacroProgressSection macroData={item.macroData} />;
      case "DailyNutritionPerformanceSummary":
        return <DailyNutritionPerformanceSummary />;
      default:
        return null;
    }
  }, [item]);

  return (
    <View
      style={{
        flex: 1,
        padding: 5,
      }}
    >
      {renderContent}
    </View>
  );
});

export default CarouselRenderItemComponent;
