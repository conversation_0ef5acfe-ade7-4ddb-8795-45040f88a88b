import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Switch,
  Dimensions,
  Image,
} from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import Feather from "react-native-vector-icons/Feather";

const { width: SCREEN_WIDTH } = Dimensions.get("window");

const WaterGoalModal = ({ isVisible, closeModal }) => {
  const { theme } = useThemeContext();
  const { setWaterGoal, getNutritionalGoals } = useUserSettings();
  const { waterGoal } = getNutritionalGoals();
  const initialGoal = waterGoal || { amount: 2000, unit: "ml" };

  const [inputGoal, setInputGoal] = useState(initialGoal.amount.toString());
  const [unit, setUnit] = useState(initialGoal.unit);

  useEffect(() => {
    if (isVisible) {
      setInputGoal(initialGoal.amount.toString());
      setUnit(initialGoal.unit);
    }
  }, [isVisible, initialGoal.amount, initialGoal.unit]);

  const handleSave = () => {
    const newGoalAmount = parseInt(inputGoal, 10);
    if (!isNaN(newGoalAmount)) {
      setWaterGoal({ amount: newGoalAmount, unit: unit });
      closeModal();
    }
  };

  const toggleUnit = () => {
    setUnit((prev) => (prev === "ml" ? "fl oz" : "ml"));
  };

  const styles = StyleSheet.create({
    backdrop: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "rgba(0,0,0,0.8)",
    },
    modalView: {
      minWidth: "80%",
      maxWidth: "90%",
      backgroundColor: theme.colors.screenBackground,
      borderRadius: 12,
      padding: 20,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
      gap: 20,
    },
    headerRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    inputGroup: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    modalText: {
      fontSize: 14,
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
      flex: 1,
    },
    textInput: {
      flex: 1 / 2,
      height: 40,
      borderWidth: 1,
      color: theme.colors.primary,
      padding: 10,
      textAlign: "center",
      backgroundColor: theme.colors.screenBackground,
      borderRadius: 8,
    },
    section: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      gap: 12,
    },
    infoText: {
      fontSize: 14,
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      paddingBottom: 10,
    },
    unitToggle: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      padding: 10,
    },
    image: {
      width: SCREEN_WIDTH * 0.7,
      height: 150,
      borderRadius: 12,
      marginVertical: 10,
    },
  });

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={closeModal}
    >
      <View style={styles.backdrop}>
        <View style={styles.modalView}>
          <View style={styles.headerRow}>
            <TouchableOpacity onPress={closeModal}>
              <Feather
                name="chevron-left"
                color={theme.colors.primaryTextColor}
                size={24}
              />
            </TouchableOpacity>

            <TouchableOpacity onPress={handleSave}>
              <Feather
                name="check-circle"
                color={theme.colors.primary}
                size={24}
              />
            </TouchableOpacity>
          </View>

          <View style={{ alignItems: "center" }}>
            <Text style={styles.infoText}>
              Set your daily water intake goal to stay hydrated and support
              optimal bodily functions.
            </Text>
            <Image
              style={styles.image}
              source={{
                uri: "https://images.pexels.com/photos/416528/pexels-photo-416528.jpeg",
              }}
            />
          </View>

          <View style={styles.section}>
            <View style={styles.inputGroup}>
              <Text style={styles.modalText}>Water Goal:</Text>
              <TextInput
                style={styles.textInput}
                keyboardType="numeric"
                value={inputGoal}
                onChangeText={setInputGoal}
              />
            </View>

            <View style={styles.unitToggle}>
              <Text style={[styles.modalText, { paddingRight: 10 }]}>
                {unit === "ml" ? "Milliliters" : "Fluid Ounces"}
              </Text>
              <Switch
                trackColor={{ false: "#767577", true: "#81b0ff" }}
                thumbColor={unit === "ml" ? "#f5dd4b" : "#f4f3f4"}
                ios_backgroundColor="#3e3e3e"
                onValueChange={toggleUnit}
                value={unit === "ml"}
              />
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default WaterGoalModal;
