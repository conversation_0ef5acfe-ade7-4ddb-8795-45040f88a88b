import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Keyboard,
} from "react-native";
import DismissKeyboard from "../../../components/DismissKeyboard.js";
import { Card } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext.js";
import ProgressPicture from "./ProgressPicture.js";

const CheckpointCard = React.memo(
  ({ checkpoint, index, formatDate, submitWeight }) => {
    const { theme } = useThemeContext();

    const [userLoggedWeight, setUserLoggedWeight] = useState(
      checkpoint.userLoggedWeight?.toString() || ""
    );
    const [userInputWeight, setUserInputWeight] = useState(
      checkpoint.userLoggedWeight?.toString() || ""
    );

    const [grade, setGrade] = useState(checkpoint?.grade || "");
    const [feedback, setFeedback] = useState(checkpoint?.feedback || "");

    const handleWeightChange = (text) => {
      setUserInputWeight(text); // Update userInputWeight as user types
    };

    const handleWeightSubmit = () => {
      Keyboard.dismiss();

      // Convert userInputWeight to a number fixed to 1 decimal place
      const formattedWeight = parseFloat(userInputWeight).toFixed(1);

      if (formattedWeight === userLoggedWeight) {
        // If weights match, don't submit and return early
        return;
      }

      // Proceed to submit the weight if it differs
      submitWeight(checkpoint.week, formattedWeight)
        .then((updatedCheckpoint) => {
          // After successful submission, update the weight states
          setUserLoggedWeight(formattedWeight);
          setUserInputWeight(formattedWeight);

          // Update the grade and feedback states with the new data from the updated checkpoint
          setGrade(updatedCheckpoint?.grade || "");
          setFeedback(updatedCheckpoint?.feedback || "");
        })
        .catch((error) => {
          console.error("Error submitting weight: ", error);
          // Optionally handle error or display a message to the user
        });
    };

    const currentDate = new Date().toISOString().split("T")[0]; // Current date in YYYY-MM-DD format
    const checkpointDate = new Date(checkpoint.date)
      .toISOString()
      .split("T")[0];

    const isPast = currentDate >= checkpointDate;
    const isFuture = currentDate < checkpointDate;

    const styles = StyleSheet.create({
      checkpointCard: {
        width: 300,
        marginVertical: 8,
        backgroundColor: theme.colors.surface,
        borderRadius: 10,
        // Conditionally apply the bottom border shadow when the condition is met
        ...(isPast && !isFuture && userLoggedWeight
          ? {
              shadowColor: theme.colors.primary, // Primary shadow color
              shadowOffset: { width: 0, height: 2 }, // Set offset for the shadow
              shadowOpacity: 0.4, // Opacity of the shadow
              shadowRadius: 4, // Radius of the shadow
            }
          : {}),
      },
      cardContent: {
        flexDirection: "row",
        justifyContent: "space-between",
      },
      row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 8,
      },
      label: {
        fontSize: 11,
        fontWeight: "bold",
        color: theme.colors.primaryTextColor,
      },
      value: {
        fontSize: 11,
        color: theme.colors.primaryTextColor,
      },
      input: {
        borderWidth: 1,
        borderColor: "gray",
        padding: 8,
        borderRadius: 5,
        flex: 1,
        color: theme.colors.primaryTextColor,
      },
    });

    // Applies to checkpoint cards in the future and not the currently active checkpoint card based on current date.
    const overlayStyles = StyleSheet.create({
      overlay: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: -10,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        justifyContent: "center",
        alignItems: "center",
        width: 340,
        borderRadius: 10,
      },
      icon: {
        fontSize: 25,
        color: "white",
      },
    });

    return (
      <DismissKeyboard>
        <Card style={styles.checkpointCard}>
          <Card.Content>
            <View style={styles.row}>
              <Text style={styles.label}>
                Week: {checkpoint?.week || "N/A"}
              </Text>

              <TouchableOpacity
                onPress={handleWeightSubmit}
                disabled={
                  parseFloat(userInputWeight).toFixed(1) === userLoggedWeight ||
                  userInputWeight.trim() === "" || // Check if the input is empty or only white space
                  isNaN(parseFloat(userInputWeight)) // Check if the input is not a number
                }
              >
                <MaterialCommunityIcons
                  name="send-circle"
                  size={22}
                  color={
                    parseFloat(userInputWeight).toFixed(1) ===
                      userLoggedWeight || userInputWeight === ""
                      ? "gray"
                      : theme.colors.primary
                  } // Gray if weights match, primary color otherwise
                />
              </TouchableOpacity>
            </View>
            <View
              style={[
                styles.row,
                {
                  gap: 4,
                  borderBottomWidth: 1,
                  borderBottomColor: "rgba(255, 255, 255, 0.4)",
                  paddingBottom: 8,
                },
              ]}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: 4,
                }}
              >
                <Text style={styles.label}>Date To Log:</Text>
                <Text style={[styles.value, { fontSize: 11 }]}>
                  {formatDate(checkpoint?.date || new Date())}
                </Text>
              </View>
            </View>
            <View>
              <View
                style={{ flex: 1, justifyContent: "space-between", gap: 4 }}
              >
                <View style={{ gap: 4 }}>
                  <View style={{ flexDirection: "row", gap: 4 }}>
                    <Text style={styles.label}>Expected Weight:</Text>
                    <Text style={styles.value}>
                      <Text style={{ color: theme.colors.primary }}>
                        {checkpoint?.expectedWeight || ""}
                      </Text>{" "}
                      lbs
                    </Text>
                  </View>
                  <TextInput
                    keyboardType="numeric"
                    editable={!isFuture}
                    style={styles.input}
                    placeholder="Log Weight"
                    placeholderTextColor={theme.colors.subTextColor}
                    value={userInputWeight}
                    onChangeText={handleWeightChange}
                    onSubmitEditing={() => {
                      // Check conditions before submitting
                      if (
                        userInputWeight.trim() !== "" &&
                        !isNaN(parseFloat(userInputWeight)) &&
                        parseFloat(userInputWeight).toFixed(1) !==
                          userLoggedWeight
                      ) {
                        handleWeightSubmit();
                      } else {
                        // Optionally, handle feedback to the user about why the submission didn't occur
                      }
                    }}
                    returnKeyType="done"
                  />
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingTop: 8,
                  justifyContent: "space-between",
                  gap: "5%",
                }}
              >
                <View style={{ flex: 1, gap: 4 }}>
                  <Text style={styles.label}>Feedback</Text>
                  <Text style={styles.value}>
                    {feedback || "No feedback available"}
                  </Text>
                </View>
                <View style={{ flex: 1, gap: 4, alignItems: "center" }}>
                  <Text style={styles.label}>Progress Picture</Text>
                  <ProgressPicture size={180} checkpointId={checkpoint?.week} />
                </View>
              </View>
            </View>
            <View
              style={[
                styles.row,
                {
                  paddingTop: 14,
                  alignItems: "baseline",
                  marginBottom: 0,
                },
              ]}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text style={styles.label}>Grade: </Text>
                <Text
                  style={[
                    styles.label,
                    { fontSize: 16, color: theme.colors.primary },
                  ]}
                >
                  {grade || ""}
                </Text>
              </View>

              {isPast && !isFuture && userLoggedWeight && (
                <MaterialCommunityIcons
                  name="check-circle"
                  size={24}
                  color="green"
                />
              )}
            </View>

            {/* Semi-transparent overlay with a message and icon */}
            {isFuture && (
              <View style={overlayStyles.overlay}>
                <MaterialCommunityIcons
                  name="lock"
                  style={overlayStyles.icon}
                />
              </View>
            )}
          </Card.Content>
        </Card>
      </DismissKeyboard>
    );
  }
);

export default CheckpointCard;
