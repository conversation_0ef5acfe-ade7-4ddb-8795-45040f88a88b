import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Button,
} from "react-native";
import { CameraView, useCameraPermissions } from "expo-camera"; // Corrected imports
import { searchFoodByBarcode } from "../api/EdamamFoodDB/edamamMethods.js";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import barcodeScannerStyles from "./styles/barcodeScannerStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";

const BarcodeScanner = ({ onBarcodeScanned, onClose }) => {
  const { theme } = useThemeContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [manualBarcode, setManualBarcode] = useState("");
  const [isTextInputFocused, setIsTextInputFocused] = useState(false); // Track if TextInput is focused
  const [facing, setFacing] = useState("back"); // Camera type to control camera direction
  const [permission, requestPermission] = useCameraPermissions(); // Handle camera permissions

  const styles = barcodeScannerStyles();

  // Handle camera permissions loading or not granted cases
  if (!permission) {
    return <View />;
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={{ textAlign: "center" }}>
          We need your permission to show the camera
        </Text>
        <Button onPress={requestPermission} title="Grant Permission" />
      </View>
    );
  }

  // Barcode scanning
  const handleBarcodeScanned = async ({ data }) => {
    if (!data || data === undefined || data.trim() === "") {
      setError("Barcode not recognized. Please try again.");
      setTimeout(() => setError(null), 3000); // Clear error after 3 seconds
      // If data is undefined or an empty string, return early
      return;
    }

    try {
      setLoading(true);
      const foodBarcodeResults = await searchFoodByBarcode(data);

      console.log("Scan Results: ", JSON.stringify(foodBarcodeResults));
      if (!foodBarcodeResults) {
        setError("Barcode not recognized. Please try again.");
        setTimeout(() => setError(null), 3000); // Clear error after 3 seconds
      } else {
        onBarcodeScanned(foodBarcodeResults);
      }
    } catch (error) {
      console.log("Barcode scanner error: ", error);
      setError("An error occurred while scanning. Please try again.");
      setTimeout(() => setError(null), 3000); // Clear error after 3 seconds
    } finally {
      setLoading(false);
    }
  };

  // Manual barcode search
  const handleManualSearch = async () => {
    if (
      !manualBarcode ||
      manualBarcode === undefined ||
      manualBarcode.trim() === ""
    ) {
      setError("Barcode not recognized. Please try again.");
      setTimeout(() => setError(null), 3000); // Clear error after 3 seconds
      // If manualBarcode is undefined or an empty string, return early
      return;
    }

    try {
      setLoading(true);
      const foodBarcodeResults = await searchFoodByBarcode(manualBarcode);

      if (!foodBarcodeResults) {
        setError("Barcode not recognized. Please try again.");
        setTimeout(() => setError(null), 3000); // Clear error after 3 seconds
      } else {
        onBarcodeScanned(foodBarcodeResults);
      }
    } catch (error) {
      console.log("Manual barcode search error: ", error);
      setError("An error occurred while searching. Please try again.");
      setTimeout(() => setError(null), 3000); // Clear error after 3 seconds
    } finally {
      setLoading(false);
    }
  };

  // Toggle camera facing direction
  const toggleCameraFacing = () => {
    setFacing((current) => (current === "back" ? "front" : "back"));
  };

  const handleTextInputFocus = () => {
    setIsTextInputFocused(true);
  };

  const handleTextInputBlur = () => {
    setIsTextInputFocused(false);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
    >
      <CameraView
        style={styles.camera}
        facing={facing}
        onBarcodeScanned={loading ? undefined : handleBarcodeScanned}
        barcodeScannerSettings={{
          barcodeTypes: [
            "qr",
            "pdf417",
            "ean13",
            "code128",
            "code39",
            "upc_a",
            "upc_e",
            "ean8",
            "itf14",
            "interleaved2of5",
            "codabar",
          ], // Added support for multiple barcode types
        }}
      >
        {/* Translucent gray boxes (masks) */}
        <View style={styles.maskTop} />
        <View style={styles.maskLeft} />
        <View style={styles.maskRight} />
        <View style={styles.maskBottom} />

        {/* Clear overlay with conditional tinting */}
        <View
          style={[
            styles.targetAreaClearOverlay,
            isTextInputFocused && styles.targetAreaTintedOverlay, // Tint when TextInput is focused
          ]}
        />

        {!isTextInputFocused && (
          <>
            <View style={styles.scanbarCodeInstructionsContainer}>
              <Text style={styles.scanText}>Scan Barcode</Text>
              <Text style={styles.instructionText}>
                Keep the barcode in the frame to scan it
              </Text>
            </View>

            <View style={styles.targetArea}>
              <View style={styles.targetBorder} />
            </View>
          </>
        )}

        {/* Close button */}
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => {
            setError(null);
            onClose();
          }}
        >
          <MaterialIcons name="keyboard-arrow-left" size={40} color="white" />
        </TouchableOpacity>

        {/* Flip camera button */}
        <TouchableOpacity
          style={styles.flipButton}
          onPress={toggleCameraFacing}
        >
          <MaterialIcons name="rotate-right" size={40} color="white" />
        </TouchableOpacity>

        {/* Manual barcode input */}
        <View style={styles.manualInputContainer}>
          <Ionicons
            name="barcode-outline"
            size={34}
            color={theme.colors.primaryTextColor}
            style={styles.barcodeIcon}
          />
          <TextInput
            style={styles.manualInput}
            placeholder="Manually Enter Barcode"
            placeholderTextColor={theme.colors.primaryTextColor}
            value={manualBarcode}
            onChangeText={setManualBarcode}
            onFocus={handleTextInputFocus}
            onBlur={handleTextInputBlur}
          />
          <TouchableOpacity
            style={styles.manualSearchButton}
            onPress={handleManualSearch}
          >
            <MaterialIcons name="check" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </CameraView>

      {/* Loading indicator */}
      {loading && (
        <ActivityIndicator
          size="large"
          color="black"
          style={styles.loadingIndicator}
        />
      )}

      {/* Error message */}
      {error && <Text style={styles.errorText}>{error}</Text>}
    </KeyboardAvoidingView>
  );
};

export default BarcodeScanner;
