import React, { useEffect } from "react";
import { View, Text } from "react-native";
import { Card, ProgressBar } from "react-native-paper"; // Import ProgressBar
import { Entypo } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext.js";
import ThreeLevelProgressRing from "../../../components/ThreeLevelProgressRing.js";

const CaloriesAndWaterProgressSection = React.memo(
  ({ calorieData, caloriesBurned, waterProgressBarPercentage }) => {
    const { theme } = useThemeContext();

    return (
      <View style={{ flex: 1 }}>
        <Card
          style={{
            flex: 1,
            borderRadius: theme.dimensions.sectionBorderRadius,
            backgroundColor: theme.colors.cardDarkGrayBackground,
            elevation: 4,
          }}
        >
          <Card.Content
            style={{
              height: "100%",
            }}
          >
            {/* <View style={{ paddingBottom: 12 }}>
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: 12,
                }}
              >
                Calories Remaining & Water Intake
              </Text>
            </View> */}
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              {/** Left Side: Progress Ring */}
              <View
                style={{
                  alignItems: "center",
                  justifyContent: "center",
                  flex: 1,
                }}
              >
                <ThreeLevelProgressRing
                  size={90}
                  percentages={[
                    calorieData?.foodConsumedPercentage || 0, // Right half circle
                    calorieData?.netCalorieProgressPercentage || 0, // Center full circle
                    calorieData?.caloriesBurnedPercentage || 0, // Left half circle
                  ]}
                  values={[
                    calorieData?.caloriesRemaining || 0, // Remaining calories
                    calorieData?.calorieGoal || 0, // Calorie goal
                  ]}
                  fillColorsRight={{
                    consumedColor: "#A078A0",
                    remainingColor: "#D8BFD8",
                  }}
                  fillColorsCenter={{
                    consumedColor: [
                      theme.colors.secondary,
                      theme.colors.primary,
                    ],
                    remainingColor: theme.colors.chartRemainingColor,
                  }}
                  fillColorsLeft={{
                    consumedColor: "#EB9A1A",
                    remainingColor: "#FFD580",
                  }}
                />
              </View>

              {/** Right Side: Equation and Labels */}
              <View
                style={{
                  height: "100%",
                  width: "70%",
                  justifyContent: "space-evenly",
                  paddingLeft: "5%", // Add spacing from the progress ring
                }}
              >
                <View style={{ flexDirection: "row", gap: 8 }}>
                  {/** Goal */}
                  <View style={{ alignItems: "flex-start" }}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 10,
                      }}
                    >
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        {calorieData?.calorieGoal || 0}
                      </Text>
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        -
                      </Text>
                    </View>
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 12,
                      }}
                    >
                      Goal
                    </Text>
                  </View>

                  {/** Food */}
                  <View style={{ alignItems: "flex-start" }}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 10,
                      }}
                    >
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        {Math.round(calorieData?.totalCalories || 0)}
                      </Text>
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        +
                      </Text>
                    </View>
                    <Text style={{ color: "#C29AC2", fontSize: 12 }}>Food</Text>
                  </View>

                  {/** Exercise */}
                  <View style={{ alignItems: "flex-start" }}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 18,
                      }}
                    >
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        {Math.round(caloriesBurned)}
                      </Text>
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        =
                      </Text>
                    </View>
                    <Text style={{ color: "#EB9A1A", fontSize: 12 }}>
                      Exercise
                    </Text>
                  </View>

                  {/** Remaining */}
                  <View style={{ alignItems: "flex-start" }}>
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 12,
                      }}
                    >
                      {Math.round(calorieData?.caloriesRemaining || 0)}
                    </Text>

                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 12,
                      }}
                    >
                      Remaining
                    </Text>
                  </View>
                </View>

                {/** Water Progress Bar */}
                <View
                  style={{
                    flexDirection: "row",
                    gap: 10,
                    paddingTop: 16,
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <View style={{ flex: 1 }}>
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 12,
                        paddingBottom: 4,
                      }}
                    >
                      Water Intake
                    </Text>
                    <ProgressBar
                      progress={waterProgressBarPercentage} // Uses the passed water progress percentage
                      color="rgba(33, 150, 243, 1)" // Water drop color
                      style={{
                        height: 8,
                        borderRadius: 5,
                        backgroundColor: "rgba(144, 202, 249, 1)", // Background color for remaining water
                      }}
                    />
                  </View>
                  <Entypo name="drop" size={26} color="rgba(33, 150, 243, 1)" />
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>
      </View>
    );
  }
);

export default CaloriesAndWaterProgressSection;
