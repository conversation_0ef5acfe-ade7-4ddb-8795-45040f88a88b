import React, { useMemo, useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from "react-native";
import DismissKeyboard from "../../../components/DismissKeyboard.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { LineChart } from "react-native-chart-kit";
import SubCollapsibleSection from "../../../components/SubCollapsibleSection.js";
import CheckpointCard from "./CheckpointCard.js";
import { MaterialCommunityIcons } from "@expo/vector-icons";

const ManageActiveNutritionalProgram = () => {
  const { theme } = useThemeContext();
  const {
    activeNutritionalProgram,
    submitWeight,
    deactivateNutritionalProgram,
  } = useNutritionProgram();
  const { getSelectedDateAsDate } = useTime();

  const [programStarted, setProgramStarted] = useState(false);

  useEffect(() => {
    if (activeNutritionalProgram?.startDate) {
      const currentDate = getSelectedDateAsDate(); // Consistent date logic
      const programStartDate = new Date(activeNutritionalProgram.startDate);
      setProgramStarted(currentDate >= programStartDate);
    }
  }, [activeNutritionalProgram]);

  const formatDate = (dateString) => {
    if (dateString === "") return;
    const options = { month: "long", day: "numeric", year: "numeric" };
    const correctedDate = new Date(
      new Date(dateString).toUTCString().split(" GMT")[0]
    );
    return correctedDate.toLocaleDateString("en-US", options);
  };

  const handleProgramDeactivation = async () => {
    try {
      const response = await deactivateNutritionalProgram(
        activeNutritionalProgram?.programId
      );
      if (response) {
        console.log("Program deactivated successfully.");
        // Optionally, you can provide further feedback or navigate the user.
      }
    } catch (error) {
      console.error("Failed to deactivate the program:", error);
    }
  };

  const styles = StyleSheet.create({
    container: {
      paddingTop: 16,
      paddingHorizontal: 16,
      borderRadius: 8,
      width: "100%",
      backgroundColor: theme.colors.screenBackground,
    },
    heading: {
      fontSize: 16,
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
      paddingBottom: 14,
    },
    sectionContent: {
      marginBottom: 12,
    },
    cardContent: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    item: {
      flexDirection: "row",
      width: "45%", // Adjust this to control how many items per row
      marginBottom: 8,
    },
    label: {
      fontSize: 12,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
      marginRight: 4,
    },
    value: {
      fontSize: 12,
      color: theme.colors.primary,
    },
    chartContainer: {
      height: 250,
      justifyContent: "center",
    },
    chartLabel: {
      fontSize: 14,
      fontWeight: "bold",
      textAlign: "center",
      paddingTop: 10,
      paddingBottom: 15,
      color: theme.colors.primaryTextColor,
    },
    deactivateButton: {
      backgroundColor: "red",
      padding: 8,
      borderRadius: 8,
      alignItems: "center",
      marginVertical: 16,
    },
    deactivateButtonText: {
      color: theme.colors.primaryTextColor,
      fontWeight: "bold",
      fontSize: 12,
    },
  });

  return (
    <DismissKeyboard>
      <View style={styles.container}>
        <Text style={styles.heading}>
          {programStarted
            ? "Your Active Nutritional Program"
            : "Upcoming Program"}
        </Text>

        {/* Checkpoints Section */}
        {programStarted && (
          <SubCollapsibleSection title="Weigh-In Checkpoints">
            <ScrollView contentContainerStyle={{ gap: 12 }} horizontal>
              {activeNutritionalProgram?.checkpoints?.map(
                (checkpoint, index) => (
                  <CheckpointCard
                    key={checkpoint?.week || index}
                    checkpoint={checkpoint}
                    index={index}
                    formatDate={formatDate}
                    submitWeight={submitWeight}
                  />
                )
              )}
            </ScrollView>
          </SubCollapsibleSection>
        )}

        {/* Program Has Not Started Nutritional Goals Update Note*/}

        {!programStarted && (
          <View
            style={{
              alignSelf: "center",
              width: "80%",
              justifyContent: "center",
              alignItems: "flex-start",
              paddingTop: 16,
              paddingBottom: 26,
              gap: 12,
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                gap: 12,
              }}
            >
              <MaterialCommunityIcons
                name="alert-rhombus-outline"
                size={22}
                color={"orange"}
              />
              <Text style={styles.label}>Start Date: </Text>
              <Text style={styles.value}>
                {formatDate(activeNutritionalProgram?.startDate)}
              </Text>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                gap: 12,
              }}
            >
              <MaterialCommunityIcons
                name="alert-rhombus-outline"
                size={22}
                color={"orange"}
              />
              <Text
                style={{
                  fontSize: 12,
                  fontWeight: "600",
                  color: theme.colors.subTextColor,
                }}
              >
                You will be notified to update your daily nutritional goals when
                your program starts.
              </Text>
            </View>
          </View>
        )}

        {/* Program Timeline */}
        <SubCollapsibleSection title="Program Timeline">
          <View style={styles.sectionContent}>
            <View style={styles.cardContent}>
              <View style={styles.item}>
                <Text style={styles.label}>Start Date: </Text>
                <Text style={styles.value}>
                  {formatDate(activeNutritionalProgram?.startDate)}
                </Text>
              </View>

              <View style={styles.item}>
                <Text style={styles.label}>End Date: </Text>
                <Text style={styles.value}>
                  {formatDate(activeNutritionalProgram?.endDate)}
                </Text>
              </View>

              <View style={styles.item}>
                <Text style={styles.label}>Program Duration: </Text>
                <Text style={styles.value}>
                  {Math.ceil(
                    (new Date(activeNutritionalProgram?.endDate) -
                      new Date(activeNutritionalProgram?.startDate)) /
                      (1000 * 60 * 60 * 24)
                  )}{" "}
                  days
                </Text>
              </View>

              <View style={styles.item}>
                <Text style={styles.label}>Starting Weight: </Text>
                <Text style={styles.value}>
                  {activeNutritionalProgram?.startWeight} lbs
                </Text>
              </View>

              <View style={styles.item}>
                <Text style={styles.label}>Goal Weight: </Text>
                <Text style={styles.value}>
                  {activeNutritionalProgram?.goalWeight} lbs
                </Text>
              </View>
            </View>

            {activeNutritionalProgram?.checkpoints && (
              <View style={styles.chartContainer}>
                <Text style={styles.chartLabel}>Projected Weight vs. Time</Text>

                <ScrollView
                  horizontal
                  contentContainerStyle={{
                    flexGrow: 1,
                    paddingLeft: 12,
                  }}
                >
                  {/* Wrap the entire LineChart area with TouchableWithoutFeedback to ensure touch events propagate */}
                  <TouchableWithoutFeedback>
                    <View>
                      {/* <LineChart
                        data={{
                          labels: activeNutritionalProgram?.checkpoints?.map(
                            (checkpoint) => {
                              // Apply timezone correction, similar to the checkpoint modal
                              const correctedDate = new Date(
                                new Date(checkpoint?.date)
                                  .toUTCString()
                                  .split(" GMT")[0]
                              );

                              // Format to include day and month
                              return new Intl.DateTimeFormat("en-US", {
                                month: "short",
                                day: "numeric",
                              }).format(correctedDate);
                            }
                          ),
                          datasets: [
                            {
                              data: activeNutritionalProgram?.checkpoints.map(
                                (checkpoint) => checkpoint.expectedWeight
                              ),
                              color: () => `rgba(255, 255, 255, 0.5)`,
                              strokeWidth: 2,
                            },
                          ],
                        }}
                        width={
                          activeNutritionalProgram?.checkpoints.length * 80
                        } // Adjust width dynamically based on number of checkpoints
                        height={250}
                        yAxisSuffix=" lbs"
                        chartConfig={{
                          backgroundGradientFrom: theme.colors.surface,
                          backgroundGradientTo: theme.colors.surface,
                          backgroundGradientFromOpacity: 0.0,
                          backgroundGradientToOpacity: 0.0,
                          decimalPlaces: 1,
                          color: (opacity = 1) =>
                            `rgba(255, 255, 255, ${opacity})`,
                          labelColor: (opacity = 1) =>
                            `rgba(255, 255, 255, ${opacity})`,
                          propsForDots: {
                            r: "4",
                            strokeWidth: "2",
                            stroke: theme.colors.primary,
                            fill: theme.colors.primary,
                          },
                          propsForBackgroundLines: {
                            stroke: "rgba(255, 255, 255, 0.2)",
                            strokeDasharray: "",
                          },
                          propsForVerticalLabels: {
                            fontSize: 11,
                            fontWeight: "bold",
                            fill: theme.colors.primaryTextColor,
                          },
                          propsForHorizontalLabels: {
                            fontSize: 11,
                            fontWeight: "bold",
                            fill: theme.colors.primaryTextColor,
                          },
                        }}
                        withVerticalLines={false}
                        withHorizontalLines={true}
                        verticalLabelRotation={0}
                      /> */}
                      <LineChart
                        data={{
                          labels: activeNutritionalProgram?.checkpoints?.map(
                            (checkpoint) => {
                              // Apply timezone correction, similar to the checkpoint modal
                              const correctedDate = new Date(
                                new Date(checkpoint?.date)
                                  .toUTCString()
                                  .split(" GMT")[0]
                              );

                              // Format to include day and month
                              return new Intl.DateTimeFormat("en-US", {
                                month: "short",
                                day: "numeric",
                              }).format(correctedDate);
                            }
                          ),
                          datasets: [
                            {
                              data: activeNutritionalProgram?.checkpoints?.map(
                                (checkpoint) => checkpoint?.expectedWeight
                              ),
                              color: () => theme.colors.primary,
                              strokeWidth: 2,
                            },
                          ],
                        }}
                        width={
                          activeNutritionalProgram?.checkpoints?.length * 80
                        }
                        height={200}
                        yAxisSuffix=" lbs"
                        chartConfig={{
                          backgroundGradientFrom: theme.colors.surface,
                          backgroundGradientTo: theme.colors.surface,
                          decimalPlaces: 1,
                          color: () => theme.colors.primaryTextColor,
                          labelColor: () => theme.colors.primaryTextColor,
                          propsForLabels: { fontSize: 11 },
                          propsForDots: {
                            r: "4",
                            strokeWidth: "2",
                            stroke: theme.colors.primary,
                            fill: theme.colors.primary,
                          },
                          paddingLeft: "30",
                          paddingRight: "10",
                        }}
                        withVerticalLines={false}
                        withHorizontalLines={true}
                      />
                    </View>
                  </TouchableWithoutFeedback>
                </ScrollView>
              </View>
            )}
          </View>
        </SubCollapsibleSection>

        {/* Nutritional Goals */}
        <SubCollapsibleSection title="Nutritional Goals">
          <View style={styles.sectionContent}>
            <View style={styles.cardContent}>
              <View style={styles.item}>
                <Text style={styles.label}>Calories: </Text>
                <Text style={styles.value}>
                  {activeNutritionalProgram?.nutritionalGoals?.calorieGoal?.toLocaleString()}{" "}
                  kcal
                </Text>
              </View>

              <View style={styles.item}>
                <Text style={styles.label}>Carbs: </Text>
                <Text style={styles.value}>
                  {
                    activeNutritionalProgram?.nutritionalGoals?.macroGoals?.carb
                      ?.dailyGrams
                  }{" "}
                  grams (
                  {activeNutritionalProgram?.nutritionalGoals?.macroGoals?.carb
                    ?.dailyPercentage * 100}
                  %)
                </Text>
              </View>

              <View style={styles.item}>
                <Text style={styles.label}>Protein: </Text>
                <Text style={styles.value}>
                  {
                    activeNutritionalProgram?.nutritionalGoals?.macroGoals
                      ?.protein?.dailyGrams
                  }{" "}
                  grams (
                  {activeNutritionalProgram?.nutritionalGoals?.macroGoals
                    ?.protein?.dailyPercentage * 100}
                  %)
                </Text>
              </View>

              <View style={styles.item}>
                <Text style={styles.label}>Fats: </Text>
                <Text style={styles.value}>
                  {
                    activeNutritionalProgram?.nutritionalGoals?.macroGoals?.fat
                      ?.dailyGrams
                  }{" "}
                  grams (
                  {activeNutritionalProgram?.nutritionalGoals?.macroGoals?.fat
                    ?.dailyPercentage * 100}
                  %)
                </Text>
              </View>
            </View>
          </View>
        </SubCollapsibleSection>

        <View
          style={{
            marginTop: 8,
            alignItems: "flex-end",
            backgroundColor: "rgba(255, 255, 255, 0.05)",
            paddingHorizontal: 12,
            borderRadius: 24,
          }}
        >
          {/* Deactivate Program Button */}
          <TouchableOpacity
            style={styles.deactivateButton}
            onPress={handleProgramDeactivation}
          >
            <Text style={styles.deactivateButtonText}>
              {programStarted ? "End Program" : "Abandon Program"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </DismissKeyboard>
  );
};

export default ManageActiveNutritionalProgram;
