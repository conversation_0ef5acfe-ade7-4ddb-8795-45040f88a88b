import React, { useEffect, useState } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  Dimensions,
} from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";

const QuickAddModal = ({
  isVisible,
  onClose,
  onAdd,
  mealTypeId,
  activeItem,
}) => {
  const { theme } = useThemeContext();

  const [foodName, setFoodName] = useState("");
  const [calories, setCalories] = useState("");
  const [carbs, setCarbs] = useState("");
  const [protein, setProtein] = useState("");
  const [fat, setFat] = useState("");

  useEffect(() => {
    if (isVisible) {
      setFoodName(String(activeItem?.foodLabel || ""));
      setCalories(String(activeItem?.nutrients?.ENERC_KCAL?.quantity || ""));
      setCarbs(String(activeItem?.nutrients?.CHOCDF?.quantity || ""));
      setProtein(String(activeItem?.nutrients?.PROCNT?.quantity || ""));
      setFat(String(activeItem?.nutrients?.FAT?.quantity || ""));
    }
  }, [isVisible]);

  const resetAndClose = () => {
    setFoodName("");
    setCalories("");
    setCarbs("");
    setProtein("");
    setFat("");

    setTimeout(onClose, 1); // Delay closing the modal
  };

  const handleAdd = async () => {
    if (!foodName || !calories) return;

    if (activeItem?.id && activeItem?.mealType) {
      await onAdd(activeItem.mealType, {
        id: activeItem.id,
        foodLabel: foodName,
        nutrients: {
          ENERC_KCAL: { quantity: parseFloat(calories) },
          CHOCDF: { quantity: parseFloat(carbs || 0) },
          PROCNT: { quantity: parseFloat(protein || 0) },
          FAT: { quantity: parseFloat(fat || 0) },
        },
      });
    } else {
      await onAdd(mealTypeId, {
        foodLabel: foodName,
        nutrients: {
          ENERC_KCAL: { quantity: parseFloat(calories) },
          CHOCDF: { quantity: parseFloat(carbs || 0) },
          PROCNT: { quantity: parseFloat(protein || 0) },
          FAT: { quantity: parseFloat(fat || 0) },
        },
      });
    }

    resetAndClose();
  };

  const styles = StyleSheet.create({
    centeredView: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.8)", // Tinted backdrop
    },
    modalView: {
      margin: 20,
      backgroundColor: theme.colors.surface,
      borderRadius: 20,
      padding: 35,
      alignItems: "center",
      elevation: 5,
    },
    inputContainer: {
      flexDirection: "row",
      position: "relative",
      marginBottom: 12,
    },
    input: {
      width: "100%",
      height: 40,
      borderWidth: 1,
      borderRadius: 12,
      padding: 10,
      borderColor: theme.colors.primary,
      paddingRight: 50, // Adjust padding to not overlap the label
      color: theme.colors.primaryTextColor,
    },
    inputLabel: {
      position: "absolute",
      right: 10,
      top: 10,
      backgroundColor: "transparent",
    },
  });

  function createInputWithLabel(
    placeholder,
    value,
    setValue,
    label,
    keyboardType = "default"
  ) {
    return (
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.primaryTextColor}
          value={value}
          onChangeText={setValue}
          keyboardType={keyboardType}
        />
        <Text
          style={[
            styles.inputLabel,
            {
              color: value
                ? theme.colors.primaryTextColor
                : theme.colors.subTextColor,
            },
          ]}
        >
          {label}
        </Text>
      </View>
    );
  }

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View
          style={[
            styles.modalView,
            { backgroundColor: theme.colors.screenBackground },
          ]}
        >
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              fontSize: 20,
              fontWeight: "600",
              paddingBottom: 10,
            }}
          >
            Quick Add
          </Text>

          {createInputWithLabel("Food Name", foodName, setFoodName, "Name")}
          {createInputWithLabel(
            "Calories",
            calories,
            setCalories,
            "Calories",
            "numeric"
          )}
          {createInputWithLabel(
            "Carbs (g)",
            carbs,
            setCarbs,
            "Carbs (g)",
            "numeric"
          )}
          {createInputWithLabel(
            "Protein (g)",
            protein,
            setProtein,
            "Protein (g)",
            "numeric"
          )}
          {createInputWithLabel("Fat (g)", fat, setFat, "Fat (g)", "numeric")}

          <Button
            color={theme.colors.primary}
            title="Add Food"
            onPress={handleAdd}
          />
          <Button color={theme.colors.error} title="Cancel" onPress={onClose} />
        </View>
      </View>
    </Modal>
  );
};

export default QuickAddModal;
