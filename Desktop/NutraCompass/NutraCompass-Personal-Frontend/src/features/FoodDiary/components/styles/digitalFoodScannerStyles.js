import { StyleSheet } from "react-native";
import {
  ThemeContext,
  useThemeContext,
} from "../../../../context/ThemeContext.js";

const digitalFoodScannerStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      width: "100%",
      justifyContent: "center",
      backgroundColor: theme.colors.screenBackground,
    },
    camera: {
      flex: 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 10,
      paddingTop: 50,
      paddingBottom: 10,
      backgroundColor: theme.colors.surface,
    },
    closeButton: {
      padding: 5,
    },
    title: {
      fontSize: 20,
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      paddingLeft: "27%",
    },
    footer: {
      flexDirection: "row",
      justifyContent: "space-around",
      alignItems: "center",
      padding: 20,
      paddingBottom: 40,
      backgroundColor: theme.colors.surfaces,
    },
    footerButton: {
      justifyContent: "center",
      alignItems: "center",
    },
    loadingIndicator: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: "center",
      alignItems: "center",
    },
    permissionText: {
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      fontSize: 16,
      marginVertical: 10,
    },
    permissionButton: {
      alignSelf: "center",
      padding: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.primary,
    },
    permissionButtonText: {
      color: theme.colors.screenBackground,
      fontSize: 16,
    },
    cornerHooksContainer: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    cornerHook: {
      position: "absolute",
      width: 40,
      height: 40,
      borderColor: "white",
    },
    topLeftHook: {
      top: 0,
      left: 0,
      borderLeftWidth: 4,
      borderTopWidth: 4,
    },
    topRightHook: {
      top: 0,
      right: 0,
      borderRightWidth: 4,
      borderTopWidth: 4,
    },
    bottomLeftHook: {
      bottom: 0,
      left: 0,
      borderLeftWidth: 4,
      borderBottomWidth: 4,
    },
    bottomRightHook: {
      bottom: 0,
      right: 0,
      borderRightWidth: 4,
      borderBottomWidth: 4,
    },
  });
};

export default digitalFoodScannerStyles;
