import { StyleSheet } from "react-native";
import { useTheme } from "react-native-paper";
import { useThemeContext } from "../../../../context/ThemeContext.js";

const swipeableFoodEntryItemStyles = () => {
  const paperTheme = useTheme();
  const { theme } = useThemeContext();

  return StyleSheet.create({
    containerStyle: {
      flex: 1,
      flexDirection: "row",
      elevation: 3,
      backgroundColor: theme.colors.surface,
      paddingHorizontal: 2,
      paddingBottom: 2,
    },
    textContainer: {
      //width: SCREEN_WIDTH / 1.2,
      flex: 1,
      // borderRadius: theme.dimensions.cardBorderRadius,
      //backgroundColor: theme.colors.screenBackground,
      backgroundColor: theme.colors.screenBackground, // Matte Gray background color
      elevation: 3,
      zIndex: 2,
    },
    textStyle: {
      fontSize: 14,
    },
    entryInfo: {
      flex: 1, // Take up all available space
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: 8,
    },
    entryFoodNameText: {
      color: theme.colors.primaryTextColor,
      fontSize: 14,
    },
    entryCaloriesText: {
      color: theme.colors.primaryTextColor,
      fontSize: 14,
    },
    rightButtonContainer: {
      position: "absolute",
      left: null,
      right: 0,
      alignItems: "center",
      justifyContent: "center",
      marginRight: 5,
      borderRadius: theme.dimensions.cardBorderRadius,
      paddingHorizontal: 18,
      paddingVertical: 10,
      elevation: 3,
      backgroundColor: "#D50000",
      zIndex: 1,
    },
  });
};

export default swipeableFoodEntryItemStyles;
