import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";

const dailyNutritionGoalsCalculationModalStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    // ... your existing styles

    contentContainer: {
      flex: 1,
      width: "100%",
    },
    formContainer: {
      flex: 1,
      justifyContent: "flex-start",
      gap: 20,
      padding: 20,
    },
    header: {
      minWidth: "100%",
      padding: 10,
      justifyContent: "flex-end",
    },
    title: {
      fontSize: 22,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
      marginBottom: 10,
      alignSelf: "center",
    },
    infoBox: {
      fontSize: 14,
      textAlign: "left",
      color: "gray",
    },
    label: {
      fontSize: 20,
      color: theme.colors.primaryTextColor,
      alignSelf: "center",
      paddingTop: 20,
    },
    answer: {
      fontSize: 16,
      color: theme.colors.primaryTextColor,
      marginBottom: 20,
    },
    segmentedButtonContainer: {
      alignSelf: "center",
    },
    input: {
      flex: 1,
      height: 40,
    },
    resultsLabel: {
      color: theme.colors.primaryTextColor,
    },
    results: {
      color: theme.colors.primaryTextColor,
    },
    navigationButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-end",
      padding: 12,
      height: "15%",
      width: "100%",
    },
    backButton: {
      marginRight: "auto", // Aligns to the left
    },
    nextButton: {
      marginLeft: "auto", // Aligns to the right
    },
    card: {
      backgroundColor: theme.colors.surface,
      marginVertical: 10,
      padding: 10,
      borderRadius: 8,
      elevation: 2,
    },
    selectedCard: {
      backgroundColor: theme.colors.primary,
      marginVertical: 10,
      padding: 10,
      borderRadius: 8,
      elevation: 4,
    },
    cardText: {
      fontSize: 16,
      color: theme.colors.primaryTextColor,
      textAlign: "center",
    },
  });
};

export default dailyNutritionGoalsCalculationModalStyles;
