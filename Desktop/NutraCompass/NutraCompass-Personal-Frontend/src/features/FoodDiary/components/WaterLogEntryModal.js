import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback,
} from "react-native";
import { AntDesign, MaterialIcons } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodLog } from "../context/FoodLogContext.js";

const WaterLogEntryModal = ({ isVisible, closeModal, activeItem }) => {
  const { theme } = useThemeContext();
  const { addWaterEntry } = useFoodLog();
  const [amount, setAmount] = useState("");
  const [unit, setUnit] = useState("ml");

  useEffect(() => {
    if (isVisible && activeItem && activeItem.mealType === "Water") {
      setAmount(activeItem.volume.toString());
      setUnit(activeItem.unit);
    } else {
      resetForm();
    }
    return () => {
      if (!isVisible) resetForm(); // Reset when modal is closed
    };
  }, [isVisible, activeItem]);

  const handleLogEntry = () => {
    const waterAmount = parseInt(amount, 10);
    if (!isNaN(waterAmount) && waterAmount > 0) {
      addWaterEntry(waterAmount, unit, activeItem?.id); // Pass ID if updating
      closeModal();
    } else {
      alert(`Please enter a valid amount in ${unit}`);
    }
  };

  const handleSetAmount = (value) => {
    setAmount(value.toString());
  };

  const toggleUnit = () => {
    setUnit((prev) => (prev === "ml" ? "fl oz" : "ml"));
  };

  const quickOptions = [
    { label: "1 cup", value: unit === "ml" ? 240 : 8 },
    { label: "4 cups", value: unit === "ml" ? 960 : 32 },
    { label: "8 cups", value: unit === "ml" ? 1920 : 64 },
  ];

  const resetForm = () => {
    setAmount("");
    setUnit("ml");
  };

  const styles = StyleSheet.create({
    backdrop: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalView: {
      width: "90%",
      borderRadius: 20,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.surface,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
      paddingVertical: 20,
    },
    header: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: 20,
      paddingHorizontal: 20,
    },
    inputContainer: {
      width: "100%",
      alignItems: "center",
      justifyContent: "center",
      gap: 12,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.primary,
      padding: 10,
      width: "80%",
      marginBottom: 20,
      borderRadius: 10,
      textAlign: "center",
      fontSize: 16,
      color: theme.colors.primaryTextColor,
    },
    iconButton: {
      padding: 10,
    },
    icon: {
      color: theme.colors.primaryTextColor,
    },
    quickOption: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: 10,
      backgroundColor: theme.colors.surface,
      borderRadius: 10,
      margin: 5,
    },
    quickOptionText: {
      marginLeft: 5,
      fontSize: 16,
      color: theme.colors.primaryTextColor,
    },
    optionsContainer: {
      flexDirection: "row",
      justifyContent: "space-evenly",
      width: "100%",
    },
    optionMiddle: {
      marginTop: 10,
      alignItems: "center",
    },
  });

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={() => {
        closeModal();
        resetForm(); // Reset form on modal close via swipe/back button
      }}
    >
      {/* Wrap the entire backdrop in TouchableWithoutFeedback to dismiss keyboard on tap */}
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.backdrop}>
          {/* Wrap the modal content in another TouchableWithoutFeedback with an empty onPress 
              so that touches inside the modal do not dismiss the keyboard immediately */}
          <TouchableWithoutFeedback onPress={() => {}}>
            <View style={styles.modalView}>
              <View style={styles.header}>
                <TouchableOpacity
                  style={styles.iconButton}
                  onPress={() => {
                    closeModal();
                    resetForm();
                  }}
                >
                  <AntDesign name="left" size={24} style={styles.icon} />
                </TouchableOpacity>
                <Text
                  style={{ fontSize: 18, color: theme.colors.primaryTextColor }}
                >
                  Add Water
                </Text>
                <TouchableOpacity
                  style={styles.iconButton}
                  onPress={handleLogEntry}
                >
                  <MaterialIcons name="check" size={24} style={styles.icon} />
                </TouchableOpacity>
              </View>

              <View style={styles.inputContainer}>
                <TouchableOpacity
                  style={{ alignItems: "center" }}
                  onPress={toggleUnit}
                >
                  <MaterialIcons name="local-drink" size={60} color="#00BFFF" />
                  <Text
                    style={{
                      fontSize: 16,
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    Switch to {unit === "ml" ? "fl oz" : "ml"}
                  </Text>
                </TouchableOpacity>
                <TextInput
                  style={styles.input}
                  onChangeText={setAmount}
                  value={amount}
                  keyboardType="numeric"
                  placeholder={`Enter amount (${unit})`}
                  placeholderTextColor={theme.colors.subTextColor}
                />
              </View>

              <View style={styles.optionsContainer}>
                <TouchableOpacity
                  style={styles.quickOption}
                  onPress={() => handleSetAmount(quickOptions[0].value)}
                >
                  <MaterialIcons name="local-drink" size={24} color="#00BFFF" />
                  <Text style={styles.quickOptionText}>
                    {`${quickOptions[0].label} (${quickOptions[0].value} ${unit})`}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickOption}
                  onPress={() => handleSetAmount(quickOptions[1].value)}
                >
                  <MaterialIcons name="local-drink" size={24} color="#00BFFF" />
                  <Text style={styles.quickOptionText}>
                    {`${quickOptions[1].label} (${quickOptions[1].value} ${unit})`}
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.optionMiddle}>
                <TouchableOpacity
                  style={styles.quickOption}
                  onPress={() => handleSetAmount(quickOptions[2].value)}
                >
                  <MaterialIcons name="local-drink" size={24} color="#00BFFF" />
                  <Text style={styles.quickOptionText}>
                    {`${quickOptions[2].label} (${quickOptions[2].value} ${unit})`}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default WaterLogEntryModal;
