import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from "react-native";
import { CameraView, useCameraPermissions } from "expo-camera";
import * as ImagePicker from "expo-image-picker";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import digitalFoodScannerStyles from "./styles/digitalFoodScannerStyles.js";

const DigitalFoodScanner = ({ onClose }) => {
  const [loading, setLoading] = useState(false);
  const [facing, setFacing] = useState("back"); // Camera type to control camera direction
  const [permission, requestPermission] = useCameraPermissions(); // Handle camera permissions

  const styles = digitalFoodScannerStyles();

  // Handle camera permissions loading or not granted cases
  if (!permission) {
    return <View />;
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={styles.permissionText}>
          We need your permission to use the camera.
        </Text>
        <TouchableOpacity
          onPress={requestPermission}
          style={styles.permissionButton}
        >
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Take a picture
  const handleTakePicture = async (cameraRef) => {
    if (!cameraRef) return;
    try {
      setLoading(true);
      const photo = await cameraRef.takePictureAsync();
      console.log("Picture taken:", photo.uri);
      Alert.alert("Picture Taken", `Photo saved at ${photo.uri}`);
      // Further processing logic can go here
    } catch (error) {
      console.error("Error taking picture:", error);
      Alert.alert("Error", "An error occurred while taking the picture.");
    } finally {
      setLoading(false);
    }
  };

  // Open the gallery
  const handleOpenGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 1,
      });

      if (!result.canceled) {
        console.log("Selected image:", result.assets[0].uri);
        Alert.alert(
          "Image Selected",
          `Photo selected from gallery: ${result.assets[0].uri}`
        );
        // Further processing logic can go here
      }
    } catch (error) {
      console.error("Error selecting image from gallery:", error);
      Alert.alert("Error", "An error occurred while selecting an image.");
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <MaterialIcons name="keyboard-arrow-left" size={40} color="white" />
        </TouchableOpacity>
        <Text style={styles.title}>Scanner</Text>
      </View>

      {/* Camera */}
      <CameraView style={styles.camera} facing={facing}>
        {/* Hooks for visual targeting */}
        <View style={styles.cornerHooksContainer}>
          <View style={[styles.cornerHook, styles.topLeftHook]} />
          <View style={[styles.cornerHook, styles.topRightHook]} />
          <View style={[styles.cornerHook, styles.bottomLeftHook]} />
          <View style={[styles.cornerHook, styles.bottomRightHook]} />
        </View>
      </CameraView>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          onPress={handleOpenGallery}
          style={styles.footerButton}
        >
          <Ionicons name="images" size={28} color="white" />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleTakePicture}
          style={styles.footerButton}
        >
          <Ionicons name="camera" size={32} color="white" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.footerButton}>
          <Ionicons name="flash" size={28} color="white" />
        </TouchableOpacity>
      </View>

      {/* Loading Indicator */}
      {loading && (
        <ActivityIndicator
          size="large"
          color="white"
          style={styles.loadingIndicator}
        />
      )}
    </View>
  );
};

export default DigitalFoodScanner;
