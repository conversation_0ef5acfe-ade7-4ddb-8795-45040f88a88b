import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  TouchableWithoutFeedback,
  Keyboard,
  Animated,
  Easing,
} from "react-native";
import * as Haptics from "expo-haptics";
import Modal from "react-native-modal";
import { useFoodLog } from "../context/FoodLogContext.js";
import { Card, Title } from "react-native-paper";
import mealSectionCustomizationModalStyles from "./styles/mealSectionCustomizationModalStyles.js";
import Feather from "react-native-vector-icons/Feather";
import { useThemeContext } from "../../../context/ThemeContext.js";

console.log("Meal Section Customization Modal Rendered.");

const MealSectionCustomizationModal = ({ isVisible, closeModal }) => {
  const styles = mealSectionCustomizationModalStyles();
  const { theme } = useThemeContext();
  const {
    mealSections,
    loadMealSectionCustomizations,
    updateMealSectionNames,
  } = useFoodLog();

  // Filter out the "Water" section before setting state
  const [localMealSections, setLocalMealSections] = useState(
    mealSections.filter((section) => section.id !== "Water")
  );
  const [tempMealSections, setTempMealSections] = useState(localMealSections);
  const [editingStates, setEditingStates] = useState(
    localMealSections.map(() => false)
  );

  useEffect(() => {
    console.log("Meal Customization Modal Effect executed");

    // Load meal section customizations when the modal opens
    if (isVisible) {
      loadMealSectionCustomizations();
      const filteredSections = mealSections.filter(
        (section) => section.id !== "Water"
      );
      setLocalMealSections(filteredSections);
      setTempMealSections(filteredSections);
      setEditingStates(filteredSections.map(() => false));
    }
  }, [isVisible]);

  const handleCloseModal = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    setTempMealSections([...localMealSections]); // Reset temp state to local state
    closeModal();
  };

  const updateTempMealName = (mealId, newName) => {
    const updatedSections = tempMealSections.map((section) => {
      if (section.id === mealId) {
        return { ...section, name: newName };
      }
      return section;
    });
    setTempMealSections(updatedSections);
  };

  const handleSaveCustomizations = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Create an array of objects with updates
    const mealSectionUpdates = localMealSections.map((section, index) => ({
      id: section.id,
      newName: tempMealSections[index].name.trim(),
    }));

    // Update the global mealSections state in FoodLogContext
    updateMealSectionNames(mealSectionUpdates);

    // Close the modal
    handleCloseModal();
  };

  const renderMealSectionItems = () => {
    return localMealSections.map((item, index) => {
      const isEditing = editingStates[index];
      const editedName = tempMealSections[index].name;
      const placeholderText = "New Meal";

      const handleNameClick = useCallback(() => {
        const newEditingStates = [...editingStates];
        newEditingStates[index] = true;
        setEditingStates(newEditingStates);
      }, [editingStates, index]);

      const handleNameChange = useCallback(
        (newName) => {
          // Update the name in the temporary state
          const updatedSections = [...tempMealSections];
          updatedSections[index] = { ...item, name: newName };
          setTempMealSections(updatedSections);
        },
        [index, item, tempMealSections]
      );

      const handleNameBlur = useCallback(() => {
        const newEditingStates = [...editingStates];
        newEditingStates[index] = false;
        setEditingStates(newEditingStates);

        if (editedName !== item.name) {
          // Save the updated name locally
          updateTempMealName(item.id, editedName);
        }
      }, [index, item, editedName, editingStates]);

      return (
        <TouchableWithoutFeedback key={item.id}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <View
              style={{
                ...styles.sectionRow,
                backgroundColor: theme.colors.surface,
              }}
            >
              <Text
                style={[
                  { textAlign: "left" },
                  styles.sectionIdText,
                  !editedName && { color: "rgba(169, 169, 169, 0.8)" },
                ]}
                onPress={handleNameClick}
              >
                {item.id}
              </Text>
              <TextInput
                style={{
                  ...styles.sectionInputText,
                  flex: 1,
                  textAlign: "right",
                }}
                value={editedName}
                placeholder={placeholderText}
                placeholderTextColor={"rgba(169, 169, 169, 0.8)"}
                onChangeText={handleNameChange}
                onBlur={handleNameBlur}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      );
    });
  };

  return (
    <Modal
      isVisible={isVisible}
      style={{ flex: 1, height: "100%", width: "100%", margin: 0 }}
      avoidKeyboard={true}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <TouchableWithoutFeedback style={{ flex: 1 }} onPress={Keyboard.dismiss}>
        <View style={styles.modalContent}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              width: "100%",
              paddingTop: "5%",
            }}
          >
            <TouchableOpacity
              style={styles.closeModalButton}
              onPress={handleCloseModal}
            >
              <Feather
                name="chevron-left"
                color={theme.colors.primaryTextColor}
                size={38}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSaveCustomizations}
            >
              <Feather
                name="check-circle"
                color={theme.colors.primary}
                size={28}
              />
            </TouchableOpacity>
          </View>
          <Card
            style={{
              marginTop: 30,
              marginBottom: 10,
              marginHorizontal: 16,
              elevation: 4,
              borderColor: theme.colors.cardBorderColor,
              borderBottomWidth: 1,
            }}
          >
            <Card.Content>
              <Title
                style={{
                  marginTop: 20,
                  marginBottom: 16,
                  fontSize: 18,
                  alignSelf: "center",
                }}
              >
                Customize Meal Names
              </Title>
              {renderMealSectionItems()}
            </Card.Content>
          </Card>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default MealSectionCustomizationModal;
