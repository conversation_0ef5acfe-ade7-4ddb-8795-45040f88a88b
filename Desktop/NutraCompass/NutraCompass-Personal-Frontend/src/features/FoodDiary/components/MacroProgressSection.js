import React, { useEffect } from "react";
import { View } from "react-native";
import { Card } from "react-native-paper";
import foodDiaryScreenStyles from "../../../screens/styles/foodDiaryScreenStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import ProgressRing from "../../../components/ProgressRing.js";

const MacroProgressSection = React.memo(({ macroData }) => {
  const { theme } = useThemeContext();

  return (
    <View style={{ flex: 1 }}>
      <Card
        style={{
          flex: 1,
          borderRadius: theme.dimensions.sectionBorderRadius,
          backgroundColor: theme.colors.cardDarkGrayBackground,
          elevation: 4,
        }}
      >
        <Card.Content
          style={{
            height: "100%",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <View style={{ flexDirection: "row", gap: 8, paddingBottom: 12 }}>
            {/* Use ProgressRingComponent for each macro */}
            <ProgressRingComponent data={macroData.carbsData} />
            <ProgressRingComponent data={macroData.proteinData} />
            <ProgressRingComponent data={macroData.fatData} />
          </View>
        </Card.Content>
      </Card>
    </View>
  );
});

const ProgressRingComponent = ({ data }) => (
  <View style={{ alignItems: "center", padding: 8 }}>
    <ProgressRing
      size={90}
      strokeWidth={10}
      currentValue={data.consumedGrams}
      goalValue={data.totalGramsGoal}
      consumedColor={[data.consumedColor, data.consumedColor]}
      remainingColor={data.remainingColor}
      topOuterLabel={data.label}
      firstBottomOuterLabel={`${Math.round((data.percentage || 0) * 100)}%`}
      bottomOuterNumeratorLabel={`${data.consumedGrams || 0}`}
      bottomOuterDenominatorLabel={`${data.totalGramsGoal || 1}`}
      iconName={data.iconName}
      iconType="MaterialCommunityIcons"
    />
  </View>
);

export default MacroProgressSection;
