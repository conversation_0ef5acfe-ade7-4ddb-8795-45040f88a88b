import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  TouchableWithoutFeedback,
  Keyboard,
  Animated,
} from "react-native";
import { Image } from "expo-image";
import * as Haptics from "expo-haptics";
import { <PERSON>ppbar, Searchbar, Snackbar, IconButton } from "react-native-paper";
import Ionicons from "react-native-vector-icons/Ionicons";
import foodEntryModalStyles from "./styles/foodEntryModalStyles.js";
import {
  getNutrientsForFoodItem,
  searchFood,
} from "../api/EdamamFoodDB/edamamMethods.js";
import BarcodeScanner from "./BarcodeScanner.js";
import DigitalFoodScanner from "./DigitalFoodScanner.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodLog } from "../context/FoodLogContext.js";
import { useFoodMenu } from "../../FoodMenu/context/FoodMenuContext.js";
import FoodNutrientModal from "./FoodNutrientModal.js";

console.log("Food Entry Modal Rendered.");

const FoodEntryModal = React.memo(
  ({
    isVisible,
    onCancel,
    activeFoodItem,
    setActiveFoodItem,
    activeMealSection,
    selectedDate,
    isBuildingMeal, // New prop to indicate if building a meal
  }) => {
    const { theme } = useThemeContext();
    const {
      saveMealToFoodLog,
      saveOrUpdateSingleFoodItemToFoodLog,
      foodHistory,
    } = useFoodLog();
    const { customMeals, saveFoodToTempCustomMeal } = useFoodMenu();

    const [searchTerm, setSearchTerm] = useState("");
    const [searchResults, setSearchResults] = useState([]);
    const [activeTab, setActiveTab] = useState("search"); // 'search', 'meals', or 'history'

    const [isBarcodeScannerVisible, setIsBarcodeScannerVisible] =
      useState(false);
    const [isDigitalFoodScannerVisible, setIsDigitalFoodScannerVisible] =
      useState(false);
    // State to manage snackbar visibility
    const [isSnackbarVisible, setIsSnackbarVisible] = useState(false);
    // State to track actions that should trigger the snackbar
    const [snackbarTriggered, setSnackbarTriggered] = useState(false);
    // State used to track which food entry item's icon button should be animated
    const [selectedItem, setSelectedItem] = useState(null);

    const [isFoodNutrientModalVisible, setIsFoodNutrientModalVisible] =
      useState(false);

    const [selectedScale] = useState(new Animated.Value(1));
    const styles = foodEntryModalStyles();

    // useEffect to toggle snackbar visibility when snackbarTriggered changes
    useEffect(() => {
      if (snackbarTriggered) {
        setIsSnackbarVisible(true);
        // Hide snackbar after 1000ms
        const timeout = setTimeout(() => {
          setIsSnackbarVisible(false);
          setSnackbarTriggered(false); // Reset the trigger
        }, 1000);

        // Clear timeout on component unmount or when snackbarTriggered changes again
        return () => clearTimeout(timeout);
      }
    }, [snackbarTriggered]);

    // Function to handle toggling the snackbar
    const toggleSnackbar = () => {
      setSnackbarTriggered(true);
    };

    const handleCloseFoodNutrientModal = () => {
      setIsFoodNutrientModalVisible(false);
    };

    const handleOpenFoodNutrientModal = () => {
      setIsFoodNutrientModalVisible(true);
    };

    const handleViewFoodNutrients = (selectedFood) => {
      if (selectedFood.isCustomMeal) {
        console.log("You need to process selectedFood because its a meal.");
      }
      if (selectedFood) {
        // No need to format or fetch nutrient data, it's already available

        setActiveFoodItem(selectedFood);

        handleOpenFoodNutrientModal();

        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } else {
        console.error("There is no selected food.");
      }
    };

    const handleBarcodeScanned = (resultsOrErrorMessage) => {
      console.log(
        "RESULTS OR ERROR: " + JSON.stringify(resultsOrErrorMessage, null, 2)
      );
      if (Array.isArray(resultsOrErrorMessage)) {
        setSearchResults(resultsOrErrorMessage);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        setIsBarcodeScannerVisible(false);
      } else {
        console.log("FoodEntryModal: ", resultsOrErrorMessage);
      }
    };

    const handleFoodSearch = async () => {
      Keyboard.dismiss();
      try {
        const foodSearchResults = await searchFood(searchTerm);
        setSearchResults(foodSearchResults);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } catch (error) {
        console.error("Data processing error:", error);
      }
    };

    // Function to handle selecting an item
    const handleSelectItem = (item) => {
      setSelectedItem(item);
    };

    const handleSaveFood = async (selectedFood) => {
      // If not building a meal, an active meal section exists, selected date is valid,
      // an active food item is selected, and the active food item is a custom meal
      if (
        !isBuildingMeal &&
        activeMealSection &&
        selectedDate &&
        selectedFood &&
        selectedFood?.isCustomMeal
      ) {
        // Save the custom meal to the food log
        saveMealToFoodLog(activeMealSection.id, selectedDate, selectedFood);
      }
      // If not building a meal, an active meal section exists, selected date is valid,
      // and an active food item is selected
      else if (
        !isBuildingMeal &&
        activeMealSection &&
        selectedDate &&
        selectedFood
      ) {
        const updatedSelectedFood = await getNutrientsForFoodItem(selectedFood);

        // Update existing/create the food entry with the provided details
        saveOrUpdateSingleFoodItemToFoodLog(
          selectedFood?.id || null,
          activeMealSection.id,
          updatedSelectedFood
        );
      }
      // If building a meal and the active food item is a custom meal
      else if (isBuildingMeal && selectedFood?.isCustomMeal) {
        // Save or update the custom meal
        saveOrUpdateCustomMeal(selectedFood);
      }
      // If building a meal and the active food item is not a custom meal
      else if (isBuildingMeal && selectedFood) {
        const updatedSelectedFood = await getNutrientsForFoodItem(selectedFood);

        // Save the active food item to the temporary custom meal
        saveFoodToTempCustomMeal(updatedSelectedFood);
      }
      // If none of the above conditions are met
      else {
        // Log an error indicating an invalid state
        console.error(
          "Invalid state encountered while attempting to handle food entry: " +
            "isBuildingMeal:",
          isBuildingMeal,
          ", activeMealSection:",
          activeMealSection,
          ", selectedDate:",
          selectedDate,
          ", selectedFood:",
          selectedFood
        );

        return;
      }

      // Animate the save button
      Animated.timing(selectedScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: false,
      }).start(async () => {
        try {
          // Provide feedback to the user
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          // Trigger snackbar
          toggleSnackbar();

          // Reset the scale value after a delay
          setTimeout(() => {
            setIsSnackbarVisible(false);
            selectedScale.setValue(1);
          }, 2000);
        } catch (error) {
          // Handle any errors that occur during the save operation
          console.error("Error saving food:", error);
          // Optionally, display an error message to the user
          // You can use a Snackbar, Alert, or any other UI component for this purpose
        }
      });
    };

    const handleCloseModal = () => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setSearchResults([]);
      setSearchTerm("");
      setActiveTab("search");
      onCancel();
    };

    const openBarcodeScanner = () => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setIsBarcodeScannerVisible(true);
    };

    const closeBarcodeScanner = () => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setIsBarcodeScannerVisible(false);
    };

    const openDigitalFoodScanner = () => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setIsDigitalFoodScannerVisible(true);
    };

    const closeDigitalFoodScanner = () => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setIsDigitalFoodScannerVisible(false);
    };

    return (
      <Modal visible={isVisible} animationType="slide" transparent={true}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Appbar.Header
                mode="center-aligned"
                style={{
                  height: 40,
                  backgroundColor: theme.colors.screenBackground,
                }}
              >
                <Appbar.BackAction
                  size={20}
                  color={theme.colors.primaryTextColor}
                  onPress={handleCloseModal}
                />
                <Appbar.Content
                  titleStyle={{ fontSize: 20, fontWeight: "600" }}
                  title="Add Food"
                />
              </Appbar.Header>
            </View>
            <View style={styles.modalContent}>
              <View
                style={{
                  padding: 8,
                  paddingVertical: 18,
                  backgroundColor: theme.colors.surface,
                  marginHorizontal: 6,
                  borderRadius: 12,
                  marginVertical: 4,
                }}
              >
                <Searchbar
                  placeholder="Search for a food"
                  placeholderTextColor={theme.colors.primaryTextColor}
                  value={searchTerm}
                  onChangeText={setSearchTerm}
                  onIconPress={handleFoodSearch}
                  onSubmitEditing={handleFoodSearch}
                  mode="bar"
                  editable={activeTab === "search" ? true : false}
                  style={{
                    height: 10,
                    borderRadius: 16,
                    backgroundColor: theme.colors.surface,
                    elevation: 0, // Disable shadow/elevation
                  }}
                  inputStyle={{
                    fontSize: 14,
                    color: theme.colors.primaryTextColor,
                    alignSelf: "center", // Center text vertically
                  }}
                  icon={() => (
                    <TouchableOpacity
                      d
                      onPress={handleFoodSearch}
                      activeOpacity={0.2} // Prevent any opacity change on press
                      style={{
                        justifyContent: "center",
                        alignItems: "center",
                        width: 40,
                        height: 40,
                      }}
                    >
                      <Ionicons
                        name="search-outline"
                        size={20}
                        color={theme.colors.primaryTextColor}
                      />
                    </TouchableOpacity>
                  )}
                />
              </View>

              <View style={{ flexDirection: "row", gap: 8, padding: 8 }}>
                <TouchableOpacity
                  onPress={openBarcodeScanner}
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: theme.colors.surface,
                    paddingVertical: 14,
                    paddingHorizontal: 12,
                    borderRadius: 16,
                    gap: 4,
                    borderColor: theme.colors.primary,
                    borderWidth: 1,
                  }}
                >
                  <Ionicons
                    name="scan"
                    size={18}
                    color={theme.colors.primaryTextColor}
                  />
                  <Text
                    style={{
                      color: theme.colors.primaryTextColor,
                      fontSize: 12,
                      fontWeight: "500",
                    }}
                  >
                    Scan Barcode
                  </Text>
                </TouchableOpacity>

                <View
                  style={{
                    position: "relative", // Ensures overlay is positioned correctly
                  }}
                >
                  {/* Button */}
                  <TouchableOpacity
                    disabled={true} // Disable touch interactions
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: theme.colors.surface,
                      paddingVertical: 14,
                      paddingHorizontal: 12,
                      borderRadius: 16,
                      gap: 4,
                      borderColor: theme.colors.primary,
                      borderWidth: 1,
                      opacity: 0.6, // Visually dim to indicate it's disabled
                    }}
                  >
                    <Ionicons
                      name="scan"
                      size={18}
                      color={theme.colors.primaryTextColor}
                    />
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 12,
                        fontWeight: "500",
                      }}
                    >
                      Digital Food Scanner
                    </Text>
                  </TouchableOpacity>

                  {/* Overlay */}
                  <View
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: "rgba(0, 0, 0, 0.2)", // Semi-transparent overlay
                      borderRadius: 16, // Matches button's border radius
                    }}
                  />
                </View>
              </View>

              <View
                style={{
                  backgroundColor: theme.colors.screenBackground,
                  alignItems: "flex-start",
                  paddingHorizontal: 10,
                  paddingTop: 4,
                  paddingBottom: 8,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 10,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => setActiveTab("search")}
                    style={{
                      paddingVertical: 6,
                      paddingHorizontal: 12,
                      backgroundColor:
                        activeTab === "search"
                          ? theme.colors.primary
                          : theme.colors.surface,
                      borderRadius: 12,
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: 1,
                      borderColor: "rgba(204, 204, 204, 0.3)",
                    }}
                  >
                    <Text
                      style={{
                        color:
                          activeTab === "search"
                            ? theme.colors.primaryTextColor
                            : theme.colors.primary,
                        fontSize: 14,
                      }}
                    >
                      All
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setActiveTab("meals")}
                    style={{
                      paddingVertical: 6,
                      paddingHorizontal: 12,
                      backgroundColor:
                        activeTab === "meals"
                          ? theme.colors.primary
                          : theme.colors.surface,
                      borderRadius: 12,
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: 1,
                      borderColor: "rgba(204, 204, 204, 0.3)",
                    }}
                  >
                    <Text
                      style={{
                        color:
                          activeTab === "meals"
                            ? theme.colors.primaryTextColor
                            : theme.colors.primary,
                        fontSize: 14,
                      }}
                    >
                      My Meals
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setActiveTab("history")}
                    style={{
                      paddingVertical: 6,
                      paddingHorizontal: 12,
                      backgroundColor:
                        activeTab === "history"
                          ? theme.colors.primary
                          : theme.colors.surface,
                      borderRadius: 12,
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: 1,
                      borderColor: "rgba(204, 204, 204, 0.3)",
                    }}
                  >
                    <Text
                      style={{
                        color:
                          activeTab === "history"
                            ? theme.colors.primaryTextColor
                            : theme.colors.primary,
                        fontSize: 14,
                      }}
                    >
                      History
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              <FlatList
                styles={styles.flatlist}
                contentContainerStyle={{
                  padding: 10,
                }}
                data={
                  activeTab === "history"
                    ? foodHistory // Load foodHistory for history tab
                    : activeTab === "meals"
                    ? customMeals // Load custom meals for meals tab
                    : searchResults // Default to search results for search tab
                }
                renderItem={({ item, index }) => (
                  <TouchableOpacity
                    key={`${item.foodId}_${index}`}
                    onPress={() => handleViewFoodNutrients(item)}
                    style={styles.foodItemContainer}
                  >
                    {/* Display meal image if available */}
                    {item?.mealImageUrl && (
                      <View
                        style={{
                          width: 70,
                          height: 70,
                          borderRadius: 8, // Circular shape for the image
                          backgroundColor: theme.colors.surface, // Placeholder background
                          alignItems: "center",
                          justifyContent: "center",
                          marginRight: 10, // Add some space between image and text
                        }}
                      >
                        <Image
                          source={{ uri: item.mealImageUrl }}
                          contentFit="cover"
                          style={{
                            width: 70,
                            height: 70,
                            borderRadius: 8, // Circle shape for the image
                          }}
                        />
                      </View>
                    )}
                    {/* Food info section */}
                    <View style={styles.foodInfoContainer}>
                      <Text style={styles.foodLabel}>{item.foodLabel}</Text>
                      <View
                        style={{ flexDirection: "row", gap: 5, width: "60%" }}
                      >
                        {/** Custom Meals - item?.nutrients?.ENERC_KCAL?.quantity OR Individual Food Items - item?.defaultNutrients?.ENERC_KCAL */}
                        <Text style={styles.foodLabelCalories}>
                          {Math.round(
                            parseFloat(
                              item?.nutrients?.ENERC_KCAL?.quantity ||
                                item?.defaultNutrients?.ENERC_KCAL ||
                                0
                            )
                          )}{" "}
                          cal
                          {item?.activeMeasure?.weight || item?.foodBrand
                            ? ","
                            : ""}
                        </Text>
                        <Text style={styles.foodLabelServingSize}>
                          {activeTab === "meals" || item.isCustomMeal
                            ? ""
                            : Math.round(item?.activeMeasure?.weight)}{" "}
                          {activeTab === "meals" || item.isCustomMeal
                            ? ""
                            : item?.activeMeasure?.label.toLowerCase()}
                          {item?.foodBrand ? "," : ""}
                        </Text>

                        <Text style={styles.foodLabelServingSize}>
                          {item?.foodBrand}
                        </Text>
                      </View>
                    </View>
                    {/* Icon button section */}
                    <Animated.View
                      style={{
                        transform: [{ scale: selectedScale }],
                      }}
                    >
                      <IconButton
                        iconColor={theme.colors.primary}
                        containerColor={theme.colors.screenBackground}
                        icon={
                          selectedItem === item && isSnackbarVisible
                            ? "check"
                            : "plus"
                        }
                        color={theme.colors.primaryTextColor}
                        size={24}
                        onPress={() => {
                          handleSelectItem(item); // Select the current item
                          handleSaveFood(item); // Save the selected food
                        }}
                      />
                    </Animated.View>
                  </TouchableOpacity>
                )}
              />
            </View>

            {isBarcodeScannerVisible && (
              <View style={styles.barcodeScannerContainer}>
                <BarcodeScanner
                  onBarcodeScanned={handleBarcodeScanned}
                  onClose={closeBarcodeScanner}
                />
              </View>
            )}

            {isDigitalFoodScannerVisible && (
              <View style={styles.barcodeScannerContainer}>
                <DigitalFoodScanner
                  onClose={closeDigitalFoodScanner}
                  onPictureTaken={(imageUri) => {
                    console.log("Picture taken:", imageUri);
                    // Logic to handle the picture taken
                    setIsDigitalFoodScannerVisible(false);
                  }}
                  onOpenGallery={() => {
                    console.log("Open Gallery clicked");
                    // Logic to open the gallery
                    setIsDigitalFoodScannerVisible(false);
                  }}
                />
              </View>
            )}

            <Snackbar
              visible={isSnackbarVisible}
              onDismiss={() => setIsSnackbarVisible(false)}
              style={{ backgroundColor: theme.colors.surface }}
            >
              <Text
                style={{
                  fontSize: 16,
                  alignSelf: "center",
                  color: theme.colors.primaryTextColor,
                }}
              >
                Food Logged!
              </Text>
            </Snackbar>

            {/* Food Nutrient Modal */}
            {activeFoodItem && (
              <FoodNutrientModal
                isVisible={isFoodNutrientModalVisible}
                closeModal={handleCloseFoodNutrientModal}
                activeFoodItem={activeFoodItem}
                setActiveFoodItem={setActiveFoodItem}
                foodNutrientModalType={"Add Food"}
                activeMealSection={activeMealSection}
                selectedDate={selectedDate}
                isBuildingMeal={isBuildingMeal}
                toggleSnackbar={toggleSnackbar}
              />
            )}
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    );
  }
);

export default FoodEntryModal;
