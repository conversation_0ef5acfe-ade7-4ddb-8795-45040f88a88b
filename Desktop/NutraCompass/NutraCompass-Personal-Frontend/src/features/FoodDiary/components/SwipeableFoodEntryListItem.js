import React, { useState, useEffect } from "react";
import { UIManager, LayoutAnimation } from "react-native";
import SwipeableFoodEntryItem from "./SwipeableFoodEntryItem.js";
import { useFoodLog } from "../context/FoodLogContext.js";

const SwipeableFoodEntryListItem = React.memo(
  ({
    item,
    mealType,
    handleOpenFoodNutrientModal,
    handleOpenQuickAddModal,
    handleOpenWaterLogEntryModal,
  }) => {
    const { deleteFoodEntry } = useFoodLog();
    const [isSwiping, setIsSwiping] = useState(false);

    useEffect(() => {
      UIManager.setLayoutAnimationEnabledExperimental?.(true);
      LayoutAnimation.spring();
    }, []);

    const handleDeleteEntry = (id) => {
      deleteFoodEntry(mealType, id);
    };

    return (
      <SwipeableFoodEntryItem
        key={item.id}
        swipingCheck={(swiping) => setIsSwiping(swiping)}
        id={item.id}
        itemData={item}
        handleDeleteEntry={(id) => handleDeleteEntry(id)}
        handleEditEntry={() => {
          if (item.foodCategory === "Quick Add") {
            handleOpenQuickAddModal(item);
          } else if (
            item.mealType === "Water" &&
            item.foodCategory === "Water"
          ) {
            handleOpenWaterLogEntryModal(item);
          } else {
            handleOpenFoodNutrientModal(item);
          }
        }}
      />
    );
  }
);

export default SwipeableFoodEntryListItem;
