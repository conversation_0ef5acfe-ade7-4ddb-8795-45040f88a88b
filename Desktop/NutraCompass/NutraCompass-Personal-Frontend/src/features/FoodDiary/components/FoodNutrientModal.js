import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  Text,
  TouchableWithoutFeedback,
  Keyboard,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { Image } from "expo-image";
import { TextInput, Appbar, ProgressBar, IconButton } from "react-native-paper";
import * as Haptics from "expo-haptics";
import Modal from "react-native-modal";
import Feather from "react-native-vector-icons/Feather";
import foodNutrientModalStyles from "./styles/foodNutrientModalStyles.js";
import Svg, { Circle, G, Text as SvgText, Path } from "react-native-svg";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useFoodLog } from "../context/FoodLogContext.js";
import { useFoodMenu } from "../../FoodMenu/context/FoodMenuContext.js";
import {
  getNutrientsForFoodItem,
  processActiveFoodItemNumberOfServingsUpdate,
} from "../api/EdamamFoodDB/edamamMethods.js";
import { useNavigation } from "@react-navigation/native";

console.log("Food Nutrient Modal Rendered.");

const FoodNutrientModal = ({
  isVisible,
  closeModal,
  activeFoodItem,
  setActiveFoodItem,
  foodNutrientModalType,
  activeMealSection,
  selectedDate,
  isBuildingMeal,
  toggleSnackbar,
}) => {
  const navigation = useNavigation();
  const styles = foodNutrientModalStyles();
  const { theme, mode } = useThemeContext();

  const {
    mealSections,
    saveOrUpdateSingleFoodItemToFoodLog,
    saveMealToFoodLog,
    findMealNameById,
  } = useFoodLog();
  const { saveOrUpdateCustomMeal, saveFoodToTempCustomMeal } = useFoodMenu();
  const { getNutritionalGoals } = useUserSettings();
  const { calorieGoal, macroGoals } = getNutritionalGoals();

  const [showItemList, setShowItemList] = useState(false);
  const [showNutritionFactsList, setShowNutritionFactsList] = useState(false);
  const [showVitaminList, setShowVitaminList] = useState(false);
  const [showMineralList, setShowMineralList] = useState(false);
  // State to store the nutrient data for a single serving
  const [activeFoodItemOneServing, setActiveFoodItemOneServing] = useState({});
  // State for selected serving size
  const [selectedServing, setSelectedServing] = useState(
    activeFoodItem?.activeMeasure
  );
  // State for the number of servings
  const [numberOfServings, setNumberOfServings] = useState(
    activeFoodItem?.numberOfServings || 1
  );
  // State for select/change meal section the food item is to be added to.
  const [selectedMealSection, setSelectedMealSection] = useState(
    activeFoodItem?.mealType ||
      mealSections.filter((mealOption) => mealOption.id === activeMealSection)
  );
  // State for select serving modal visibility
  const [isSelectServingSizeVisible, setIsSelectServingSizeVisible] =
    useState(false);
  // State for select meal modal visibility
  const [isSelectMealVisible, setIsSelectMealVisible] = useState(false);
  // State to keep track of whether to disable inputs
  const [disableInputs, setDisableInputs] = useState(false);

  // Get screen width
  const screenWidth = Dimensions.get("window").width;

  calorieColor = "blue";
  carbColor = "orange";
  proteinColor = "green";
  fatColor = "red";

  // Effect to update selectedServing and numberOfServings when isVisible changes
  useEffect(() => {
    const fetchData = async () => {
      if (isVisible === true) {
        // console.log(JSON.stringify(activeMealSection, null, 1));
        // console.log(JSON.stringify(activeFoodItem?.mealType, null, 1));
        if (activeFoodItem?.isCustomMeal && isBuildingMeal) {
          setDisableInputs(true); // Disable inputs if building a meal and activeFoodItem is a custom meal
          const numberOfServingsNutrientData =
            processActiveFoodItemNumberOfServingsUpdate(
              activeFoodItem,
              activeFoodItem?.numberOfServings || 1
            );

          setActiveFoodItem((prevActiveFoodItem) => ({
            ...prevActiveFoodItem,
            nutrients: numberOfServingsNutrientData,
          }));
        } else if (
          activeFoodItem?.isCustomMeal &&
          activeFoodItem?.mealItems &&
          !isBuildingMeal
        ) {
          setActiveFoodItemOneServing(activeFoodItem);
          setDisableInputs(false); // If activeFoodItem is a meal and adding it to food log then don't disable inputs
        } else {
          // Process individual food item
          setDisableInputs(false); // If processing an individual food item then don't disable inputs
          // Check if activeFoodItemOneServing is empty
          if (
            !activeFoodItemOneServing ||
            Object.keys(activeFoodItemOneServing).length === 0
          ) {
            try {
              const selectedFoodWithSearchedNutrientInfo =
                await getNutrientsForFoodItem(activeFoodItem);

              const numberOfServingsNutrientData =
                processActiveFoodItemNumberOfServingsUpdate(
                  selectedFoodWithSearchedNutrientInfo,
                  activeFoodItem?.numberOfServings || 1
                );

              setActiveFoodItem((prevActiveFoodItem) => ({
                ...prevActiveFoodItem,
                nutrients: numberOfServingsNutrientData,
              }));
              setActiveFoodItemOneServing(selectedFoodWithSearchedNutrientInfo);
            } catch (error) {
              console.error("Error fetching nutrient data:", error);
            }
          }
        }

        setSelectedServing(activeFoodItem?.activeMeasure);
        setNumberOfServings(activeFoodItem?.numberOfServings || 1);

        const activeMealSectionFromDiary = mealSections.filter(
          (mealOption) => mealOption.id === activeFoodItem?.mealType
        );

        setSelectedMealSection(
          activeMealSection || activeMealSectionFromDiary[0]
        );
      } else {
        setSelectedServing(null);
        setNumberOfServings(null);
        setSelectedMealSection(null);
        setActiveFoodItemOneServing(null);
      }
    };

    fetchData();
  }, [isVisible]);

  const toggleSelectServing = () => {
    if (isSelectMealVisible) {
      toggleSelectMeal();
    }

    setIsSelectServingSizeVisible(!isSelectServingSizeVisible);
  };

  const toggleSelectMeal = () => {
    if (isSelectServingSizeVisible) {
      toggleSelectServing();
    }

    setIsSelectMealVisible(!isSelectMealVisible);
  };

  const hideSelectServingAndSelectMealOnTextInputFocus = () => {
    if (isSelectServingSizeVisible) setIsSelectServingSizeVisible(false);

    if (isSelectMealVisible) setIsSelectMealVisible(false);
  };

  const handleSelectMeal = (mealOption) => {
    if (isSelectMealVisible) {
      toggleSelectMeal(); // Close the meal options modal
    }
    setSelectedMealSection(mealOption);
  };

  // Function to handle serving size selection
  const handleSelectServing = async (servingSizeOption) => {
    // Check if activeFoodItem is not a custom meal
    if (activeFoodItem && !activeFoodItem.isCustomMeal) {
      if (isSelectServingSizeVisible) {
        toggleSelectServing(); // Close the serving size options modal
      }
      const selectedFoodWithUpdatedNutrientInfoBasedOnServingSize =
        await getNutrientsForFoodItem(activeFoodItem, servingSizeOption);

      const numberOfServingsNutrientData =
        processActiveFoodItemNumberOfServingsUpdate(
          selectedFoodWithUpdatedNutrientInfoBasedOnServingSize,
          numberOfServings
        );

      setActiveFoodItemOneServing(
        selectedFoodWithUpdatedNutrientInfoBasedOnServingSize
      );
      setSelectedServing(servingSizeOption);
      setActiveFoodItem((prevActiveFoodItem) => ({
        ...prevActiveFoodItem,
        activeMeasure: servingSizeOption,
        nutrients: numberOfServingsNutrientData,
      }));
    }
  };

  // Function to update the number of servings
  const updateNumberOfServings = async (newNumberOfServings) => {
    // Check if activeFoodItem is a custom meal
    if (
      activeFoodItem &&
      activeFoodItem.isCustomMeal &&
      activeFoodItem.mealItems
    ) {
      const servings = parseFloat(newNumberOfServings);
      setNumberOfServings(servings);

      // Call updateNutrientsForCustomMeal with the new number of servings
      updateNutrientsForCustomMeal(servings);
    } else {
      // Handle the regular case for non-custom meals
      if (
        !activeFoodItemOneServing ||
        Object.keys(activeFoodItemOneServing).length === 0
      ) {
        console.log(
          "Cannot update nutrients based on number of serving changes, without activeFoodItemOneServing."
        );
        return;
      }

      const servings = parseFloat(newNumberOfServings);
      setNumberOfServings(servings);

      const updatedActiveItemNutrients =
        processActiveFoodItemNumberOfServingsUpdate(
          activeFoodItemOneServing,
          servings
        );

      setActiveFoodItem((prevActiveFoodItem) => ({
        ...prevActiveFoodItem,
        numberOfServings: servings,
        nutrients: updatedActiveItemNutrients,
      }));
    }
  };

  // Assuming activeFoodItem represents the custom meal with its mealItems
  function updateNutrientsForCustomMeal(newNumberOfServings) {
    const updatedMealItems = activeFoodItemOneServing?.mealItems.map((item) => {
      const scaledNutrients = scaleNutrients(
        item.nutrients,
        newNumberOfServings
      );
      return { ...item, nutrients: scaledNutrients };
    });

    // Calculate new total nutrients for the whole meal
    const updatedTotalNutrients =
      calculateTotalNutrientsForMeal(updatedMealItems);

    // Set these updated values for temporary use in adding to the food diary
    setActiveFoodItem({
      ...activeFoodItem,
      mealItems: updatedMealItems,
      nutrients: updatedTotalNutrients,
      numberOfServings: newNumberOfServings, // Reflect the new serving size
    });
  }

  function scaleNutrients(nutrients, scaleBy) {
    let scaled = {};
    Object.keys(nutrients).forEach((key) => {
      // Assume nutrients[key] is an object with a quantity and possibly a unit
      const value = nutrients[key];
      if (value.quantity) {
        scaled[key] = {
          ...value,
          quantity: value.quantity * scaleBy,
        };
      }
    });
    return scaled;
  }

  function calculateTotalNutrientsForMeal(mealItems) {
    let totals = {};
    mealItems.forEach((item) => {
      Object.keys(item.nutrients).forEach((nutrientKey) => {
        const nutrientValue = item.nutrients[nutrientKey];
        if (!totals[nutrientKey]) {
          totals[nutrientKey] = { ...nutrientValue, quantity: 0 }; // Initialize if not present
        }
        totals[nutrientKey].quantity += nutrientValue.quantity; // Aggregate
      });
    });
    return totals;
  }

  const handleEditFoodEntryAndSave = () => {
    // If not building a meal, a selected meal section exists, selected date is valid,
    // an active food item is selected, and the active food item is a custom meal
    if (
      !isBuildingMeal &&
      selectedMealSection &&
      selectedDate &&
      activeFoodItem &&
      activeFoodItem?.isCustomMeal
    ) {
      // Save the custom meal to the food log
      saveMealToFoodLog(selectedMealSection.id, selectedDate, activeFoodItem);
    }
    // If not building a meal, a selected meal section exists, selected date is valid,
    // and an active food item is selected
    else if (!isBuildingMeal && selectedMealSection && activeFoodItem) {
      // Update or save the individual food item to the food log
      saveOrUpdateSingleFoodItemToFoodLog(
        activeFoodItem?.id,
        selectedMealSection.id,
        activeFoodItem
      );
    }
    // If building a meal and the active food item is a custom meal
    else if (isBuildingMeal && activeFoodItem?.isCustomMeal) {
      // Save or update the custom meal
      saveOrUpdateCustomMeal(activeFoodItem);

      if (navigation.getState().routeNames.includes("Custom Meals")) {
        navigation.navigate("Custom Meals");
      }
    }
    // If building a meal and the active food item is not a custom meal
    else if (isBuildingMeal && activeFoodItem) {
      // Save the active food item to the temporary custom meal
      saveFoodToTempCustomMeal(activeFoodItem);
    }
    // If none of the above conditions are met
    else {
      // Log an error indicating an invalid state
      console.error(
        "Invalid state encountered while attempting to handle food entry: " +
          "isBuildingMeal:",
        isBuildingMeal,
        ", selectedMealSection:",
        selectedMealSection,
        ", selectedDate:",
        selectedDate,
        ", activeFoodItem:",
        activeFoodItem
      );
      return;
    }
    handleCloseModal();
    // Trigger snackbar if the user is not saving or updating a customMeal

    if (foodNutrientModalType === "Edit Entry") {
      return;
    } else if (foodNutrientModalType === "Edit Meal Item") {
      return;
    } else if (!(isBuildingMeal && activeFoodItem?.isCustomMeal)) {
      toggleSnackbar();
    }
  };

  const handleCloseModal = () => {
    setShowMineralList(false);
    setShowVitaminList(false);
    setShowNutritionFactsList(false);
    setActiveFoodItem(null);
    setIsSelectServingSizeVisible(false);
    setIsSelectMealVisible(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    closeModal();
  };

  const toggleItemList = () => {
    setShowItemList(!showItemList);
  };

  const toggleNutritionFactsList = () => {
    setShowNutritionFactsList(!showNutritionFactsList);
  };

  const toggleVitaminList = () => {
    setShowVitaminList(!showVitaminList);
  };

  const toggleMineralList = () => {
    setShowMineralList(!showMineralList);
  };

  /**
   * Calculates the percentage of calories from carbs, protein, and fat
   * relative to totalCalories. If the total exceeds 100%, the values are
   * scaled down uniformly.
   *
   * @param {number} totalCalories - The total calories for the food item.
   * @param {number} gramsOfCarbs
   * @param {number} gramsOfProtein
   * @param {number} gramsOfFat
   * @returns {{
   *   percentageOfCarbs: number,
   *   percentageOfProtein: number,
   *   percentageOfFat: number
   * }}
   */
  const calculateMacroPercentages = (
    totalCalories,
    gramsOfCarbs,
    gramsOfProtein,
    gramsOfFat
  ) => {
    if (
      isNaN(totalCalories) ||
      isNaN(gramsOfCarbs) ||
      isNaN(gramsOfProtein) ||
      isNaN(gramsOfFat) ||
      totalCalories <= 0
    ) {
      return {
        percentageOfCarbs: 0,
        percentageOfProtein: 0,
        percentageOfFat: 0,
      };
    }

    const totalCaloriesFloat = parseFloat(totalCalories);
    const caloriesFromCarbs = gramsOfCarbs * 4;
    const caloriesFromProtein = gramsOfProtein * 4;
    const caloriesFromFat = gramsOfFat * 9;

    // Calculate percentage values (which might be > 100)
    const calcPercentage = (macroCalories) => {
      const fraction = macroCalories / totalCaloriesFloat;
      return fraction < 0 ? 0 : fraction * 100;
    };

    let percentageOfCarbs = calcPercentage(caloriesFromCarbs);
    let percentageOfProtein = calcPercentage(caloriesFromProtein);
    let percentageOfFat = calcPercentage(caloriesFromFat);

    let totalPercentage =
      percentageOfCarbs + percentageOfProtein + percentageOfFat;

    // Scale down if total > 100%
    if (totalPercentage > 100) {
      const adjustmentFactor = 100 / totalPercentage;
      percentageOfCarbs *= adjustmentFactor;
      percentageOfProtein *= adjustmentFactor;
      percentageOfFat *= adjustmentFactor;
    }

    // Return numeric percentages, rounded to 2 decimals
    return {
      percentageOfCarbs: parseFloat(percentageOfCarbs.toFixed(2)),
      percentageOfProtein: parseFloat(percentageOfProtein.toFixed(2)),
      percentageOfFat: parseFloat(percentageOfFat.toFixed(2)),
    };
  };

  // Extract nutrient values for the food item:
  const totalCalories = activeFoodItem?.nutrients?.ENERC_KCAL?.quantity || 0;
  const gramsOfCarbs = activeFoodItem?.nutrients?.CHOCDF?.quantity || 0;
  const gramsOfProtein = activeFoodItem?.nutrients?.PROCNT?.quantity || 0;
  const gramsOfFat = activeFoodItem?.nutrients?.FAT?.quantity || 0;

  // Calculate macro percentages relative to the food item:
  const macroPercentagesInRelationToItemCalories = calculateMacroPercentages(
    totalCalories,
    gramsOfCarbs,
    gramsOfProtein,
    gramsOfFat
  );

  // console.log(
  //   "Macro Percentages in Relation To Item Calories: ",
  //   JSON.stringify(macroPercentagesInRelationToItemCalories, null, 2)
  // );

  // Calculate fractions for progress bars (should be numbers between 0 and 1)
  const caloriesFraction = Math.min(1, totalCalories / calorieGoal);
  const carbsFraction = Math.min(1, gramsOfCarbs / macroGoals.carb.dailyGrams);
  const proteinFraction = Math.min(
    1,
    gramsOfProtein / macroGoals.protein.dailyGrams
  );
  const fatFraction = Math.min(1, gramsOfFat / macroGoals.fat.dailyGrams);

  // console.log("Calories fraction (0-1):", caloriesFraction);
  // console.log("Carbs fraction (0-1):", carbsFraction);
  // console.log("Protein fraction (0-1):", proteinFraction);
  // console.log("Fat fraction (0-1):", fatFraction);

  // For display purposes, you may want to show the actual computed percentage (even if > 100)
  const caloriesDisplay = Math.round((totalCalories / calorieGoal) * 100);
  const carbsDisplay = Math.round(
    (gramsOfCarbs / macroGoals.carb.dailyGrams) * 100
  );
  const proteinDisplay = Math.round(
    (gramsOfProtein / macroGoals.protein.dailyGrams) * 100
  );
  const fatDisplay = Math.round((gramsOfFat / macroGoals.fat.dailyGrams) * 100);

  // console.log("Calories display percentage:", caloriesDisplay);
  // console.log("Carbs display percentage:", carbsDisplay);
  // console.log("Protein display percentage:", proteinDisplay);
  // console.log("Fat display percentage:", fatDisplay);

  const renderCircularChart = (
    carbsPercentage,
    fatPercentage,
    proteinPercentage,
    calories
  ) => {
    const chartData = [
      { percentage: carbsPercentage, color: carbColor, label: "Carbs" },
      { percentage: proteinPercentage, color: proteinColor, label: "Protein" },
      { percentage: fatPercentage, color: fatColor, label: "Fat" },
    ];

    const radius = 45;
    const circumference = 2 * Math.PI * radius;

    let currentAngle = 0;

    return (
      <Svg height="90" width="90">
        <G transform={{ translate: `${radius}, ${radius}` }}>
          {chartData.map((segment, index) => {
            const angle = (segment.percentage * 360) / 100;
            // Calculate the path for the entire circle (neutral background)
            const fullCirclePath = `M 0 -${radius} A ${radius} ${radius} 0 1 1 0 ${radius} A ${radius} ${radius} 0 1 1 0 -${radius}`;
            const path = `M 0 0 L ${
              radius * Math.cos((currentAngle * Math.PI) / 180)
            } ${
              radius * Math.sin((currentAngle * Math.PI) / 180)
            } A ${radius} ${radius} 0 ${angle > 180 ? 1 : 0} 1 ${
              radius * Math.cos(((currentAngle + angle) * Math.PI) / 180)
            } ${radius * Math.sin(((currentAngle + angle) * Math.PI) / 180)} Z`;
            currentAngle += angle;

            return (
              <React.Fragment key={index}>
                <Path
                  d={fullCirclePath}
                  fill={
                    mode === "dark"
                      ? "rgba(255, 255, 255, 0.1)"
                      : "rgba(0, 0, 0, 0.1)"
                  }
                />
                <Path d={path} fill={segment.color} />
              </React.Fragment>
            );
          })}
          <Circle r={radius - 5} fill={theme.colors.surface} />

          <SvgText
            fill={theme.colors.primaryTextColor}
            fontSize="16"
            fontWeight="bold"
            textAnchor="middle"
            alignmentBaseline="middle"
            transform="translate(0, -6)"
          >
            {Math.round(calories)}
          </SvgText>
          <SvgText
            fill={theme.colors.primaryTextColor}
            fontSize="12"
            textAnchor="middle"
            alignmentBaseline="middle"
            transform="translate(0, 15)"
          >
            cal
          </SvgText>
        </G>
      </Svg>
    );
  };

  const renderNutrientRows = (nutrientKeysOrder, isIndented = false) => {
    if (!Array.isArray(nutrientKeysOrder)) return;

    const NutrientComponent = isIndented
      ? NutritionFactsIndentedRow
      : NutritionFactsRow;

    return nutrientKeysOrder.map((nutrientKey, index) => (
      <NutrientComponent
        key={`${nutrientKey}_${index}`}
        label={activeFoodItem?.nutrients?.[nutrientKey]?.label}
        quantity={activeFoodItem?.nutrients?.[nutrientKey]?.quantity}
        unit={
          nutrientKey === "ENERC_KCAL"
            ? ""
            : activeFoodItem?.nutrients?.[nutrientKey]?.unit
        }
        totalDaily={
          activeFoodItem?.nutrients?.[nutrientKey]?.totalDaily?.quantity
        }
        totalDailyUnit={
          activeFoodItem?.nutrients?.[nutrientKey]?.totalDaily?.unit
        }
        color={theme.colors.primaryTextColor}
      />
    ));
  };

  const renderVitaminNutrientRows = (nutrientKeysOrder, isIndented = false) => {
    if (!Array.isArray(nutrientKeysOrder)) return;

    const NutrientComponent = isIndented
      ? NutritionFactsIndentedRow
      : NutritionFactsRow;

    return nutrientKeysOrder.map((nutrientKey, index) => (
      <NutrientComponent
        key={`${nutrientKey}_${index}`}
        label={activeFoodItem?.nutrients?.vitamins?.[nutrientKey]?.label}
        quantity={activeFoodItem?.nutrients?.vitamins?.[nutrientKey]?.quantity}
        unit={activeFoodItem?.nutrients?.vitamins?.[nutrientKey]?.unit}
        totalDaily={
          activeFoodItem?.nutrients?.vitamins?.[nutrientKey]?.totalDaily
            ?.quantity
        }
        totalDailyUnit={
          activeFoodItem?.nutrients?.vitamins?.[nutrientKey]?.totalDaily?.unit
        }
        color={theme.colors.primaryTextColor}
      />
    ));
  };

  const renderMineralNutrientRows = (nutrientKeysOrder, isIndented = false) => {
    if (!Array.isArray(nutrientKeysOrder)) return;

    const NutrientComponent = isIndented
      ? NutritionFactsIndentedRow
      : NutritionFactsRow;

    return nutrientKeysOrder.map((nutrientKey, index) => (
      <NutrientComponent
        key={`${nutrientKey}_${index}`}
        label={activeFoodItem?.nutrients?.minerals?.[nutrientKey]?.label}
        quantity={activeFoodItem?.nutrients?.minerals?.[nutrientKey]?.quantity}
        unit={activeFoodItem?.nutrients?.minerals?.[nutrientKey]?.unit}
        totalDaily={
          activeFoodItem?.nutrients?.minerals?.[nutrientKey]?.totalDaily
            ?.quantity
        }
        totalDailyUnit={
          activeFoodItem?.nutrients?.minerals?.[nutrientKey]?.totalDaily?.unit
        }
        color={theme.colors.primaryTextColor}
      />
    ));
  };

  const vitaminKeys = [
    "VITA_RAE",
    "VITC",
    "VITD",
    "TOCPHA",
    "VITK1",
    "THIA",
    "RIBF",
    "NIA",
    "VITB6A",
    "FOLDFE",
    "VITB12",
  ];
  const mineralKeys = ["CA", "FE", "MG", "P", "K", "NA", "ZN"];

  const conversionFactors = {
    ounce: 0.035274,
    pound: 0.00220462,
    kilogram: 0.001,
    gram: 1,
    pinch: 0.355, // Assuming a pinch is around 0.355 grams
    liter: 0.001, // Assuming density similar to water
    fluid_ounce: 0.0295735,
    gallon: 0.00378541,
    pint: 0.00211338,
    quart: 0.00105669,
    milliliter: 1,
    drop: 0.05, // Assuming a drop is about 0.05 ml
    cup: 0.00422675,
    tablespoon: 0.0147868,
    teaspoon: 0.00492892,
  };

  function convertGramsToUnit(grams, unit) {
    console.log("Grams: ", grams);
    console.log("Unit: ", unit);
    console.log(
      "Converted Value: ",
      grams * conversionFactors[unit.toLowerCase()]
    );
    return grams * conversionFactors[unit.toLowerCase()];
  }

  return (
    <Modal
      isVisible={isVisible}
      style={styles.container}
      avoidKeyboard={true}
      animationIn="slideInLeft"
      animationOut="slideOutLeft"
    >
      <TouchableWithoutFeedback style={{ flex: 1 }} onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          {/* Header */}
          <Appbar.Header style={styles.header}>
            <Appbar.Action
              icon="chevron-left"
              size={32}
              onPress={handleCloseModal}
              color={theme.colors.primaryTextColor}
            />
            <Appbar.Content
              title={foodNutrientModalType}
              titleStyle={styles.title}
            />
            <Appbar.Action
              icon="check"
              size={32}
              onPress={() => handleEditFoodEntryAndSave()}
              color={theme.colors.primary}
            />
          </Appbar.Header>

          {activeFoodItem && (
            <ScrollView>
              <TouchableOpacity
                onPress={() => {
                  if (isSelectServingSizeVisible) {
                    toggleSelectServing();
                  }
                  if (isSelectMealVisible) {
                    toggleSelectMeal();
                  }
                }}
                activeOpacity={1}
                style={{
                  gap: 10,
                }}
              >
                {/* Food Item Section */}
                <View
                  style={{
                    ...styles.sectionContainer,
                    flexDirection: "row",
                    justifyContent: "space-between",
                    gap: 5,
                  }}
                >
                  <View style={{ flex: 1 }}>
                    {activeMealSection?.name && (
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 14,
                          paddingBottom: 4,
                        }}
                      >
                        {activeMealSection?.name}
                      </Text>
                    )}
                    {activeFoodItem?.mealType && (
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 14,
                          paddingBottom: 4,
                        }}
                      >
                        {findMealNameById(activeFoodItem?.mealType)}
                      </Text>
                    )}
                    <Text style={styles.foodItemName}>
                      {activeFoodItem?.foodLabel}
                    </Text>
                    <Text style={styles.brandCompany}>
                      {activeFoodItem?.foodBrand ||
                        activeFoodItem?.foodCategory}
                    </Text>
                  </View>
                  {activeFoodItem?.mealImageUrl && (
                    <View style={{ width: "40%" }}>
                      <Image
                        source={{ uri: activeFoodItem?.mealImageUrl }}
                        style={{
                          width: 150,
                          height: 150,
                          borderRadius: 10,
                          borderWidth: 1,
                        }}
                      />
                    </View>
                  )}
                </View>
                {/* Input Fields Section */}
                <View style={styles.sectionContainer}>
                  {/* Inputs that are disabled if its activeFoodItem is a custom meal */}
                  {!disableInputs && (
                    <View>
                      <View style={{ marginBottom: 16 }}>
                        <ServingSizeRow
                          isVisible={isSelectServingSizeVisible}
                          toggleSelectServing={toggleSelectServing}
                          label="Serving Size"
                          selectedServing={selectedServing}
                          isCustomMeal={activeFoodItem?.isCustomMeal}
                        />
                      </View>
                      <View style={{ marginBottom: 16 }}>
                        <NumberOfServingsRow
                          label="Number of Servings"
                          keyboardType="numeric"
                          numberOfServings={numberOfServings}
                          updateNumberOfServings={updateNumberOfServings}
                          hideSelectServingAndSelectMealOnTextInputFocus={
                            hideSelectServingAndSelectMealOnTextInputFocus
                          }
                        />
                      </View>

                      {!isBuildingMeal && (
                        <View style={{ marginBottom: 16 }}>
                          <MealRow
                            isVisible={isSelectMealVisible}
                            toggleSelectMeal={toggleSelectMeal}
                            label="Meal"
                            selectedMealOption={selectedMealSection}
                          />
                        </View>
                      )}
                    </View>
                  )}

                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    {/* Macronutrient Content: Carbs, Protein, Fat */}
                    {/* Example data, replace with actual values */}
                    {/* Circular Chart that shows calorie and macronutrient breakdown */}
                    <View style={styles.macroNutrientColumn}>
                      {renderCircularChart(
                        macroPercentagesInRelationToItemCalories.percentageOfCarbs,
                        macroPercentagesInRelationToItemCalories.percentageOfFat,
                        macroPercentagesInRelationToItemCalories.percentageOfProtein,
                        totalCalories
                      )}
                    </View>

                    <View style={styles.macroNutrientColumn}>
                      <Text
                        style={{
                          ...styles.macroNutrientPercentage,
                          color: carbColor,
                        }}
                      >
                        {Math.round(
                          macroPercentagesInRelationToItemCalories.percentageOfCarbs
                        )}
                        %
                      </Text>
                      <Text style={styles.macroNutrientValue}>
                        {Math.round(gramsOfCarbs)}{" "}
                        {activeFoodItem?.nutrients?.CHOCDF?.unit}
                      </Text>
                      <Text style={styles.macroNutrientLabel}>Carbs</Text>
                    </View>
                    <View style={styles.macroNutrientColumn}>
                      <Text
                        style={{
                          ...styles.macroNutrientPercentage,
                          color: proteinColor,
                        }}
                      >
                        {Math.round(
                          macroPercentagesInRelationToItemCalories.percentageOfProtein
                        )}
                        %
                      </Text>
                      <Text style={styles.macroNutrientValue}>
                        {Math.round(gramsOfProtein)}{" "}
                        {activeFoodItem?.nutrients?.PROCNT?.unit}
                      </Text>
                      <Text style={styles.macroNutrientLabel}>Protein</Text>
                    </View>
                    <View style={styles.macroNutrientColumn}>
                      <Text
                        style={{
                          ...styles.macroNutrientPercentage,
                          color: fatColor,
                        }}
                      >
                        {Math.round(
                          macroPercentagesInRelationToItemCalories.percentageOfFat
                        )}
                        %
                      </Text>
                      <Text style={styles.macroNutrientValue}>
                        {Math.round(gramsOfFat)}{" "}
                        {activeFoodItem?.nutrients?.FAT?.unit}
                      </Text>
                      <Text style={styles.macroNutrientLabel}>Fat</Text>
                    </View>
                  </View>
                </View>
                {/* Calorie and Macronutrient Percent of Daily Goals */}
                <View style={styles.sectionContainer}>
                  <Text style={styles.nutritionFactsLabel}>
                    Percent of Daily Goals
                  </Text>
                  <View style={styles.progressContainer}>
                    {/* Progress bar for Calories */}
                    <View style={styles.progressItem}>
                      <ProgressBar
                        progress={caloriesFraction} // Fraction between 0 and 1
                        color={calorieColor}
                        style={{
                          height: 10,
                          width: screenWidth * 0.22,
                          borderRadius: 5,
                        }}
                      />
                      <Text
                        style={{
                          fontSize: 12,
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {caloriesDisplay}%
                      </Text>
                      <Text
                        style={{
                          fontSize: 12,
                          fontWeight: "bold",
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        Calories
                      </Text>
                    </View>
                    {/* Progress bar for Carbs */}
                    <View style={styles.progressItem}>
                      <ProgressBar
                        progress={carbsFraction}
                        color={carbColor}
                        style={{
                          height: 10,
                          width: screenWidth * 0.22,
                          borderRadius: 5,
                        }}
                      />
                      <Text
                        style={{
                          fontSize: 12,
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {carbsDisplay}%
                      </Text>
                      <Text
                        style={{
                          fontSize: 12,
                          fontWeight: "bold",
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        Carbs
                      </Text>
                    </View>
                    {/* Progress bar for Protein */}
                    <View style={styles.progressItem}>
                      <ProgressBar
                        progress={proteinFraction}
                        color={proteinColor}
                        style={{
                          height: 10,
                          width: screenWidth * 0.22,
                          borderRadius: 5,
                        }}
                      />
                      <Text
                        style={{
                          fontSize: 12,
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {proteinDisplay}%
                      </Text>
                      <Text
                        style={{
                          fontSize: 12,
                          fontWeight: "bold",
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        Protein
                      </Text>
                    </View>
                    {/* Progress bar for Fat */}
                    <View style={styles.progressItem}>
                      <ProgressBar
                        progress={fatFraction}
                        color={fatColor}
                        style={{
                          height: 10,
                          width: screenWidth * 0.22,
                          borderRadius: 5,
                        }}
                      />
                      <Text
                        style={{
                          fontSize: 12,
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {fatDisplay}%
                      </Text>
                      <Text
                        style={{
                          fontSize: 12,
                          fontWeight: "bold",
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        Fat
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Render Meal Items Section if it's a custom meal */}
                {activeFoodItem?.isCustomMeal && (
                  <View style={styles.sectionContainer}>
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <Text style={styles.nutritionFactsLabel}>Items</Text>
                      <TouchableOpacity
                        onPress={toggleItemList}
                        style={{
                          flexDirection: "row",
                          gap: 5,
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Text
                          style={{
                            color: theme.colors.primary,
                            fontWeight: "bold",
                          }}
                        >
                          {showItemList ? "Hide" : "Show"}
                        </Text>
                        <Feather
                          name={showItemList ? "arrow-up" : "arrow-down"}
                          color={theme.colors.primary}
                          size={20}
                        />
                      </TouchableOpacity>
                    </View>

                    {/* Meal Items */}
                    {showItemList &&
                      activeFoodItem?.mealItems.map((item, index) => (
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            padding: 10,
                            paddingTop: 14,
                            borderBottomWidth: 1,
                            borderBottomColor: theme.colors.cardBorderColor,
                          }}
                          key={index}
                        >
                          <Text
                            style={{
                              flex: 2,
                              fontSize: 16,
                              color: theme.colors.primaryTextColor,
                            }}
                          >
                            {item.foodLabel}
                          </Text>
                          <View style={{ flex: 1, gap: 5 }}>
                            <Text
                              style={{
                                fontSize: 14,
                                color: theme.colors.primaryTextColor,
                              }}
                            >
                              {Math.round(item.nutrients.ENERC_KCAL.quantity)}{" "}
                              cal
                            </Text>
                            <Text
                              style={{
                                fontSize: 14,
                                color: theme.colors.primaryTextColor,
                              }}
                            >
                              {item.foodCategory !== "Quick Add" && (
                                <>
                                  {Math.round(
                                    item.activeMeasure.weight *
                                      Math.round(item.numberOfServings)
                                  )}{" "}
                                  {item.activeMeasure.label}
                                </>
                              )}
                            </Text>
                          </View>
                        </View>
                      ))}
                  </View>
                )}
                {/* Nutritional Information List Toggle */}
                <View style={styles.sectionContainer}>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <Text style={styles.nutritionFactsLabel}>
                      Nutrition Facts
                    </Text>
                    <TouchableOpacity
                      onPress={toggleNutritionFactsList}
                      style={{
                        flexDirection: "row",
                        gap: 5,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Text
                        style={{
                          color: theme.colors.primary,
                          fontWeight: "bold",
                        }}
                      >
                        {showNutritionFactsList ? "Hide" : "Show"}
                      </Text>
                      <Feather
                        name={
                          showNutritionFactsList ? "arrow-up" : "arrow-down"
                        }
                        color={theme.colors.primary}
                        size={20}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                {showNutritionFactsList && (
                  <View
                    style={{ ...styles.sectionContainer, paddingHorizontal: 0 }}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        color: theme.colors.primaryTextColor,
                        alignSelf: "flex-end",
                        marginBottom: 10,
                        paddingHorizontal: 16,
                      }}
                    >
                      % Daily Value
                    </Text>
                    {renderNutrientRows(["ENERC_KCAL"], false)}

                    <View style={styles.nutritionFactsSeparator} />

                    <View style={styles.nutritionFactsSection}>
                      {renderNutrientRows(["FAT"], false)}
                    </View>

                    {renderNutrientRows(
                      ["FASAT", "FATRN", "FAPU", "FAMS"],
                      true
                    )}

                    <View style={styles.nutritionFactsSeparator} />

                    <View style={styles.nutritionFactsSection}>
                      {renderNutrientRows(["CHOLE"], false)}
                    </View>

                    <View style={styles.nutritionFactsSeparator} />

                    <View style={styles.nutritionFactsSection}>
                      {renderMineralNutrientRows(["NA"], false)}
                    </View>

                    <View style={styles.nutritionFactsSeparator} />

                    <View style={styles.nutritionFactsSection}>
                      {renderNutrientRows(["CHOCDF"], false)}

                      {renderNutrientRows(["FIBTG"], true)}

                      {renderNutrientRows(["SUGAR"], true)}
                    </View>

                    <View style={styles.nutritionFactsSeparator} />

                    <View style={styles.nutritionFactsSection}>
                      {renderNutrientRows(["PROCNT"], false)}
                    </View>

                    <View style={styles.nutritionFactsSeparator} />

                    <View style={styles.nutritionFactsSection}>
                      <View style={styles.nutritionFactsRow}>
                        <Text style={styles.nutritionFactsLabel}>Vitamins</Text>
                        <TouchableOpacity
                          onPress={toggleVitaminList}
                          style={{
                            flexDirection: "row",
                            gap: 5,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontWeight: "bold",
                            }}
                          >
                            {showVitaminList ? "Hide" : "Show"}
                          </Text>
                          <Feather
                            name={showVitaminList ? "arrow-up" : "arrow-down"}
                            color={theme.colors.primary}
                            size={20}
                          />
                        </TouchableOpacity>
                      </View>
                      <View style={{ paddingTop: 10 }}>
                        {showVitaminList &&
                          renderVitaminNutrientRows(vitaminKeys, true)}
                      </View>
                    </View>

                    <View style={styles.nutritionFactsSeparator} />

                    <View style={styles.nutritionFactsSection}>
                      <View style={styles.nutritionFactsRow}>
                        <Text style={styles.nutritionFactsLabel}>Minerals</Text>
                        <TouchableOpacity
                          onPress={toggleMineralList}
                          style={{
                            flexDirection: "row",
                            gap: 5,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontWeight: "bold",
                            }}
                          >
                            {showMineralList ? "Hide" : "Show"}
                          </Text>
                          <Feather
                            name={showMineralList ? "arrow-up" : "arrow-down"}
                            color={theme.colors.primary}
                            size={20}
                          />
                        </TouchableOpacity>
                      </View>
                      <View style={{ paddingTop: 10 }}>
                        {showMineralList &&
                          renderMineralNutrientRows(mineralKeys, true)}
                      </View>
                    </View>
                  </View>
                )}
              </TouchableOpacity>
            </ScrollView>
          )}
          {isSelectServingSizeVisible && (
            <View
              style={{
                position: "absolute",
                bottom: 0,
                right: 0,
                left: 0,
                top: "66%",
                backgroundColor: theme.colors.screenBackground,
                zIndex: 2,
              }}
            >
              <View style={{ flex: 1 }}>
                <View
                  style={{
                    height: "20%",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    paddingHorizontal: 10,
                    borderBottomWidth: 1,
                    borderBottomColor: "gray",
                  }}
                >
                  <Text
                    style={{
                      color: theme.colors.primaryTextColor,
                      fontSize: 20,
                    }}
                  >
                    Select Unit
                  </Text>
                  <IconButton
                    icon="close"
                    iconColor={theme.colors.primaryTextColor}
                    size={26}
                    onPress={toggleSelectServing}
                  />
                </View>
                <ScrollView>
                  <TouchableOpacity activeOpacity={1}>
                    {activeFoodItem &&
                      activeFoodItem?.measures?.map(
                        (servingSizeOption, index) => (
                          <TouchableOpacity
                            key={`${servingSizeOption?.uri}_${index}`}
                            onPress={() =>
                              handleSelectServing(servingSizeOption)
                            }
                            style={{
                              width: "100%",
                              flexDirection: "row",
                              alignItems: "center",
                              justifyContent: "space-between",
                              backgroundColor:
                                selectedServing === servingSizeOption
                                  ? theme.colors.surface
                                  : "transparent",
                              marginVertical: 5,
                              padding: 10,
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                width: "85%", // Adjust according to the space needed for the check icon
                                paddingLeft: 10,
                              }}
                            >
                              <Text
                                style={{
                                  color: theme.colors.primaryTextColor,
                                  fontSize: 20,
                                  width: "15%", // Adjust based on typical content width
                                  textAlign: "right",
                                }}
                              >
                                {Math.round(servingSizeOption?.weight)}
                              </Text>
                              <Text
                                style={{
                                  color: theme.colors.primaryTextColor,
                                  fontSize: 20,
                                  width: "20%", // Ensure this fits "grams"
                                  paddingLeft: 5,
                                }}
                              >
                                grams
                              </Text>
                              <Text
                                style={{
                                  color: theme.colors.primaryTextColor,
                                  fontSize: 20,
                                  width: "10%", // Minimal space for "="
                                  paddingLeft: 5,
                                  textAlign: "center",
                                }}
                              >
                                =
                              </Text>
                              <Text
                                style={{
                                  color: theme.colors.primaryTextColor,
                                  fontSize: 20,
                                  width: "5%", // Minimal space for "1"
                                  paddingLeft: 5,
                                }}
                              >
                                1
                              </Text>
                              <Text
                                style={{
                                  color: theme.colors.primaryTextColor,
                                  fontSize: 20,
                                  width: "50%", // Adjust based on the largest expected label
                                  paddingLeft: 5,
                                }}
                              >
                                {servingSizeOption?.label}
                              </Text>
                            </View>

                            <View
                              style={{
                                width: "15%", // Ensure enough space for the check icon without squeezing
                                alignItems: "flex-end",
                                paddingRight: 5,
                              }}
                            >
                              {selectedServing === servingSizeOption ? (
                                <Feather
                                  name="check-square"
                                  size={28}
                                  color={theme.colors.primary}
                                />
                              ) : null}
                            </View>
                          </TouchableOpacity>
                        )
                      )}
                  </TouchableOpacity>
                </ScrollView>
              </View>
            </View>
          )}
          {isSelectMealVisible && (
            <View
              style={{
                position: "absolute",
                bottom: 0,
                right: 0,
                left: 0,
                top: "66%",
                backgroundColor: theme.colors.screenBackground,
                zIndex: 2,
              }}
            >
              <View style={{ flex: 1 }}>
                <View
                  style={{
                    height: "20%",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    paddingHorizontal: 10,
                    borderBottomWidth: 1,
                    borderBottomColor: "gray",
                  }}
                >
                  <Text
                    style={{
                      color: theme.colors.primaryTextColor,
                      fontSize: 20,
                    }}
                  >
                    Select Meal
                  </Text>
                  <IconButton
                    icon="close"
                    iconColor={theme.colors.primaryTextColor}
                    size={26}
                    onPress={toggleSelectMeal}
                  />
                </View>
                <ScrollView>
                  <TouchableOpacity activeOpacity={1}>
                    {activeFoodItem &&
                      selectedMealSection &&
                      mealSections
                        ?.filter(
                          (mealOption) =>
                            mealOption.name && mealOption.id !== "Water"
                        )
                        .map((mealOption, index) => (
                          <TouchableOpacity
                            key={`${mealOption?.id}_${index}`}
                            onPress={() => handleSelectMeal(mealOption)}
                            style={{
                              width: "100%",
                              flexDirection: "row",
                              alignItems: "center",
                              backgroundColor:
                                selectedMealSection?.id === mealOption?.id
                                  ? theme.colors.surface
                                  : "transparent",
                              marginVertical: 5,
                              padding: 10,
                            }}
                          >
                            <View style={{ flex: 2 }}>
                              <Text
                                style={{
                                  color: theme.colors.primaryTextColor,
                                  fontSize: 20,
                                }}
                              >
                                {mealOption?.name}
                              </Text>
                            </View>
                            <View
                              style={{
                                flex: 1,
                                alignItems: "flex-end",
                                paddingRight: 5,
                              }}
                            >
                              {selectedMealSection?.id === mealOption?.id ? (
                                <Feather
                                  name="check-square"
                                  size={28}
                                  color={theme.colors.primary}
                                />
                              ) : null}
                            </View>
                          </TouchableOpacity>
                        ))}
                  </TouchableOpacity>
                </ScrollView>
              </View>
            </View>
          )}
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

// Sub-components

const NutritionFactsRow = ({ label, quantity, unit, totalDaily, color }) => {
  const styles = foodNutrientModalStyles();

  return (
    <View style={styles.nutritionFactsRow}>
      <View style={styles.nutritionalFactsLabelContainer}>
        <Text style={styles.nutritionFactsLabel}>{label}</Text>
      </View>
      <View style={styles.nutritionalFactsValuesContainer}>
        <Text style={{ ...styles.nutritionFactsValue, color: color }}>
          {quantity} {unit}
        </Text>
        <Text style={{ ...styles.nutritionFactsDailyValue, color: color }}>
          {totalDaily}%
        </Text>
      </View>
    </View>
  );
};

const NutritionFactsIndentedRow = ({
  label,
  quantity,
  unit,
  totalDaily,
  color,
}) => {
  const styles = foodNutrientModalStyles();

  return (
    <View style={styles.nutritionFactsRow}>
      <View style={styles.nutritionalFactsLabelContainer}>
        <Text style={styles.nutritionFactsIndentLabel}>{label}</Text>
      </View>
      <View style={styles.nutritionalFactsValuesContainer}>
        <Text style={{ ...styles.nutritionFactsIndentValue, color: color }}>
          {quantity} {unit}
        </Text>
        <Text
          style={{ ...styles.nutritionFactsIndentDailyValue, color: color }}
        >
          {totalDaily}%
        </Text>
      </View>
    </View>
  );
};

const ServingSizeRow = ({
  isVisible,
  toggleSelectServing,
  label,
  selectedServing,
  isCustomMeal,
}) => {
  const { theme } = useThemeContext();
  const styles = foodNutrientModalStyles();

  return (
    <View style={styles.rowContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <TouchableOpacity
        style={{
          width: "40%",
          alignItems: "center",
          borderWidth: 1,
          borderColor: isVisible ? theme.colors.primary : "gray",
          borderRadius: 8,
          paddingVertical: 10,
          paddingHorizontal: 20,
        }}
        onPress={toggleSelectServing}
        disabled={isCustomMeal}
      >
        {isCustomMeal ? (
          <Text style={{ color: theme.colors.primaryTextColor }}>1 meal</Text>
        ) : (
          <Text style={{ color: theme.colors.primaryTextColor }}>
            1 {selectedServing?.label}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const NumberOfServingsRow = ({
  label,
  keyboardType,
  numberOfServings,
  updateNumberOfServings,
  hideSelectServingAndSelectMealOnTextInputFocus,
}) => {
  const styles = foodNutrientModalStyles();

  // Convert NaN or null to an empty string for better user experience
  const formattedNumberOfServings =
    numberOfServings !== null && !isNaN(numberOfServings)
      ? numberOfServings.toString()
      : "";

  return (
    <View style={styles.rowContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <TextInput
        onFocus={hideSelectServingAndSelectMealOnTextInputFocus}
        keyboardType={keyboardType}
        style={{ ...styles.textInput, width: "40%" }}
        value={formattedNumberOfServings}
        onChangeText={(newNumberOfServings) =>
          updateNumberOfServings(newNumberOfServings)
        }
      />
    </View>
  );
};

const MealRow = ({
  isVisible,
  toggleSelectMeal,
  label,
  selectedMealOption,
}) => {
  const { theme } = useThemeContext();
  const styles = foodNutrientModalStyles();

  return (
    <View style={styles.rowContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <TouchableOpacity
        style={{
          width: "40%",
          alignItems: "center",
          borderWidth: 1,
          borderColor: isVisible ? theme.colors.primary : "gray",
          borderRadius: 8,
          paddingVertical: 10,
          paddingHorizontal: 20,
        }}
        onPress={toggleSelectMeal}
      >
        <Text style={{ color: theme.colors.primaryTextColor }}>
          {selectedMealOption?.name}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default FoodNutrientModal;
