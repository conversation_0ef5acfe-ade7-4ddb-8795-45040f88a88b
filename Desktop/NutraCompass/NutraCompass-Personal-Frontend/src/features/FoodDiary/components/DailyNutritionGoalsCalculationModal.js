import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  ScrollView,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
} from "react-native";
import { Image } from "expo-image";
import { Card, Button, TextInput, Snackbar } from "react-native-paper";
import * as Haptics from "expo-haptics";
import Modal from "react-native-modal";
import Feather from "react-native-vector-icons/Feather";
import dailyNutritionGoalsCalculationModalStyles from "./styles/dailyNutritionGoalsCalculationModalStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useNutritionProgram } from "../../NutritionalProgram/context/NutritionProgramContext.js";

const ResultsSlideContent = React.memo(
  ({
    goal,
    sex,
    heightFeet,
    heightInches,
    weight,
    age,
    bodyFatPercentageRange,
    activityLevel,
    theme,
    generatedNutritionalGoals,
  }) => {
    const styles = dailyNutritionGoalsCalculationModalStyles();

    return (
      <ScrollView
        style={{ height: "100%", width: "100%" }}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: 200,
        }}
      >
        <TouchableOpacity activeOpacity={1} style={{ flexGrow: 1 }}>
          {/* Avatar and Floating Cards Section */}
          <View style={{ alignItems: "center", gap: 8 }}>
            {/* Floating Cards Around the Avatar */}
            <View
              style={{
                flexDirection: "row",
                flexWrap: "wrap",
                justifyContent: "center",
                width: "100%",
                gap: 8,
              }}
            >
              {/* Floating Cards */}
              <View
                style={{
                  paddingHorizontal: 8,
                  paddingVertical: 8,
                  backgroundColor: theme.colors.surface,
                  borderRadius: 8,
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  minWidth: 100,
                }}
              >
                <Text style={[styles.cardText, { fontSize: 11 }]}>
                  Goal: {goal}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: 8,
                  paddingVertical: 8,
                  backgroundColor: theme.colors.surface,
                  borderRadius: 8,
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  minWidth: 100,
                }}
              >
                <Text style={[styles.cardText, { fontSize: 11 }]}>
                  Sex: {sex || "Not specified"}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: 8,
                  paddingVertical: 8,
                  backgroundColor: theme.colors.surface,
                  borderRadius: 8,
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  minWidth: 100,
                }}
              >
                <Text style={[styles.cardText, { fontSize: 11 }]}>
                  Height: {heightFeet} ft {heightInches} in
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: 8,
                  paddingVertical: 8,
                  backgroundColor: theme.colors.surface,
                  borderRadius: 8,
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  minWidth: 100,
                }}
              >
                <Text style={[styles.cardText, { fontSize: 11 }]}>
                  Weight: {weight} lbs
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: 8,
                  paddingVertical: 8,
                  backgroundColor: theme.colors.surface,
                  borderRadius: 8,
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  minWidth: 100,
                }}
              >
                <Text style={[styles.cardText, { fontSize: 11 }]}>
                  Age: {age} years
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: 8,
                  paddingVertical: 8,
                  backgroundColor: theme.colors.surface,
                  borderRadius: 8,
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  minWidth: 100,
                }}
              >
                <Text style={[styles.cardText, { fontSize: 11 }]}>
                  Body Fat: {bodyFatPercentageRange || "Not specified"}%
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: 8,
                  paddingVertical: 8,
                  backgroundColor: theme.colors.surface,
                  borderRadius: 8,
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  minWidth: 100,
                }}
              >
                <Text style={[styles.cardText, { fontSize: 11 }]}>
                  Activity Level:{" "}
                  {activityLevel
                    ? activityLevel.replace(/\s*\([^)]*\)/g, "") // Removes text between parentheses
                    : "Not specified"}
                </Text>
              </View>
            </View>

            {/* User Avatar in the center */}
            {/* <View style={{ flexDirection: "row", paddingTop: 8 }}>
              <View style={{ flex: 1, alignItems: "center" }}>
                <Image
                  source={require("../../../../assets/BodyFatImages/1.png")}
                  style={{
                    width: 120,
                    height: 120,
                    borderRadius: 60,
                    backgroundColor: "#ddd", // Placeholder background, change as needed
                  }}
                />
              </View> 
            </View> */}
          </View>

          {/* Nutritional Goals Section When There Is No Goal Weight, Start Date, nor End Date */}
          {generatedNutritionalGoals && (
            <View style={{ paddingHorizontal: 16, marginTop: 16 }}>
              <Card style={{ marginBottom: 20 }}>
                <Card.Title title="Your Nutritional Goals" />
                <Card.Content style={{ gap: 6 }}>
                  {/* Daily Calories */}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: theme.colors.screenBackground,
                      padding: 10,
                      borderRadius: 8,
                    }}
                  >
                    <Text style={[styles.cardText]}>Calories</Text>
                    <Text
                      style={[
                        styles.inputValue,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {generatedNutritionalGoals?.dailyCalories.toLocaleString() ||
                        "Not calculated yet"}
                    </Text>
                  </View>

                  {/* Carbs */}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: theme.colors.screenBackground,
                      padding: 10,
                      borderRadius: 8,
                    }}
                  >
                    <Text style={[styles.cardText]}>Carbs</Text>
                    <Text
                      style={[
                        styles.inputValue,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {`${generatedNutritionalGoals?.carbGrams} grams` ||
                        "Not calculated yet"}{" "}
                      ({generatedNutritionalGoals?.carbPercentage * 100}%)
                    </Text>
                  </View>

                  {/* Protein */}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: theme.colors.screenBackground,
                      padding: 10,
                      borderRadius: 8,
                    }}
                  >
                    <Text style={[styles.cardText]}>Protein</Text>
                    <Text
                      style={[
                        styles.inputValue,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {`${generatedNutritionalGoals?.proteinGrams} grams` ||
                        "Not calculated yet"}{" "}
                      ({generatedNutritionalGoals?.proteinPercentage * 100}%)
                    </Text>
                  </View>

                  {/* Fats */}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: theme.colors.screenBackground,
                      padding: 10,
                      borderRadius: 8,
                    }}
                  >
                    <Text style={[styles.cardText]}>Fats</Text>
                    <Text
                      style={[
                        styles.inputValue,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {`${generatedNutritionalGoals?.fatGrams} grams` ||
                        "Not calculated yet"}{" "}
                      ({generatedNutritionalGoals?.fatPercentage * 100}%)
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            </View>
          )}
        </TouchableOpacity>
      </ScrollView>
    );
  }
);

const DailyNutritionGoalsCalculationModal = ({ isVisible, closeModal }) => {
  const styles = dailyNutritionGoalsCalculationModalStyles();
  const { theme } = useThemeContext();
  const { calculateNutritionalGoals, generatedNutritionalGoals } =
    useNutritionProgram();
  const { setCalorieAndMacroGoals } = useUserSettings();

  const [formState, setFormState] = useState({
    goal: "Maintain",
    sex: "",
    heightFeet: "",
    heightInches: "",
    weight: "",
    age: "",
    bodyFatPercentageRange: null,
    activityLevel: "",
  });

  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [currentSlide, setCurrentSlide] = useState(0);
  const [heightFeetFocused, setHeightFeetFocused] = useState(false);
  const [heightInchesFocused, setHeightInchesFocused] = useState(false);
  const [weightFocused, setWeightFocused] = useState(false);
  const [ageFocused, setAgeFocused] = useState(false);

  useEffect(() => {
    if (!isVisible) {
      resetFormFields();
      setSnackbarMessage("");
      setSnackbarVisible(false);
      setCurrentSlide(0); // Reset to the first slide
    }
  }, [isVisible]);

  const resetFormFields = () => {
    setFormState({
      goal: "Maintain",
      sex: "",
      heightFeet: "",
      heightInches: "",
      weight: "",
      age: "",
      bodyFatPercentageRange: null,
      activityLevel: "",
    });
  };

  const updateFormState = (key, value) => {
    setFormState((prevState) => {
      if (prevState[key] === value) return prevState;
      return { ...prevState, [key]: value };
    });
  };

  const handleOptionSelect = (key, value) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    updateFormState(key, value);
  };

  const handleSlideChange = (newSlide) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (newSlide >= 0 && newSlide < slides.length) {
      setHeightFeetFocused(false);
      setHeightInchesFocused(false);
      setWeightFocused(false);
      setAgeFocused(false);
      setCurrentSlide(newSlide);
      Keyboard.dismiss(); // Dismiss the keyboard when changing slides
    } else {
      console.error("Invalid slide index:", newSlide);
    }
  };

  const handleUpdateNutritionalGoals = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      if (
        generatedNutritionalGoals.dailyCalories &&
        generatedNutritionalGoals.proteinPercentage &&
        generatedNutritionalGoals.carbPercentage &&
        generatedNutritionalGoals.fatPercentage
      ) {
        setCalorieAndMacroGoals({
          calorieGoal: generatedNutritionalGoals.dailyCalories,
          proteinPercentage: generatedNutritionalGoals.proteinPercentage * 100,
          carbPercentage: generatedNutritionalGoals.carbPercentage * 100,
          fatPercentage: generatedNutritionalGoals.fatPercentage * 100,
        });
        setSnackbarMessage("Nutritional goals updated successfully!");
        setSnackbarVisible(true);
      }
    } catch (error) {
      console.error("Error updating nutritional goals:", error);
      setSnackbarMessage("Failed to update nutritional goals.");
      setSnackbarVisible(true);
    }
  };

  const calculateDailyNutritionalGoals = useCallback(async () => {
    try {
      // Ensure all necessary fields are provided
      if (
        !formState.sex ||
        !formState.heightFeet ||
        !formState.heightInches ||
        !formState.weight ||
        !formState.age ||
        !formState.activityLevel ||
        !formState.goal
      ) {
        console.error("Missing or invalid input fields");
        return;
      }

      // Call the backend for calculating daily nutritional goals
      await calculateNutritionalGoals(
        formState.sex,
        formState.heightFeet,
        formState.heightInches,
        formState.weight,
        formState.age,
        formState.activityLevel,
        formState.goal
      );
    } catch (error) {
      console.error("Error calculating nutritional goals:", error);
    }
  }, [
    formState.sex,
    formState.heightFeet,
    formState.heightInches,
    formState.weight,
    formState.age,
    formState.activityLevel,
    formState.goal,
  ]);

  const bodyTypes = [
    {
      id: 1,
      value: 8,
      description: "Very Lean\n5-10% body fat",
      image: require("../../../../assets/BodyFatImages/1.png"),
    },
    {
      id: 2,
      value: 13,
      description: "Lean\n11-15% body fat",
      image: require("../../../../assets/BodyFatImages/2.png"),
    },
    {
      id: 3,
      value: 18,
      description: "Moderately Lean\n16-20% body fat",
      image: require("../../../../assets/BodyFatImages/3.png"),
    },
    {
      id: 4,
      value: 23,
      description: "Average\n21-25% body fat",
      image: require("../../../../assets/BodyFatImages/4.png"),
    },
    {
      id: 5,
      value: 28,
      description: "Above Average\n26-30% body fat",
      image: require("../../../../assets/BodyFatImages/5.png"),
    },
    {
      id: 6,
      value: 33,
      description: "Overweight\n31-35% body fat",
      image: require("../../../../assets/BodyFatImages/6.png"),
    },
    {
      id: 7,
      value: 40,
      description: "Obese\n36%+ body fat",
      image: require("../../../../assets/BodyFatImages/7.png"),
    },
  ];

  const slides = [
    {
      title: "Goals Based Nutritional Calculator ",
      content: (
        <View style={{ alignItems: "center", padding: 20 }}>
          <Image
            source={require("../../../../assets/GoalsBasedNutritionalCalculator.png")}
            style={{
              width: "90%",
              height: 300,
              contentFit: "cover",
              borderRadius: 30,
            }}
          />
          <Card style={{ marginVertical: 20, marginHorizontal: 20 }}>
            <Card.Content>
              <Text
                style={{ fontSize: 18, textAlign: "center", color: "gray" }}
              >
                Get ready to enter some key details about your body and
                lifestyle. We'll use this information to estimate your daily
                calorie needs and suggest the best macronutrient distribution to
                help you achieve your goals.
              </Text>
            </Card.Content>
          </Card>
        </View>
      ),
    },
    {
      title: "Your Sex",
      content: (
        <View>
          <TouchableOpacity onPress={() => handleOptionSelect("sex", "Male")}>
            <Card
              style={
                formState?.sex === "Male" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Male</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleOptionSelect("sex", "Female")}>
            <Card
              style={
                formState?.sex === "Female" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Female</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
        </View>
      ),
    },
    {
      title: "Your Height",
      content: (
        <View style={{ flexDirection: "row", gap: 20 }}>
          <View
            style={[
              {
                flex: 1,
                borderRadius: 10,
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              heightFeetFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: 28,
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: 10,
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="Feet"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.heightFeet}
              onFocus={() => setHeightFeetFocused(true)}
              onBlur={() => setHeightFeetFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^[1-8]$/.test(text)) {
                  updateFormState("heightFeet", text);
                }
              }}
            />
          </View>
          <View
            style={[
              {
                flex: 1,
                borderRadius: 10,
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              heightInchesFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: 28,
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: 10,
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="Inches"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.heightInches}
              onFocus={() => setHeightInchesFocused(true)}
              onBlur={() => setHeightInchesFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^([0-9]|1[0-1])$/.test(text)) {
                  updateFormState("heightInches", text);
                }
              }}
            />
          </View>
        </View>
      ),
    },
    {
      title: "Your Bodyweight",
      content: (
        <View style={{ flexDirection: "row", gap: 20 }}>
          <View
            style={[
              {
                flex: 1,
                borderRadius: 10,
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              weightFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: 28,
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: 10,
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="lbs"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.weight}
              onFocus={() => setWeightFocused(true)}
              onBlur={() => setWeightFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^[0-9]{1,3}$/.test(text)) {
                  updateFormState("weight", text);
                }
              }}
            />
          </View>
        </View>
      ),
    },
    {
      title: "Your Age",
      content: (
        <View style={{ flexDirection: "row", gap: 20 }}>
          <View
            style={[
              {
                flex: 1,
                borderRadius: 10,
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              ageFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: 28,
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: 10,
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="# years old"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.age}
              onFocus={() => setAgeFocused(true)}
              onBlur={() => setAgeFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^[0-9]{1,2}$/.test(text)) {
                  updateFormState("age", text);
                }
              }}
            />
          </View>
        </View>
      ),
    },
    {
      title: "Your Body Type",
      content: (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: 12 }}
        >
          {bodyTypes.map((bodyType) => (
            <TouchableOpacity
              key={bodyType.id}
              onPress={() =>
                updateFormState(
                  "bodyFatPercentageRange",
                  bodyType.value.toString()
                )
              }
              style={[
                formState?.bodyFatPercentageRange ===
                  bodyType.value.toString() && {
                  backgroundColor: theme.colors.primary,
                },
                {
                  borderWidth: 1,
                  flex: 1,
                  aspectRatio: 3 / 4,
                  borderRadius: 10,
                  justifyContent: "center",
                  alignItems: "center",
                  margin: 10,
                  padding: 10,
                },
              ]}
            >
              <Image
                source={bodyType.image}
                style={{ width: 175, height: 200, alignSelf: "center" }}
                contentFit="contain"
              />
              <Text
                style={[
                  styles.cardText,
                  { paddingBottom: 12 },
                  formState?.bodyFatPercentageRange ===
                    bodyType.value.toString(),
                ]}
              >
                {bodyType.description}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      ),
    },
    {
      title: "Your Activity Level",
      content: (
        <View
          style={{
            flexDirection: "row",
            flexWrap: "wrap",
            justifyContent: "space-between",
          }}
        >
          {[
            { value: "Sedentary (BMR x 0.2)", label: "Sedentary" },
            { value: "Lightly Active (BMR x 0.375)", label: "Lightly Active" },
            {
              value: "Moderately Active (BMR x 0.5)",
              label: "Moderately Active",
            },
            { value: "Very Active (BMR x 0.9)", label: "Very Active" },
          ].map((activity) => (
            <TouchableOpacity
              key={activity.value}
              onPress={() => updateFormState("activityLevel", activity.value)}
              style={[
                {
                  width: "48%",
                  borderRadius: 10,
                  padding: 10,
                  paddingVertical: 20,
                  justifyContent: "center",
                  alignItems: "center",
                  marginVertical: 10,
                  borderWidth: 1,
                  backgroundColor: theme.colors.surface,
                },
                formState?.activityLevel === activity.value && {
                  backgroundColor: theme.colors.primary,
                },
              ]}
            >
              <Text
                style={{
                  textAlign: "center",
                  fontSize: 14,
                  color: theme.colors.primaryTextColor,
                  paddingHorizontal: 5,
                }}
              >
                {activity.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      ),
    },
    {
      title: "I want to",
      content: (
        <View>
          <TouchableOpacity onPress={() => handleOptionSelect("goal", "Lose")}>
            <Card
              style={
                formState?.goal === "Lose" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Lose Weight</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleOptionSelect("goal", "Maintain")}
          >
            <Card
              style={
                formState?.goal === "Maintain"
                  ? styles.selectedCard
                  : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Maintain Weight</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleOptionSelect("goal", "Build")}>
            <Card
              style={
                formState?.goal === "Build" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Build Muscle</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
        </View>
      ),
    },
    {
      title: "Your Results",
      content: (
        <ResultsSlideContent
          goal={formState?.goal}
          sex={formState?.sex}
          heightFeet={formState?.heightFeet}
          heightInches={formState?.heightInches}
          weight={formState?.weight}
          age={formState?.age}
          bodyFatPercentageRange={formState?.bodyFatPercentageRange}
          activityLevel={formState?.activityLevel}
          theme={theme}
          generatedNutritionalGoals={generatedNutritionalGoals}
        />
      ),
    },
  ];

  return (
    <Modal
      isVisible={isVisible}
      style={{ flex: 1, height: "100%", width: "100%", margin: 0 }}
      avoidKeyboard={true}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView
          style={{
            flex: 1,
            flexDirection: "column",
            backgroundColor: theme.colors.screenBackground,
            alignItems: "center",
            paddingBottom: 0,
          }}
        >
          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                closeModal();
              }}
            >
              <Feather
                name="chevron-left"
                color={theme.colors.primaryTextColor}
                size={38}
              />
            </TouchableOpacity>
          </View>
          <KeyboardAvoidingView style={styles.contentContainer}>
            <View
              style={{
                flex: 1,
                paddingBottom: 6,
              }}
            >
              <View
                style={{
                  flex: 1,
                  gap:
                    currentSlide > 0 && currentSlide < slides.length - 1
                      ? 34
                      : 12,
                  paddingTop:
                    currentSlide > 0 && currentSlide < slides.length - 1
                      ? "5%"
                      : 0,
                }}
              >
                <Text
                  style={{
                    ...styles.label,
                    textAlign: "center",
                    paddingTop: currentSlide == slides.length - 1 ? 0 : 0,
                  }}
                >
                  {slides[currentSlide].title}
                </Text>
                <View>
                  <View>{slides[currentSlide].content}</View>
                </View>
              </View>

              <View style={styles.navigationButtons}>
                {currentSlide > 0 && currentSlide !== slides.length - 1 && (
                  <Button
                    style={styles.backButton}
                    mode="outlined"
                    onPress={() => {
                      handleSlideChange(currentSlide - 1);
                    }}
                  >
                    Back
                  </Button>
                )}
                <View style={{ flex: 1 }} />

                {currentSlide < slides.length - 1 && (
                  <>
                    {slides[currentSlide].title === "I want to" ? (
                      <Button
                        style={styles.nextButton}
                        mode="outlined"
                        onPress={() => {
                          if (
                            formState.goal === "Maintain" ||
                            formState.goal === "Build" ||
                            formState.goal === "Lose"
                          ) {
                            calculateDailyNutritionalGoals();
                            handleSlideChange(currentSlide + 1);
                          } else {
                            setSnackbarMessage("Please select a goal.");
                            setSnackbarVisible(true);
                          }
                        }}
                      >
                        Calculate
                      </Button>
                    ) : (
                      <Button
                        style={styles.nextButton}
                        mode="outlined"
                        onPress={() => {
                          // Validate input for the slide
                          if (
                            (currentSlide === 1 && !formState?.sex) ||
                            (currentSlide === 2 &&
                              (!formState?.heightFeet ||
                                !formState?.heightInches)) ||
                            (currentSlide === 3 && !formState?.weight) ||
                            (currentSlide === 4 && !formState?.age) ||
                            (currentSlide === 5 &&
                              !formState?.bodyFatPercentageRange) ||
                            (currentSlide === 6 && !formState?.activityLevel)
                          ) {
                            setSnackbarMessage("Please provide an answer.");
                            setSnackbarVisible(true);
                          } else {
                            handleSlideChange(currentSlide + 1);
                          }
                        }}
                      >
                        Next
                      </Button>
                    )}
                  </>
                )}

                {currentSlide === slides.length - 1 && (
                  <Button
                    style={styles.nextButton}
                    mode="contained"
                    onPress={handleUpdateNutritionalGoals} // Method for updating nutritional goals
                  >
                    Update Nutritional Goals
                  </Button>
                )}
              </View>
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </TouchableWithoutFeedback>
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: "OK",
          onPress: () => {},
        }}
        style={{ backgroundColor: theme.colors.surface }}
      >
        <Text
          style={{
            fontSize: 16,
            alignSelf: "center",
            color: theme.colors.primaryTextColor,
          }}
        >
          {snackbarMessage}
        </Text>
      </Snackbar>
    </Modal>
  );
};

export default DailyNutritionGoalsCalculationModal;
