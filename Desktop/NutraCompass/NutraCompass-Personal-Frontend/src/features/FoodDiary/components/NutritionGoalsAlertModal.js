import React from "react";
import { View, Text, TouchableOpacity, Modal } from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import * as Haptics from "expo-haptics";
import { MaterialIcons } from "@expo/vector-icons";

const NutritionGoalsAlertModal = ({
  isVisible,
  onConfirm,
  onCancel,
  onAlignWithProgram,
}) => {
  const { theme } = useThemeContext();

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0, 0, 0, 0.5)",
        }}
      >
        <View
          style={{
            width: "80%",
            backgroundColor: theme.colors.surface,
            borderRadius: 10,
            padding: 20,
            alignItems: "center",
          }}
        >
          {/* Alert Icon */}
          <MaterialIcons
            name="error-outline"
            size={30}
            color="red"
            style={{
              paddingBottom: 8,
            }}
          />
          <Text
            style={{
              fontSize: 16,
              fontWeight: "bold",
              marginBottom: 15,
              textAlign: "center",
              color: theme.colors.primaryTextColor,
            }}
          >
            Are you sure you want to update your nutritional goals?
          </Text>
          <Text
            style={{
              fontSize: 13,
              color: theme.colors.primaryTextColor,
              marginBottom: 20,
              textAlign: "center",
            }}
          >
            These goals do not align with the goals set by your active
            nutritional program.
          </Text>

          {/* Buttons */}
          <View style={{ width: "100%", gap: 18 }}>
            {/* Align with Program Button */}
            <TouchableOpacity
              style={{
                alignItems: "center",
                padding: 10,
                backgroundColor: theme.colors.screenBackground,
                borderRadius: 5,
                borderWidth: 1,
                borderColor: theme.colors.primaryTextColor,
              }}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                onAlignWithProgram();
              }}
            >
              <Text
                style={{
                  fontSize: 14,
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                }}
              >
                Align With My Nutritional Program
              </Text>
            </TouchableOpacity>

            {/* Cancel and Confirm Buttons */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              {/* Cancel Button */}
              <TouchableOpacity
                style={{
                  flex: 1,
                  alignItems: "center",
                  paddingVertical: 10,
                  marginRight: 10,
                  backgroundColor: theme.colors.screenBackground,
                  borderRadius: 5,
                }}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  onCancel();
                }}
              >
                <Text
                  style={{
                    fontSize: 14,
                    color: "red",
                  }}
                >
                  Cancel
                </Text>
              </TouchableOpacity>

              {/* Confirm Button */}
              <TouchableOpacity
                style={{
                  flex: 1,
                  alignItems: "center",
                  paddingVertical: 10,
                  marginLeft: 10,
                  backgroundColor: theme.colors.screenBackground,
                  borderRadius: 5,
                }}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  onConfirm();
                }}
              >
                <Text
                  style={{
                    fontSize: 14,
                    color: "green",
                  }}
                >
                  Confirm
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default NutritionGoalsAlertModal;
