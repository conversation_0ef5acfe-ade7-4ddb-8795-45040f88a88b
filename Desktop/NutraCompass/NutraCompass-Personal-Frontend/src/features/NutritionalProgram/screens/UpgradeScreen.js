// screens/UpgradeScreen.js

import React, { useState } from "react";
import { View, Text, StyleSheet } from "react-native";
import { <PERSON><PERSON>, Card, Snackbar } from "react-native-paper";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import axios from "axios";
import Configs from "../../../../configs.js";
import * as Haptics from "expo-haptics";

const UpgradeScreen = () => {
  const { theme } = useThemeContext();
  const { user } = useAuth();
  const userId = user?.uid;
  const apiUrl = Configs.NutraCompass_API_URL;

  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleUpgrade = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setLoading(true);
    try {
      // Integrate with payment gateway (e.g., Stripe) here
      // For demonstration, we'll simulate a successful upgrade
      const response = await axios.post(`${apiUrl}/v1/upgrade`, {
        userId,
        // Include payment details as necessary
      });

      if (response.data.success) {
        setSnackbarMessage(
          "Upgrade successful! You now have access to premium features."
        );
        setSnackbarVisible(true);
        // Optionally, refresh feature flags
      } else {
        throw new Error("Upgrade failed.");
      }
    } catch (err) {
      console.error("Upgrade error:", err);
      setSnackbarMessage("Upgrade failed. Please try again.");
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: theme.colors.screenBackground },
      ]}
    >
      <Text style={[styles.title, { color: theme.colors.primaryTextColor }]}>
        Upgrade to Premium
      </Text>
      <Card style={styles.card}>
        <Card.Content>
          <Text
            style={[
              styles.description,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            Unlock advanced features like AI-generated meal plans and
            voice-assisted program creation to enhance your nutritional journey.
          </Text>
        </Card.Content>
      </Card>
      <Button
        mode="contained"
        onPress={handleUpgrade}
        loading={loading}
        disabled={loading}
        style={styles.upgradeButton}
      >
        Upgrade Now
      </Button>
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

export default UpgradeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
  },
  card: {
    width: "100%",
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    textAlign: "center",
  },
  upgradeButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
});
