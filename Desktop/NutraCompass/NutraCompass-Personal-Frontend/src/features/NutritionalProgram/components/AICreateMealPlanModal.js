// components/AICreateMealPlanModal.js

import React, { useState } from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Snackbar } from "react-native-paper";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { useFeatureFlags } from "../../../context/FeatureFlagsContext.js";
import * as Haptics from "expo-haptics";
import GeneratedMealPlanView from "./GeneratedMealPlanView.js";

const AICreateMealPlanModal = ({ isVisible, closeModal }) => {
  const { theme } = useThemeContext();
  const { createNutritionProgram, loading, error } = useNutritionProgram();
  const { features } = useFeatureFlags();

  const [mealPlan, setMealPlan] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");

  const handleGenerateMealPlan = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      // Assuming there's an API endpoint for AI meal plan generation
      const response = await createNutritionProgram({ ai: true });
      if (response) {
        setMealPlan(response.mealPlan); // Adjust based on your API response
        setSnackbarMessage("AI-generated meal plan created successfully!");
        setSnackbarVisible(true);
      } else {
        throw new Error("AI meal plan creation failed.");
      }
    } catch (err) {
      console.error("Error generating AI meal plan:", err);
      setSnackbarMessage("Failed to generate AI meal plan.");
      setSnackbarVisible(true);
    }
  };

  return (
    <Modal
      visible={isVisible}
      onDismiss={closeModal}
      contentContainerStyle={[
        styles.modalContainer,
        { backgroundColor: theme.colors.surface },
      ]}
      animationType="slide"
      transparent={true}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <Text style={[styles.title, { color: theme.colors.primaryTextColor }]}>
          AI-Generated Meal Plan
        </Text>
        <Card style={styles.card}>
          <Card.Content>
            <Text>
              Let our AI generate a personalized meal plan tailored to your
              nutritional goals and preferences.
            </Text>
          </Card.Content>
        </Card>
        <Button
          mode="contained"
          onPress={handleGenerateMealPlan}
          loading={loading}
          disabled={loading}
          style={styles.generateButton}
        >
          Generate Meal Plan
        </Button>
        {mealPlan && <GeneratedMealPlanView mealPlan={mealPlan} />}
        <Button mode="outlined" onPress={closeModal} style={styles.closeButton}>
          Close
        </Button>
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
        >
          {snackbarMessage}
        </Snackbar>
      </ScrollView>
    </Modal>
  );
};

export default AICreateMealPlanModal;

const styles = StyleSheet.create({
  modalContainer: {
    margin: 20,
    borderRadius: 10,
    padding: 16,
    // Adjust height as needed
  },
  scrollContainer: {
    paddingVertical: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  card: {
    marginBottom: 20,
    padding: 16,
  },
  generateButton: {
    marginBottom: 20,
  },
  closeButton: {
    marginTop: 10,
  },
});
