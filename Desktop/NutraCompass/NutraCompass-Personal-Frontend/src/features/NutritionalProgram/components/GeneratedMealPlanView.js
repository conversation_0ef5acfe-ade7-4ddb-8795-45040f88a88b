// components/GeneratedMealPlanView.js

import React from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { Card } from "react-native-paper";
import { useThemeContext } from "../../../context/ThemeContext.js";

const GeneratedMealPlanView = ({ mealPlan }) => {
  const { theme } = useThemeContext();

  return (
    <ScrollView style={styles.container}>
      <Text style={[styles.title, { color: theme.colors.primaryTextColor }]}>
        Your AI-Generated Meal Plan
      </Text>
      {mealPlan.days.map((day, idx) => (
        <Card key={idx} style={styles.dayCard}>
          <Card.Title title={`Day ${idx + 1}`} />
          <Card.Content>
            {day.meals.map((meal, mIdx) => (
              <View key={mIdx} style={styles.mealContainer}>
                <Text
                  style={[
                    styles.mealTitle,
                    { color: theme.colors.primaryTextColor },
                  ]}
                >
                  {meal.name}
                </Text>
                <Text
                  style={[
                    styles.mealDetails,
                    { color: theme.colors.primaryTextColor },
                  ]}
                >
                  {meal.description}
                </Text>
              </View>
            ))}
          </Card.Content>
        </Card>
      ))}
    </ScrollView>
  );
};

export default GeneratedMealPlanView;

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  dayCard: {
    marginBottom: 12,
  },
  mealContainer: {
    marginBottom: 8,
  },
  mealTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  mealDetails: {
    fontSize: 14,
    marginLeft: 8,
  },
});
