import React, { useState, useMemo } from "react";
import {
  View,
  TouchableOpacity,
  Text,
  Alert,
  Linking,
  StyleSheet,
} from "react-native";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { CustomImageUploadAlert } from "../../../components/CustomImageUploadAlert.js";
import { MaterialCommunityIcons } from "@expo/vector-icons";

export default function ProgressPicture({ size, checkpointId, isDisabled }) {
  const { theme } = useThemeContext();
  const {
    uploadProgressPicture,
    removeProgressPicture,
    activeNutritionalProgram,
  } = useNutritionProgram();
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Find the checkpoint by ID and get the progress picture URL
  const checkpoint = useMemo(
    () =>
      activeNutritionalProgram.checkpoints.find((c) => c.week === checkpointId),
    [checkpointId, activeNutritionalProgram.checkpoints]
  );

  const progressPictureUrl = checkpoint ? checkpoint.progressPictureUrl : null;

  const handlePickImage = async () => {
    setIsModalVisible(true);
  };

  const handleImagePick = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Alert.alert(
        "Permission Required",
        "Please enable access to your photos in your settings.",
        [
          { text: "OK" },
          { text: "Settings", onPress: () => Linking.openSettings() },
        ]
      );
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images, // Ensure you're picking images only
      allowsEditing: true,
      quality: 1, // Full quality image
    });

    if (
      pickerResult.canceled ||
      !pickerResult.assets ||
      !pickerResult.assets.length
    ) {
      return; // Handle cancellation properly
    }

    const imageUri = pickerResult.assets[0].uri;

    // Proceed with uploading the image
    if (imageUri) {
      await uploadProgressPicture(imageUri, checkpointId);
      setIsModalVisible(false);
    }
  };

  const handleTakePhoto = async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

    if (!permissionResult.granted) {
      Alert.alert(
        "Permission Required",
        "Please enable access to your camera in your settings.",
        [
          { text: "OK" },
          { text: "Settings", onPress: () => Linking.openSettings() },
        ]
      );
      return;
    }

    const pickerResult = await ImagePicker.launchCameraAsync();
    if (pickerResult.cancelled) {
      return;
    }

    if (pickerResult.uri) {
      await uploadProgressPicture(pickerResult.uri, checkpointId);
      setIsModalVisible(false);
    }
  };

  const handleRemovePhoto = async () => {
    Alert.alert(
      "Remove Progress Picture",
      "Are you sure you want to remove this progress picture?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          onPress: async () => {
            await removeProgressPicture(checkpointId);
            setIsModalVisible(false);
          },
          style: "destructive",
        },
      ]
    );
  };

  return (
    <View>
      <TouchableOpacity
        style={{
          height: size,
          width: size - size / 4,
          borderRadius: 12,
          borderWidth: 1,
          borderColor: progressPictureUrl ? "transparent" : theme.colors.shadow,
          overflow: "hidden",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: theme.colors.surface,
        }}
        onPress={handlePickImage}
        disabled={isDisabled}
      >
        {progressPictureUrl ? (
          <Image
            source={{ uri: progressPictureUrl }}
            style={{ height: size, width: size - size / 4, borderRadius: 12 }}
          />
        ) : (
          <MaterialCommunityIcons
            name="camera"
            size={40}
            color={theme.colors.primary}
          />
        )}
      </TouchableOpacity>
      <CustomImageUploadAlert
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onPickImage={handleImagePick}
        onTakePhoto={handleTakePhoto}
        onRemoveImage={handleRemovePhoto}
        hasImage={!!progressPictureUrl}
      />
    </View>
  );
}
