import React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { Card, Button } from "react-native-paper";
import Feather from "react-native-vector-icons/Feather";
import { useNavigation } from "@react-navigation/native";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";

const NutritionalProgramCard = ({ theme }) => {
  const navigation = useNavigation();
  const { activeNutritionalProgram } = useNutritionProgram();

  return (
    <View style={{ width: "100%", paddingHorizontal: 10 }}>
      <Card style={{ padding: 5 }}>
        <Card.Content>
          <TouchableOpacity
            onPress={() => navigation.navigate("Nutritional Program")}
            style={{ flexDirection: "row" }}
          >
            {/* Left side: Title, subtext, optional button */}
            <View style={{ flex: 1, alignItems: "flex-start", rowGap: 12 }}>
              {/* Image + Title/Subtext row */}
              <View
                style={{ flexDirection: "row", gap: 10, alignItems: "center" }}
              >
                {/* Replace the Icon with a local image */}
                <Image
                  source={require("../../../../assets/NutritionalProgramIcon.png")}
                  style={{ width: 45, height: 45 }}
                  resizeMode="contain"
                />
                <View style={{ rowGap: 5 }}>
                  <Text
                    style={{
                      color: theme.colors.primaryTextColor,
                      fontSize: 16,
                      fontWeight: "600", // or "bold"
                    }}
                  >
                    Nutritional Program
                  </Text>
                  {activeNutritionalProgram ? (
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 13,
                      }}
                    >
                      Active Program:{" "}
                      {activeNutritionalProgram?.programName ||
                        "Untitled Program"}
                    </Text>
                  ) : (
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 13,
                      }}
                    >
                      No active program yet
                    </Text>
                  )}
                </View>
              </View>
            </View>

            {/* Right side: Chevron icon */}
            <View style={{ alignItems: "flex-end", justifyContent: "center" }}>
              <Feather name="chevron-right" color="gray" size={24} />
            </View>
          </TouchableOpacity>
        </Card.Content>
      </Card>
    </View>
  );
};

export default NutritionalProgramCard;
