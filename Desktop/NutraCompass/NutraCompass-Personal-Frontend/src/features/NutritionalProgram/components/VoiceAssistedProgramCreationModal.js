// VoiceAssistedProgramCreationModal.js

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from "react-native";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Snackbar } from "react-native-paper";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { useFeatureFlags } from "../../../context/FeatureFlagsContext.js";
import * as Haptics from "expo-haptics";
// import Voice from "@react-native-voice/voice";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
// Enable custom formats like "MMMM D"
dayjs.extend(customParseFormat);

const VoiceAssistedProgramCreationModal = ({ isVisible, closeModal }) => {
  const { theme } = useThemeContext();
  const { createNutritionProgram } = useNutritionProgram();
  const { features } = useFeatureFlags();

  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");

  // By default, dayjs() is 'now'. .add(3, 'month') yields 3 months from now.
  const [formState, setFormState] = useState({
    goalWeight: "",
    startDate: new Date(),
    endDate: dayjs().add(3, "month").toDate(),
  });

  useEffect(() => {
    // Voice.onSpeechResults = onSpeechResultsHandler;
    // Voice.onSpeechError = onSpeechErrorHandler;
    return () => {
      // Voice.destroy().then(Voice.removeAllListeners);
    };
  }, []);

  // const onSpeechResultsHandler = (event) => {
  //   if (event.value && event.value.length > 0) {
  //     setTranscript(event.value[0]);
  //   }
  // };

  // const onSpeechErrorHandler = (event) => {
  //   console.error("Voice recognition error:", event.error);
  //   setSnackbarMessage("Voice recognition error. Please try again.");
  //   setSnackbarVisible(true);
  //   setIsListening(false);
  // };

  const handleStartListening = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      // await Voice.start("en-US");
      setIsListening(true);

      // Mocking voice input for testing
      setTimeout(() => {
        const mockTranscript =
          "I want to lose 20 pounds starting from January 1 to June 1";
        setTranscript(mockTranscript);
        processTranscript(mockTranscript);
      }, 2000);
    } catch (e) {
      console.error("Failed to start Voice recognition:", e);
      setSnackbarMessage("Failed to start voice recognition.");
      setSnackbarVisible(true);
    }
  };

  const handleStopListening = async () => {
    try {
      // await Voice.stop();
      setIsListening(false);
    } catch (e) {
      console.error("Failed to stop Voice recognition:", e);
      setSnackbarMessage("Failed to stop voice recognition.");
      setSnackbarVisible(true);
    }
  };

  /**
   * Helper to parse "January 1" into a Date, forcing the current year.
   * dayjs supports custom formats with the 'customParseFormat' plugin.
   */
  const parseDateWithCurrentYear = (input) => {
    // Parse strings like "January 1" as "MMMM D"
    // Then set the year to the current year.
    let parsed = dayjs(input, "MMMM D");
    // If parsing failed or gave an invalid date, parsed.isValid() would be false
    if (!parsed.isValid()) {
      return null;
    }
    // Force the year to the current one
    const currentYear = dayjs().year();
    parsed = parsed.year(currentYear);
    return parsed.toDate();
  };

  const processTranscript = (receivedTranscript) => {
    const weightMatch = receivedTranscript.match(/lose\s+(\d+)\s+pounds?/i);
    const startMatch = receivedTranscript.match(
      /starting\s+from\s+([a-zA-Z]+\s+\d{1,2})(?:st|nd|rd|th)?/i
    );
    const endMatch = receivedTranscript.match(
      /to\s+([a-zA-Z]+\s+\d{1,2})(?:st|nd|rd|th)?/i
    );

    if (weightMatch && startMatch && endMatch) {
      const goalWeight = parseInt(weightMatch[1], 10);
      const startDate = parseDateWithCurrentYear(startMatch[1]);
      const endDate = parseDateWithCurrentYear(endMatch[1]);

      if (!startDate || !endDate) {
        setSnackbarMessage("Couldn't parse dates. Please try again.");
        setSnackbarVisible(true);
        return;
      }

      setFormState({
        goalWeight,
        startDate,
        endDate,
      });

      // Proceed to create the program
      createProgram({ goalWeight, startDate, endDate });
    } else {
      setSnackbarMessage("Couldn't parse your input. Please try again.");
      setSnackbarVisible(true);
    }
  };

  const createProgram = async (data) => {
    try {
      const success = await createNutritionProgram({
        goalWeight: data.goalWeight,
        // Format the date as "YYYY-MM-DD"
        startDate: dayjs(data.startDate).format("YYYY-MM-DD"),
        endDate: dayjs(data.endDate).format("YYYY-MM-DD"),
      });
      if (success) {
        setSnackbarMessage(
          "Nutritional program created successfully via voice!"
        );
        setSnackbarVisible(true);
        closeModal();
      } else {
        throw new Error("Program creation failed.");
      }
    } catch (err) {
      console.error("Error creating program via voice:", err);
      setSnackbarMessage("Failed to create nutritional program.");
      setSnackbarVisible(true);
    }
  };

  return (
    <Modal
      visible={isVisible}
      onDismiss={closeModal}
      contentContainerStyle={[
        styles.modalContainer,
        { backgroundColor: theme.colors.surface },
      ]}
      animationType="slide"
      transparent={true}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <Text style={[styles.title, { color: theme.colors.primaryTextColor }]}>
          Voice-Assisted Program Creation
        </Text>
        <Card style={styles.card}>
          <Card.Content>
            <Text>
              Speak your goals clearly, and we'll create a personalized
              nutrition program for you.
            </Text>
          </Card.Content>
        </Card>
        <TouchableOpacity
          style={[
            styles.voiceButton,
            {
              backgroundColor: isListening
                ? theme.colors.primary
                : theme.colors.surface,
            },
          ]}
          onPress={isListening ? handleStopListening : handleStartListening}
        >
          <Text
            style={{
              color: isListening ? "#fff" : theme.colors.primaryTextColor,
            }}
          >
            {isListening ? "Stop Listening" : "Start Voice Input"}
          </Text>
        </TouchableOpacity>
        {transcript ? (
          <Text
            style={[
              styles.transcript,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            Transcript: {transcript}
          </Text>
        ) : null}
        <Button mode="outlined" onPress={closeModal} style={styles.closeButton}>
          Close
        </Button>
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
        >
          {snackbarMessage}
        </Snackbar>
      </ScrollView>
    </Modal>
  );
};

export default VoiceAssistedProgramCreationModal;

const styles = StyleSheet.create({
  modalContainer: {
    margin: 20,
    borderRadius: 10,
    padding: 16,
  },
  scrollContainer: {
    paddingVertical: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  card: {
    marginBottom: 20,
    padding: 16,
  },
  voiceButton: {
    marginBottom: 20,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  transcript: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: "center",
  },
  closeButton: {
    marginTop: 10,
  },
});
