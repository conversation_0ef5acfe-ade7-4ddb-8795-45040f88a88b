// CategoriesGrid.js
import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
  ScrollView,
} from "react-native";
import Animated, { FadeInUp } from "react-native-reanimated";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import adaptiveIcon from "../../../../../assets/adaptive-icon.png";

export default function CategoriesGrid({ theme, features, onSelectCategory }) {
  // Categories array with only real categories
  const categories = [
    {
      label: "Basic",
      icon: "leaf",
      premium: false,
      image: adaptiveIcon,
      description:
        "Free tier nutritional program guiding you with specific calorie and macro goals to achieve your weight target by the end date. Includes biweekly weigh-ins and image submissions.",
      type: "Free Tier Category",
    },
    {
      label: "Food Tracker",
      icon: "food",
      premium: true,
      image: adaptiveIcon,
      description:
        "Receive a curated list of recommended foods, track your consumption through diary entries, and visualize your progress with gamified elements. Includes all features from Basic.",
      type: "Premium Feature",
    },
    {
      label: "AI Meal Plans",
      icon: "robot",
      premium: true,
      image: adaptiveIcon,
      description:
        "Generate personalized meal plans using AI. Includes all features from Food Tracker.",
      type: "Premium Feature",
    },
    {
      label: "Voice-Assisted Creation",
      icon: "microphone",
      premium: true,
      image: adaptiveIcon,
      description:
        "Create programs using voice commands. This is the most comprehensive program creator, including all features from AI Meal Plans.",
      type: "Premium Feature",
    },
  ];

  /**
   * Helper function to map category label to feature flag key
   */
  const featureKey = (label) => {
    switch (label) {
      case "AI Meal Plans":
        return "aiMealPlans";
      case "Voice-Assisted Creation":
        return "voiceAssistedCreation";
      case "Food Tracker":
        return "foodTracker";
      case "Basic":
        return "basic";
      default:
        return null;
    }
  };

  /**
   * Renders each category item with a Start button
   */
  const renderCategory = (cat, idx) => {
    const isLocked = cat.premium && !features[featureKey(cat.label)];

    return (
      <Animated.View
        key={idx}
        entering={FadeInUp.duration(500)}
        style={[
          styles.categoryItem,
          isLocked && styles.lockedCategoryItem, // Apply different styles if locked
        ]}
      >
        <TouchableOpacity
          onPress={() => onSelectCategory(cat)}
          disabled={isLocked}
          style={[
            styles.categoryButton,
            {
              backgroundColor: !isLocked && theme.colors.surface,
              borderBottomWidth: 1,
            },
          ]}
          accessibilityLabel={`Start ${cat.label} program`}
          accessibilityHint={`Starts the ${cat.label} program`}
        >
          {/* Gradient Header with Icon, Title, and START Button */}
          <LinearGradient
            colors={
              isLocked
                ? [
                    theme.colors.lockedGradientStart,
                    theme.colors.lockedGradientEnd,
                  ]
                : [theme.colors.primary, theme.colors.secondary]
            }
            style={styles.cardHeader}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.headerContent}>
              <MaterialCommunityIcons
                name={cat.icon}
                size={20}
                color={
                  isLocked ? theme.colors.subTextColor : theme.colors.surface
                }
                style={{ marginRight: 4 }}
              />
              <Text
                style={[
                  styles.categoryLabel,
                  {
                    color: isLocked
                      ? theme.colors.subTextColor
                      : theme.colors.surface,
                  },
                ]}
              >
                {cat.label}
              </Text>
            </View>
            {/* START Button */}
            <TouchableOpacity
              style={[
                styles.startButton,
                isLocked
                  ? { backgroundColor: "#555" }
                  : { backgroundColor: theme.colors.surface },
              ]}
              onPress={() => onSelectCategory(cat)}
              disabled={isLocked}
              accessibilityLabel={
                isLocked
                  ? `${cat.label} program is locked. Subscribe to Premium to unlock.`
                  : `Start ${cat.label} program`
              }
              accessibilityHint={
                isLocked
                  ? "Subscribe to Premium to unlock this program"
                  : `Starts the ${cat.label} program`
              }
            >
              <MaterialCommunityIcons
                name="play"
                size={20}
                color={
                  isLocked ? theme.colors.subTextColor : theme.colors.primary
                }
              />
            </TouchableOpacity>
          </LinearGradient>

          {/* Image */}
          {/* {cat.image && (
            <Image
              source={cat.image}
              style={styles.cardImage}
              resizeMode="cover"
              onError={(error) => {
                console.log("Image failed to load:", error.nativeEvent.error);
                // Optionally set a fallback image or display an error message
              }}
            />
          )} */}

          {/* Description */}
          {cat.description && (
            <Text
              style={[
                styles.cardDescription,
                {
                  color: isLocked
                    ? theme.colors.subTextColor
                    : theme.colors.primaryTextColor,
                  paddingTop: 20,
                  lineHeight: 20,
                },
              ]}
            >
              {cat.description}
            </Text>
          )}

          {/* Lock Indicator */}
          {isLocked && (
            <View style={styles.lockIndicatorContainer}>
              <MaterialCommunityIcons
                name="lock"
                size={14}
                color={theme.colors.primaryTextColor}
                style={{ marginRight: 4 }}
              />
              <Text
                style={[
                  styles.lockIndicatorText,
                  { color: theme.colors.primaryTextColor },
                ]}
              >
                Premium
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
    >
      {categories.map((cat, idx) => renderCategory(cat, idx))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    width: "100%",
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  categoryItem: {
    width: "100%",
    marginBottom: 20,
    borderRadius: 12,
    overflow: "hidden",
    // Shadows for iOS
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // Shadows for Android
    elevation: 3,
  },
  lockedCategoryItem: {
    // Darker background for locked cards
    backgroundColor: "#333",
  },
  categoryButton: {
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "#fff",
    paddingBottom: 8,
    position: "relative",
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    justifyContent: "space-between",
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  categoryLabel: {
    fontSize: 16,
    fontWeight: "600",
  },
  startButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    // Shadows for iOS
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    // Shadows for Android
    elevation: 2,
  },
  cardImage: {
    width: "100%",
    height: 150,
    backgroundColor: "#eee",
  },
  cardDescription: {
    paddingHorizontal: 12,
    paddingTop: 8,
    paddingBottom: 12,
    fontSize: 14,
    textAlign: "flex-start",
  },
  lockIndicatorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    paddingHorizontal: 12,
  },
  lockIndicatorText: {
    fontSize: 12,
    fontWeight: "500",
  },
});
