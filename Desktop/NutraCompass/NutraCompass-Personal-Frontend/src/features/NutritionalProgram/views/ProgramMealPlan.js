// ProgramMealPlan.js
import React from "react";
import { View, Text } from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";

const ProgramMealPlan = () => {
  const { theme } = useThemeContext();
  const { activeNutritionalProgram } = useNutritionProgram();

  if (!activeNutritionalProgram) {
    return (
      <View
        style={{
          padding: 16,
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: theme.colors.screenBackground,
        }}
      >
        <Text
          style={{
            fontWeight: "bold",
            fontSize: 18,
            marginBottom: 6,
            textAlign: "center",
            color: theme.colors.primaryTextColor,
          }}
        >
          No Meal Plan
        </Text>
        <Text
          style={{
            fontSize: 14,
            textAlign: "center",
            color: theme.colors.subTextColor,
            textAlign: "center",
          }}
        >
          You currently have no active program with an existing meal plan.
          Create one from the menu above!
        </Text>
      </View>
    );
  }

  return (
    <View style={{ padding: 16 }}>
      <Text
        style={{
          fontWeight: "bold",
          fontSize: 16,
          marginBottom: 8,
          color: theme.colors.primaryTextColor,
        }}
      >
        Meal Plan (Coming Soon)
      </Text>
      <Text style={{ color: theme.colors.subTextColor }}>
        In the future, we’ll integrate AI-based meal planning, grocery lists,
        etc.
      </Text>
    </View>
  );
};

export default ProgramMealPlan;
