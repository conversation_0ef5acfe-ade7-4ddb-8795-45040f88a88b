import React, { useEffect, useState } from "react";
import { View, Text, ScrollView, TouchableWithoutFeedback } from "react-native";
import { LineChart } from "react-native-chart-kit";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { useThemeContext } from "../../../context/ThemeContext.js";

const ProgramTimeline = () => {
  const { theme } = useThemeContext();
  const { activeNutritionalProgram } = useNutritionProgram();
  const { getSelectedDateAsDate } = useTime();
  const [programStarted, setProgramStarted] = useState(false);

  useEffect(() => {
    if (activeNutritionalProgram?.startDate) {
      const now = getSelectedDateAsDate();
      const programStartDate = new Date(activeNutritionalProgram.startDate);
      setProgramStarted(now >= programStartDate);
    }
  }, [activeNutritionalProgram, getSelectedDateAsDate]);

  if (!activeNutritionalProgram) {
    return (
      <View style={{ padding: 16 }}>
        <Text style={{ color: theme.colors.primaryTextColor }}>
          No Program Timeline (no active program).
        </Text>
      </View>
    );
  }

  const startDate = new Date(activeNutritionalProgram.startDate);
  const endDate = new Date(activeNutritionalProgram.endDate);
  const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const d = new Date(dateString);
    return d.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  return (
    <View style={{ padding: 16 }}>
      <Text
        style={{
          fontWeight: "bold",
          fontSize: 16,
          marginBottom: 8,
          color: theme.colors.primaryTextColor,
        }}
      >
        Program Timeline
      </Text>

      <View style={{ marginBottom: 8 }}>
        <Text
          style={{ fontWeight: "600", color: theme.colors.primaryTextColor }}
        >
          Start Date: {formatDate(activeNutritionalProgram.startDate)}
        </Text>
        <Text
          style={{ fontWeight: "600", color: theme.colors.primaryTextColor }}
        >
          End Date: {formatDate(activeNutritionalProgram.endDate)}
        </Text>
        <Text
          style={{ fontWeight: "600", color: theme.colors.primaryTextColor }}
        >
          Duration: {totalDays} days
        </Text>
      </View>

      <View style={{ marginBottom: 8 }}>
        <Text
          style={{ fontWeight: "600", color: theme.colors.primaryTextColor }}
        >
          Starting Weight: {activeNutritionalProgram?.startWeight} lbs
        </Text>
        <Text
          style={{ fontWeight: "600", color: theme.colors.primaryTextColor }}
        >
          Goal Weight: {activeNutritionalProgram?.goalWeight} lbs
        </Text>
      </View>

      {!programStarted && (
        <Text style={{ color: "orange", marginTop: 4 }}>
          Program not started yet, chart might be predictive only.
        </Text>
      )}

      {activeNutritionalProgram?.checkpoints?.length > 0 && (
        <ScrollView
          horizontal
          contentContainerStyle={{
            flexGrow: 1,
            paddingLeft: 12,
            marginTop: 12,
          }}
        >
          <TouchableWithoutFeedback>
            <View>
              <LineChart
                data={{
                  labels: activeNutritionalProgram.checkpoints.map((cp) => {
                    const d = new Date(cp.date);
                    return d.toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
                  }),
                  datasets: [
                    {
                      data: activeNutritionalProgram.checkpoints.map(
                        (cp) => cp.expectedWeight
                      ),
                      color: () => "#FF6384",
                      strokeWidth: 2,
                    },
                  ],
                }}
                width={Math.max(
                  300,
                  activeNutritionalProgram.checkpoints.length * 70
                )}
                height={220}
                yAxisSuffix=" lbs"
                chartConfig={{
                  backgroundGradientFrom: theme.colors.screenBackground,
                  backgroundGradientTo: theme.colors.screenBackground,
                  color: () => "#888",
                  labelColor: () => theme.colors.subTextColor,
                  propsForDots: {
                    r: "4",
                    strokeWidth: "2",
                    stroke: "#FF6384",
                    fill: "#FF6384",
                  },
                }}
                withVerticalLines={false}
                withHorizontalLines={true}
              />
            </View>
          </TouchableWithoutFeedback>
        </ScrollView>
      )}
    </View>
  );
};

export default ProgramTimeline;
