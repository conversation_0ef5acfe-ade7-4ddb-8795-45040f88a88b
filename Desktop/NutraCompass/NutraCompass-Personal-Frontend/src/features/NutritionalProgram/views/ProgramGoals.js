import React, { useEffect, useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import * as Animatable from "react-native-animatable";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";

const bounceAnimation = {
  0: { scale: 1 },
  0.5: { scale: 1.05 },
  1: { scale: 1 },
};

const ProgramGoals = () => {
  const { theme } = useThemeContext();
  const { activeNutritionalProgram, isNutritionalGoalsAlignmentNeeded } =
    useNutritionProgram();
  const { setCalorieAndMacroGoals } = useUserSettings();

  const [programStarted, setProgramStarted] = useState(false);

  useEffect(() => {
    if (activeNutritionalProgram?.startDate) {
      const now = new Date();
      const start = new Date(activeNutritionalProgram.startDate);
      setProgramStarted(now >= start);
    }
  }, [activeNutritionalProgram]);

  if (!activeNutritionalProgram?.nutritionalGoals) {
    return (
      <View style={{ padding: 16 }}>
        <Text style={{ color: theme.colors.primaryTextColor }}>
          No Nutritional Goals set yet.
        </Text>
      </View>
    );
  }

  const { calorieGoal, macroGoals } = activeNutritionalProgram.nutritionalGoals;

  const handleAlignGoals = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const { calorieGoal, macroGoals } =
        activeNutritionalProgram.nutritionalGoals;
      await setCalorieAndMacroGoals({
        calorieGoal: calorieGoal || 0,
        proteinPercentage: (macroGoals?.protein?.dailyPercentage || 0) * 100,
        carbPercentage: (macroGoals?.carb?.dailyPercentage || 0) * 100,
        fatPercentage: (macroGoals?.fat?.dailyPercentage || 0) * 100,
      });

      console.log("Aligned user goals with program!");
    } catch (error) {
      console.error("Error aligning nutritional goals:", error);
    }
  };

  return (
    <View style={{ padding: 16 }}>
      <Text
        style={{
          fontWeight: "bold",
          fontSize: 16,
          marginBottom: 8,
          color: theme.colors.primaryTextColor,
        }}
      >
        Nutritional Goals
      </Text>
      <Text style={{ marginBottom: 6, color: theme.colors.primaryTextColor }}>
        Calories: {calorieGoal?.toLocaleString()} kcal
      </Text>
      <Text style={{ marginBottom: 6, color: theme.colors.primaryTextColor }}>
        Carbs: {macroGoals?.carb?.dailyGrams}g (
        {((macroGoals?.carb?.dailyPercentage || 0) * 100).toFixed(0)}%)
      </Text>
      <Text style={{ marginBottom: 6, color: theme.colors.primaryTextColor }}>
        Protein: {macroGoals?.protein?.dailyGrams}g (
        {((macroGoals?.protein?.dailyPercentage || 0) * 100).toFixed(0)}%)
      </Text>
      <Text style={{ marginBottom: 6, color: theme.colors.primaryTextColor }}>
        Fat: {macroGoals?.fat?.dailyGrams}g (
        {((macroGoals?.fat?.dailyPercentage || 0) * 100).toFixed(0)}%)
      </Text>

      {programStarted && (
        <Animatable.View
          animation={
            isNutritionalGoalsAlignmentNeeded ? bounceAnimation : undefined
          }
          iterationCount="infinite"
          duration={1600}
          easing="ease-in-out"
          style={{ alignItems: "flex-start", marginTop: 12 }}
        >
          <TouchableOpacity
            onPress={handleAlignGoals}
            disabled={!isNutritionalGoalsAlignmentNeeded}
            style={{
              backgroundColor: isNutritionalGoalsAlignmentNeeded
                ? theme.colors.primary
                : "gray",
              paddingVertical: 10,
              paddingHorizontal: 14,
              borderRadius: 6,
              flexDirection: "row",
            }}
          >
            <Text
              style={{
                color: theme.colors.primaryTextColor,
                fontWeight: "bold",
                fontSize: 14,
              }}
            >
              {isNutritionalGoalsAlignmentNeeded
                ? "Align Nutritional Goals"
                : "Goals Aligned"}
            </Text>
          </TouchableOpacity>
        </Animatable.View>
      )}
    </View>
  );
};

export default ProgramGoals;
