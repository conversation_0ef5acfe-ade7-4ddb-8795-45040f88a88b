// AIAgent/components/AiMessagesList.js

import React, { useRef, useEffect } from "react";
import {
  StyleSheet,
  FlatList,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import AiMessageBubble from "./AiMessagesBubble.js";
import { useThemeContext } from "../../../context/ThemeContext.js";

const AiMessagesList = ({ messages }) => {
  const { theme } = useThemeContext();

  // 1) Create a reference to the FlatList
  const listRef = useRef(null);

  // 2) Whenever 'messages' changes, scroll to end
  useEffect(() => {
    if (listRef.current && messages.length > 0) {
      // Attempt to scroll a bit beyond the bottom by using scrollToOffset
      listRef.current.scrollToOffset({
        offset: 9999999, // big number to ensure we’re beyond content
        animated: true,
      });
    }
  }, [messages]);

  const renderItem = ({ item }) => <AiMessageBubble message={item} />;

  return (
    <TouchableWithoutFeedback
      onPress={Keyboard.dismiss}
      style={[styles.listContainer, { backgroundColor: "transparent" }]}
    >
      <FlatList
        // 3) Attach the ref to FlatList
        ref={listRef}
        data={messages}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        contentContainerStyle={{ padding: 16, paddingBottom: 200 }}
        // Dismiss keyboard on scroll
        keyboardDismissMode="on-drag"
        keyboardShouldPersistTaps="handled"
      />
    </TouchableWithoutFeedback>
  );
};

export default AiMessagesList;

const styles = StyleSheet.create({
  listContainer: {
    flex: 1,
  },
});
