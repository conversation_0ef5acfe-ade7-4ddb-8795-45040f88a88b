// AIAgent/components/AiMessageBubble.js
import React from "react";
import { View, Text, StyleSheet, Image } from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import dayjs from "dayjs";

const AiMessageBubble = ({ message }) => {
  const { theme } = useThemeContext();
  const isUser = message.sender === "user";
  const formattedTime = dayjs(message.timestamp).format("HH:mm");

  /**
   * If AI, use `typedText`; if user, use `fullText`.
   */
  const displayText = isUser ? message.fullText : message.typedText;

  return (
    <View style={[styles.row, isUser ? styles.userRow : styles.aiRow]}>
      {/* Optional Avatars */}
      {!isUser && (
        <Image
          source={{
            uri: "https://img.icons8.com/color/48/robot.png",
          }}
          style={styles.avatar}
        />
      )}
      {isUser && (
        <Image
          source={{
            uri: "https://img.icons8.com/color/48/user.png",
          }}
          style={styles.avatar}
        />
      )}

      <View
        style={[
          styles.bubble,
          {
            backgroundColor: isUser
              ? theme.colors.surface
              : theme.colors.surface,
            borderWidth: isUser ? 1 : 0,
            borderColor: isUser ? theme.colors.primary : theme.colors.surface,
            shadowColor: theme.colors.shadow || "#000",
          },
        ]}
      >
        <Text
          style={[
            styles.bubbleText,
            {
              color: isUser
                ? theme.colors.primaryTextColor
                : theme.colors.primaryTextColor,
            },
          ]}
        >
          {displayText}
        </Text>

        {/* Timestamp */}
        <Text style={[styles.timestamp, { color: theme.colors.subTextColor }]}>
          {formattedTime}
        </Text>
      </View>
    </View>
  );
};

export default AiMessageBubble;

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
    marginVertical: 6,
    alignItems: "flex-end",
  },
  userRow: {
    justifyContent: "flex-end",
  },
  aiRow: {
    justifyContent: "flex-start",
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginHorizontal: 6,
  },
  bubble: {
    maxWidth: "85%",
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 8,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  bubbleText: {
    fontSize: 14,
    lineHeight: 20,
  },
  timestamp: {
    fontSize: 10,
    textAlign: "right",
    marginTop: 4,
  },
});
