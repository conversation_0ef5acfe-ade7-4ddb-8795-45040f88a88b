// AIAgent/components/AttachmentMenu.js
import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { Ionicons } from "@expo/vector-icons";

const AttachmentMenu = ({
  onDismiss,
  onTakePhoto,
  onAttachPhoto,
  onAttachFile,
}) => {
  const { theme } = useThemeContext();

  return (
    <View
      style={[styles.menuContainer, { backgroundColor: theme.colors.surface }]}
    >
      <TouchableOpacity style={styles.menuItem} onPress={onTakePhoto}>
        <Ionicons
          name="camera"
          size={20}
          color={theme.colors.primaryTextColor}
        />
        <Text
          style={[styles.menuText, { color: theme.colors.primaryTextColor }]}
        >
          Take Photo
        </Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuItem} onPress={onAttachPhoto}>
        <Ionicons
          name="image"
          size={20}
          color={theme.colors.primaryTextColor}
        />
        <Text
          style={[styles.menuText, { color: theme.colors.primaryTextColor }]}
        >
          Attach Photo
        </Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuItem} onPress={onAttachFile}>
        <Ionicons
          name="document"
          size={20}
          color={theme.colors.primaryTextColor}
        />
        <Text
          style={[styles.menuText, { color: theme.colors.primaryTextColor }]}
        >
          Attach File
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  menuContainer: {
    position: "absolute",
    bottom: 70,
    left: 20,
    borderRadius: 12,
    padding: 12,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
  },
});

export default AttachmentMenu;
