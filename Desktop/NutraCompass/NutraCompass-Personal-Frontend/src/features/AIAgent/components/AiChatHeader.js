// AIAgent/components/AiChatHeader.js
import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext.js";

const AiChatHeader = ({ title }) => {
  const navigation = useNavigation();
  const { theme } = useThemeContext();

  return (
    <View
      style={[
        styles.headerContainer,
        { backgroundColor: theme.colors.screenBackground },
      ]}
    >
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={styles.backBtn}
      >
        <MaterialCommunityIcons
          name="chevron-left"
          size={32}
          color={theme.colors.primaryTextColor}
        />
      </TouchableOpacity>

      <View style={styles.titleContainer}>
        <Text
          style={[styles.titleText, { color: theme.colors.primaryTextColor }]}
        >
          {title}
        </Text>
      </View>

      {/* Filler space to center the title */}
      <View style={{ width: 40 }} />
    </View>
  );
};

export default AiChatHeader;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
    elevation: 2,
  },
  backBtn: {
    marginRight: 8,
  },
  titleContainer: {
    flex: 1,
    alignItems: "center",
  },
  titleText: {
    fontSize: 20,
    fontWeight: "bold",
  },
});
