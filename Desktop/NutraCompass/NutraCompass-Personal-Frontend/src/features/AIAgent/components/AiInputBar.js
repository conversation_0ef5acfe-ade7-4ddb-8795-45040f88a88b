// AIAgent/components/AiInputBar.js
import React, { useState, useEffect, useRef } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Keyboard,
  Animated,
  Text,
} from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { MaterialIcons, Feather, Ionicons } from "@expo/vector-icons";

const AiInputBar = ({
  userInput,
  setUserInput,
  onSend,
  startRecording,
  stopRecording,
  showAttachmentMenu,
}) => {
  const { theme } = useThemeContext();
  const [isRecording, setIsRecording] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const translateY = useState(new Animated.Value(0))[0];
  const textInputRef = useRef(null);

  // Auto-focus input
  useEffect(() => {
    const timeout = setTimeout(() => {
      textInputRef.current?.focus();
    }, 300);
    return () => clearTimeout(timeout);
  }, []);

  const handleSend = () => {
    onSend();
    Keyboard.dismiss();
  };

  // Keyboard handling
  useEffect(() => {
    const showListener = Keyboard.addListener("keyboardDidShow", (e) => {
      Animated.spring(translateY, {
        toValue: -e.endCoordinates.height,
        useNativeDriver: true,
      }).start();
    });

    const hideListener = Keyboard.addListener("keyboardDidHide", () => {
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    });

    return () => {
      showListener.remove();
      hideListener.remove();
    };
  }, []);

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY }],
          backgroundColor: theme.colors.surface,
        },
      ]}
    >
      {/* Floating Cards Row - Now properly positioned above input */}
      {/* <View
        style={[
          styles.floatingCardsContainer,
          { bottom: 130 }, // Adjust this value based on your needs
        ]}
      >
        {[1, 2, 3].map((num) => (
          <View
            key={num}
            style={[
              styles.card,
              {
                backgroundColor: theme.colors.surface,
                shadowColor: theme.colors.shadow,
              },
            ]}
          >
            <Text style={{ color: theme.colors.primaryTextColor }}>
              Tip {num}
            </Text>
          </View>
        ))}
      </View> */}

      {/* Text Input Container */}
      <View
        style={[
          styles.inputContainer,
          {
            backgroundColor: theme.colors.surface,
          },
        ]}
      >
        <TextInput
          ref={textInputRef}
          style={[
            styles.input,
            {
              color: theme.colors.primaryTextColor,
            },
          ]}
          placeholder="Message NutraCompass AI"
          placeholderTextColor={theme.colors.subTextColor}
          value={userInput}
          onChangeText={setUserInput}
          multiline
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />

        {/* Buttons Row */}
        <View
          style={[styles.buttonsRow, { backgroundColor: theme.colors.surface }]}
        >
          <TouchableOpacity
            onPress={showAttachmentMenu}
            style={styles.iconButton}
          >
            <Feather
              name="plus-circle"
              size={28}
              color={theme.colors.primary}
            />
          </TouchableOpacity>

          {userInput.trim().length > 0 ? (
            <TouchableOpacity onPress={handleSend} style={styles.iconButton}>
              <Ionicons name="send" size={24} color={theme.colors.primary} />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              onPressIn={() => {
                setIsRecording(true);
                startRecording();
              }}
              onPressOut={() => {
                setIsRecording(false);
                stopRecording();
              }}
              style={styles.iconButton}
            >
              <MaterialIcons
                name={isRecording ? "mic" : "mic-none"}
                size={24}
                color={theme.colors.primary}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: Platform.select({ ios: 12, android: 12 }),
    borderTopLeftRadius: 22,
    borderTopRightRadius: 22,
  },
  floatingCardsContainer: {
    position: "absolute",
    flexDirection: "row",
    justifyContent: "space-evenly",
    width: "100%",
    zIndex: 2, // Higher than input
  },
  card: {
    borderRadius: 16,
    padding: 12,
    width: 100,
    justifyContent: "center",
    alignItems: "center",
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  inputContainer: {
    width: "100%",
    overflow: "hidden",
    zIndex: 1,
    borderTopLeftRadius: 22,
    borderTopRightRadius: 22,
    paddingTop: 6,
  },
  input: {
    fontSize: 16,
    minHeight: 50,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  buttonsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  iconButton: {
    padding: 8,
    borderRadius: 20,
    marginHorizontal: 4,
  },
});

export default AiInputBar;
