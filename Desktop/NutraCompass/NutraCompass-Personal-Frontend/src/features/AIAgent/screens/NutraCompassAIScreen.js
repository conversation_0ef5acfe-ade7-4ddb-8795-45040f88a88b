// AIAgent/screens/NutraCompassAIScreen.js

import React, { useState, useRef, useEffect } from "react";
import {
  SafeAreaView,
  KeyboardAvoidingView,
  StyleSheet,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useThemeContext } from "../../../context/ThemeContext.js";

// Components from AIAgent/components
import <PERSON><PERSON>hatHeader from "../components/AiChatHeader.js";
import AiMessagesList from "../components/AiMessagesList.js";
import AiInputBar from "../components/AiInputBar.js";
import AttachmentMenu from "../components/AttachmentMenu.js";

const MAX_MESSAGES = 5; // Limit total messages to 5

const NutraCompassAIScreen = () => {
  const navigation = useNavigation();
  const { theme } = useThemeContext();

  const [messages, setMessages] = useState([
    {
      id: "1",
      sender: "ai",
      fullText:
        "Hi! I'm your NutraCompass AI. Ask me anything about nutrition, fitness, or mental wellness!",
      typedText: "",
      timestamp: Date.now(),
    },
  ]);

  const [userInput, setUserInput] = useState("");
  const [showAttachMenu, setShowAttachMenu] = useState(false);
  const intervalsRef = useRef({});

  /** On mount, animate the initial AI message */
  useEffect(() => {
    animateAIMessage("1");
  }, []);

  const animateAIMessage = (messageId) => {
    setMessages((prev) => {
      const updated = [...prev];
      const idx = updated.findIndex((m) => m.id === messageId);
      if (idx < 0) return updated;

      const msg = updated[idx];
      msg.typedText = "";
      updated[idx] = { ...msg };

      let currentIndex = 0;
      const chars = msg.fullText.split("");

      const intervalId = setInterval(() => {
        currentIndex++;
        msg.typedText = chars.slice(0, currentIndex).join("");
        updated[idx] = { ...msg };
        setMessages([...updated]);

        if (currentIndex >= chars.length) {
          clearInterval(intervalsRef.current[messageId]);
          delete intervalsRef.current[messageId];
        }
      }, 25);

      intervalsRef.current[messageId] = intervalId;
      return updated;
    });
  };

  const handleSend = () => {
    const text = userInput.trim();
    if (!text) return;

    if (messages.length >= MAX_MESSAGES) {
      console.log("Max messages reached");
      setUserInput("");
      return;
    }

    // Add user message
    const userMsg = {
      id: Date.now().toString(),
      sender: "user",
      fullText: text,
      typedText: text,
      timestamp: Date.now(),
    };
    setMessages((prev) => [...prev, userMsg]);
    setUserInput("");
    setShowAttachMenu(false);

    // Add AI response
    setTimeout(() => {
      setMessages((prev) => {
        if (prev.length >= MAX_MESSAGES) return prev;
        const aiMsg = {
          id: (Date.now() + 1).toString(),
          sender: "ai",
          fullText: `You said: "${text}". Let's explore that further!`,
          typedText: "",
          timestamp: Date.now(),
        };
        return [...prev, aiMsg];
      });

      // Animate AI response
      setTimeout(() => {
        setMessages((prev) => {
          const lastMsg = prev[prev.length - 1];
          if (lastMsg.sender === "ai") {
            animateAIMessage(lastMsg.id);
          }
          return prev;
        });
      }, 50);
    }, 700);
  };

  const handleScreenPress = () => {
    Keyboard.dismiss();
    setShowAttachMenu(false);
  };

  const handleStartRecording = () => {
    console.log("Recording started...");
  };

  const handleStopRecording = () => {
    console.log("Recording stopped...");
  };

  return (
    <TouchableWithoutFeedback onPress={handleScreenPress}>
      <SafeAreaView
        style={[
          styles.safeArea,
          { backgroundColor: theme.colors.screenBackground },
        ]}
      >
        <AiChatHeader title="NutraCompass AI" />

        <KeyboardAvoidingView
          style={styles.chatContainer}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          keyboardVerticalOffset={Platform.OS === "ios" ? -40 : 0}
        >
          <AiMessagesList messages={messages} />

          {showAttachMenu && (
            <AttachmentMenu
              onDismiss={() => setShowAttachMenu(false)}
              onTakePhoto={() => console.log("Take Photo")}
              onAttachPhoto={() => console.log("Attach Photo")}
              onAttachFile={() => console.log("Attach File")}
            />
          )}

          <AiInputBar
            userInput={userInput}
            setUserInput={setUserInput}
            onSend={handleSend}
            startRecording={handleStartRecording}
            stopRecording={handleStopRecording}
            showAttachmentMenu={() => setShowAttachMenu(true)}
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  chatContainer: {
    flex: 1,
    paddingBottom: Platform.select({
      ios: 100,
      android: 80,
    }),
  },
});

export default NutraCompassAIScreen;
