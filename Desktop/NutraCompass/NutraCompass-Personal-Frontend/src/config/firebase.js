// Import AsyncStorage for React Native and Platform for determining the platform
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";

// Import Firebase functions from the appropriate SDKs
import { initializeApp } from "firebase/app";
import { getAnalytics, isSupported } from "firebase/analytics";
import {
  initializeAuth,
  browserLocalPersistence,
  getReactNativePersistence,
} from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Import Firebase configuration object
import Configs from "../../configs.js";

// Initialize Firebase and Firestore
let app, analytics, auth, db, storage;

try {
  app = initializeApp(Configs.firebaseConfig);
  (async () => {
    const supported = await isSupported();
    if (supported) {
      analytics = getAnalytics(app);
      // proceed with analytics usage
    } else {
      // skip analytics or handle gracefully
    }
  })();
  // Set persistence mechanism based on platform
  if (Platform.OS === "web") {
    auth = initializeAuth(app, {
      persistence: browserLocalPersistence,
    });
  } else {
    auth = initializeAuth(app, {
      persistence: getReactNativePersistence(AsyncStorage),
    });
    // auth = initializeAuth(app); // Initialize without persistence
  }

  db = getFirestore(app);
  storage = getStorage(app);
} catch (error) {
  console.error("Firebase initialization error", error);
}

// Export Firebase auth and Firestore instances
export { auth, db, storage };
