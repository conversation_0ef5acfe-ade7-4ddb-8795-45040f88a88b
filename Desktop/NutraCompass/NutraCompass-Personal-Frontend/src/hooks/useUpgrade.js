// /hooks/useUpgrade.js

import { useState } from "react";
import axios from "axios";
import Configs from "../../configs.js";
import { useAuth } from "../authentication/context/AuthContext.js";
import * as Haptics from "expo-haptics";

export const useUpgrade = () => {
  const { user } = useAuth();
  const userId = user?.uid;
  const [loading, setLoading] = useState(false);
  const [upgradeError, setUpgradeError] = useState(null);

  const apiUrl = Configs.NutraCompass_API_URL;

  const handleUpgrade = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      setLoading(true);
      setUpgradeError(null);

      // Example upgrade endpoint; pass userId as needed
      const response = await axios.post(`${apiUrl}/v1/upgrade`, {
        userId,
      });

      if (response.data.success) {
        // If needed, you can refresh user data or feature flags here
        // E.g., fetch updated subscription info
      } else {
        throw new Error("Upgrade failed. Please try again.");
      }
    } catch (error) {
      console.error("Upgrade error:", error);
      setUpgradeError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    upgradeError,
    handleUpgrade,
  };
};
