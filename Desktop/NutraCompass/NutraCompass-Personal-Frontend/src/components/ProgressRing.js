import React, { useEffect, useMemo } from "react";
import { View, Text, StyleSheet } from "react-native";
import Svg, { Circle, Defs, LinearGradient, Stop } from "react-native-svg";
import PropTypes from "prop-types";
import Feather from "react-native-vector-icons/Feather";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useThemeContext } from "../context/ThemeContext.js";
import Animated, {
  useSharedValue,
  useAnimatedProps,
  withTiming,
} from "react-native-reanimated";

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const ProgressRing = React.memo(
  ({
    size,
    strokeWidth = 15,
    currentValue,
    goalValue,
    topOuterLabel = "",
    topInteriorLabel = "",
    bottomInteriorLabel = "",
    firstBottomOuterLabel = "",
    bottomOuterNumeratorLabel = "",
    bottomOuterDenominatorLabel = "",
    consumedColor = ["#3498db", "#2980b9"],
    remainingColor = "#2980b9",
    iconName = null,
    iconType = null,
  }) => {
    const { theme } = useThemeContext();
    const radius = size / 2 - strokeWidth / 2;
    const circumference = 2 * Math.PI * radius;

    // Shared value for the animated stroke offset
    const strokeOffset = useSharedValue(circumference);

    const percentage = useMemo(() => {
      return goalValue > 0
        ? currentValue / goalValue < 0.01
          ? 0
          : Math.min(currentValue / goalValue, 1)
        : 0;
    }, [currentValue, goalValue]);

    // Animated props that will smoothly transition the strokeDashoffset
    const animatedCircleProps = useAnimatedProps(() => ({
      strokeDashoffset: withTiming(strokeOffset.value, { duration: 1000 }),
    }));

    useEffect(() => {
      if (percentage > 0) {
        strokeOffset.value = circumference - percentage * circumference;
      }
    }, [percentage, circumference]);

    // Add an empty listener to prevent the warning
    // useAnimatedReaction(
    //   () => strokeOffset.value,
    //   (value, previousValue) => {
    //     // Empty listener
    //   }
    // );

    // Format the labels for use in Text components
    const formatLabel = (label) => {
      return label !== undefined && label !== null
        ? typeof label === "number"
          ? label.toLocaleString()
          : label
        : ""; // Fallback to an empty string if undefined or null
    };

    const formattedTopOuterLabel = formatLabel(topOuterLabel);
    const formattedTopInteriorLabel = formatLabel(topInteriorLabel);
    const formattedBottomInteriorLabel = formatLabel(bottomInteriorLabel);
    const formattedFirstBottomOuterLabel = formatLabel(firstBottomOuterLabel);
    const formattedBottomOuterNumeratorLabel = formatLabel(
      bottomOuterNumeratorLabel
    );
    const formattedBottomOuterDenominatorLabel = formatLabel(
      bottomOuterDenominatorLabel
    );

    return (
      <View style={{ width: size, height: size }}>
        {/* Top Outer Label */}
        {formattedTopOuterLabel && (
          <Text
            style={[
              styles.topOuterLabel,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            {formattedTopOuterLabel}
          </Text>
        )}

        <Svg width={size} height={size}>
          <Defs>
            <LinearGradient
              id="gradConsumed"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <Stop offset="0%" stopColor={consumedColor[0]} />
              <Stop offset="100%" stopColor={consumedColor[1]} />
            </LinearGradient>
          </Defs>

          {/* Background Circle */}
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={remainingColor}
            strokeWidth={strokeWidth}
            fill="none"
          />

          {/* Progress Circle with Gradient Stroke */}
          <AnimatedCircle
            animatedProps={animatedCircleProps}
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="url(#gradConsumed)"
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={circumference}
            strokeLinecap="round"
            rotation="-90"
            origin={`${size / 2}, ${size / 2}`}
          />
        </Svg>

        {/* Icon */}
        {iconName && (
          <View style={styles.iconContainer}>
            {iconType === "Feather" && (
              <Feather name={iconName} size={28} color={remainingColor} />
            )}
            {iconType === "MaterialCommunityIcons" && (
              <MaterialCommunityIcons
                name={iconName}
                size={28}
                color={remainingColor}
              />
            )}
          </View>
        )}

        {/* Interior Labels (Inside the ring) */}
        <View style={styles.interiorLabelContainer}>
          {formattedTopInteriorLabel && (
            <Text
              style={[
                styles.interiorLabel,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              {formattedTopInteriorLabel}
            </Text>
          )}
          {formattedBottomInteriorLabel && (
            <Text style={[styles.interiorLabel, { color: consumedColor[1] }]}>
              {formattedBottomInteriorLabel}
            </Text>
          )}
        </View>

        {/* Bottom Outer Labels (Below the ring) */}
        <View style={styles.bottomOuterLabelContainer}>
          {formattedFirstBottomOuterLabel && (
            <Text
              style={[
                styles.bottomOuterLabel,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              {formattedFirstBottomOuterLabel}
            </Text>
          )}
          {formattedBottomOuterNumeratorLabel &&
            formattedBottomOuterDenominatorLabel && (
              <View style={{ flexDirection: "row" }}>
                <Text
                  style={[styles.bottomOuterLabel, { color: consumedColor[1] }]}
                >
                  {Math.round(formattedBottomOuterNumeratorLabel)}
                </Text>
                <Text
                  style={[
                    styles.bottomOuterLabel,
                    { color: theme.colors.primaryTextColor },
                  ]}
                >
                  /{Math.round(formattedBottomOuterDenominatorLabel)}g
                </Text>
              </View>
            )}
        </View>
      </View>
    );
  }
);

ProgressRing.propTypes = {
  size: PropTypes.number.isRequired,
  strokeWidth: PropTypes.number,
  currentValue: PropTypes.oneOfType([PropTypes.number, PropTypes.string])
    .isRequired,
  goalValue: PropTypes.oneOfType([PropTypes.number, PropTypes.string])
    .isRequired,
  topOuterLabel: PropTypes.string,
  topInteriorLabel: PropTypes.string,
  bottomInteriorLabel: PropTypes.string,
  firstBottomOuterLabel: PropTypes.string,
  bottomOuterNumeratorLabel: PropTypes.string,
  bottomOuterDenominatorLabel: PropTypes.string,
  consumedColor: PropTypes.array,
  remainingColor: PropTypes.string,
  iconName: PropTypes.string,
  iconType: PropTypes.string,
  showGoalInside: PropTypes.bool,
};

const styles = StyleSheet.create({
  iconContainer: {
    position: "absolute",
    top: "36%",
    left: 0,
    right: 0,
    alignItems: "center",
  },
  interiorLabelContainer: {
    position: "absolute",
    top: "36%",
    left: 0,
    right: 0,
    alignItems: "center",
  },
  interiorLabel: {
    fontSize: 11,
    fontWeight: "500",
  },
  topOuterLabel: {
    position: "absolute",
    top: -20,
    left: 0,
    right: 0,
    textAlign: "center",
    fontSize: 12,
    fontWeight: "500",
  },
  bottomOuterLabelContainer: {
    position: "absolute",
    bottom: -35,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  bottomOuterLabel: {
    fontSize: 11,
    fontWeight: "500",
    textAlign: "center",
  },
});

export default ProgressRing;
