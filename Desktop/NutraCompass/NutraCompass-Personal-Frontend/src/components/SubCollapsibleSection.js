import React, { useState, useEffect } from "react";
import { View, LayoutAnimation, UIManager, Platform } from "react-native";
import { Card, Title, IconButton } from "react-native-paper";
import { useThemeContext } from "../context/ThemeContext.js";

// Enable LayoutAnimation for Android
if (
  Platform.OS === "android" &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const SubCollapsibleSection = ({ title, children }) => {
  const { theme } = useThemeContext();

  const [expanded, setExpanded] = useState(false);
  const [renderChildren, setRenderChildren] = useState(false); // Manage child rendering

  // Trigger LayoutAnimation and delay hiding children when collapsing
  useEffect(() => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);

    if (expanded) {
      setRenderChildren(true); // Show children when expanding
    } else {
      // Delay hiding children until the animation finishes
      setTimeout(() => setRenderChildren(false), 300); // Adjust timing based on animation duration
    }
  }, [expanded]);

  return (
    <Card
      style={{
        marginVertical: 5,
        elevation: 2,
        borderRadius: 10,
      }}
    >
      <Card.Content>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Title
            style={{
              fontSize: 16,
              margin: 5,
              color: theme.colors.primary,
            }}
          >
            {title}
          </Title>
          <IconButton
            icon={expanded ? "chevron-up" : "chevron-down"}
            onPress={() => setExpanded(!expanded)}
          />
        </View>
        <View
          style={{
            height: expanded ? "auto" : 0,
            overflow: "hidden",
          }}
        >
          {renderChildren && children}
        </View>
      </Card.Content>
    </Card>
  );
};

export default SubCollapsibleSection;
