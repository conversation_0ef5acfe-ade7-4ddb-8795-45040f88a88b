import React, { useState } from "react";
import { Modal, View, Button, StyleSheet, Text } from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useThemeContext } from "../context/ThemeContext.js";
import { useTime } from "../context/TimeContext.js";

const DatePickerModal = ({ isVisible, onClose }) => {
  const { theme } = useThemeContext();
  const { getSelectedDateAsDate, updateSelectedDate } = useTime();
  const [temporaryDate, setTemporaryDate] = useState(getSelectedDateAsDate());

  const handleConfirm = () => {
    updateSelectedDate(temporaryDate);
    onClose();
  };

  const handleCancel = () => {
    setTemporaryDate(getSelectedDateAsDate()); // Reset to initial value on cancel
    onClose();
  };

  const styles = StyleSheet.create({
    centeredView: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.8)", // semi-transparent background
    },
    modalView: {
      margin: 20,
      backgroundColor: theme.colors.surface,
      borderRadius: 20,
      padding: 35,
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    buttonContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginTop: 20,
      width: "100%",
    },
  });

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              fontSize: 18,
              fontWeight: "600",
            }}
          >
            Select A Date
          </Text>
          {isVisible && (
            <DateTimePicker
              value={temporaryDate}
              mode="date"
              display="spinner"
              onChange={(event, date) => setTemporaryDate(date)}
              textColor={theme.colors.primaryTextColor}
            />
          )}
          <View style={styles.buttonContainer}>
            <Button title="Cancel" onPress={handleCancel} color="red" />
            <Button title="Confirm" onPress={handleConfirm} color="green" />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DatePickerModal;
