import React, { useEffect } from "react";
import { View, Text } from "react-native";
import Svg, { Circle, Defs, LinearGradient, Stop } from "react-native-svg";
import { AnimatedCircularProgress } from "react-native-circular-progress";
import { useThemeContext } from "../context/ThemeContext.js";
import Animated, {
  useSharedValue,
  useAnimatedProps,
} from "react-native-reanimated";

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const ThreeLevelProgressRing = React.memo(
  ({
    size,
    percentages,
    values,
    fillColorsRight,
    fillColorsCenter,
    fillColorsLeft,
  }) => {
    const { theme } = useThemeContext();
    const radius = size / 2;
    const largerRadius = radius * 1.22;
    const strokeWidthCenter = 14;
    const fullCircleCircumference =
      2 * Math.PI * (radius - strokeWidthCenter / 2);

    const remainingCalories = values[0];
    const calorieGoal = values[1];
    const remainingColor = remainingCalories < 0 ? "red" : "green";

    // Clamp percentages between 0 and 1
    const clampPercentage = (percentage) =>
      Math.min(Math.max(percentage, 0), 1);
    const clampedPercentages = percentages.map(clampPercentage);

    // Shared value for the center circle's animated stroke
    const strokeOffsetCenter = useSharedValue(fullCircleCircumference);

    // Animated props for the center circle
    const animatedCenterCircleProps = useAnimatedProps(() => ({
      strokeDashoffset: strokeOffsetCenter.value,
    }));

    useEffect(() => {
      if (clampedPercentages[1] > 0) {
        strokeOffsetCenter.value =
          fullCircleCircumference * (1 - clampedPercentages[1]);
      }
    }, [clampedPercentages[1]]);

    return (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {/* Left Half Circle */}
        <View style={{ marginRight: -largerRadius * 1.8 }}>
          <AnimatedCircularProgress
            size={largerRadius * 2}
            width={8}
            fill={clampedPercentages[2] * 100}
            tintColor={fillColorsLeft.consumedColor}
            backgroundColor={fillColorsLeft.remainingColor}
            rotation={180} // Start from the left side
            lineCap="round"
            arcSweepAngle={170} // Half-circle
          />
        </View>

        {/* Full Center Circle */}
        <Svg height={size} width={size}>
          <Defs>
            <LinearGradient id="gradCenter" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor={fillColorsCenter.consumedColor[0]} />
              <Stop
                offset="100%"
                stopColor={fillColorsCenter.consumedColor[1]}
              />
            </LinearGradient>
          </Defs>
          <Circle
            cx={radius}
            cy={radius}
            r={radius - strokeWidthCenter / 2}
            stroke={fillColorsCenter.remainingColor}
            strokeWidth={strokeWidthCenter}
            strokeLinecap="round"
            fill="none"
          />
          {clampedPercentages[1] > 0 && (
            <AnimatedCircle
              animatedProps={animatedCenterCircleProps}
              cx={radius}
              cy={radius}
              r={radius - strokeWidthCenter / 2}
              stroke="url(#gradCenter)"
              strokeWidth={strokeWidthCenter}
              strokeLinecap="round"
              fill="none"
              strokeDasharray={fullCircleCircumference}
              transform={`rotate(-90 ${radius} ${radius})`}
            />
          )}
        </Svg>

        {/* Right Half Circle */}
        <View style={{ marginLeft: -largerRadius * 1.8 }}>
          <AnimatedCircularProgress
            size={largerRadius * 2}
            width={8}
            fill={clampedPercentages[0] * 100}
            tintColor={fillColorsRight.consumedColor}
            backgroundColor={fillColorsRight.remainingColor}
            rotation={0} // Start from the right side
            lineCap="round"
            arcSweepAngle={170} // Half-circle
          />
        </View>

        {/* Text inside the Center Circle */}
        <View
          style={{
            position: "absolute",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 10,
          }}
        >
          <Text
            style={{
              fontSize: size / 7,
              color: theme.colors.primaryTextColor,
              fontWeight: "bold",
            }}
          >
            {calorieGoal}
          </Text>
          <Text
            style={{
              fontSize: size / 8,
              color: remainingColor,
              fontWeight: "bold",
            }}
          >
            {Math.round(remainingCalories)}
          </Text>
        </View>
      </View>
    );
  }
);

export default ThreeLevelProgressRing;
