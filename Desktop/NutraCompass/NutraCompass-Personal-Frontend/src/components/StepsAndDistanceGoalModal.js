import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Switch,
  Dimensions,
} from "react-native";
import { Image } from "expo-image";
import Feather from "react-native-vector-icons/Feather";
import { useThemeContext } from "../context/ThemeContext.js";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";
import { useStepsLog } from "../features/StepsLog/context/StepsLogContext.js";

const { width: SCREEN_WIDTH } = Dimensions.get("window");

const StepsAndDistanceGoalModal = ({
  isVisible,
  closeModal,
  stepGoal,
  distanceGoal,
  distanceUnit,
}) => {
  const { theme } = useThemeContext();
  const { setPhysicalFitnessGoals } = useUserSettings();
  const { calculateCaloriesBurned, calculateDistanceFromSteps } = useStepsLog();
  const [localStepGoal, setLocalStepGoal] = useState(stepGoal.toLocaleString());
  const [localDistanceGoal, setLocalDistanceGoal] = useState(
    distanceGoal.toString()
  );
  const [localDistanceUnit, setLocalDistanceUnit] = useState(distanceUnit);

  useEffect(() => {
    setLocalStepGoal(stepGoal.toLocaleString());
    setLocalDistanceGoal(distanceGoal.toString());
    setLocalDistanceUnit(distanceUnit);
  }, [stepGoal, distanceGoal, distanceUnit]);

  const handleStepChange = (value) => {
    const numericValue = parseInt(value.replace(/,/g, ""), 10) || 0;
    setLocalStepGoal(numericValue.toLocaleString());
    const distance = calculateDistanceFromSteps(
      numericValue,
      localDistanceUnit
    );
    setLocalDistanceGoal(distance.toFixed(2).toString());
  };

  const handleDistanceChange = (value) => {
    const numericValue = parseFloat(value) || 0;
    setLocalDistanceGoal(numericValue.toString());
    const steps =
      (numericValue * (localDistanceUnit === "mi" ? 1609.34 : 1000)) / 0.762;
    setLocalStepGoal(Math.round(steps).toLocaleString());
  };

  const handleUnitChange = () => {
    setLocalDistanceUnit(localDistanceUnit === "mi" ? "km" : "mi");
    const updatedDistance = calculateDistanceFromSteps(
      parseInt(localStepGoal.replace(/,/g, ""), 10),
      localDistanceUnit
    );
    setLocalDistanceGoal(updatedDistance.toFixed(2).toString());
  };

  const commitChanges = async () => {
    const finalSteps = parseInt(localStepGoal.replace(/,/g, ""), 10) || 0;
    const finalDistance = parseFloat(localDistanceGoal) || 0;
    const finalDistanceUnit = localDistanceUnit;

    // Update the backend
    await setPhysicalFitnessGoals({
      stepsGoal: finalSteps,
      distanceGoal: finalDistance,
      distanceUnit: finalDistanceUnit,
    });
  };

  const styles = StyleSheet.create({
    backdrop: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "rgba(0,0,0,0.8)",
    },
    modalView: {
      minWidth: "80%",
      maxWidth: "90%",
      maxHeight: "80%",
      backgroundColor: theme.colors.screenBackground,
      borderRadius: 12,
      padding: 20,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
      gap: 20,
    },
    scrollView: {
      alignItems: "center",
    },
    inputGroup: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    modalText: {
      fontSize: 14,
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
      flex: 1,
    },
    textInput: {
      flex: 1 / 2,
      height: 40,
      borderWidth: 1,
      borderRadius: 8,
      color: theme.colors.primary,
      padding: 10,
      textAlign: "center",
      backgroundColor: theme.colors.screenBackground,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      padding: 10,
      marginTop: 20,
      alignSelf: "flex-end",
    },
    infoText: {
      fontSize: 14,
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      paddingLeft: 10,
      paddingBottom: 10,
    },
    toggleButton: {
      backgroundColor: theme.colors.secondary,
      borderRadius: 5,
      alignItems: "center",
      justifyContent: "center",
    },
  });
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={closeModal}
    >
      <View style={styles.backdrop}>
        <View style={styles.modalView}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <TouchableOpacity onPress={closeModal}>
              <Feather
                name="chevron-left"
                color={theme.colors.primaryTextColor}
                size={24}
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                commitChanges();
                closeModal();
              }}
            >
              <Feather
                name="check-circle"
                color={theme.colors.primary}
                size={24}
              />
            </TouchableOpacity>
          </View>
          <View style={{ alignItems: "center" }}>
            <Text style={styles.infoText}>
              Set a daily step and distance goal tailored to enhance your
              well-being and endurance, without overextending yourself.
            </Text>
            <Image
              contentFit="stretch"
              style={{
                width: SCREEN_WIDTH * 0.8,
                height: 150,
                borderRadius: 12,
              }}
              source={{
                uri: "https://static.scientificamerican.com/sciam/cache/file/C4AE8E3A-8039-4AA9-9079C9661BDEA166_source.jpeg?w=900",
              }} // Add your actual image URL here
            />
          </View>

          <View
            style={{
              backgroundColor: theme.colors.surface,
              alignItems: "center",
              paddingHorizontal: 22,
              paddingVertical: 14,
              gap: 12,
              borderTopLeftRadius: 12,
              borderTopRightRadius: 12,
            }}
          >
            <View style={styles.inputGroup}>
              <Text style={styles.modalText}>Step Goal:</Text>
              <TextInput
                style={styles.textInput}
                keyboardType="numeric"
                value={localStepGoal}
                onChangeText={handleStepChange}
              />
            </View>
            <View style={{ width: "100%", gap: 5 }}>
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: 14,
                }}
              >
                Estimated Daily Calories You Would Burn
              </Text>
              <Text
                style={{
                  color: theme.colors.primary,
                  fontSize: 14,
                  fontWeight: 600,
                }}
              >
                {calculateCaloriesBurned(
                  parseInt(localStepGoal.replace(/,/g, ""), 10)
                ).toFixed(2)}
              </Text>
            </View>
          </View>

          <View
            style={{
              backgroundColor: theme.colors.surface,
              alignItems: "center",
              paddingHorizontal: 22,
              paddingVertical: 14,
              gap: 12,
              borderBottomLeftRadius: 12,
              borderBottomRightRadius: 12,
            }}
          >
            <View style={styles.inputGroup}>
              <Text style={styles.modalText}>
                Distance Goal ({localDistanceUnit}):
              </Text>
              <TextInput
                style={styles.textInput}
                keyboardType="numeric"
                value={localDistanceGoal}
                onChangeText={handleDistanceChange}
              />
            </View>

            <View
              style={{
                width: "100%",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "flex-end",
                padding: 10,
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  paddingRight: 10,
                  fontSize: 16,
                }}
              >
                {localDistanceUnit === "mi" ? "Miles" : "Kilometers"}
              </Text>
              <Switch
                trackColor={{ false: "#767577", true: "#81b0ff" }}
                thumbColor={localDistanceUnit === "mi" ? "#f5dd4b" : "#f4f3f4"}
                ios_backgroundColor="#3e3e3e"
                onValueChange={handleUnitChange}
                value={localDistanceUnit === "mi"}
              />
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default StepsAndDistanceGoalModal;
