import React, { useState, useMemo } from "react";
import { Text, View, TouchableOpacity, Modal } from "react-native";
import * as Haptics from "expo-haptics";
import { Feather, Entypo, FontAwesome } from "@expo/vector-icons";
import { Calendar } from "react-native-calendars";
import { LinearGradient } from "expo-linear-gradient";
import { useThemeContext } from "../context/ThemeContext.js";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js";
import { useTime } from "../context/TimeContext.js";

export default function DateSelector() {
  const { selectedDate, updateSelectedDate, getSelectedDateAsDate } = useTime();
  const { mealSections, foodEntries } = useFoodLog();
  const { theme } = useThemeContext();

  const [isCalendarModalVisible, setIsCalendarModalVisible] = useState(false);
  const [temporarySelectedDate, setTemporarySelectedDate] =
    useState(selectedDate);
  const [isInfoVisible, setIsInfoVisible] = useState(true);
  const [sevenDayRange, setSevenDayRange] = useState(
    getInitial7Days(selectedDate)
  );

  const handleCalendarToggle = () => {
    setIsCalendarModalVisible(!isCalendarModalVisible);
  };

  const handleConfirmDate = () => {
    updateSelectedDate(temporarySelectedDate);
    update7DayRangeIfNeeded(temporarySelectedDate);
    setIsCalendarModalVisible(false);
  };

  const handlePrevDay = () => {
    const newDate = subtractDay(selectedDate);
    update7DayRangeIfNeeded(newDate); // Check if we need to update the 7-day range
    updateSelectedDate(newDate);
  };

  const handleNextDay = () => {
    const newDate = addDay(selectedDate);
    update7DayRangeIfNeeded(newDate); // Check if we need to update the 7-day range
    updateSelectedDate(newDate);
  };

  const handleDateSelection = (date) => {
    setTemporarySelectedDate(date.dateString);
  };

  const handleCancel = () => {
    setIsCalendarModalVisible(false);
  };

  const getCurrentDate = () => {
    const date = getSelectedDateAsDate();
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const addDay = (date) => {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + 1);
    return newDate.toISOString().split("T")[0];
  };

  const subtractDay = (date) => {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() - 1);
    return newDate.toISOString().split("T")[0];
  };

  // Function to load the initial 7-day range based on selected date
  function getInitial7Days(date) {
    const days = [];
    let currentDate = new Date(date);
    currentDate.setHours(0, 0, 0, 0);

    // Load initial 7-day range
    for (let i = -2; i <= 4; i++) {
      const newDate = new Date(currentDate);
      newDate.setDate(currentDate.getDate() + i);
      newDate.setHours(0, 0, 0, 0);

      days.push({
        date: newDate,
        dateString: newDate.toISOString().split("T")[0],
      });
    }

    return days;
  }

  // Function to update the 7-day range if the selected date goes outside the current range
  const update7DayRangeIfNeeded = (newSelectedDate) => {
    const firstDay = sevenDayRange[0].dateString;
    const lastDay = sevenDayRange[6].dateString;

    // Check if the new selected date is outside the current 7-day range
    if (newSelectedDate < firstDay || newSelectedDate > lastDay) {
      // Update the 7-day range to center around the new selected date
      setSevenDayRange(getInitial7Days(newSelectedDate));
    }
  };
  const completionStatus = useMemo(() => {
    const status = {};
    const visibleMealSections = mealSections.filter((section) => section.name);

    Object.entries(foodEntries).forEach(([mealType, entries]) => {
      entries.forEach((entry) => {
        if (!status[entry.date]) {
          status[entry.date] = {
            total: visibleMealSections.length,
            count: 0,
            completedSections: new Set(),
          };
        }

        if (visibleMealSections.some((section) => section.id === mealType)) {
          status[entry.date].completedSections.add(mealType);
        }

        status[entry.date].count = status[entry.date].completedSections.size;
      });
    });

    const markedDates = {};
    Object.entries(status).forEach(([date, { total, count }]) => {
      const completionRatio = count / total;
      markedDates[date] = {
        customStyles: {
          container: {
            backgroundColor:
              completionRatio === 1
                ? "green"
                : completionRatio >= 0.5
                ? "orange"
                : "transparent",
            borderRadius: 20,
          },
        },
      };
    });

    return markedDates;
  }, [mealSections, foodEntries]);

  const todayDate = getSelectedDateAsDate().toISOString().split("T")[0];

  return (
    <View>
      <View
        style={{
          backgroundColor: theme.colors.surface,
          borderRadius: 10,
          paddingVertical: 4,
          paddingHorizontal: 4,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <TouchableOpacity
            style={{
              borderRadius: 30,
              padding: 8,
              flexDirection: "row",
              alignItems: "center",
            }}
            onPress={() => {
              handlePrevDay();
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            }}
          >
            <Feather
              name="chevron-left"
              size={18}
              color={theme.colors.primaryTextColor}
            />
            <FontAwesome
              name="calendar-check-o"
              size={18}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
          <View
            style={{
              padding: 8,
              borderRadius: 16,
            }}
          >
            <TouchableOpacity onPress={handleCalendarToggle}>
              <Text
                style={{
                  paddingLeft: 10,
                  fontSize: 14,
                  fontWeight: "bold",
                  color: theme.colors.primaryTextColor,
                }}
              >
                {getCurrentDate(selectedDate)}
              </Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={{
              borderRadius: 30,
              padding: 8,
              flexDirection: "row",
              alignItems: "center",
            }}
            onPress={() => {
              handleNextDay();
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            }}
          >
            <FontAwesome
              name="calendar-o"
              size={18}
              color={theme.colors.primaryTextColor}
            />
            <Feather
              name="chevron-right"
              size={18}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Visual-only 7-day bar */}
      <View
        style={{
          alignItems: "center",
          paddingHorizontal: 8,
          paddingTop: 4,
          flexDirection: "row",
          justifyContent: "space-evenly",
        }}
      >
        {sevenDayRange.map((day) => {
          const isSelectedDate = day.dateString === selectedDate;
          return (
            <View
              key={day.dateString}
              style={[
                {
                  borderRadius: 60,
                  height: 30,
                  width: 30,
                  justifyContent: "center",
                  alignItems: "center",
                },
              ]}
            >
              {isSelectedDate ? (
                <LinearGradient
                  colors={[theme.colors.primary, theme.colors.secondary]}
                  style={{
                    borderRadius: 60,
                    height: 30,
                    width: 30,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Text
                    style={{
                      fontSize: 12,
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    {day.date.getDate()}
                  </Text>
                </LinearGradient>
              ) : (
                <Text
                  style={{ fontSize: 12, color: theme.colors.primaryTextColor }}
                >
                  {day.date.getDate()}
                </Text>
              )}
            </View>
          );
        })}
      </View>

      <Modal
        visible={isCalendarModalVisible}
        animationType="slide"
        transparent={true}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0,0,0,1)",
          }}
        >
          <View
            style={{
              width: "95%",
              minHeight: "85%",
              justifyContent: "space-between",
              backgroundColor: theme.colors.screenBackground,
            }}
          >
            <View>
              <Calendar
                style={{
                  minHeight: "65%",
                  height: "auto",
                  width: "100%",
                  backgroundColor: theme.colors.surface,
                }}
                current={selectedDate.toString()}
                markedDates={completionStatus}
                markingType="custom"
                hideExtraDays
                theme={{
                  calendarBackground: theme.colors.surface,
                  selectedDayBackgroundColor: theme.colors.primary,
                  selectedDayTextColor: theme.colors.primaryTextColor,
                  todayTextColor: theme.colors.primaryTextColor,
                  "stylesheet.calendar.header": {
                    monthText: {
                      color: theme.colors.primaryTextColor,
                      fontSize: 18,
                    },
                    dayHeader: {
                      color: theme.colors.primaryTextColor,
                      fontSize: 14,
                      flex: 1,
                      textAlign: "center",
                    },
                  },
                }}
                dayComponent={({ date, state }) => {
                  const isSelectedDate =
                    date.dateString === temporarySelectedDate;
                  const isToday = date.dateString === todayDate;
                  const dayCompletion = completionStatus[date.dateString];

                  return (
                    <TouchableOpacity
                      style={{
                        height: 36,
                        width: 36,
                        marginVertical: 2,
                      }}
                      onPress={() =>
                        handleDateSelection({ dateString: date.dateString })
                      }
                    >
                      <View
                        style={{
                          flex: 1,
                          alignItems: "center",
                          justifyContent: "center",
                          backgroundColor: isToday
                            ? theme.colors.primary
                            : "transparent",
                          borderWidth: 2,
                          borderRadius: 22.5,
                          borderColor: isSelectedDate
                            ? theme.colors.secondary
                            : isToday
                            ? theme.colors.primary
                            : "transparent",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 14,
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          {date.day}
                        </Text>
                        {dayCompletion && (
                          <Entypo
                            name="check"
                            size={16}
                            color={
                              dayCompletion.customStyles.container
                                .backgroundColor
                            }
                            style={{
                              position: "absolute",
                              top: -14,
                              right: -10,
                            }}
                          />
                        )}
                      </View>
                    </TouchableOpacity>
                  );
                }}
              />
            </View>

            {isInfoVisible && (
              <View
                style={{
                  position: "absolute",
                  bottom: 50,
                  left: 40,
                  padding: 10,
                  gap: 10,
                  backgroundColor: theme.colors.surface,
                  borderRadius: 10,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.2,
                  shadowRadius: 1.5,
                  elevation: 4,
                  alignItems: "flex-start",
                }}
              >
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: "bold",
                    color: theme.colors.primaryTextColor,
                  }}
                >
                  Legend:
                </Text>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Entypo
                    name="check"
                    size={16}
                    color="green"
                    style={{ height: 20, width: 20 }}
                  />
                  <Text
                    style={{
                      marginLeft: 10,
                      fontSize: 14,
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    - All entries completed (100%)
                  </Text>
                </View>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Entypo
                    name="check"
                    size={16}
                    color="orange"
                    style={{ height: 20, width: 20 }}
                  />
                  <Text
                    style={{
                      marginLeft: 10,
                      fontSize: 14,
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    - Partially completed (More than 50%)
                  </Text>
                </View>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View
                    style={{
                      height: 20,
                      width: 20,
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: theme.colors.primary,
                      borderWidth: 2,
                      borderRadius: 22.5,
                      borderColor: theme.colors.primary,
                    }}
                  />
                  <Text
                    style={{
                      marginLeft: 10,
                      fontSize: 14,
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    - Today's Date
                  </Text>
                </View>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View
                    style={{
                      height: 20,
                      width: 20,
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "transparent",
                      borderWidth: 2,
                      borderRadius: 22.5,
                      borderColor: theme.colors.secondary,
                    }}
                  />
                  <Text
                    style={{
                      marginLeft: 10,
                      fontSize: 14,
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    - Selected Date
                  </Text>
                </View>
              </View>
            )}

            <View
              style={{
                flexDirection: "row",
                backgroundColor: theme.colors.cardBackgroundColor,
                justifyContent: "flex-end",
              }}
            >
              <TouchableOpacity
                onPress={() => setIsInfoVisible(!isInfoVisible)}
                style={{
                  position: "absolute",
                  left: 0,
                  color: theme.colors.primaryTextColor,
                  textAlign: "center",
                  padding: 15,
                }}
              >
                <Entypo
                  name="info-with-circle"
                  size={18}
                  color={theme.colors.primaryTextColor}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={{ padding: 15 }}
                onPress={() => handleCancel()}
              >
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    textAlign: "center",
                  }}
                >
                  Cancel
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{ padding: 15 }}
                onPress={() => handleConfirmDate()}
              >
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    textAlign: "center",
                  }}
                >
                  OK
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
