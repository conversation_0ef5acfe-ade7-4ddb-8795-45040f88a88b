import { StyleSheet } from "react-native";
import { useThemeContext } from "../../context/ThemeContext.js";

const dashboardScreenStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground, // Use the dynamic theme color
      alignItems: "center",
      paddingBottom: "10%",
    },
    header: {
      height: "14%",
      width: "100%",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 10,
      paddingBottom: 4,
    },
    title: {
      color: theme.colors.primaryTextColor,
      fontSize: 24,
      fontWeight: "bold",
      textAlign: "left",
    },
    datePickerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
    },
    dateText: {
      color: theme.colors.primaryTextColor,
      fontSize: 16,
      marginLeft: 10,
    },
    scrollViewContent: {
      paddingHorizontal: 15,
      paddingVertical: 20,
      width: "100%",
    },
    calorieSection: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      backgroundColor: theme.colors.cardBackground, // Dynamic card background color
      padding: 20,
      borderRadius: 10,
      marginBottom: 20,
      width: "100%",
    },
    calorieDetails: {
      flex: 1,
      marginLeft: 20,
    },
    calorieText: {
      fontSize: 18,
      color: theme.colors.primaryTextColor,
      fontWeight: "bold",
    },
    remainingCalories: {
      fontSize: 16,
      color: theme.colors.highlightColor, // Use a highlight color for the remaining calories
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 20,
      width: "100%",
    },
    card: {
      flex: 1,
      backgroundColor: theme.colors.cardBackground,
      borderRadius: 10,
      padding: 15,
      alignItems: "center",
      marginHorizontal: 5,
    },
    label: {
      fontSize: 14,
      color: theme.colors.primaryTextColor,
      marginTop: 10,
    },
    macroContainer: {
      marginBottom: 20,
      width: "100%",
    },
    sectionTitle: {
      fontSize: 18,
      color: theme.colors.primaryTextColor,
      fontWeight: "bold",
      marginBottom: 10,
    },
    macroRow: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    macroCard: {
      flex: 1,
      backgroundColor: theme.colors.cardBackground,
      borderRadius: 10,
      padding: 15,
      alignItems: "center",
      marginHorizontal: 5,
    },
    macroLabel: {
      fontSize: 14,
      color: theme.colors.primaryTextColor,
      marginTop: 10,
    },
  });
};

export default dashboardScreenStyles;
