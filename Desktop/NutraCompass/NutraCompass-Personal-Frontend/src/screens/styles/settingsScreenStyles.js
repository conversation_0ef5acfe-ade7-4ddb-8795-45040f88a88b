import { StyleSheet } from "react-native";
import { useThemeContext } from "../../context/ThemeContext.js";

const settingsScreenStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    rowContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: 15,
      paddingHorizontal: 20,
      backgroundColor: theme.colors.surface,
    },
    rowLeft: {
      flexDirection: "row",
      alignItems: "center",
    },
    rowIcon: {
      marginRight: 15,
    },
    rowText: {
      fontSize: 16,
      fontWeight: "500",
      color: theme.colors.primaryTextColor,
    },
    rowBorder: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
  });
};

export default settingsScreenStyles;
