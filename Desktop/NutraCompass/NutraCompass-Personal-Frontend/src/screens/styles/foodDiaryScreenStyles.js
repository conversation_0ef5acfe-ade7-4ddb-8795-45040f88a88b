import { StyleSheet } from "react-native";
import { useTheme } from "react-native-paper";
import { useThemeContext } from "../../context/ThemeContext.js";

const foodDiaryScreenStyles = () => {
  const paperTheme = useTheme();
  const { theme } = useThemeContext();

  /**
   * Theme Backgrounds: White, Black, <PERSON> Gray, Dark Gray,
   * Meal Cards: Darker Colors
   * primary can't clash with secondary
   */

  return StyleSheet.create({
    safeAreaView: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
      paddingBottom: "25%",
    },
    scrollViewContainer: {
      flex: 1,
      padding: 5,
      paddingBottom: 10,
      backgroundColor: theme.colors.screenBackground,
    },
    scrollViewContainerContent: {
      flexGrow: 1,
    },
    headerSection: {
      marginBottom: 7, // Reduce marginBottom for smaller sections
      borderRadius: theme.dimensions.sectionBorderRadius,
      backgroundColor: theme.colors.screenBackground, // Change to surface color for cards
      elevation: 2, // Add elevation for a card-like effect
    },
    headerDateContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingBottom: 5,
    },
    date: {
      paddingLeft: 10,
      fontSize: 18,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    calendarModal: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: theme.colors.cardBackgroundColor,
    },
    calendarWrapper: {
      width: "85%",
      backgroundColor: theme.colors.screenBackground,
    },
    cancelDateButton: {
      padding: 15,
    },
    cancelDateButtonText: {
      color: theme.colors.primaryTextColor,
      textAlign: "center",
    },
    calendarModalButton: {
      flexDirection: "row",
      alignItems: "center",
    },
    totalDayCalories: {
      fontSize: 16,
      paddingTop: 12, // Increase padding for better spacing
      color: theme.colors.primaryTextColor,
    },
    totalDayCaloriesProgressSectionText: {
      fontSize: 14,
      paddingTop: 12, // Increase padding for better spacing
      color: theme.colors.primaryTextColor,
    },
    section: {
      marginBottom: 12, // Reduce marginBottom for smaller sections
      borderRadius: theme.dimensions.sectionBorderRadius,
      backgroundColor: theme.colors.sectionBackgroundColor, // Change to surface color for cards
      elevation: 2, // Add elevation for a card-like effect
    },
    mealSection: {
      marginBottom: 0, // Reduce marginBottom for smaller sections
      paddingVertical: 10,
      backgroundColor: "transparent", // Change to surface color for cards
      borderRadius: theme.dimensions.cardBorderRadius,
      borderColor: theme.colors.cardBorderColor,
      elevation: 2, // Add elevation for a card-like effect
      borderWidth: 0,
    },
    calorieSection: {
      flex: 3,
      height: "100%",
      justifyContent: "center",
      borderRadius: theme.dimensions.sectionBorderRadius,
      backgroundColor: theme.colors.cardDarkGrayBackground,
      borderColor: theme.colors.sectionBorderColor,
      borderRightWidth: 1,
      borderLeftWidth: 1,
      elevation: 4,
    },
    fabMenuSection: {},
    sectionHeaderContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: 0,
    },
    mealSectionHeaderContainer: {
      backgroundColor: theme.colors.cardDarkGrayBackground,
      borderBottomRightRadius: 4,
      borderBottomLeftRadius: 4,
    },
    mealSectionFooterContainer: {
      padding: 2,
      marginBottom: 10,
      backgroundColor: theme.colors.surface,
      borderBottomLeftRadius: 8,
      borderBottomRightRadius: 8,
    },
    sectionTitle: {
      fontSize: 15, // Reduce fontSize for the section title
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    footerButton: {
      alignSelf: "flex-start",
      marginTop: 15,
      marginHorizontal: 16,
      borderRadius: 8,
      padding: 0,
    },
    totalMealSectionCalories: {
      fontSize: 15,
      fontWeight: "bold",
      color: theme.colors.primary,
    },
    dateInfo: {
      alignSelf: "center",
      marginTop: 4, // Reduce marginTop for date info
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    sliderSectionContainer: {
      marginVertical: 20,
      flexDirection: "row",
      minHeight: "22%",
      maxHeight: "30%",
      gap: 10,
      padding: 5,
    },
  });
};

export default foodDiaryScreenStyles;
