// useWorkoutDiaryScreenStyles.js
import { StyleSheet, Dimensions } from "react-native";
import { useTheme } from "react-native-paper";
import { useThemeContext } from "../../context/ThemeContext";

export default function useWorkoutDiaryScreenStyles() {
    const { theme } = useThemeContext(); // Custom theme context
    const paperTheme = useTheme(); // Paper theme
    const screenWidth = Dimensions.get('window').width;

    return StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: paperTheme.colors.background,
            alignItems: 'center',
            justifyContent: 'flex-start',
            paddingTop: 20,
        },
        gridContainer: {
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            width: screenWidth, // full width of the screen
        },
        gridItem: {
            width: (screenWidth / 2) - 20, // Adjust width for two columns, accounting for padding
            aspectRatio: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: theme.colors.surface,
            borderRadius: 8,
            marginVertical: 10,
            marginHorizontal: 5,
            elevation: 5,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            padding: 10,
        },
        icon: {
            fontSize: 40,
            color: theme.colors.primary,
        },
        text: {
            fontSize: 16,
            color: theme.colors.primaryTextColor, // Updated to use primary text color from theme
            textAlign: 'center',
            marginTop: 5,
        },
    });
}