import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Text,
  View,
  SectionList,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import * as Haptics from "expo-haptics";
import { <PERSON>, <PERSON><PERSON>, IconButton, Divider } from "react-native-paper";
import { LinearGradient } from "expo-linear-gradient";
import { useIsFocused } from "@react-navigation/native";
import FoodEntryModal from "../features/FoodDiary/components/FoodEntryModal.js";
import MealSectionCustomizationModal from "../features/FoodDiary/components/MealSectionCustomizationModal.js";
import DailyNutritionGoalsCustomizationModal from "../features/FoodDiary/components/DailyNutritionGoalsCustomizationModal.js";
import DailyNutritionGoalsCalculationModal from "../features/FoodDiary/components/DailyNutritionGoalsCalculationModal.js";
import FoodNutrientModal from "../features/FoodDiary/components/FoodNutrientModal.js";
import foodDiaryScreenStyles from "./styles/foodDiaryScreenStyles.js";
import SwipeableFoodEntryListItem from "../features/FoodDiary/components/SwipeableFoodEntryListItem.js";
import DateSelector from "../components/DateSelector.js";
import FoodlogFabGroupMenu from "../features/FoodDiary/components/FabGroupMenu.js";
import { useTime } from "../context/TimeContext.js";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js";
import { useFoodMenu } from "../features/FoodMenu/context/FoodMenuContext.js";
import { useStepsLog } from "../features/StepsLog/context/StepsLogContext.js";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";
import { useThemeContext } from "../context/ThemeContext.js";
import CarouselRenderItemComponent from "../features/FoodDiary/components/CarouselRenderItemComponent.js";
import CarouselWithIndicators from "../features/FoodDiary/components/CarouselWithIndicators.js";
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from "react-native-popup-menu";
import { Entypo, Ionicons } from "@expo/vector-icons";
import QuickAddModal from "../features/FoodDiary/components/MealSectionMenuOptions/QuickAddModal.js";
import CopyMealSectionFromDateModal from "../features/FoodDiary/components/MealSectionMenuOptions/CopyMealSectionFromDateModal.js";
import CreateCustomMealModal from "../features/FoodMenu/components/CreateCustomMealModal.js";
import WaterLogEntryModal from "../features/FoodDiary/components/WaterLogEntryModal.js";
// import { SimpleLineIcons } from "@expo/vector-icons";
// import { EvilIcons } from "@expo/vector-icons";

const { height: screenHeight, width: screenWidth } = Dimensions.get("window");

export default function FoodDiaryScreen() {
  const styles = foodDiaryScreenStyles();
  const { theme, mode } = useThemeContext();
  const { selectedDate, updateSelectedDate } = useTime();
  const {
    mealSections,
    foodEntries,
    calculateTotalCaloriesAndMacros,
    totalDailyCaloriesAndMacrosConsumed,
    deleteMealSectionEntries,
    copyEntriesBetweenMealSections,
    addQuickFoodEntry,
    totalDailyWaterConsumedPercentage,
    calorieData,
    macroData,
  } = useFoodLog();
  const { clearTempCustomMeal, addLoggedFoodItemsToTempCustomMeal } =
    useFoodMenu();
  const { caloriesBurned } = useStepsLog();
  const { getNutritionalGoals } = useUserSettings();
  const { calorieGoal, macroGoals } = getNutritionalGoals();
  const [activeMealSection, setActiveMealSection] = useState(null);
  const [activeFoodItem, setActiveFoodItem] = useState({});

  const [isWaterLogEntryModalVisible, setIsWaterLogEntryModalVisible] =
    useState(false);
  const [isFoodEntryModalVisible, setIsFoodEntryModalVisible] = useState(false);
  const [
    isMealSectionCustomizationModalVisible,
    setIsMealSectionCustomizationModalVisible,
  ] = useState(false);
  const [
    isDailyNutritionGoalsCustomizationModalVisible,
    setIsDailyNutritionGoalsCustomizationModalVisible,
  ] = useState(false);
  const [
    isDailyNutritionGoalsCalculationModalVisible,
    setIsDailyNutritionGoalsCalculationModalVisible,
  ] = useState(false);
  const [isFoodNutrientModalVisible, setIsFoodNutrientModalVisible] =
    useState(false);
  const [collapsedSections, setCollapsedSections] = useState([]);
  const [sections, setSections] = useState([]);

  const isFocused = useIsFocused();

  useEffect(() => {
    setRefreshSectionList(true);
    updateSections();
  }, [selectedDate, foodEntries, mealSections]);

  const handleOpenWaterLogEntryModal = (activeItem) => {
    if (activeItem) {
      setActiveFoodItem(activeItem);
    }
    setIsWaterLogEntryModalVisible(true);
  };

  const handleCloseWaterLogEntryModal = () => {
    setActiveFoodItem(null);
    setIsWaterLogEntryModalVisible(false);
  };

  const handleOpenFoodEntryModal = (mealType) => {
    setActiveMealSection(mealType);
    setIsFoodEntryModalVisible(true);
  };

  const handleCloseFoodEntryModal = () => {
    setIsFoodEntryModalVisible(false);
    setActiveMealSection(null);
  };

  const handleCloseDailyNutritionGoalsCalculationModal = () => {
    setIsDailyNutritionGoalsCalculationModalVisible(false);
  };

  const handleOpenDailyNutritionGoalsCalculationModal = () => {
    setIsDailyNutritionGoalsCalculationModalVisible(true);
  };

  const handleCloseMealSectionCustomizationModal = () => {
    setIsMealSectionCustomizationModalVisible(false);
  };

  const handleOpenMealSectionCustomizationModal = () => {
    setIsMealSectionCustomizationModalVisible(true);
  };

  const handleCloseDailyNutritionGoalsCustomizationModal = () => {
    setIsDailyNutritionGoalsCustomizationModalVisible(false);
  };

  const handleOpenDailyNutritionGoalsCustomizationModal = () => {
    setIsDailyNutritionGoalsCustomizationModalVisible(true);
  };

  const handleCloseFoodNutrientModal = () => {
    setIsFoodNutrientModalVisible(false);
  };

  const handleOpenFoodNutrientModal = (activeItem) => {
    setActiveFoodItem(activeItem);
    setIsFoodNutrientModalVisible(true);
  };

  const toggleSection = (sectionId) => {
    setCollapsedSections((prev) =>
      prev.includes(sectionId)
        ? prev.filter((id) => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const updateSections = () => {
    const newSections = mealSections
      .filter((section) => section.name)
      .map((section) => {
        const dataForSelectedDate = foodEntries[section.id]
          ?.filter((entry) => entry?.date === selectedDate)
          .map((entry) => ({
            ...entry,
            mealType: section.id, // Set the mealType based on the section.id
          }));

        return {
          id: section.id,
          name: section.name,
          data: dataForSelectedDate || [],
        };
      });

    setSections(newSections);
    setRefreshSectionList(false); // Set refreshing back to false after updating sections
  };

  const renderSectionHeader = ({ section }) => {
    const totalSectionCaloriesAndMacrosConsumed =
      calculateTotalCaloriesAndMacros(section.data);

    // Determine if the section is collapsed
    const isCollapsed = collapsedSections.includes(section.id);

    return (
      <Card
        style={[
          styles.mealSectionHeaderContainer,
          // Apply marginVertical of 1 if the section is collapsed
          isCollapsed && { marginVertical: 1 },
        ]}
      >
        <Card.Content
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
            paddingLeft: 12,
            paddingRight: 4,
            paddingVertical: 2,
          }}
        >
          <Text style={styles.sectionTitle}>{section.name}</Text>

          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "flex-end",
              gap: 6,
            }}
          >
            <Text style={styles.totalMealSectionCalories}>
              {Math.round(totalSectionCaloriesAndMacrosConsumed.calories)}
            </Text>
            <IconButton
              icon={"dots-hexagon"}
              iconColor={theme.colors.primaryTextColor}
              style={{ margin: 0, padding: 0 }}
              onPress={() => toggleSection(section.id)}
            >
              {isCollapsed ? "Expand" : "Collapse"}
            </IconButton>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderItem = useCallback(({ item, section }) => {
    // If the section is collapsed or the section has no data, return null
    if (collapsedSections.includes(section.id) || !item) {
      return null;
    }

    return (
      <SwipeableFoodEntryListItem
        item={item}
        foodEntryItems={section.data}
        mealType={item.mealType}
        handleOpenFoodNutrientModal={(activeItem) =>
          handleOpenFoodNutrientModal(activeItem)
        }
        handleOpenQuickAddModal={(activeItem) =>
          openQuickAddModal(item.mealType, activeItem)
        }
        handleOpenWaterLogEntryModal={(activeItem) =>
          handleOpenWaterLogEntryModal(activeItem)
        }
      />
    );
  });

  const [isQuickAddModalVisible, setIsQuickAddModalVisible] = useState(false);

  const openQuickAddModal = (mealType, activeItem) => {
    if (activeItem) {
      setActiveFoodItem(activeItem);
    }
    setActiveMealSection(mealType);
    setIsQuickAddModalVisible(true);
  };

  const closeQuickAddModal = () => {
    setActiveFoodItem(null);
    setActiveMealSection(null);
    setIsQuickAddModalVisible(false);
  };

  // Meal Section Toggle Action Menu Option
  const QuickAddToMealSection = ({ text, mealType, iconName, iconColor }) => (
    <MenuOption
      onSelect={() => openQuickAddModal(mealType)}
      customStyles={{
        optionWrapper: {
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        },
      }}
    >
      <Text style={{ color: theme.colors.primaryTextColor }}>{text}</Text>
      <Entypo name={iconName} size={24} color={iconColor} />
    </MenuOption>
  );

  // Meal Section Toggle Action Menu Option
  const DeleteAllMealSectionEntries = ({
    text,
    mealType,
    iconName,
    iconColor,
  }) => (
    <MenuOption
      customStyles={{
        optionWrapper: {
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        },
      }}
      onSelect={() => {
        // Check if data array is not empty
        if (mealType.data && mealType.data.length > 0) {
          deleteMealSectionEntries(mealType.id);
        } else {
          console.log("No entries to delete for", mealType.name);
        }
      }}
    >
      <Text style={{ color: theme.colors.primaryTextColor }}>{text}</Text>
      <Entypo name={iconName} size={24} color={iconColor} />
    </MenuOption>
  );

  const [
    isCopyMealSectionFromDateModalVisible,
    setIsCopyMealSectionFromDateModalVisible,
  ] = useState(false);
  const [
    copyMealSectionFromDateModalType,
    setCopyMealSectionFromDateModalType,
  ] = useState("");

  const handleDateConfirm = (currentMealType, targetMealType, targetDate) => {
    if (copyMealSectionFromDateModalType === "To Date") {
      // Copying from the currently active meal section to another meal section on a selected date
      copyEntriesBetweenMealSections(
        currentMealType,
        targetMealType,
        selectedDate, // source date is the current app date
        targetDate // destination date is picked from the date picker
      );
    } else if (copyMealSectionFromDateModalType === "From Date") {
      // Copying from another meal section on a selected date to the currently active meal section
      copyEntriesBetweenMealSections(
        targetMealType,
        currentMealType,
        targetDate, // source date is picked from the date picker
        selectedDate // destination date is the current app date
      );
    }

    setIsCopyMealSectionFromDateModalVisible(false);
    setCopyMealSectionFromDateModalType("");
  };

  const handleOpenCopyMealSectionFromDateModal = (mealType, modalType) => {
    setCopyMealSectionFromDateModalType(modalType);
    setActiveMealSection(mealType);
    setIsCopyMealSectionFromDateModalVisible(true);
  };

  // Meal Section Toggle Action Menu Option
  const CopyMealSectionFromDate = ({ text, mealType, iconName, iconColor }) => (
    <MenuOption
      onSelect={() =>
        handleOpenCopyMealSectionFromDateModal(mealType, "From Date")
      }
      customStyles={{
        optionWrapper: {
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        },
      }}
    >
      <Text style={{ color: theme.colors.primaryTextColor }}>{text}</Text>
      <Entypo name={iconName} size={24} color={iconColor} />
    </MenuOption>
  );

  // Meal Section Toggle Action Menu Option
  const CopyMealSectionToDate = ({ text, mealType, iconName, iconColor }) => (
    <MenuOption
      onSelect={() =>
        handleOpenCopyMealSectionFromDateModal(mealType, "To Date")
      }
      customStyles={{
        optionWrapper: {
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        },
      }}
    >
      <Text style={{ color: theme.colors.primaryTextColor }}>{text}</Text>
      <Entypo name={iconName} size={24} color={iconColor} />
    </MenuOption>
  );

  const [isCreateCustomMealModalVisible, setIsCreateCustomMealModalVisible] =
    useState(false);

  // Meal Section Toggle Action Menu Option
  const SaveMealSectionEntriesAsCustomMeal = ({
    text,
    entries,
    iconName,
    iconColor,
  }) => (
    <MenuOption
      onSelect={() => {
        addLoggedFoodItemsToTempCustomMeal(entries);
        setIsCreateCustomMealModalVisible(true);
      }}
      customStyles={{
        optionWrapper: {
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        },
      }}
    >
      <Text style={{ color: theme.colors.primaryTextColor }}>{text}</Text>
      <Entypo name={iconName} size={24} color={iconColor} />
    </MenuOption>
  );

  // Meal Section Toggle Action Menu
  const MealSectionToggleMenu = ({ mealType }) => {
    return (
      <Menu>
        <MenuTrigger
          style={{
            alignSelf: "center",
            borderRadius: 8,
            padding: 4,
          }}
        >
          <Entypo
            name="dots-three-horizontal"
            size={20}
            color={theme.colors.primaryTextColor}
          />
        </MenuTrigger>
        <MenuOptions
          customStyles={{
            optionsContainer: {
              borderRadius: 10,
              borderWidth: 1,
              borderColor: theme.colors.cardBorderColor,
              backgroundColor: theme.colors.surface,
            },
          }}
        >
          {mealType.id !== "Water" && (
            <>
              <QuickAddToMealSection
                text="Quick Add"
                mealType={mealType}
                iconName="add-to-list"
                iconColor={"#53E032"}
              />
              <Divider />
              <CopyMealSectionFromDate
                text="Copy From Date"
                mealType={mealType}
                iconName="cycle"
                iconColor={"#FED589"}
              />
              <Divider />

              <CopyMealSectionToDate
                text="Copy To Date"
                mealType={mealType}
                iconName="copy"
                iconColor={"lightblue"}
              />
              <Divider />
              <SaveMealSectionEntriesAsCustomMeal
                text="Save as Meal"
                entries={mealType.data}
                iconName="database"
                iconColor={"#9089FE"}
              />
              <Divider />
            </>
          )}

          <DeleteAllMealSectionEntries
            text="Clear All Entries"
            mealType={mealType}
            iconName="trash"
            iconColor={"#FE9089"}
          />
        </MenuOptions>
      </Menu>
    );
  };

  const renderSectionFooter = ({ section }) => {
    return (
      !collapsedSections.includes(section.id) && (
        <View
          style={{
            paddingTop: 0,
            padding: 2,
            marginBottom: 10,
            borderBottomColor: theme.colors.cardBorderColor,
            backgroundColor: theme.colors.surface,
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
          }}
        >
          <View
            style={{
              flex: 1,
              alignItems: "center",
              flexDirection: "row",
              justifyContent: "space-between",
              backgroundColor: theme.colors.screenBackground,
              borderBottomLeftRadius: 8,
              borderBottomRightRadius: 8,
              paddingVertical: 6,
              paddingLeft: 12,
              paddingRight: 6,
            }}
          >
            <TouchableOpacity
              style={{
                // Updated shadow for iOS
                shadowColor: "#555", // Lighter shadow color for better visibility
                shadowOffset: { width: 0, height: 0 }, // Increased offset for better visibility
                shadowOpacity: 0.7, // Increased opacity for stronger shadow
                shadowRadius: 5, // Increased radius for smoother shadow
                // Updated shadow for Android
                backgroundColor: theme.colors.surface,
                paddingHorizontal: 6,
                borderRadius: 8,
              }}
              onPress={() => {
                if (section.id === "Water") {
                  handleOpenWaterLogEntryModal();
                } else {
                  handleOpenFoodEntryModal(section);
                }
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <Ionicons
                name="add"
                size={24} // You can adjust the size as needed
                color={theme.colors.primary} // Adjust color to match your theme
              />
            </TouchableOpacity>
            <MealSectionToggleMenu mealType={section} />
          </View>
        </View>
      )
    );
  };

  const carouselSlides = useMemo(
    () => [
      {
        type: "CaloriesAndWaterProgressSection",
        calorieData,
        caloriesBurned: caloriesBurned,
        waterProgressBarPercentage: totalDailyWaterConsumedPercentage / 100,
      },
      {
        type: "MacroProgressSection",
        macroData,
      },
    ],
    [calorieData, caloriesBurned, totalDailyWaterConsumedPercentage, macroData]
  );

  const [refreshSectionList, setRefreshSectionList] = useState(false);

  // Calculate the total number of items to render initially, including headers and footers
  const totalItemsToRender = useMemo(() => {
    return sections.reduce(
      (total, section) => total + section.data.length + 2,
      0
    ); // +2 for the header and footer of each section
  }, [sections]);

  return (
    <View style={styles.safeAreaView}>
      {/* Date Selector Section */}
      <DateSelector />

      <View
        style={{
          alignItems: "center",
          justifyContent: "center",
          height: screenHeight * 0.26,
        }}
      >
        <CarouselWithIndicators
          carouselHeight={screenHeight * 0.23}
          carouselWidth={screenWidth}
          carouselSlides={carouselSlides}
          renderItem={({ item }) => <CarouselRenderItemComponent item={item} />}
          theme={theme}
        />
      </View>

      <View
        style={{
          backgroundColor: theme.colors.screenBackground,
          flex: 1,
        }}
      >
        <SectionList
          sections={sections}
          keyExtractor={(item, index) => item.id}
          onRefresh={updateSections}
          refreshing={refreshSectionList}
          renderItem={renderItem}
          renderSectionHeader={renderSectionHeader}
          renderSectionFooter={renderSectionFooter}
          stickySectionHeadersEnabled
          contentContainerStyle={{ flexGrow: 1, paddingHorizontal: 8 }}
          style={{
            marginBottom: 16,
            backgroundColor: theme.colors.screenBackground,
          }}
          initialNumToRender={totalItemsToRender}
        />
      </View>

      <FoodlogFabGroupMenu
        isFocused={isFocused}
        openMealSectionCustomizationModal={
          handleOpenMealSectionCustomizationModal
        }
        openDailyNutritionGoalsCustomizationModal={
          handleOpenDailyNutritionGoalsCustomizationModal
        }
        openDailyNutritionGoalsCalculationModal={
          handleOpenDailyNutritionGoalsCalculationModal
        }
      />

      {/* Food Entry Modal opens when Add Food button is clicked */}
      <FoodEntryModal
        isVisible={isFoodEntryModalVisible}
        onCancel={handleCloseFoodEntryModal}
        activeFoodItem={activeFoodItem}
        setActiveFoodItem={setActiveFoodItem}
        activeMealSection={activeMealSection}
        selectedDate={selectedDate}
        isBuildingMeal={false}
      />
      {/* Meal Section Customization Modal opens when the Fab Group action button called Customize Meal Names is clicked */}
      <MealSectionCustomizationModal
        isVisible={isMealSectionCustomizationModalVisible}
        closeModal={handleCloseMealSectionCustomizationModal}
      />
      {/* Daily Nutrition Goals Customization opens when the Fab Group action button called Customize Daily Nutrition Goals is clicked */}
      <DailyNutritionGoalsCustomizationModal
        isVisible={isDailyNutritionGoalsCustomizationModalVisible}
        closeModal={handleCloseDailyNutritionGoalsCustomizationModal}
      />
      {/* Daily Nutrition Goals Calculation opens when the Fab Group action button called Calculate Daily Nutrition Goals is clicked */}
      <DailyNutritionGoalsCalculationModal
        isVisible={isDailyNutritionGoalsCalculationModalVisible}
        closeModal={handleCloseDailyNutritionGoalsCalculationModal}
      />
      {/* Food Nutrient Modal */}
      <FoodNutrientModal
        isVisible={isFoodNutrientModalVisible}
        closeModal={handleCloseFoodNutrientModal}
        activeFoodItem={activeFoodItem}
        setActiveFoodItem={setActiveFoodItem}
        foodNutrientModalType={"Edit Entry"}
        selectedDate={selectedDate}
        isBuildingMeal={false}
      />

      {/* Water Log Entry Modal opens when Add Water button is clicked */}
      <WaterLogEntryModal
        isVisible={isWaterLogEntryModalVisible}
        closeModal={handleCloseWaterLogEntryModal}
        activeItem={activeFoodItem}
      />

      {/* Menu Option Modal - Quick Add Food Entry */}
      <QuickAddModal
        isVisible={isQuickAddModalVisible}
        onClose={closeQuickAddModal}
        onAdd={addQuickFoodEntry}
        mealTypeId={activeMealSection?.id || ""}
        activeItem={activeFoodItem}
      />

      {/* Menu Option Modal - Copy From Date, Copy To Date */}
      <CopyMealSectionFromDateModal
        isVisible={isCopyMealSectionFromDateModalVisible}
        onClose={() => {
          setIsCopyMealSectionFromDateModalVisible(false);
          setCopyMealSectionFromDateModalType("");
        }}
        onDateConfirm={handleDateConfirm}
        activeMealSection={activeMealSection}
        modalType={copyMealSectionFromDateModalType}
      />

      {/* Menu Option Modal - Save As Meal */}
      <CreateCustomMealModal
        isVisible={isCreateCustomMealModalVisible}
        closeModal={() => {
          setIsCreateCustomMealModalVisible(!isCreateCustomMealModalVisible);
          clearTempCustomMeal();
        }}
      />
    </View>
  );
}
