import React, { useState, useEffect } from "react";
import { View, ActivityIndicator, Dimensions } from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { useThemeContext } from "../context/ThemeContext.js";
import { useNavigation } from "@react-navigation/native";
import { useAuth } from "../authentication/context/AuthContext.js";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js";
import { useNutritionProgram } from "../features/NutritionalProgram/context/NutritionProgramContext.js";

const { height: screenHeight, width: screenWidth } = Dimensions.get("window");

const AppLoadingScreen = () => {
  const { user, token, loading } = useAuth();
  const { fetchUserSettings, userSettings } = useUserSettings();
  const { loadMealSectionCustomizations, mealSections, foodEntries } =
    useFoodLog();
  const {
    loadActiveNutritionalProgram,
    loadInactiveNutritionalPrograms,
    activeNutritionalProgram,
    actionState,
  } = useNutritionProgram();

  const { theme } = useThemeContext();
  const navigation = useNavigation();

  const [isDataLoaded, setIsDataLoaded] = useState(false); // Track if data is fully loaded

  useEffect(() => {
    const loadData = async () => {
      if (!loading && user && token) {
        try {
          // Load user settings, meal sections, nutritional programs, and steps history
          await Promise.all([
            fetchUserSettings(),
            loadMealSectionCustomizations(),
            loadActiveNutritionalProgram(user.uid),
            loadInactiveNutritionalPrograms(user.uid),
          ]);

          // After all data is loaded, set the flag to true
          setIsDataLoaded(true);
        } catch (error) {
          console.error("Error loading data: ", error);
        }
      }
    };

    loadData();
  }, [loading, user, token]); // Run only when loading or user changes

  // Separate effect to handle navigation once everything is loaded
  useEffect(() => {
    if (
      isDataLoaded && // Check if data is fully loaded
      userSettings &&
      mealSections?.length > 0 &&
      activeNutritionalProgram !== undefined &&
      foodEntries &&
      actionState !== "loading"
    ) {
      setTimeout(() => {
        navigation.navigate("Main");
      }, 3000); // Add a 3-second delay before navigating
    }
  }, [
    isDataLoaded,
    userSettings,
    mealSections,
    activeNutritionalProgram,
    foodEntries,
    actionState,
  ]);

  return (
    <LinearGradient
      colors={["black", "white"]} // Adjusted for simplicity. Replace with theme-based colors if needed.
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
      start={{ x: 0, y: 0.8 }} // Start the gradient from the bottom of the screen
      end={{ x: 0, y: 0.2 }} // End the gradient at the top of the screen
    >
      <Image
        source={require("../../assets/brandmark-design-logo.png")}
        contentFit="contain"
        style={{
          width: screenWidth / 1.1,
          height: screenHeight / 7,
          alignSelf: "center",
          marginBottom: 30,
        }}
      />
      <ActivityIndicator size="large" color={"white"} />
    </LinearGradient>
  );
};

export default AppLoadingScreen;
