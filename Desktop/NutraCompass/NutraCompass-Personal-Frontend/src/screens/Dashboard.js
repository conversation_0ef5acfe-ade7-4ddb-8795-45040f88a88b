import React, { useState, useEffect } from "react";
import { Text, View, TouchableOpacity, ScrollView } from "react-native";
import { useIsFocused } from "@react-navigation/native";
import { FontAwesome, Ionicons } from "@expo/vector-icons";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import * as Haptics from "expo-haptics";
import { useAuth } from "../authentication/context/AuthContext.js";
import { useThemeContext } from "../context/ThemeContext.js";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";
import { useTime } from "../context/TimeContext.js";
import dashboardScreenStyles from "./styles/dashboardScreenStyles.js";
import LinearGradientCard from "../components/LinearGradientCard.js";
import ThreeLevelProgressRing from "../components/ThreeLevelProgressRing.js";
import { useNavigation } from "@react-navigation/native";
import StepsAndDistanceGoalModal from "../components/StepsAndDistanceGoalModal.js";
import { useStepsLog } from "../features/StepsLog/context/StepsLogContext.js";
import DatePickerModal from "../components/DatePickerModal.js";
import WaterGoalModal from "../features/FoodDiary/components/WaterGoalModal.js";
import ProgressRing from "../components/ProgressRing.js";
import { useNutritionProgram } from "../features/NutritionalProgram/context/NutritionProgramContext.js";
import NutritionGoalsAlignmentModal from "../features/FoodDiary/components/NutritionGoalsAlignmentModal.js";
import NutritionalProgramCard from "../features/NutritionalProgram/components/NutritionalProgramCard.js";

const DashboardScreen = () => {
  const isFocused = useIsFocused(); // Tells us if this screen is currently in focus
  const navigation = useNavigation();
  const styles = dashboardScreenStyles();
  const { user } = useAuth();
  const { getSelectedDateAsDate } = useTime();
  const { theme } = useThemeContext();
  const { totalDailyWaterConsumed, calorieData, macroData } = useFoodLog();
  const { getNutritionalGoals, getPhysicalFitnessGoals } = useUserSettings();
  const { waterGoal } = getNutritionalGoals();
  const { stepsGoal, distanceGoal, distanceUnit } = getPhysicalFitnessGoals();
  const {
    isNutritionalGoalsAlignmentNeeded,
    setIsNutritionalGoalsAlignmentNeeded,
  } = useNutritionProgram();
  const { caloriesBurned, steps, distance } = useStepsLog();
  const [
    isStepsAndDistanceGoalModalVisible,
    setIsStepsAndDistanceGoalModalVisible,
  ] = useState(false);
  const [isWaterGoalModalVisible, setIsWaterGoalModalVisible] = useState(false);
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  // local state to open/close the alignment modal
  const [isAlignmentModalOpen, setIsAlignmentModalOpen] = useState(false);

  // Whenever the screen is focused and alignment is needed, we can open the modal
  useEffect(() => {
    if (isFocused && isNutritionalGoalsAlignmentNeeded) {
      setIsAlignmentModalOpen(true);
    }
  }, [isFocused, isNutritionalGoalsAlignmentNeeded]);

  // Format selectedDate to match the currentDate format for display
  const formattedSelectedDate = new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(getSelectedDateAsDate());

  const getRelativeDateLabel = (selectedDate) => {
    const today = new Date();
    const selected = new Date(selectedDate);

    // Set both dates to the start of the day for accurate comparison
    today.setHours(0, 0, 0, 0);
    selected.setHours(0, 0, 0, 0);

    const diffTime = selected - today;
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

    switch (diffDays) {
      case 0:
        return "TODAY";
      case 1:
        return "TOMORROW";
      case -1:
        return "YESTERDAY";
      default:
        if (diffDays > 0) {
          return `${diffDays} DAYS FROM NOW`;
        } else {
          return `${Math.abs(diffDays)} DAYS AGO`;
        }
    }
  };

  const dateLabel = getRelativeDateLabel(getSelectedDateAsDate());

  return (
    <View style={styles.container}>
      {/** Home Header Container */}
      <View style={[styles.header, { flexDirection: "row" }]}>
        {/** Left half: Title on top, Date container on bottom */}
        <View
          style={{
            flex: 1,
            justifyContent: "space-between",
            paddingVertical: 4,
            paddingTop: 12,
          }}
        >
          {/* Title */}
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              fontSize: 22,
              fontWeight: "700",
            }}
          >
            NUTRACOMPASS
          </Text>

          {/* Date Container */}
          <TouchableOpacity onPress={() => setDatePickerVisible(true)}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                gap: 8,
              }}
            >
              <FontAwesome
                name="calendar"
                size={28}
                color={theme.colors.primaryTextColor}
              />
              <View style={{ alignItems: "flex-start" }}>
                <Text
                  style={{
                    color: theme.colors.primary,
                    fontSize: 12,
                    fontWeight: "600",
                  }}
                >
                  {dateLabel}
                </Text>
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    fontSize: 16,
                    fontWeight: "700",
                  }}
                >
                  {formattedSelectedDate}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>

        {/** Right half: AI navigation container */}
        <TouchableOpacity
          style={{
            width: "45%",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: theme.colors.surface,
            borderRadius: 8,
            marginLeft: 8,
            paddingVertical: 12,
            paddingHorizontal: 8,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.15,
            shadowRadius: 3,
            elevation: 3, // for Android shadow
          }}
          disabled={true} // Disable the button
          //onPress={() => navigation.navigate("AI")} // <-- Ensure "AI" is a valid route
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 4,
            }}
          >
            <Ionicons
              name="chatbubbles-outline"
              size={24}
              color={theme.colors.primaryTextColor}
              style={{ marginRight: 6 }}
            />
            <Text
              style={{
                color: theme.colors.primaryTextColor,
                fontSize: 16,
                fontWeight: "700",
              }}
            >
              AI Assistant
            </Text>
          </View>
          <Text
            style={{
              color: theme.colors.primaryTextColor || "#888",
              fontSize: 12,
              textAlign: "center",
              fontWeight: "500",
              maxWidth: "90%",
            }}
          >
            (Coming Soon)
          </Text>
          {/* Subtext describing the AI */}
          <Text
            style={{
              color: theme.colors.subTextColor || "#888",
              fontSize: 12,
              textAlign: "center",
              fontWeight: "500",
              maxWidth: "90%",
            }}
          >
            Your personal nutrition, fitness & wellness assistant
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={{ flexGrow: 1, width: "100%" }}
        contentContainerStyle={{
          flexGrow: 1,
          alignItems: "center",
          paddingBottom: 100,
        }}
      >
        {/** Summary Container Including Calories Remaining, Step Counter, Distance Covered, and Macro Consumption Progress */}
        <View
          style={{
            width: "100%",
            padding: 10,
            gap: 10,
          }}
        >
          {/** Calories Remaining Card */}
          <View
            style={{
              backgroundColor: theme.colors.surface,
              borderRadius: 8,
              padding: 8,
            }}
          >
            <Text
              style={{
                color: theme.colors.primaryTextColor,
                fontSize: 14,
                marginBottom: 14,
              }}
            >
              Calories Remaining
            </Text>
            <View style={{ flexDirection: "row" }}>
              <View
                style={{
                  flex: 1,
                  alignItems: "flex-start",
                  padding: 8,
                  paddingLeft: 4,
                  paddingBottom: 12,
                }}
              >
                {/** Calories Remaining Chart */}
                <ThreeLevelProgressRing
                  size={90}
                  percentages={[
                    calorieData?.foodConsumedPercentage, // Percentage for the right circle
                    calorieData?.netCalorieProgressPercentage,
                    calorieData?.caloriesBurnedPercentage, // Percentage for the left circle
                  ]}
                  values={[
                    calorieData.caloriesRemaining, // caloriesRemaining
                    calorieData.calorieGoal, // calorieGoal
                  ]}
                  fillColorsRight={{
                    consumedColor: "#A078A0", // Purple
                    remainingColor: "#D8BFD8", // Light Purple
                  }}
                  fillColorsCenter={{
                    consumedColor: [
                      theme.colors.secondary,
                      theme.colors.primary,
                    ], // Gradient
                    remainingColor: theme.colors.chartRemainingColor,
                  }}
                  fillColorsLeft={{
                    consumedColor: "#EB9A1A", // Orange
                    remainingColor: "#FFD580", // Light Orange
                  }}
                />
              </View>
              <View
                style={{
                  flex: 2,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <View style={{ flexDirection: "row", gap: 8 }}>
                  {/** Goal */}
                  <View style={{ alignItems: "flex-start" }}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 8,
                      }}
                    >
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 13,
                        }}
                      >
                        {calorieData.calorieGoal}
                      </Text>
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        -
                      </Text>
                    </View>
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 10,
                      }}
                    >
                      Goal
                    </Text>
                  </View>

                  {/** Food */}
                  <View style={{ alignItems: "flex-start" }}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 8,
                      }}
                    >
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 13,
                        }}
                      >
                        {Math.round(calorieData.totalCalories)}
                      </Text>
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        +
                      </Text>
                    </View>
                    <Text style={{ color: "#C29AC2", fontSize: 10 }}>Food</Text>
                  </View>

                  {/** Exercise */}
                  <View style={{ alignItems: "flex-start" }}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 18,
                      }}
                    >
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 13,
                        }}
                      >
                        {Math.round(caloriesBurned)}
                      </Text>
                      <Text
                        style={{
                          color: theme.colors.primaryTextColor,
                          fontSize: 12,
                        }}
                      >
                        =
                      </Text>
                    </View>
                    <Text style={{ color: "#EB9A1A", fontSize: 10 }}>
                      Exercise
                    </Text>
                  </View>

                  {/** Remaining */}
                  <View style={{ alignItems: "flex-start" }}>
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 13,
                      }}
                    >
                      {Math.round(calorieData.caloriesRemaining)}
                    </Text>

                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: 10,
                      }}
                    >
                      Remaining
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>

          {/** Water Intake, Steps, and Distance in one row */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              gap: 8,
            }}
          >
            {/** Water Intake Card */}
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setIsWaterGoalModalVisible(true);
              }}
              style={{
                flex: 1,
                borderRadius: 12,
                padding: 8,
                gap: 8,
                alignItems: "center",
                backgroundColor: theme.colors.surface,
                position: "relative", // Needed for absolute positioning of the icon
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: 14,
                  marginBottom: 5,
                  alignSelf: "flex-start",
                }}
              >
                Water Intake
              </Text>
              <ProgressRing
                size={90}
                strokeWidth={13}
                currentValue={Math.round(totalDailyWaterConsumed)}
                goalValue={waterGoal?.amount || 0}
                consumedColor={["#6DAEFF", "#6DAEFF"]}
                remainingColor="#80DFFF"
                topInteriorLabel={`${(
                  waterGoal?.amount || 0
                ).toLocaleString()}${
                  waterGoal?.unit === "fl oz" ? " fl oz" : "ml"
                }`}
                bottomInteriorLabel={`${Math.round(
                  totalDailyWaterConsumed
                ).toLocaleString()}${
                  waterGoal?.unit === "fl oz" ? " fl oz" : "ml"
                }`}
              />
              <MaterialCommunityIcons
                name="gesture-tap"
                size={24}
                color="gray"
                style={{
                  position: "absolute",
                  top: 5,
                  right: 5,
                }}
              />
            </TouchableOpacity>

            {/** Steps Card */}
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setIsStepsAndDistanceGoalModalVisible(true);
              }}
              style={{
                flex: 1,
                borderRadius: 12,
                padding: 8,
                gap: 8,
                alignItems: "center",
                backgroundColor: theme.colors.surface,
                position: "relative",
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: 14,
                  marginBottom: 5,
                  alignSelf: "flex-start",
                }}
              >
                Steps
              </Text>
              <ProgressRing
                size={90}
                strokeWidth={13}
                currentValue={steps}
                goalValue={stepsGoal || 1}
                consumedColor={[theme.colors.secondary, theme.colors.primary]}
                remainingColor={theme.colors.chartRemainingColor}
                topInteriorLabel={stepsGoal.toLocaleString()}
                bottomInteriorLabel={steps.toLocaleString()}
              />
              <MaterialCommunityIcons
                name="gesture-tap"
                size={24}
                color="gray"
                style={{
                  position: "absolute",
                  top: 5,
                  right: 5,
                }}
              />
            </TouchableOpacity>

            {/** Distance Card */}
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setIsStepsAndDistanceGoalModalVisible(true);
              }}
              style={{
                flex: 1,
                borderRadius: 12,
                padding: 8,
                gap: 8,
                alignItems: "center",
                backgroundColor: theme.colors.surface,
                position: "relative",
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: 14,
                  marginBottom: 5,
                  alignSelf: "flex-start",
                }}
              >
                Distance
              </Text>
              <ProgressRing
                size={90}
                strokeWidth={13}
                currentValue={distance.toFixed(2)}
                goalValue={distanceGoal || 1}
                consumedColor={[theme.colors.secondary, theme.colors.primary]}
                remainingColor={theme.colors.chartRemainingColor}
                topInteriorLabel={`${distanceGoal.toLocaleString()}${distanceUnit}`}
                bottomInteriorLabel={`${distance
                  .toFixed(2)
                  .toLocaleString()}${distanceUnit}`}
              />
              <MaterialCommunityIcons
                name="gesture-tap"
                size={24}
                color="gray"
                style={{
                  position: "absolute",
                  top: 5,
                  right: 5,
                }}
              />
            </TouchableOpacity>
          </View>

          {/** Macros Section */}
          <View
            style={{
              width: "100%",
              backgroundColor: theme.colors.surface,
              borderRadius: 8,
              padding: 8,
              paddingBottom: 35,
              gap: 8,
            }}
          >
            {/** Macros Header */}
            <Text
              style={{
                color: theme.colors.primaryTextColor,
                fontSize: 14,
                marginBottom: 14,
              }}
            >
              Macros
            </Text>

            {/** Row containing the three macro boxes */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
                padding: 4,
              }}
            >
              {/** Carbs */}
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  padding: 8,
                }}
              >
                <ProgressRing
                  size={90}
                  strokeWidth={10}
                  currentValue={macroData.carbsData.consumedGrams}
                  goalValue={macroData.carbsData.totalGramsGoal}
                  consumedColor={[
                    macroData.carbsData.consumedColor,
                    macroData.carbsData.consumedColor,
                  ]}
                  remainingColor={macroData.carbsData.remainingColor}
                  topOuterLabel="Carbs"
                  firstBottomOuterLabel={`${Math.round(
                    macroData.carbsData.percentage * 100
                  )}%`}
                  bottomOuterNumeratorLabel={String(
                    macroData.carbsData.consumedGrams
                  )}
                  bottomOuterDenominatorLabel={String(
                    macroData.carbsData.totalGramsGoal
                  )}
                  iconName={macroData.carbsData.iconName}
                  iconType="MaterialCommunityIcons"
                />
              </View>

              {/** Protein */}
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  padding: 8,
                }}
              >
                <ProgressRing
                  size={90}
                  strokeWidth={10}
                  currentValue={macroData.proteinData.consumedGrams}
                  goalValue={macroData.proteinData.totalGramsGoal}
                  consumedColor={[
                    macroData.proteinData.consumedColor,
                    macroData.proteinData.consumedColor,
                  ]}
                  remainingColor={macroData.proteinData.remainingColor}
                  topOuterLabel="Protein"
                  firstBottomOuterLabel={`${Math.round(
                    macroData.proteinData.percentage * 100
                  )}%`}
                  bottomOuterNumeratorLabel={String(
                    macroData.proteinData.consumedGrams
                  )}
                  bottomOuterDenominatorLabel={String(
                    macroData.proteinData.totalGramsGoal
                  )}
                  iconName={macroData.proteinData.iconName}
                  iconType="MaterialCommunityIcons"
                />
              </View>

              {/** Fat */}
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  padding: 8,
                }}
              >
                <ProgressRing
                  size={90}
                  strokeWidth={10}
                  currentValue={macroData.fatData.consumedGrams}
                  goalValue={macroData.fatData.totalGramsGoal}
                  consumedColor={[
                    macroData.fatData.consumedColor,
                    macroData.fatData.consumedColor,
                  ]}
                  remainingColor={macroData.fatData.remainingColor}
                  topOuterLabel="Fat"
                  firstBottomOuterLabel={`${Math.round(
                    macroData.fatData.percentage * 100
                  )}%`}
                  bottomOuterNumeratorLabel={String(
                    macroData.fatData.consumedGrams
                  )}
                  bottomOuterDenominatorLabel={String(
                    macroData.fatData.totalGramsGoal
                  )}
                  iconName={macroData.fatData.iconName}
                  iconType="MaterialCommunityIcons"
                />
              </View>
            </View>
          </View>
        </View>

        {/* Nutritional Program Card */}
        <NutritionalProgramCard theme={theme} />

        {/** Modals */}
        <DatePickerModal
          isVisible={isDatePickerVisible}
          onClose={() => setDatePickerVisible(false)}
        />

        <StepsAndDistanceGoalModal
          isVisible={isStepsAndDistanceGoalModalVisible}
          closeModal={() => setIsStepsAndDistanceGoalModalVisible(false)}
          stepGoal={stepsGoal}
          distanceGoal={distanceGoal}
          distanceUnit={distanceUnit}
        />

        <WaterGoalModal
          isVisible={isWaterGoalModalVisible}
          closeModal={() => setIsWaterGoalModalVisible(false)}
          waterGoal={waterGoal}
        />

        {/* We only MOUNT the alignment modal if the screen is focused, 
          then we use 'isVisible' to show/hide it. 
      */}
        {isFocused && (
          <NutritionGoalsAlignmentModal
            isVisible={
              isAlignmentModalOpen && isNutritionalGoalsAlignmentNeeded
            }
            onClose={() => setIsAlignmentModalOpen(false)}
          />
        )}
      </ScrollView>
    </View>
  );
};

export default DashboardScreen;
