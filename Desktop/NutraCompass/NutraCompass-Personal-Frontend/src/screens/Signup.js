import React, { useEffect } from "react";
// import { Asset } from "expo-asset";
// const logo = Asset.fromModule(
//   require("../../assets/brandmark-design-logo.png")
// );
// logo.downloadAsync();
import Icon from "@expo/vector-icons/MaterialCommunityIcons";
import {
  Text,
  View,
  Alert,
  TouchableWithoutFeedback,
  TouchableOpacity,
  Keyboard,
  SafeAreaView,
  Dimensions,
} from "react-native";
import { Image } from "expo-image";
import signupScreenStyles from "./styles/signupScreenStyles.js";
// Firebase API method imports
import { useAuth } from "../authentication/context/AuthContext.js";
import { useTime } from "../context/TimeContext.js";
import { MaterialIcons, FontAwesome } from "@expo/vector-icons";
import Feather from "react-native-vector-icons/Feather";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../context/ThemeContext.js";
import AboutYouSection from "../authentication/components/AboutYouSection.js";
import ActivityLevelSection from "../authentication/components/ActivityLevelSection.js";
import SetAGoalSection from "../authentication/components/SetAGoalSection.js";
import AccountDetailsSection from "../authentication/components/AccountDetailsSection.js";
import BodyTypeSection from "../authentication/components/BodyTypeSection.js";
import { LinearGradient } from "expo-linear-gradient";

const screenWidth = Dimensions.get("window").width;

const SectionBarComponent = ({ currentSection }) => {
  const { theme } = useThemeContext();

  // Render nothing if currentSection is greater than or equal to 5
  if (currentSection >= 5) {
    return null;
  }

  return (
    <View
      style={{
        marginVertical: 10,
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "row",
        gap: 10,
      }}
    >
      <View
        style={{
          borderRadius: 16,
          width: 50,
          height: 8,
          backgroundColor:
            currentSection >= 1
              ? theme.colors.primary
              : "rgba(169, 169, 169, 0.7)",
        }}
      />

      <View
        style={{
          borderRadius: 16,
          width: 50,
          height: 8,
          backgroundColor:
            currentSection >= 2
              ? theme.colors.primary
              : "rgba(169, 169, 169, 0.7)",
        }}
      />

      <View
        style={{
          borderRadius: 16,
          width: 50,
          height: 8,
          backgroundColor:
            currentSection >= 3
              ? theme.colors.primary
              : "rgba(169, 169, 169, 0.7)",
        }}
      />

      <View
        style={{
          borderRadius: 16,
          width: 50,
          height: 8,
          backgroundColor:
            currentSection >= 4
              ? theme.colors.primary
              : "rgba(169, 169, 169, 0.7)",
        }}
      />
    </View>
  );
};

function SignUpScreen({ navigation }) {
  const styles = signupScreenStyles(); // Use the imported styles
  const { theme } = useThemeContext();
  const {
    registration,
    signInWithGoogle,
    checkEmailExists,
    sendVerificationCode,
    verifyCode,
    resendCode,
  } = useAuth();
  const { deviceTimezone, deviceRegion } = useTime();

  const [currentSection, setCurrentSection] = React.useState(1);
  const [value, setValue] = React.useState({
    sex: "",
    birthday: "",
    age: null,
    height: { inches: null, centimeters: null, unit: "in" },
    weight: "",
    activityLevel: "",
    maintenanceCalories: null,
    weightTrendGoal: null,
    customEnergyTarget: null,
    bodyFatPercentageRange: null,
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    error: "",
  });

  const emptyState = () => {
    setValue({
      sex: "",
      birthday: "",
      age: null,
      height: { inches: null, centimeters: null, unit: "in" },
      weight: "",
      activityLevel: "",
      maintenanceCalories: null,
      weightTrendGoal: null,
      customEnergyTarget: null,
      bodyFatPercentageRange: null,
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      error: "",
    });
  };

  const handleNextSection = () => {
    setCurrentSection(currentSection + 1);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    //console.log("Values: ", JSON.stringify(value, null, 1));
  };

  const handleGoogleSignUp = async () => {
    try {
      // Calculate settings from form data
      const weightUnit = determineWeightUnit(value.weight);
      const parsedWeight = convertWeightToKg(value.weight, weightUnit);
      const firstCalorieGoal = calculateFirstCalorieGoal(value, parsedWeight);

      // Create settings object from form values
      const defaultSettings = createDefaultUserSettings(
        value,
        firstCalorieGoal
      );

      // Execute Google sign-up with form data
      await signInWithGoogle(defaultSettings);
    } catch (error) {
      console.error("Google sign-in error:", error);
    }
  };

  /**
   * Handles the sign-up process, including sending the verification code.
   */
  const handleSignUp = async () => {
    if (!isFormValid(value)) {
      return; // Exit early if the form is invalid
    }

    try {
      // Send the verification code to the user's email
      await sendVerificationCode(value.email, value.firstName);
      return true; // Indicate success
    } catch (error) {
      console.error("Error during signup:", error);
      Alert.alert("Signup Error", "Unable to proceed with signup.");
      return false; // Indicate failure
    }
  };

  /**
   * Verifies the code entered by the user and completes the registration process.
   */
  const handleVerifyCode = async (code) => {
    try {
      const isVerified = await verifyCode(value.email, code);

      if (isVerified) {
        // Calculate settings and complete registration
        const weightUnit = determineWeightUnit(value.weight);
        const parsedWeight = convertWeightToKg(value.weight, weightUnit);
        const firstCalorieGoal = calculateFirstCalorieGoal(value, parsedWeight);
        const defaultSettings = createDefaultUserSettings(
          value,
          firstCalorieGoal
        );

        await registration(value.email, value.password, defaultSettings);
        emptyState();
      } else {
        Alert.alert("Verification Failed", "Invalid code. Please try again.");
      }
    } catch (error) {
      console.error("Verification Error:", error);
      Alert.alert(
        "Verification Failed",
        "An error occurred during verification."
      );
    }
  };

  /**
   * Handles resending the verification code if the user didn't receive it.
   */
  const handleResendCode = async () => {
    try {
      await resendCode(value.email, value.firstName);
      //Alert.alert("Verification Code Resent", "Please check your email.");
    } catch (error) {
      console.error("Resend Code Error:", error);
      Alert.alert("Error", "Unable to resend verification code.");
    }
  };

  /**
   * Validates the sign-up form inputs.
   */
  const isFormValid = (value) => {
    if (!value.firstName || !value.lastName) {
      Alert.alert("Please enter your first name and last name.");
      return false;
    }
    if (!value.userName) {
      Alert.alert("Please enter your user name.");
      return false;
    }
    if (!value.email || !value.password) {
      Alert.alert("Email and password are mandatory.");
      return false;
    }
    if (value.password !== value.confirmPassword) {
      Alert.alert("Password and Confirm Password don't match.");
      return false;
    }
    return true;
  };

  /**
   * Determines the weight unit (lbs or kg) from the weight input.
   */
  const determineWeightUnit = (weight) => {
    const weightParts = weight.split(" ");
    return weightParts.length === 2 ? weightParts[1] : null;
  };

  /**
   * Converts weight to kilograms based on the provided unit.
   */
  const convertWeightToKg = (weight, unit) => {
    const weightValue = parseFloat(weight.split(" ")[0]);
    if (unit === "lbs") {
      return weightValue * 0.453592;
    }
    return weightValue; // Assumes the weight is already in kilograms
  };

  /**
   * Calculates the first daily calorie goal for the user.
   */
  const calculateFirstCalorieGoal = (value, weightInKg) => {
    if (value.customEnergyTarget) {
      return value.customEnergyTarget;
    }
    if (value.maintenanceCalories) {
      return value.maintenanceCalories;
    }
    if (
      value.activityLevel &&
      weightInKg &&
      value.sex &&
      value.age &&
      value.height.centimeters
    ) {
      return calculateDailyCalories(
        parseFloat(value.height.centimeters),
        weightInKg,
        value.sex,
        value.age,
        value.activityLevel,
        value.weightTrendGoal,
        value.bodyFatPercentageRange
      );
    }
    return 2000; // Default calorie goal if no inputs provided
  };

  /**
   * Creates the default settings object for a new user.
   */
  const createDefaultUserSettings = (value, calorieGoal) => ({
    profile: {
      firstName: value?.firstName || "",
      lastName: value?.lastName || "",
      userName: value?.userName || "",
      userNameLower: value?.userName?.toLowerCase() || "",
      email: value?.email || "",
      birthday: value.birthday,
      age: value.age,
      sex: value.sex,
      bodyWeight: value.weight,
      height: {
        inches: value.height.inches,
        centimeters: value.height.centimeters,
        unit: value.height.unit,
      },
      pictureUrl: "",
    },
    location: {
      timezone: deviceTimezone,
      region: deviceRegion,
    },
    appAppearance: {
      theme: "Default",
      isDark: true, // Default to dark theme
    },
    nutritionalGoals: {
      calorieGoal,
      waterGoal: { amount: 64, unit: "fl oz" },
      macroGoals: {
        carb: {
          dailyPercentage: 0.4,
          dailyCalories: calculateCarbDailyCalories(calorieGoal, 40),
          dailyGrams: calculateCarbDailyGrams(calorieGoal, 40),
        },
        protein: {
          dailyPercentage: 0.3,
          dailyCalories: calculateProteinDailyCalories(calorieGoal, 30),
          dailyGrams: calculateProteinDailyGrams(calorieGoal, 30),
        },
        fat: {
          dailyPercentage: 0.3,
          dailyCalories: calculateFatDailyCalories(calorieGoal, 30),
          dailyGrams: calculateFatDailyGrams(calorieGoal, 30),
        },
      },
    },
    physicalFitnessGoals: {
      stepsGoal: 10000,
      distanceGoal: 4.73,
      distanceUnit: "mi",
    },
  });

  // HELPER METHODS FOR DETERMINING DEFAULT USER SETTINGS VALUES

  const calculateDailyCalories = (
    heightInCm,
    weightInKg,
    sex,
    age,
    activityLevel,
    weightTrendGoal,
    bodyFatPercentageRange // This is the user's selected body fat percentage range
  ) => {
    let dailyCalories;

    // Check if bodyFatPercentageRange exists to determine which formula to use
    if (bodyFatPercentageRange) {
      // Use Katch-McArdle formula for increased accuracy if bodyFatPercentageRange exists

      // Calculate BMR based on Katch-McArdle formula
      const leanBodyMass = calculateLeanBodyMass(
        weightInKg,
        bodyFatPercentageRange
      );
      const bmr = 370 + 21.6 * leanBodyMass;

      // Adjust BMR based on activity level
      const activityMultiplier = getActivityMultiplier(activityLevel);
      const maintenanceCalories = Math.floor(bmr * activityMultiplier);

      // Adjust maintenance calories based on weight trend goal
      dailyCalories = adjustCaloriesForWeightTrend(
        maintenanceCalories,
        weightTrendGoal
      );
    } else {
      // Use Mifflin-St Jeor formula if bodyFatPercentageRange does not exist

      // Calculate BMR based on Mifflin-St Jeor formula
      let bmr;
      if (sex === "Male") {
        bmr = 10 * weightInKg + 6.25 * heightInCm - 5 * age + 5;
      } else if (sex === "Female") {
        bmr = 10 * weightInKg + 6.25 * heightInCm - 5 * age - 161;
      } else {
        console.error("Please select your gender.");
        return;
      }

      // Adjust BMR based on activity level
      const activityMultiplier = getActivityMultiplier(activityLevel);
      const maintenanceCalories = Math.floor(bmr * activityMultiplier);

      // Adjust maintenance calories based on weight trend goal
      dailyCalories = adjustCaloriesForWeightTrend(
        maintenanceCalories,
        weightTrendGoal
      );
    }

    return dailyCalories;
  };

  // Function to calculate lean body mass based on weight and body fat percentage range
  const calculateLeanBodyMass = (weightInKg, bodyFatPercentageRange) => {
    // Convert body fat percentage to a decimal
    const bodyFatPercentage = parseFloat(bodyFatPercentageRange) / 100;

    // Calculate lean body mass using the formula: Lean Body Mass = Weight - (Weight * Body Fat Percentage)
    const leanBodyMass = weightInKg - weightInKg * bodyFatPercentage;

    return leanBodyMass;
  };

  // Function to get activity multiplier based on activity level
  const getActivityMultiplier = (activityLevel) => {
    // Define activity multipliers based on activity level
    const activityMultipliers = {
      None: 1,
      "Sedentary (BMR x 0.2)": 1.2,
      "Lightly Active (BMR x 0.375)": 1.375,
      "Moderately Active (BMR x 0.5)": 1.5,
      "Very Active (BMR x 0.9)": 1.9,
    };
    return activityMultipliers[activityLevel];
  };

  // Function to adjust maintenance calories based on weight trend goal
  const adjustCaloriesForWeightTrend = (
    maintenanceCalories,
    weightTrendGoal
  ) => {
    // Adjust maintenance calories based on weight trend goal (losing or gaining weight)
    let dailyCalories = maintenanceCalories;
    if (weightTrendGoal < 0) {
      // Losing weight
      dailyCalories -= Math.abs(weightTrendGoal) * 500; // 1 lb per week = 500 calorie deficit per day
    } else if (weightTrendGoal > 0) {
      // Gaining weight
      dailyCalories += weightTrendGoal * 500; // 1 lb per week = 500 calorie surplus per day
    }
    return dailyCalories;
  };

  // Method to calculate total daily calorie carbs goal based on percentage of the total daily calories
  const calculateCarbDailyCalories = (totalDailyCalories, carbPercentage) => {
    return Math.round((totalDailyCalories * carbPercentage) / 100);
  };

  // Method to calculate total daily calorie protein goal based on percentage of the total daily calories
  const calculateProteinDailyCalories = (
    totalDailyCalories,
    proteinPercentage
  ) => {
    return Math.round((totalDailyCalories * proteinPercentage) / 100);
  };

  // Method to calculate total daily calorie fat goal based on percentage of the total daily calories
  const calculateFatDailyCalories = (totalDailyCalories, fatPercentage) => {
    return Math.round((totalDailyCalories * fatPercentage) / 100);
  };

  const calculateCarbDailyGrams = (totalDailyCalories, carbPercentage) => {
    const carbCaloriesPerGram = 4;
    return Math.round(
      (totalDailyCalories * (carbPercentage / 100)) / carbCaloriesPerGram
    );
  };

  const calculateProteinDailyGrams = (
    totalDailyCalories,
    proteinPercentage
  ) => {
    const proteinCaloriesPerGram = 4;
    return Math.round(
      (totalDailyCalories * (proteinPercentage / 100)) / proteinCaloriesPerGram
    );
  };

  const calculateFatDailyGrams = (totalDailyCalories, fatPercentage) => {
    const fatCaloriesPerGram = 9;
    return Math.round(
      (totalDailyCalories * (fatPercentage / 100)) / fatCaloriesPerGram
    );
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: theme.colors.screenBackground,
        }}
      >
        <LinearGradient
          //colors={["#2498E3", "#4AAE56"]}
          colors={["black", "white"]}
          style={{
            flex: 1,
          }}
          start={{ x: 0, y: 0.6 }}
          end={{ x: 0, y: 0.2 }}
        >
          <View
            style={{
              height: 85,
              minWidth: "100%",
              justifyContent: "center",
            }}
          >
            <TouchableOpacity
              style={{ alignSelf: "flex-start", padding: 15, zIndex: 2 }}
              onPress={() => {
                currentSection == 1
                  ? navigation.navigate("Welcome")
                  : setCurrentSection(currentSection - 1);

                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <Feather name="chevron-left" color={"black"} size={38} />
            </TouchableOpacity>
          </View>
          {/* <Image source={logo} style={styles.logo} /> */}

          <View style={styles.contentContainer}>
            <SectionBarComponent currentSection={currentSection} />
            {currentSection === 1 && (
              <AboutYouSection
                value={value}
                setValue={setValue}
                onNext={handleNextSection}
              />
            )}

            {currentSection === 2 && (
              <ActivityLevelSection
                value={value}
                setValue={setValue}
                onNext={handleNextSection}
              />
            )}

            {currentSection === 3 && (
              <SetAGoalSection
                value={value}
                setValue={setValue}
                onNext={handleNextSection}
              />
            )}

            {currentSection === 4 && (
              <BodyTypeSection
                value={value}
                setValue={setValue}
                onNext={handleNextSection}
              />
            )}

            {currentSection === 5 && (
              <View
                style={{
                  alignItems: "center",
                  padding: 20,
                  paddingTop: "50%",
                }}
              >
                <Text
                  style={{
                    fontSize: 18,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: theme.colors.primaryTextColor,
                  }}
                >
                  Join now to start{"\n"}your journey
                </Text>

                <View
                  style={{
                    width: screenWidth * 0.9,
                    alignItems: "stretch",
                    gap: 10,
                    marginTop: 20,
                  }}
                >
                  {/* Google Sign-In Button */}
                  <TouchableOpacity
                    style={{
                      flexDirection: "row",
                      backgroundColor: theme.colors.surface,
                      padding: 12,
                      borderRadius: 10,
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: 1,
                      borderColor: theme.colors.primaryTextColor,
                    }}
                    onPress={handleGoogleSignUp}
                  >
                    <Image
                      source={require("../../assets/google.png")}
                      style={{ width: 20, height: 20, marginRight: 10 }}
                    />
                    <Text style={{ color: "white", fontSize: 16 }}>
                      Continue with Google
                    </Text>
                  </TouchableOpacity>

                  {/* Email Sign-In Button */}
                  <TouchableOpacity
                    style={{
                      flexDirection: "row",
                      backgroundColor: theme.colors.surface,
                      padding: 12,
                      borderRadius: 10,
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: 1,
                      borderColor: theme.colors.primaryTextColor,
                    }}
                    onPress={handleNextSection} // Proceeds to AccountDetailsSection
                  >
                    <FontAwesome
                      name="envelope"
                      size={20}
                      color="white"
                      style={{ marginRight: 10 }}
                    />
                    <Text style={{ color: "white", fontSize: 16 }}>
                      Continue with Email
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {currentSection === 6 && (
              <AccountDetailsSection
                value={value}
                setValue={setValue}
                handleSignUp={handleSignUp}
                checkEmailExists={checkEmailExists}
                handleVerifyCode={handleVerifyCode}
                handleResendCode={handleResendCode}
              />
            )}
          </View>
        </LinearGradient>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
}

export default SignUpScreen;
