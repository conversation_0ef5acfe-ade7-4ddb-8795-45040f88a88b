import React, { useState } from "react";
import {
  Text,
  View,
  TouchableOpacity,
  Alert,
  Keyboard,
  Platform,
  SafeAreaView,
} from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import Feather from "react-native-vector-icons/Feather";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../context/ThemeContext.js";
import { TextInput, Button } from "react-native-paper";
import signinScreenStyles from "./styles/signinScreenStyles.js";
import { useAuth } from "../authentication/context/AuthContext.js";
import ResetPasswordModal from "../authentication/components/ResetPasswordModal.js";

// Validation utilities
const validateEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
const validatePassword = (password) =>
  password.length >= 8 && /\d/.test(password);

function SignInScreen({ navigation }) {
  const styles = signinScreenStyles(); // Use the imported styles
  const { theme } = useThemeContext();
  const { signIn } = useAuth();

  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] =
    useState(false);
  const [emailIconColor, setEmailIconColor] = useState("transparent");
  const [passwordIconColor, setPasswordIconColor] = useState("transparent");
  const [isSecureTextEntry, setIsSecureTextEntry] = useState(true);
  const [errors, setErrors] = useState({});
  const [isFormValid, setIsFormValid] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  const [value, setValue] = useState({
    email: "",
    password: "",
  });

  // Validate the form whenever inputs change
  const handleInputChange = (field, text) => {
    setValue((prev) => ({ ...prev, [field]: text }));

    const newErrors = { ...errors };

    if (field === "email") {
      if (!validateEmail(text)) {
        newErrors.email = "Invalid email address.";
      } else {
        delete newErrors.email;
      }
    }

    if (field === "password") {
      if (!validatePassword(text)) {
        newErrors.password =
          "Password must be at least 8 characters and include a number.";
      } else {
        delete newErrors.password;
      }
    }

    setErrors(newErrors);
    setIsFormValid(Object.keys(newErrors).length === 0);
  };

  const handleSignIn = async () => {
    if (!isFormValid) {
      Alert.alert("Invalid Input", "Please enter a valid email and password.");
      return;
    }

    Keyboard.dismiss(); // Unfocus the input fields
    setIsLoggingIn(true); // Disable the login button and show "Signing In..."

    try {
      await signIn(value.email, value.password);
      setValue({ email: "", password: "" }); // Reset input fields after successful login
    } catch (error) {
      // Differentiate error messages
      if (error.message.includes("auth")) {
        console.log("Login Failed", "The email or password is incorrect.");
      } else {
        console.log(
          "Login Failed",
          "An error occurred during validation. Please try again later."
        );
      }
    } finally {
      setIsLoggingIn(false); // Re-enable the login button
      setIsFormValid(false); // Disable login until corrected
    }
  };

  const toggleResetPasswordModal = () => {
    setIsResetPasswordModalVisible(!isResetPasswordModalVisible);
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
      }}
    >
      <TouchableOpacity
        style={{ flex: 1 }}
        activeOpacity={1}
        onPress={Keyboard.dismiss}
      >
        <LinearGradient
          colors={["black", "white"]}
          style={{
            flex: 1,
          }}
          start={{ x: 0, y: 0.6 }}
          end={{ x: 0, y: 0.2 }}
        >
          <View
            style={{
              height: 85,
              minWidth: "100%",
              justifyContent: "center",
            }}
          >
            <TouchableOpacity
              style={{ alignSelf: "flex-start", padding: 15 }}
              onPress={() => {
                navigation.navigate("Welcome");
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <Feather name="chevron-left" color={"black"} size={38} />
            </TouchableOpacity>
          </View>

          <View style={{ flex: 1 }}>
            <Image
              source={require("../../assets/brandmark-design-logo.png")}
              style={styles.logo}
            />
            <View
              style={{
                ...styles.contentContainer,
                gap: 10,
                paddingTop: "15%",
              }}
            >
              <View
                style={{ justifyContent: "center", width: "100%", gap: 20 }}
              >
                <TextInput
                  placeholder="EMAIL"
                  value={value.email}
                  style={{
                    backgroundColor: "transparent",
                    paddingLeft: 12,
                    fontSize: 14,
                  }}
                  textColor="white"
                  outlineStyle={{ borderRadius: 14 }}
                  onChangeText={(text) => handleInputChange("email", text)}
                  autoCapitalize="none"
                  mode="flat"
                  onFocus={() => {
                    setEmailIconColor("white");
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  onBlur={() => setEmailIconColor("transparent")}
                  left={
                    <TextInput.Icon
                      icon="account"
                      color={"lightgray"}
                      style={{ backgroundColor: emailIconColor }}
                    />
                  }
                />
                {errors.email && (
                  <Text style={{ color: "red", fontSize: 12 }}>
                    {errors.email}
                  </Text>
                )}

                <TextInput
                  placeholder="PASSWORD"
                  value={value.password}
                  style={{
                    backgroundColor: "transparent",
                    paddingLeft: 12,
                    fontSize: 14,
                  }}
                  textColor="white"
                  outlineStyle={{ borderRadius: 14 }}
                  onChangeText={(text) => handleInputChange("password", text)}
                  secureTextEntry={isSecureTextEntry}
                  autoCapitalize="none"
                  mode="flat"
                  onFocus={() => {
                    setPasswordIconColor("white");
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  onBlur={() => setPasswordIconColor("transparent")}
                  left={
                    <TextInput.Icon
                      icon="lock"
                      color={"lightgray"}
                      style={{ backgroundColor: passwordIconColor }}
                    />
                  }
                  right={
                    <TextInput.Icon
                      icon="eye"
                      color={"lightgray"}
                      onPress={() => setIsSecureTextEntry(!isSecureTextEntry)}
                    />
                  }
                />
                {errors.password && (
                  <Text style={{ color: "red", fontSize: 12 }}>
                    {errors.password}
                  </Text>
                )}
              </View>

              <Button
                mode="contained"
                style={{
                  backgroundColor:
                    isFormValid && !isLoggingIn ? "white" : "gray",
                  borderRadius: 8,
                  paddingVertical: 2,
                  marginVertical: 15,
                  width: "90%",
                }}
                labelStyle={{
                  color: isFormValid && !isLoggingIn ? "black" : "darkgray",
                  fontSize: 18,
                  fontWeight: "bold",
                }}
                onPress={handleSignIn}
                disabled={!isFormValid || isLoggingIn}
              >
                {isLoggingIn ? "LOGGING IN..." : "LOG IN"}
              </Button>

              <Text
                style={{
                  color: "gray",
                  marginTop: 5,
                  marginBottom: 15,
                }}
                onPress={() => {
                  toggleResetPasswordModal();
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                Forgot your password?
              </Text>
              <Text
                style={{
                  color: "white",
                  fontSize: 16,
                  marginTop: 20,
                  textAlign: "center",
                  marginBottom: 20,
                }}
              >
                Don't Have an account?{" "}
                <Text
                  style={{ color: "white" }}
                  onPress={() => {
                    navigation.navigate("Sign Up");
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                >
                  SIGN UP
                </Text>
              </Text>
            </View>
          </View>
        </LinearGradient>
      </TouchableOpacity>
      <ResetPasswordModal
        visible={isResetPasswordModalVisible}
        onClose={() => setIsResetPasswordModalVisible(false)}
      />
    </SafeAreaView>
  );
}

export default SignInScreen;
