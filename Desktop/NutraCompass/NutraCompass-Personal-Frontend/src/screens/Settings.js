import React from "react";
import {
  Text,
  View,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import settingsScreenStyles from "./styles/settingsScreenStyles.js";
import { useThemeContext } from "../context/ThemeContext.js";

function SettingsScreen() {
  const styles = settingsScreenStyles();
  const { theme } = useThemeContext();
  const navigation = useNavigation();

  const handleNavigateToProfile = () => {
    navigation.navigate("ProfileSettings");
  };

  const handleNavigateToNotifications = () => {
    navigation.navigate("NotificationsSettings");
  };

  const handleNavigateToNutrition = () => {
    navigation.navigate("NutritionSettings");
  };

  const handleNavigateToAccount = () => {
    navigation.navigate("AccountSettings");
  };

  const settingsOptions = [
    {
      name: "Profile",
      icon: "account-circle-outline",
      onPress: handleNavigateToProfile,
    },
    // {
    //   name: "Nutrition Settings",
    //   icon: "food",
    //   onPress: handleNavigateToNutrition,
    // },
    {
      name: "Notifications",
      icon: "bell-outline",
      onPress: handleNavigateToNotifications,
    },
    {
      name: "Account",
      icon: "account-settings-outline",
      onPress: handleNavigateToAccount,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Text
        style={{
          alignSelf: "center",
          color: theme.colors.primaryTextColor,
          fontSize: 18,
          fontWeight: "700",
          paddingBottom: 18,
        }}
      >
        Account
      </Text>

      <ScrollView contentContainerStyle={{ paddingBottom: 50 }}>
        {settingsOptions.map((option, index) => (
          <TouchableOpacity
            key={index}
            onPress={option.onPress}
            style={[
              styles.rowContainer,
              index < settingsOptions.length - 1 && styles.rowBorder,
            ]}
          >
            <View style={styles.rowLeft}>
              <MaterialCommunityIcons
                name={option.icon}
                size={24}
                color={theme.colors.primaryTextColor}
                style={styles.rowIcon}
              />
              <Text style={styles.rowText}>{option.name}</Text>
            </View>
            <MaterialCommunityIcons
              name="chevron-right"
              size={24}
              color={theme.colors.subTextColor}
            />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

export default SettingsScreen;
