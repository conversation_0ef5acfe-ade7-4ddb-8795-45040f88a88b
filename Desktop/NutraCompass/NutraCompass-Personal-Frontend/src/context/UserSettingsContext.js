import React, {
    createContext,
    useContext,
    useState,
    useEffect,
    useMemo,
  } from "react";
  import { useAuth } from "../../../authentication/context/AuthContext.js";
  import { storage } from "../../../config/firebase.js";
  import {
    ref,
    uploadBytes,
    getDownloadURL,
    deleteObject,
  } from "firebase/storage";
  import Configs from "../../../../constants.js";
  
  const UserSettingsContext = createContext();
  
  export function useUserSettings() {
    return useContext(UserSettingsContext);
  }
  
  export function UserSettingsProvider({ children }) {
    const { user, token } = useAuth();
    const userId = user?.uid;
    const [userSettings, setUserSettings] = useState(null);
    const apiUrl = Configs.NutraCompass_API_URL; // Base URL for API, should move to environment variables for production
  
    // Fetch user settings from the backend when the component mounts
    useEffect(() => {
      const fetchUserSettings = async () => {
        if (userId && token) {
          try {
            const response = await fetch(`${apiUrl}/v1/settings/${userId}`, {
              headers: {
                Authorization: `Bearer ${token}`, // Add the token to the Authorization header
              },
            });
  
            if (!response.ok) {
              const errorText = await response.text();
              console.error("Error fetching user settings:", errorText);
              return;
            }
  
            const data = await response.json();
            setUserSettings(data);
          } catch (error) {
            console.error("Error fetching user settings:", error);
          }
        }
      };
  
      fetchUserSettings();
    }, [userId, token]);
  
    // Update user settings in the backend
    const updateUserSettings = async (updatedSettings) => {
      if (userId) {
        try {
          await fetch(`${apiUrl}/v1/settings/${userId}`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(updatedSettings),
          });
          setUserSettings((prevSettings) => ({
            ...prevSettings,
            ...updatedSettings,
          }));
        } catch (error) {
          console.error("Error updating user settings:", error);
        }
      }
    };
  
    // Method to calculate total daily calorie carbs goal based on percentage of the total daily calories
    const calculateCarbDailyCalories = (totalDailyCalories, carbPercentage) => {
      return Math.round((totalDailyCalories * carbPercentage) / 100);
    };
  
    // Method to calculate total daily calorie protein goal based on percentage of the total daily calories
    const calculateProteinDailyCalories = (
      totalDailyCalories,
      proteinPercentage
    ) => {
      return Math.round((totalDailyCalories * proteinPercentage) / 100);
    };
  
    // Method to calculate total daily calorie fat goal based on percentage of the total daily calories
    const calculateFatDailyCalories = (totalDailyCalories, fatPercentage) => {
      return Math.round((totalDailyCalories * fatPercentage) / 100);
    };
  
    const calculateCarbDailyGrams = (totalDailyCalories, carbPercentage) => {
      const carbCaloriesPerGram = 4;
      return Math.round(
        (totalDailyCalories * (carbPercentage / 100)) / carbCaloriesPerGram
      );
    };
  
    const calculateProteinDailyGrams = (
      totalDailyCalories,
      proteinPercentage
    ) => {
      const proteinCaloriesPerGram = 4;
      return Math.round(
        (totalDailyCalories * (proteinPercentage / 100)) / proteinCaloriesPerGram
      );
    };
  
    const calculateFatDailyGrams = (totalDailyCalories, fatPercentage) => {
      const fatCaloriesPerGram = 9;
      return Math.round(
        (totalDailyCalories * (fatPercentage / 100)) / fatCaloriesPerGram
      );
    };
  
    // Function to upload profile picture
    const uploadProfilePicture = async ({ uri }) => {
      if (!userId || !uri) return null;
  
      try {
        const response = await fetch(uri);
        const blob = await response.blob();
        // Use a fixed filename for the profile picture, e.g., "profilePic.jpg"
        const imageRef = ref(storage, `profilePictures/${userId}/profilePic.jpg`);
        await uploadBytes(imageRef, blob);
        const downloadURL = await getDownloadURL(imageRef);
  
        // Assuming you have the user's current profile data available
        const currentProfile = userSettings.profile || {};
        // Update the profile with the new picture URL
        const newProfile = { ...currentProfile, pictureUrl: downloadURL };
        await setUserProfile(newProfile);
  
        return downloadURL;
      } catch (error) {
        console.error("Error uploading profile picture:", error);
        return null; // Handle the error appropriately
      }
    };
  
    // Function to remove profile picture
    const removeProfilePicture = async () => {
      if (!userId) return null;
  
      try {
        // Use a fixed filename for the profile picture, e.g., "profilePic.jpg"
        const imageRef = ref(storage, `profilePictures/${userId}/profilePic.jpg`);
        await deleteObject(imageRef); // Use Firebase Storage delete function
        // Update the user profile to remove the picture URL
        const currentProfile = userSettings.profile || {};
        const newProfile = { ...currentProfile, pictureUrl: null };
        await setUserProfile(newProfile);
      } catch (error) {
        console.error("Error removing profile picture:", error);
        throw error;
      }
    };
  
    // Getter methods
    const getUserProfile = () => userSettings?.profile || {};
    const getAppAppearance = () => userSettings?.appAppearance || {};
    const getNutritionalGoals = () => userSettings?.nutritionalGoals || {};
    const getPhysicalFitnessGoals = () => {
      return {
        stepsGoal: userSettings?.physicalFitnessGoals?.stepsGoal ?? 0,
        distanceGoal: userSettings?.physicalFitnessGoals?.distanceGoal ?? 0,
        distanceUnit: userSettings?.physicalFitnessGoals?.distanceUnit ?? "km",
      };
    };
  
    // Setter methods with added error handling
  
    const setUserProfile = async (newProfile) => {
      try {
        await updateUserSettings({ profile: newProfile });
      } catch (error) {
        console.error("Failed to update profile:", error);
      }
    };
  
    const setAppAppearance = async (newAppearance) => {
      try {
        await updateUserSettings({ appAppearance: newAppearance });
      } catch (error) {
        console.error("Failed to update app appearance:", error);
      }
    };
  
    // Set calorie and macro goals that's in nutritional goals
    const setCalorieAndMacroGoals = async (newGoals) => {
      if (!userId) return; // Ensure there's a user logged in
  
      try {
        const currentGoals = userSettings.nutritionalGoals || {}; // Fetch existing goals
  
        // Calculate new macros based on the input
        const newMacroGoals = {
          carb: {
            dailyPercentage: newGoals.carbPercentage / 100,
            dailyCalories: calculateCarbDailyCalories(
              newGoals.calorieGoal,
              newGoals.carbPercentage
            ),
            dailyGrams: calculateCarbDailyGrams(
              newGoals.calorieGoal,
              newGoals.carbPercentage
            ),
          },
          protein: {
            dailyPercentage: newGoals.proteinPercentage / 100,
            dailyCalories: calculateProteinDailyCalories(
              newGoals.calorieGoal,
              newGoals.proteinPercentage
            ),
            dailyGrams: calculateProteinDailyGrams(
              newGoals.calorieGoal,
              newGoals.proteinPercentage
            ),
          },
          fat: {
            dailyPercentage: newGoals.fatPercentage / 100,
            dailyCalories: calculateFatDailyCalories(
              newGoals.calorieGoal,
              newGoals.fatPercentage
            ),
            dailyGrams: calculateFatDailyGrams(
              newGoals.calorieGoal,
              newGoals.fatPercentage
            ),
          },
        };
  
        // Merge new goals with existing ones
        const updatedGoals = {
          ...currentGoals,
          calorieGoal: newGoals.calorieGoal, // Overwrite calorie goal
          macroGoals: { ...currentGoals.macroGoals, ...newMacroGoals }, // Merge macro goals
        };
  
        // Update user settings with the merged goals
        await updateUserSettings({ nutritionalGoals: updatedGoals });
      } catch (error) {
        console.error("Failed to update nutritional goals:", error);
      }
    };
  
    // Set water consumption goal that's in nutritional goals
    const setWaterGoal = async (waterGoal) => {
      if (!userId) return; // Ensure there's a user logged in
  
      try {
        const updatedSettings = {
          nutritionalGoals: {
            ...userSettings.nutritionalGoals, // Preserve existing nutritional goals
            waterGoal: waterGoal, // Update only the water goal
          },
        };
  
        await updateUserSettings(updatedSettings);
      } catch (error) {
        console.error("Failed to update water goal:", error);
      }
    };
  
    const setPhysicalFitnessGoals = async (newGoals) => {
      try {
        await updateUserSettings({ physicalFitnessGoals: newGoals });
      } catch (error) {
        console.error("Failed to update physical fitness goals:", error);
      }
    };
  
    const contextValue = useMemo(() => {
      return {
        userSettings,
        getUserProfile,
        getAppAppearance,
        getNutritionalGoals,
        getPhysicalFitnessGoals,
        setUserProfile,
        setAppAppearance,
        setCalorieAndMacroGoals,
        setPhysicalFitnessGoals,
        calculateProteinDailyGrams,
        calculateCarbDailyGrams,
        calculateFatDailyGrams,
        uploadProfilePicture,
        removeProfilePicture,
        setWaterGoal,
      };
    }, [
      userSettings,
      getUserProfile,
      getAppAppearance,
      getNutritionalGoals,
      getPhysicalFitnessGoals,
      setUserProfile,
      setAppAppearance,
      setCalorieAndMacroGoals,
      setPhysicalFitnessGoals,
      calculateProteinDailyGrams,
      calculateCarbDailyGrams,
      calculateFatDailyGrams,
      uploadProfilePicture,
      removeProfilePicture,
      setWaterGoal,
    ]);
  
    return (
      <UserSettingsContext.Provider value={contextValue}>
        {children}
      </UserSettingsContext.Provider>
    );
  }