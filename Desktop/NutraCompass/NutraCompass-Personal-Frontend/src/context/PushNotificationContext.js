import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import * as Notifications from "expo-notifications";
import { Platform } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";
import { useAuth } from "../authentication/context/AuthContext.js";

const DEFAULT_SETTINGS = {
  meals: {
    "Meal 1": { name: "Breakfast", hour: 8, minute: 0, enabled: false },
    "Meal 2": { name: "Lunch", hour: 12, minute: 0, enabled: false },
    "Meal 3": { name: "Dinner", hour: 18, minute: 0, enabled: false },
    "Meal 4": { name: "", hour: 10, minute: 0, enabled: false },
    "Meal 5": { name: "", hour: 15, minute: 0, enabled: false },
    "Meal 6": { name: "", hour: 21, minute: 0, enabled: false },
    Water: { name: "Water", hour: 12, minute: 0, enabled: false },
  },
  steps: { hour: 14, minute: 0, enabled: false },
};

// Notification templates
const MEAL_NOTIFICATION_TEMPLATES = [
  {
    title: "🍽️ Time to log {mealName}",
    body: "Hey {userName}, record your meal!",
  },
  { title: "Ready for {mealName}? 😋", body: "Log your meal, {userName}!" },
  {
    title: "🍴 {mealName} Time!",
    body: "{userName}, your {mealName} is waiting to be logged!",
  },
  {
    title: "Hungry? Let's log {mealName}!",
    body: "Fuel your day right, {userName}! Track your {mealName} now 🥗",
  },
  {
    title: "📱 Time to Snap & Log {mealName}",
    body: "Don't forget to record your {mealName}, {userName}! 📸",
  },
  {
    title: "🌟 {mealName} Reminder",
    body: "Consistency is key, {userName}! Log your {mealName} for progress",
  },
  {
    title: "🥄 Did you eat {mealName}?",
    body: "Track it now, {userName}! Small entries lead to big results 💪",
  },
  {
    title: "🔔 {mealName} Alert!",
    body: "Hey {userName}, don't let this meal go unrecorded!",
  },
  {
    title: "📝 {mealName} Journal Time",
    body: "Your future self will thank you for tracking, {userName}!",
  },
  {
    title: "⏰ Time Check: {mealName}",
    body: "Quick reminder to log your meal, {userName}! ⏳",
  },
  {
    title: "🍔 Variety Matters!",
    body: "How was your {mealName}, {userName}? Log it for nutritional balance",
  },
  {
    title: "🧠 Mindful Eating Reminder",
    body: "{userName}, take a moment to log your {mealName} consciously",
  },
];

const WATER_REMINDER_TEMPLATES = [
  {
    title: "💧 Time to Hydrate!",
    body: "Hey {userName}, don't forget to log your water intake!",
  },
  {
    title: "🚰 Water Break Reminder",
    body: "{userName}, your body needs hydration! Track your water now",
  },
  {
    title: "🌊 Stay Hydrated!",
    body: "Hey {userName}, time to log some H2O 💦",
  },
  {
    title: "💦 Water Tracking Time",
    body: "{userName}, remember to record your water consumption!",
  },
  {
    title: "🚰 Hydration Station Alert",
    body: "Hey {userName}, let's log that water intake!",
  },
];

const STEP_GOAL_TEMPLATES = [
  {
    title: "🚶♂️ Step Goal Reminder",
    body: "Hey {userName}, time to hit your daily step target!",
  },
  {
    title: "Walking Time! 🏃♀️",
    body: "You're close to your step goal, {userName}! Keep moving!",
  },
  {
    title: "👟 Step It Up!",
    body: "{userName}, your daily step target is calling! 🚶♂️",
  },
  {
    title: "🌞 Midday Movement Check",
    body: "How's your step progress, {userName}? Time to stretch those legs!",
  },
  {
    title: "🎯 Step Goal Ahead!",
    body: "You've got this, {userName}! Crush your daily step target 💥",
  },
  {
    title: "🚦 Step Counter Active",
    body: "Hey {userName}, keep moving! Every step counts towards your goal",
  },
  {
    title: "🏅 Step Champion Reminder",
    body: "{userName}, your step throne awaits! Claim it today 👑",
  },
  {
    title: "🌆 Evening Step Boost",
    body: "Sunset stroll time, {userName}! Let's hit those step numbers 🌇",
  },
  {
    title: "📈 Progress Checkpoint",
    body: "{userName}, you're X steps away from your goal! Keep going 🚶♀️",
  },
  {
    title: "🎵 Walking Rhythm Alert",
    body: "Put on your favorite tunes and step it out, {userName}! 🎧",
  },
  {
    title: "🌱 Fresh Air + Steps = 🌟",
    body: "Combine nature with progress, {userName}! Time for a walk",
  },
  {
    title: "🏃♀️ Step Sprint Opportunity",
    body: "Quick burst of steps could make all the difference, {userName}!",
  },
];

// Move templates outside the provider for better performance
const getRandomTemplate = (templates) =>
  templates[Math.floor(Math.random() * templates.length)];

const replacePlaceholders = (text, replacements) => {
  return Object.entries(replacements).reduce(
    (str, [key, value]) => str.replace(new RegExp(`{${key}}`, "g"), value),
    text
  );
};

const PushNotificationContext = createContext();

/**
 * Local notification management system
 *
 * Responsibilities:
 * - Meal reminder scheduling/management
 * - Notification permission handling
 * - Notification content updates
 */
export const PushNotificationProvider = ({ children }) => {
  const [scheduledNotifications, setScheduledNotifications] = useState([]);
  const [permissions, setPermissions] = useState({ granted: false });
  const [error, setError] = useState(null);
  const [notificationSettings, setNotificationSettings] =
    useState(DEFAULT_SETTINGS);
  const isInitialized = useRef(false);
  const { user } = useAuth();
  const { getUserProfile } = useUserSettings();
  const userProfile = getUserProfile();

  // Get storage key based on current user
  const getStorageKey = useCallback(() => {
    const userId = user?.uid;
    return userId ? `@notification_settings_${userId}` : null;
  }, [user?.uid]);

  // Load stored settings
  useEffect(() => {
    const loadStoredSettings = async () => {
      try {
        const storageKey = getStorageKey();
        if (!storageKey) {
          setNotificationSettings(DEFAULT_SETTINGS);
          return;
        }

        const storedData = await AsyncStorage.getItem(storageKey);
        setNotificationSettings(
          storedData ? JSON.parse(storedData) : DEFAULT_SETTINGS
        );
      } catch (error) {
        console.error("Failed to load notification settings:", error);
        setNotificationSettings(DEFAULT_SETTINGS);
      }
    };
    loadStoredSettings();
  }, [getStorageKey]); // Reload when user changes

  // Persist settings
  useEffect(() => {
    const saveSettings = async () => {
      const storageKey = getStorageKey();
      if (!storageKey) return;

      await AsyncStorage.setItem(
        storageKey,
        JSON.stringify(notificationSettings)
      );
    };
    saveSettings();
  }, [notificationSettings, getStorageKey]);

  // Sync notifications with settings
  useEffect(() => {
    const syncNotifications = async () => {
      if (!isInitialized.current) return;

      // Cancel all existing notifications
      await Notifications.cancelAllScheduledNotificationsAsync();

      // Schedule meals
      Object.entries(notificationSettings.meals).forEach(
        async ([mealId, { hour, minute, enabled, name }]) => {
          if (enabled && name) {
            // Only schedule if name exists
            await scheduleMealReminder(
              mealId,
              name, // Use stored name
              {
                hour,
                minute,
                repeats: true,
              }
            );
          }
        }
      );

      // Schedule steps
      if (notificationSettings.steps.enabled) {
        await scheduleStepReminder(notificationSettings.steps);
      }
    };

    syncNotifications();
  }, [notificationSettings]);

  // Notification handler setup
  const setupNotificationHandler = useCallback(() => {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: false,
      }),
    });
  }, []);

  // Settings updaters
  const updateMealSettings = useCallback((mealId, settings) => {
    setNotificationSettings((prev) => ({
      ...prev,
      meals: { ...prev.meals, [mealId]: settings },
    }));
  }, []);

  const updateStepSettings = useCallback((settings) => {
    setNotificationSettings((prev) => ({
      ...prev,
      steps: { ...prev.steps, ...settings },
    }));
  }, []);

  // Permissions
  const checkPermissions = useCallback(async () => {
    const { status } = await Notifications.getPermissionsAsync();
    setPermissions({ granted: status === "granted" });
    return status === "granted";
  }, []);

  const requestPermissions = useCallback(async () => {
    const { status } = await Notifications.requestPermissionsAsync({
      ios: { allowAlert: true, allowSound: true },
      android: { channelId: "meal-reminders" },
    });
    return status === "granted";
  }, []);

  // Notification scheduling
  const scheduleMealReminder = useCallback(
    async (mealId, mealName, { hour, minute, repeats }) => {
      // Choose template based on meal type
      const template = getRandomTemplate(
        mealId === "Water"
          ? WATER_REMINDER_TEMPLATES
          : MEAL_NOTIFICATION_TEMPLATES
      );

      const userName = userProfile?.firstName || "User";
      const replacements =
        mealId === "Water" ? { userName } : { mealName, userName };

      await Notifications.scheduleNotificationAsync({
        content: {
          title: replacePlaceholders(template.title, replacements),
          body: replacePlaceholders(template.body, replacements),
          data: { mealId },
        },
        trigger: {
          hour,
          minute,
          repeats,
        },
      });
      await loadScheduledNotifications();
    },
    [userProfile?.firstName, loadScheduledNotifications]
  );

  const cancelMealReminder = useCallback(
    async (mealId) => {
      const notifications =
        await Notifications.getAllScheduledNotificationsAsync();
      const toCancel = notifications.filter(
        (n) => n.content.data.mealId === mealId
      );
      await Promise.all(
        toCancel.map((n) =>
          Notifications.cancelScheduledNotificationAsync(n.identifier)
        )
      );
      await loadScheduledNotifications();
    },
    [loadScheduledNotifications]
  );

  const scheduleStepReminder = useCallback(
    async ({ hour, minute }) => {
      const template = getRandomTemplate(STEP_GOAL_TEMPLATES);
      const userName = userProfile?.firstName || "User";

      await Notifications.scheduleNotificationAsync({
        content: {
          title: replacePlaceholders(template.title, { userName }),
          body: replacePlaceholders(template.body, { userName }),
          data: { type: "step-goal" },
        },
        trigger: { hour, minute, repeats: true },
      });
      await loadScheduledNotifications();
    },
    [userProfile?.firstName]
  );

  const syncMealNames = useCallback(
    async (mealUpdates) => {
      try {
        const notifications =
          await Notifications.getAllScheduledNotificationsAsync();

        for (const [mealId, newName] of Object.entries(mealUpdates)) {
          // Skip Water and invalid entries
          if (
            mealId === "Water" ||
            !notificationSettings.meals.hasOwnProperty(mealId)
          ) {
            continue;
          }

          const mealConfig = notificationSettings.meals[mealId];

          // Skip if name hasn't changed
          if (mealConfig.name === newName) continue;

          // Handle empty names
          if (newName.trim() === "") {
            await cancelMealReminder(mealId);
            updateMealSettings(mealId, {
              ...mealConfig,
              name: "", // Keep empty string instead of default name
              enabled: false,
            });
            continue;
          }

          // Update name while preserving other settings
          updateMealSettings(mealId, {
            ...mealConfig,
            name: newName,
          });

          // Only process if meal was enabled
          if (mealConfig.enabled) {
            const existingNotification = notifications.find(
              (n) => n.content.data.mealId === mealId
            );

            if (existingNotification) {
              await Notifications.cancelScheduledNotificationAsync(
                existingNotification.identifier
              );

              await scheduleMealReminder(mealId, newName, {
                hour: existingNotification.trigger.hour,
                minute: existingNotification.trigger.minute,
                repeats: true,
              });
            }
          }
        }

        await loadScheduledNotifications();
      } catch (error) {
        setError(error);
        throw error;
      }
    },
    [
      notificationSettings.meals,
      cancelMealReminder,
      scheduleMealReminder,
      updateMealSettings,
      loadScheduledNotifications,
    ]
  );

  const loadScheduledNotifications = useCallback(async () => {
    const notifications =
      await Notifications.getAllScheduledNotificationsAsync();
    setScheduledNotifications(notifications);
  }, []);

  // Initialization
  useEffect(() => {
    if (!isInitialized.current) {
      setupNotificationHandler();
      checkPermissions();
      isInitialized.current = true;
    }
  }, [setupNotificationHandler, checkPermissions]);

  const contextValue = useMemo(
    () => ({
      notificationSettings,
      updateMealSettings,
      updateStepSettings,
      requestPermissions,
      scheduledNotifications,
      syncMealNames,
    }),
    [
      notificationSettings,
      updateMealSettings,
      updateStepSettings,
      requestPermissions,
      scheduledNotifications,
      syncMealNames,
    ]
  );

  return (
    <PushNotificationContext.Provider value={contextValue}>
      {children}
    </PushNotificationContext.Provider>
  );
};

export const usePushNotification = () => {
  const context = useContext(PushNotificationContext);
  if (!context) throw new Error("Missing PushNotificationProvider");
  return context;
};

// import React, {
//   createContext,
//   useContext,
//   useState,
//   useEffect,
//   useCallback,
//   useMemo,
//   useRef,
// } from "react";
// import * as Notifications from "expo-notifications";
// import AsyncStorage from "@react-native-async-storage/async-storage";
// import axios from "axios";
// import { useAuth } from "../authentication/context/AuthContext.js";
// import Configs from "../../configs.js";
// import { Platform } from "react-native";

// const PushNotificationContext = createContext();

// const useNotificationStateLogger = (notifications) => {
//   const prevStateRef = useRef();

//   useEffect(() => {
//     const logDifference = (prev, current) => {
//       const changes = {};

//       // 1. Guard against null/undefined current
//       if (!current || typeof current !== "object") {
//         console.warn("logDifference: Invalid current value", current);
//         return changes;
//       }

//       // 2. Safely get keys from current
//       const keys = Object.keys(current);

//       keys.forEach((key) => {
//         try {
//           // 3. Safe property access with null coalescing
//           const prevValue = prev?.[key] ?? null; // Using null as fallback for comparison
//           const currentValue = current[key];

//           // 4. Handle circular references and special types
//           const safeStringify = (value) => {
//             try {
//               return JSON.stringify(value, (k, v) =>
//                 typeof v === "undefined" ? null : v
//               );
//             } catch (e) {
//               return "unserializable-value";
//             }
//           };

//           if (safeStringify(prevValue) !== safeStringify(currentValue)) {
//             changes[key] = {
//               from: prevValue,
//               to: currentValue,
//               // Add type information for debugging
//               types: {
//                 from: typeof prevValue,
//                 to: typeof currentValue,
//               },
//             };
//           }
//         } catch (error) {
//           console.error(`Error comparing key "${key}":`, error);
//         }
//       });

//       return changes;
//     };

//     if (prevStateRef.current !== undefined) {
//       const changes = logDifference(prevStateRef.current, notifications);
//       if (Object.keys(changes).length > 0) {
//         console.groupCollapsed("[Notifications] State Update");
//         console.log("Previous:", prevStateRef.current);
//         console.log("Current:", notifications);
//         console.log("Changes:", changes);
//         console.groupEnd();
//       }
//     } else if (notifications) {
//       console.log("[Notifications] Initial State:", notifications);
//     }

//     prevStateRef.current = notifications;
//   }, [notifications]);
// };

// /**
//  * Centralized push notification management system
//  *
//  * Responsibilities:
//  * - Push token lifecycle management
//  * - Notification settings synchronization
//  * - Local state caching with offline support
//  * - Notification event handling
//  *
//  * Architecture Features:
//  * - Cache-first data loading strategy
//  * - Atomic transaction patterns for settings updates
//  * - Cross-platform notification channel management
//  */
// export const PushNotificationProvider = ({ children }) => {
//   const { user, token } = useAuth();
//   const [notifications, setNotifications] = useState(null);
//   const [expoPushToken, setExpoPushToken] = useState(null);
//   const [notification, setNotification] = useState(null);
//   const [error, setError] = useState(null);
//   const [isLoading, setIsLoading] = useState(true);
//   const isInitialized = useRef(false);

//   const apiUrl = Configs.NutraCompass_API_URL; // Base URL for API

//   // =============================================
//   // Push Token Lifecycle Management
//   // =============================================

//   const handlePushTokenRegistration = useCallback(async () => {
//     if (!Platform.isDevice) {
//       console.warn("Push notifications require a physical device");
//       return null;
//     }

//     try {
//       // 1. Validate authentication first
//       if (!user?.uid || !token) {
//         console.log("Deferring push token registration until auth completes");
//         return;
//       }

//       // 2. Check permissions properly
//       const { granted, canRetry } = await checkPermissions();
//       if (!granted) {
//         if (!canRetry) {
//           console.log("Permanent permission denial detected");
//           return;
//         }
//         throw new Error("Permission not granted - enable in Settings");
//       }

//       // 3. Get Expo token
//       const { data: expoToken } = await Notifications.getExpoPushTokenAsync({
//         projectId: Configs.EXPO_PROJECT_ID,
//       });

//       // Validate token format
//       if (!/^ExponentPushToken\[.+\]$/.test(expoToken)) {
//         throw new Error("Invalid push token format");
//       }

//       // 4. Store locally and update state
//       await AsyncStorage.setItem("@expoPushToken", expoToken);
//       setExpoPushToken(expoToken);

//       // 5. Register with server
//       await axios.post(
//         `${apiUrl}/v1/notification-settings/${user.uid}/push-token`,
//         { expoPushToken: expoToken },
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             "Content-Type": "application/json",
//           },
//           timeout: 5000,
//         }
//       );

//       return expoToken;
//     } catch (error) {
//       console.error("Push registration failed:", error.message);

//       // Clear invalid token state
//       await AsyncStorage.removeItem("@expoPushToken");
//       setExpoPushToken(null);

//       // Re-throw for error handling
//       throw error;
//     }
//   }, [user?.uid]);

//   const retryPushTokenRegistration = useCallback(async () => {
//     try {
//       // Get fresh permissions first
//       const { status } = await Notifications.getPermissionsAsync();

//       if (status !== "granted") {
//         Alert.alert("Permission Required", "Enable notifications in Settings", [
//           {
//             text: "Open Settings",
//             onPress: () => Linking.openURL("app-settings:"),
//           },
//           { text: "Cancel" },
//         ]);
//         return;
//       }

//       const newToken = await handlePushTokenRegistration();
//       return newToken;
//     } catch (error) {
//       Alert.alert("Registration Failed", error.message, [
//         {
//           text: "Try Again",
//           onPress: retryPushTokenRegistration,
//         },
//         { text: "Cancel" },
//       ]);
//     }
//   }, [handlePushTokenRegistration]);

//   const checkPermissions = useCallback(async () => {
//     try {
//       const { status, canAskAgain } = await Notifications.getPermissionsAsync();

//       if (status === "denied" && !canAskAgain) {
//         return { granted: false, canRetry: false };
//       }

//       if (status !== "granted") {
//         const { status: newStatus } =
//           await Notifications.requestPermissionsAsync({
//             ios: {
//               allowAlert: true,
//               allowBadge: true,
//               allowSound: true,
//               allowDisplayInCarPlay: false,
//               allowCriticalAlerts: true,
//             },
//             android: {
//               channelId: "default",
//               importance: Notifications.AndroidImportance.HIGH,
//             },
//           });
//         return { granted: newStatus === "granted", canRetry: true };
//       }

//       return { granted: true, canRetry: true };
//     } catch (error) {
//       console.error("Permission check failed:", error);
//       return { granted: false, canRetry: true };
//     }
//   }, []);

//   // =============================================
//   // Data Synchronization Engine
//   // =============================================

//   const initializeNotificationState = useCallback(async () => {
//     if (isInitialized.current) return;

//     try {
//       setIsLoading(true);

//       // 1. Load cached settings first
//       const cachedSettings = await AsyncStorage.getItem(
//         "@notification_settings"
//       );

//       if (cachedSettings) {
//         const processed = processSettingsResponse(JSON.parse(cachedSettings));
//         setNotifications((prev) =>
//           JSON.stringify(prev) !== JSON.stringify(processed) ? processed : prev
//         );
//       }

//       // 2. Server sync only when authenticated
//       if (user?.uid && token) {
//         const currentVersion = notifications?._version || 0;

//         try {
//           const { data } = await axios.get(
//             `${apiUrl}/v1/notification-settings/${user.uid}`,
//             {
//               headers: { Authorization: `Bearer ${token}` },
//               params: { versionCheck: currentVersion },
//               timeout: 5000,
//             }
//           );

//           if (data?.settings && data.settings._version > currentVersion) {
//             const processed = processSettingsResponse(data.settings);
//             await AsyncStorage.setItem(
//               "@notification_settings",
//               JSON.stringify(processed)
//             );
//             setNotifications(processed);
//           }
//         } catch (error) {
//           if (error.response?.status !== 304) {
//             console.error("Server sync failed:", error);
//             throw error;
//           }
//         }
//       }

//       isInitialized.current = true;
//     } catch (error) {
//       setError(error);
//       console.error("Notification init error:", error);
//     } finally {
//       setIsLoading(false);
//     }
//   }, [user?.uid, token, notifications?._version]); // Added token to deps

//   const synchronizeServerSettings = useCallback(async () => {
//     const currentNotifications = notifications;
//     const currentVersion = currentNotifications?._version || 0;

//     try {
//       // Basic pre-checks
//       if (!user?.uid) throw new Error("User authentication required");
//       if (!token) throw new Error("Missing authentication token");

//       const { data } = await axios.get(
//         `${apiUrl}/v1/notification-settings/${user.uid}`,
//         {
//           headers: { Authorization: `Bearer ${token}` },
//           params: { versionCheck: currentVersion },
//           timeout: 8000, // 8-second timeout
//         }
//       );

//       // Handle response structure validation
//       if (!data || typeof data !== "object") {
//         throw new Error("Invalid server response format");
//       }

//       // Extract settings from standardized response
//       const serverSettings = data.settings;

//       // Validate settings existence and structure
//       if (!serverSettings || typeof serverSettings._version !== "number") {
//         throw new Error("Invalid settings format in server response");
//       }

//       // Process settings through standard pipeline
//       const processedSettings = processSettingsResponse(serverSettings);

//       // Update state only if version is newer
//       setNotifications((prev) => {
//         if (!prev || processedSettings._version > prev._version) {
//           return Object.freeze({
//             // Prevent accidental mutation
//             ...processedSettings,
//             _lastUpdated: Date.now(),
//           });
//         }
//         return prev;
//       });

//       // Persist only if version advances
//       if (processedSettings._version > currentVersion) {
//         await AsyncStorage.setItem(
//           "@notification_settings",
//           JSON.stringify({
//             ...processedSettings,
//             _lastUpdated: Date.now(), // Add timestamp to storage
//           })
//         );
//       }
//     } catch (error) {
//       setError(error.message || "Sync failed");

//       // Handle specific error cases
//       if (error.response?.status === 401) {
//         await clearNotificationCache();
//       }
//       if (!notifications) {
//         setNotifications({
//           mealReminders: {},
//           stepGoal: {},
//           _version: 0,
//           _lastUpdated: Date.now(),
//         });
//       }
//     }
//   }, [user, token]);

//   const syncMealNames = useCallback(
//     async (mealSectionUpdates) => {
//       if (!notifications || Object.keys(mealSectionUpdates).length === 0)
//         return;

//       try {
//         // Create safe copy of current reminders
//         const updatedReminders = { ...notifications.mealReminders };

//         // Only update entries that exist in both systems
//         Object.entries(mealSectionUpdates).forEach(([mealId, newName]) => {
//           if (
//             updatedReminders[mealId] &&
//             updatedReminders[mealId].mealName !== newName
//           ) {
//             updatedReminders[mealId] = {
//               ...updatedReminders[mealId],
//               mealName: newName,
//             };
//           }
//         });

//         if (Object.keys(updatedReminders).length > 0) {
//           await executeSettingsUpdate({ mealReminders: updatedReminders });
//         }
//       } catch (error) {
//         console.error("Notifications and meal names sync failed:", error);
//         throw error;
//       }
//     },
//     [notifications]
//   );

//   // =============================================
//   // Transactional Settings Operations
//   // =============================================

//   const executeSettingsUpdate = useCallback(
//     async (updates) => {
//       if (!expoPushToken) {
//         throw new Error("Expo push token required to update notification");
//       }

//       const currentState =
//         notifications ||
//         JSON.parse(
//           (await AsyncStorage.getItem("@notification_settings")) || "{}"
//         );

//       const currentVersion = currentState._version || 0;
//       const expectedVersion = currentVersion + 1;

//       const transactionState = {
//         ...currentState,
//         ...updates,
//         _version: expectedVersion,
//         _lastUpdated: Date.now(),
//       };

//       if (!validateState(transactionState)) {
//         throw new Error("Invalid notification state structure");
//       }

//       try {
//         // Atomic operation: storage + server sync

//         const serverResponse = await axios.put(
//           `${apiUrl}/v1/notification-settings/${user?.uid}`,
//           {
//             settings: serializeSettings(transactionState, user),
//             expoPushToken: expoPushToken,
//             _transactionId: `${user.uid}-${Date.now()}`,
//           },
//           {
//             headers: {
//               Authorization: `Bearer ${token}`,
//               "Content-Type": "application/json",
//             },
//           }
//         );

//         // Validate response structure first
//         if (!serverResponse.data?.settings) {
//           throw new Error("Invalid server response structure");
//         }

//         // Extract validated settings from response
//         const committedSettings = serverResponse.data.settings;

//         // Verify version against what we committed
//         if (committedSettings._version !== transactionState._version) {
//           throw new Error(
//             `Version mismatch (Server: ${committedSettings._version} vs Client: ${transactionState._version})`
//           );
//         }
//         await AsyncStorage.setItem(
//           "@notification_settings",
//           JSON.stringify(committedSettings) // Use server-validated settings
//         );

//         setNotifications((prev) => ({
//           ...prev,
//           ...committedSettings, // Use server-validated settings
//         }));

//         console.log(
//           "[Notification State Update] Successful version",
//           committedSettings._version
//         );
//       } catch (error) {
//         console.error("[Notification Update Failed]", error);
//         // Rollback to last stable state
//         const lastGoodState = JSON.parse(
//           (await AsyncStorage.getItem("@notification_settings")) || "{}"
//         );
//         setNotifications(lastGoodState);
//         throw error;
//       }
//     },
//     [user?.uid, notifications?._version]
//   );

//   const handleMealReminderCancellation = useCallback(
//     async (mealIds) => {
//       try {
//         const { data } = await axios.post(
//           `${apiUrl}/v1/notification-settings/${user.uid}/cancel-meal-reminders`,
//           { mealIds },
//           { headers: { Authorization: `Bearer ${token}` } }
//         );

//         await synchronizeServerSettings();
//         return data;
//       } catch (error) {
//         setError(error);
//         throw error;
//       }
//     },
//     [user, token, synchronizeServerSettings]
//   );

//   // =============================================
//   // Event Subsystem
//   // =============================================

//   const clearNotificationCache = async () => {
//     try {
//       await Promise.all([
//         AsyncStorage.removeItem("@expoPushToken"),
//         AsyncStorage.removeItem("@notification_settings"),
//       ]);
//       console.log("Notification cache cleared successfully");

//       // Reset state
//       setExpoPushToken(null);
//       setNotifications(null);
//       setError(null);
//       setIsLoading(true);

//       // Reinitialize
//       //await initializeNotificationState();
//     } catch (error) {
//       console.error("Cache clearance failed:", error);
//     }
//   };

//   useEffect(() => {
//     // Main initialization effect
//     let isMounted = true;
//     let notificationSubscription;

//     const initialize = async () => {
//       if (!user?.uid || !token) return;

//       try {
//         // 1. Check existing token first
//         const storedToken = await AsyncStorage.getItem("@expoPushToken");
//         if (storedToken && isMounted) {
//           setExpoPushToken(storedToken);
//         }

//         // 2. Initialize notification state
//         await initializeNotificationState();

//         // 3. Register token if missing
//         if (!storedToken && isMounted) {
//           await handlePushTokenRegistration();
//         }

//         // 4. Setup notification listeners
//         notificationSubscription =
//           Notifications.addNotificationReceivedListener((notification) => {
//             if (isMounted) setNotification(notification);
//           });
//       } catch (error) {
//         if (isMounted) setError(error);
//       }
//     };

//     initialize();

//     return () => {
//       isMounted = false;
//       if (notificationSubscription) notificationSubscription.remove();
//     };
//   }, [user?.uid, token]); // Only run when auth state changes

//   // Effect for permission checks
//   useEffect(() => {
//     const verifyPermissions = async () => {
//       if (!user?.uid) return;
//       try {
//         await checkPermissions();
//       } catch (error) {
//         setError(error);
//       }
//     };

//     verifyPermissions();
//   }, [user?.uid]);

//   // useEffect(() => {
//   //   clearNotificationCache();
//   // }, []);

//   // useNotificationStateLogger(notifications);

//   // useEffect(() => {
//   //   console.log(
//   //     "Notifications State: ",
//   //     JSON.stringify(notifications, null, 1)
//   //   );
//   // }, [notifications]);

//   // =============================================
//   // Context Exposure
//   // =============================================

//   const contextValue = useMemo(
//     () => ({
//       expoPushToken,
//       checkPermissions,
//       notification,
//       notifications,
//       error,
//       isLoading,
//       updateNotificationSettings: executeSettingsUpdate,
//       cancelMealReminders: handleMealReminderCancellation,
//       refreshSettings: synchronizeServerSettings,
//       syncMealNames,
//       retryPushTokenRegistration,
//     }),
//     [
//       expoPushToken,
//       checkPermissions,
//       notification,
//       notifications?._version, // Only track version changes
//       error?.message, // Track error identity
//       isLoading,
//       executeSettingsUpdate,
//       handleMealReminderCancellation,
//       synchronizeServerSettings,
//       syncMealNames,
//       handlePushTokenRegistration,
//     ]
//   );

//   return (
//     <PushNotificationContext.Provider value={contextValue}>
//       {children}
//     </PushNotificationContext.Provider>
//   );
// };

// // =============================================
// // Data Transformation Layer
// // =============================================

// const serializeSettings = (settings, user) => {
//   const baseSettings = {
//     ...settings,
//     _user: user?.profile?.firstName || "User",
//   };

//   // Safe date handling
//   const safeDateConvert = (date) => {
//     if (!date || !(date instanceof Date)) return null;
//     return date.toISOString();
//   };

//   return {
//     ...baseSettings,
//     mealReminders: Object.fromEntries(
//       Object.entries(settings.mealReminders).map(([id, config]) => [
//         id,
//         {
//           ...config,
//           time: safeDateConvert(config.time),
//         },
//       ])
//     ),
//     stepGoal: {
//       ...settings.stepGoal,
//       time: safeDateConvert(settings.stepGoal.time),
//     },
//   };
// };

// const validateState = (state) => {
//   const requiredStructure = {
//     mealReminders: "object",
//     stepGoal: "object",
//     _version: "number",
//   };

//   return Object.entries(requiredStructure).every(([key, type]) => {
//     const valid = typeof state[key] === type;
//     if (!valid) {
//       console.error(`State validation failed: ${key} must be ${type}`);
//     }
//     return valid;
//   });
// };

// const processSettingsResponse = (settings) => {
//   const defaultSettings = {
//     mealReminders: {},
//     stepGoal: {},
//     _version: 0,
//   };

//   // Add validation layer
//   if (
//     settings &&
//     (typeof settings._version !== "number" ||
//       typeof settings.mealReminders !== "object" ||
//       typeof settings.stepGoal !== "object")
//   ) {
//     console.error("Invalid settings structure", settings);
//     return defaultSettings;
//   }

//   const safeSettings = settings || defaultSettings;

//   return {
//     ...safeSettings,
//     mealReminders: Object.fromEntries(
//       Object.entries(safeSettings.mealReminders).map(([id, config]) => [
//         id,
//         {
//           ...config,
//           time: config.time ? new Date(config.time) : null,
//         },
//       ])
//     ),
//     stepGoal: {
//       ...safeSettings.stepGoal,
//       time: safeSettings.stepGoal.time
//         ? new Date(safeSettings.stepGoal.time)
//         : null,
//     },
//   };
// };

// const toUTCIsoString = (localDate) => {
//   return new Date(
//     localDate.getTime() - localDate.getTimezoneOffset() * 60000
//   ).toISOString();
// };

// const fromUTCIsoToLocal = (isoString) => {
//   const date = new Date(isoString);
//   return new Date(date.getTime() + date.getTimezoneOffset() * 60000);
// };

// export const usePushNotification = () => {
//   const context = useContext(PushNotificationContext);
//   if (!context) {
//     throw new Error(
//       "usePushNotification must be within a PushNotificationProvider"
//     );
//   }
//   return context;
// };
