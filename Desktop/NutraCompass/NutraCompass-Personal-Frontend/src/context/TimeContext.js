import React, { createContext, useContext, useState, useEffect } from "react";
import * as Localization from "expo-localization";

const TimeContext = createContext();

export const useTime = () => useContext(TimeContext);

export const TimeProvider = ({ children }) => {
  // Date state
  const [selectedDate, setSelectedDate] = useState(() => {
    const localDate = new Date();
    localDate.setHours(0, 0, 0, 0);
    return localDate.toISOString().split("T")[0];
  });

  // Location states
  const [deviceTimezone, setDeviceTimezone] = useState("UTC");
  const [deviceRegion, setDeviceRegion] = useState(null);

  // Log timezone changes
  // useEffect(() => {
  //   console.log("Device Timezone:", deviceTimezone);
  //   console.log("Device Region:", deviceRegion);
  // }, [deviceTimezone, deviceRegion]);

  // Detect device location info on mount
  useEffect(() => {
    const detectDeviceLocation = async () => {
      try {
        // Get localization data
        const calendars = Localization.getCalendars();
        const locales = Localization.getLocales();

        // Set timezone
        const detectedTimezone = calendars[0]?.timeZone;
        setDeviceTimezone(
          detectedTimezone ||
            Intl.DateTimeFormat().resolvedOptions().timeZone ||
            "UTC"
        );

        // Set region
        const detectedRegion = locales[0]?.regionCode;
        setDeviceRegion(detectedRegion || null);
      } catch (error) {
        console.log("Location detection error:", error);
        setDeviceTimezone("UTC");
        setDeviceRegion(null);
      }
    };

    detectDeviceLocation();
  }, []);

  // Date manipulation methods
  const updateSelectedDate = (date) => {
    let newDate = date;
    if (typeof date === "string") {
      newDate = new Date(date + "T00:00:00");
    }

    if (newDate instanceof Date && !isNaN(newDate)) {
      newDate.setHours(0, 0, 0, 0);
      setSelectedDate(newDate.toISOString().split("T")[0]);
    } else {
      console.error("Invalid date:", date);
    }
  };

  const getSelectedDateAsDate = () => {
    const date = new Date(selectedDate + "T00:00:00");
    date.setHours(0, 0, 0, 0);
    return date;
  };

  // Context value
  const contextValue = {
    selectedDate,
    updateSelectedDate,
    getSelectedDateAsDate,
    deviceTimezone,
    deviceRegion,
  };

  return (
    <TimeContext.Provider value={contextValue}>{children}</TimeContext.Provider>
  );
};
