// contexts/FeatureFlagsContext.js

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
} from "react";
// import axios from "axios";
import { useAuth } from "../authentication/context/AuthContext.js";
import Configs from "../../configs.js";

const FeatureFlagsContext = createContext();

export function useFeatureFlags() {
  return useContext(FeatureFlagsContext);
}

export function FeatureFlagsProvider({ children }) {
  const { user } = useAuth();
  const userId = user?.uid;
  const apiUrl = Configs.NutraCompass_API_URL;

  // Mock feature flags state
  // Set to true or false to simulate premium access
  const [features, setFeatures] = useState({
    foodTracker: true,
    aiMealPlans: true,
    voiceAssistedCreation: true,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch feature flags from the backend
  const loadFeatureFlags = async () => {
    if (!userId) return;
    setLoading(true);
    try {
      // Uncomment the following lines when backend route is ready
      /*
      const response = await axios.get(`${apiUrl}/v1/user-feature-flags`, {
        params: { userId },
      });
      setFeatures(response.data.features);
      */

      // Mock delay to simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mock response (you can adjust the values as needed)
      const mockResponse = {
        foodTracker: false,
        aiMealPlans: false,
        voiceAssistedCreation: false,
      };
      setFeatures(mockResponse);
    } catch (err) {
      console.error("Error fetching feature flags:", err);
      setError("Failed to load feature flags.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFeatureFlags();
  }, [userId]);

  const contextValue = useMemo(
    () => ({
      features,
      loadFeatureFlags,
      error,
      loading,
    }),
    [features, error, loading]
  );

  return (
    <FeatureFlagsContext.Provider value={contextValue}>
      {children}
    </FeatureFlagsContext.Provider>
  );
}
