import React from "react";
import {
  createStackNavigator,
  CardStyleInterpolators,
} from "@react-navigation/stack";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createDrawerNavigator } from "@react-navigation/drawer";
import { NavigationContainer } from "@react-navigation/native";
import AppLoadingScreen from "../screens/AppLoadingScreen";
import DashboardScreen from "../screens/Dashboard";
import NutraCompassAIScreen from "../features/AIAgent/screens/NutraCompassAIScreen.js";
import FoodDiaryScreen from "../screens/FoodDiary";
import FoodsScreen from "../screens/Foods";
import WorkoutDiaryScreen from "../screens/WorkoutDiary";
import GoalsScreen from "../screens/Goals";
import ThemeScreen from "../features/ThemeChanger/screens/ThemesScreen";
import SettingsScreen from "../screens/Settings";
import CustomMealsScreen from "../features/FoodMenu/screens/CustomMealsScreen";
// Social Media Screens
import MyProfileScreen from "../features/SocialMedia/MyProfile/screens/MyProfileScreen";
import MyLibraryScreen from "../features/SocialMedia/MyLibrary/screens/MyLibraryScreen";
import MyAccomplishmentsScreen from "../features/SocialMedia/MyAccomplishments/screens/MyAccomplishmentsScreen";
import MarketPlaceScreen from "../features/SocialMedia/MarketPlace/screens/MarketPlaceScreen";
import SocialSettingsScreen from "../features/SocialMedia/SocialSettings/screens/SocialSettingsScreen";
import ChatScreen from "../features/SocialMedia/Messages/screens/ChatScreen";
import SelectFriend from "../features/SocialMedia/Messages/screens/SelectFriend";
import TextScreen from "../features/SocialMedia/Messages/screens/TextScreen";
import AddFriendScreen from "../features/SocialMedia/Messages/screens/AddFriend";
// Custom Components
import CustomTopTabBar from "./components/CustomTopTabBar";
import CustomPersonalBottomTabBar from "./components/CustomPersonalBottomTabBar";
import CustomSocialBottomTabBar from "./components/CustomSocialBottomTabBar";
import { CustomDrawerContent } from "./components/CustomDrawerContent.js";
import ProfileSettings from "../features/Settings/screens/ProfileSettings.js";
import NotificationsSettings from "../features/Settings/screens/NotificationSettings.js";
import NutritionSettings from "../features/Settings/screens/NutritionSettings.js";
import AccountSettings from "../features/Settings/screens/AccountSettings.js";
import NutritionalProgramScreen from "../features/NutritionalProgram/screens/NutritionalProgramScreen.js";

const Stack = createStackNavigator();
const BottomTabNavigator = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

const screenOptions = {
  header: (props) => <CustomTopTabBar {...props} />, // Use your CustomTopTabBar as the header
};

const HomeStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="Dashboard" component={DashboardScreen} />
      <Stack.Screen name="AI" component={NutraCompassAIScreen} />
      <Stack.Screen
        name="Nutritional Program"
        component={NutritionalProgramScreen}
      />
    </Stack.Navigator>
  );
};

const FoodsStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Food Options" component={FoodsScreen} />
      <Stack.Screen name="Custom Meals" component={CustomMealsScreen} />
    </Stack.Navigator>
  );
};

const PersonalBottomTabs = () => {
  return (
    <BottomTabNavigator.Navigator
      initialRouteName="Home"
      tabBar={(props) => <CustomPersonalBottomTabBar {...props} />}
      screenOptions={{ headerShown: false }}
    >
      <BottomTabNavigator.Screen name="Foods" component={FoodsStack} />
      <BottomTabNavigator.Screen name="Home" component={HomeStack} />
      <BottomTabNavigator.Screen name="Diary" component={FoodDiaryScreen} />
      <BottomTabNavigator.Screen
        name="Workout"
        component={WorkoutDiaryScreen}
      />
    </BottomTabNavigator.Navigator>
  );
};

const SocialBottomTabs = () => {
  return (
    <BottomTabNavigator.Navigator
      tabBar={(props) => <CustomSocialBottomTabBar {...props} />}
      screenOptions={{ headerShown: false }}
    >
      {/* <BottomTabNavigator.Screen name="Home" component={MyProfileScreen} />
      <BottomTabNavigator.Screen name="Search" component={MyProfileScreen} />
      <BottomTabNavigator.Screen name="Post" component={MyProfileScreen} />
      <BottomTabNavigator.Screen name="Reels" component={MyProfileScreen} /> */}
      <BottomTabNavigator.Screen
        name="My Profile"
        component={MyProfileScreen}
      />
    </BottomTabNavigator.Navigator>
  );
};

const MessagesStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
      }}
    >
      <Stack.Screen name="Chat" component={ChatScreen} />
      <Stack.Screen name="SelectFriend" component={SelectFriend} />
      <Stack.Screen name="TextScreen" component={TextScreen} />
    </Stack.Navigator>
  );
};

const SettingsStack = () => (
  <Stack.Navigator>
    <Stack.Screen
      name="Settings"
      component={SettingsScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="ProfileSettings"
      component={ProfileSettings}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="NutritionSettings"
      component={NutritionSettings}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="NotificationsSettings"
      component={NotificationsSettings}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="AccountSettings"
      component={AccountSettings}
      options={{ headerShown: false }}
    />
  </Stack.Navigator>
);

const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={screenOptions}
    >
      <Drawer.Screen name="Personal" component={PersonalBottomTabs} />
      {/* <Drawer.Screen
        name="Social"
        component={SocialBottomTabs}
        screenOptions={screenOptions}
      /> */}
      {/* <Drawer.Screen name="Messages" component={MessagesStack} /> */}
      <Drawer.Screen name="Themes" component={ThemeScreen} />
      <Drawer.Screen name="SettingsStack" component={SettingsStack} />
    </Drawer.Navigator>
  );
};

const RootNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName={"AppLoadingScreen"}
      screenOptions={{ headerShown: false, gestureEnabled: false }}
    >
      <Stack.Screen
        name="AppLoadingScreen"
        component={AppLoadingScreen}
        options={{ title: "AppLoadingScreen" }}
      />
      <Stack.Screen
        name="Main"
        component={DrawerNavigator}
        options={{ title: "Main" }}
      />
    </Stack.Navigator>
  );
};

const UserStack = () => {
  return (
    <NavigationContainer>
      <RootNavigator />
    </NavigationContainer>
  );
};

export default UserStack;
