import React from "react";
import { View, StyleSheet, Dimensions, Text } from "react-native";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../../context/ThemeContext.js";
import CustomTabButton from "./CustomTabButton.js";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

const CustomSocialBottomTabBar = ({ state, descriptors, navigation }) => {
  const { theme } = useThemeContext();

  const navigateToAddFriend = () => {
    // Navigate to the AddFriend screen
    navigation.navigate("AddFriend");
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // Assuming the index of AddFriend tab is known, for example 2
    state.index = 2; // This line is just illustrative, not recommended to mutate state directly
  };

  const styles = StyleSheet.create({
    tabBar: {
      alignSelf: "center",
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: "row",
      paddingLeft: 5,
      height: SCREEN_HEIGHT / 14,
      elevation: 2,
      borderTopRightRadius: 30,
      borderTopLeftRadius: 30,
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0,
      backgroundColor: theme.colors.primary,
    },
  });

  const getIconName = (routeName) => {
    switch (routeName) {
      case "Home":
        return "home";
      case "Search":
        return "search";
      case "Post":
        return "duplicate-outline";
      case "Reels":
        return "easel";
      case "My Profile":
        return "body";
      default:
        return "home";
    }
  };

  return (
    <View style={{ flexDirection: "row" }}>
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : options.title || route.name;
          const isFocused = state.index === index;

          return (
            <CustomTabButton
              key={route.key}
              label={label}
              isFocused={isFocused}
              onPress={() => navigation.navigate(route.name)}
              icon={getIconName(route.name)}
            />
          );
        })}
        {/* Switch Tab Button */}
        {/* <CustomTabButton
          label="Switch"
          isFocused={false}
          onPress={() => navigation.navigate("Personal")}
          icon={"swap-horizontal"}
        /> */}
      </View>
    </View>
  );
};

export default CustomSocialBottomTabBar;
