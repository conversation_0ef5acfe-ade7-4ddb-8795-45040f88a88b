import React from "react";
import {
  View,
  TouchableOpacity,
  Text,
  ScrollView,
  StyleSheet,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { DrawerContentScrollView } from "@react-navigation/drawer";
import { useThemeContext } from "../../context/ThemeContext.js";
import ProfilePicture from "../../features/SocialMedia/components/ProfilePicture.js";
import { useAuth } from "../../authentication/context/AuthContext.js";

export const CustomDrawerContent = (props) => {
  const { state, navigation } = props;
  const { theme } = useThemeContext();
  const { loggingOut } = useAuth();
  const styles = StyleSheet.create({
    profileContainer: { alignItems: "center", padding: 20 },
    item: {
      paddingVertical: 15,
      paddingHorizontal: 20,
      backgroundColor: theme.colors.screenBackground,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    itemText: {
      fontSize: 16,
      color: theme.colors.primaryTextColor,
    },
    itemFocused: {
      backgroundColor: theme.colors.secondary,
    },
  });

  // // Define your navigation items
  // const navigationItems = [
  //   { name: "Personal", label: "Personal", icon: "account-circle" },
  //   { name: "Social", label: "Social", icon: "forum" },
  //   { name: "Messages", label: "Messages", icon: "message-text" },
  //   { name: "My Library", label: "My Library", icon: "library" },
  //   {
  //     name: "My Accomplishments",
  //     label: "My Accomplishments",
  //     icon: "trophy-award",
  //   },
  //   { name: "Market Place", label: "Market Place", icon: "store" },
  //   { name: "Goals", label: "Goals", icon: "bullseye-arrow" },
  //   { name: "Themes", label: "Theme Palette", icon: "palette" },
  //   { name: "Settings", label: "Settings", icon: "dots-horizontal" },
  //   // Add other navigation items here
  // ];

  // Define your navigation items
  const navigationItems = [
    { name: "Personal", label: "Personal", icon: "account-circle" },
    // { name: "Social", label: "Social", icon: "forum" },
    // { name: "Messages", label: "Messages", icon: "message-text" },
    { name: "Themes", label: "Theme Palette", icon: "palette" },
    { name: "SettingsStack", label: "Account", icon: "message-text" },

    // Add other navigation items here
  ];

  const handleLogout = () => {
    loggingOut();
  };

  return (
    <DrawerContentScrollView
      style={{ backgroundColor: theme.colors.screenBackground }}
      {...props}
    >
      <View style={styles.profileContainer}>
        <ProfilePicture size={150} />
      </View>
      <ScrollView>
        {navigationItems.map(({ name, label, icon }) => {
          // Check if the route is active
          const isFocused = state.routes[state.index].name === name;
          return (
            <TouchableOpacity
              key={name}
              onPress={() => navigation.navigate(name)}
              style={[styles.item, isFocused && styles.itemFocused]}
            >
              <Text style={styles.itemText}>{label}</Text>
              {icon && (
                <MaterialCommunityIcons
                  name={icon}
                  size={28}
                  color={theme.colors.primaryTextColor}
                />
              )}
            </TouchableOpacity>
          );
        })}
        {/* Logout */}
        <TouchableOpacity onPress={handleLogout} style={styles.item}>
          <Text style={styles.itemText}>Logout</Text>
          <MaterialCommunityIcons
            name={"logout"}
            size={28}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
      </ScrollView>
    </DrawerContentScrollView>
  );
};
