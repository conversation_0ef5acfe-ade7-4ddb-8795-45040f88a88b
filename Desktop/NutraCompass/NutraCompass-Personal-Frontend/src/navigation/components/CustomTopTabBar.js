import React, { useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  SafeAreaView,
  Text,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Ionicons from "@expo/vector-icons/Ionicons";
import {
  useNavigation,
  DrawerActions,
  useNavigationState,
} from "@react-navigation/native";
import { useThemeContext } from "../../context/ThemeContext.js";

const { height } = Dimensions.get("window");

const CustomTopTabBar = () => {
  const navigation = useNavigation();
  const { theme } = useThemeContext();
  const [streak, setStreak] = useState(0);

  useEffect(() => {
    // Load streak data from AsyncStorage when the component mounts
    const loadStreakData = async () => {
      try {
        const storedStreak = await AsyncStorage.getItem("streak");
        const storedLastOpenedDate = await AsyncStorage.getItem(
          "lastOpenedDate"
        );
        const currentDate = new Date().toDateString(); // Simplify date comparison to day granularity

        if (storedStreak && storedLastOpenedDate) {
          const streakCount = parseInt(storedStreak, 10);
          const lastDate = new Date(storedLastOpenedDate);

          // Check if the user has opened the app today
          if (currentDate === lastDate.toDateString()) {
            setStreak(streakCount);
          } else if (
            currentDate ===
            new Date(lastDate.setDate(lastDate.getDate() + 1)).toDateString()
          ) {
            // Increment streak if the app was opened yesterday
            const newStreak = streakCount + 1;
            setStreak(newStreak);
            await AsyncStorage.setItem("streak", newStreak.toString());
            await AsyncStorage.setItem("lastOpenedDate", new Date().toString());
          } else {
            // Reset streak if it's been more than one day
            setStreak(1);
            await AsyncStorage.setItem("streak", "1");
            await AsyncStorage.setItem("lastOpenedDate", new Date().toString());
          }
        } else {
          // Initialize streak and date if not set
          setStreak(1);
          await AsyncStorage.setItem("streak", "1");
          await AsyncStorage.setItem("lastOpenedDate", new Date().toString());
        }
      } catch (error) {
        console.error("Error loading streak data:", error);
      }
    };

    loadStreakData();
  }, []);

  const toggleDrawer = () => {
    navigation.dispatch(DrawerActions.toggleDrawer());
  };

  // Improved check for current tab context
  const currentRouteName = useNavigationState(
    (state) => state.routes[state.index].name
  );

  const isCurrentTabPersonal = currentRouteName.includes("Personal");

  const navigateToChatScreen = () => {
    const originStack = isCurrentTabPersonal
      ? "PersonalBottomTabs"
      : "SocialBottomTabs";

    // Navigate to the ChatScreen within MessagesStack, which is hosted in a DrawerNavigator
    navigation.navigate("Messages", {
      screen: "Chat",
      params: { originStack: originStack },
    });
  };

  const styles = StyleSheet.create({
    tabContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 10,
      paddingBottom: 5,
      backgroundColor: theme.colors.screenBackground,
    },
    leftSection: {
      flexDirection: "row",
      alignItems: "center",
    },
    streakContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    streakText: {
      fontSize: 16,
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
    },
    centerSection: {
      flex: 1,
      justifyContent: "center", // Center content horizontally
      alignItems: "center", // Center content vertically
    },
  });

  return (
    <SafeAreaView style={{ backgroundColor: theme.colors.screenBackground }}>
      <View style={styles.tabContainer}>
        {/* Left Section: Menu Icon */}
        <TouchableOpacity onPress={toggleDrawer}>
          <Ionicons
            name="menu"
            size={32}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>

        {/* Center Section: Commented Out */}
        {/* <TouchableOpacity style={styles.icon} onPress={navigateToChatScreen}>
          <Ionicons
            name="chatbubble-ellipses-outline"
            size={36}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity> */}

        {/* Right Section: Streak Counter */}
        <View style={styles.streakContainer}>
          {streak >= 2 && <Text style={styles.streakText}>{streak} 🔥</Text>}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CustomTopTabBar;
