import React from "react";
import { View, StyleSheet, Dimensions } from "react-native";
import { useThemeContext } from "../../context/ThemeContext.js";
import CustomTabButton from "./CustomTabButton.js";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

const CustomPersonalBottomTabBar = ({ state, descriptors, navigation }) => {
  const { theme } = useThemeContext();

  // A helper function to find the active (deepest) route name
  function getActiveRouteNameFromState(navState) {
    if (!navState || !navState.routes) {
      return null;
    }
    const route = navState.routes[navState.index];

    // If this route has nested state, dive in
    if (route.state) {
      return getActiveRouteNameFromState(route.state);
    }

    return route.name;
  }

  // Check if the active route is your AI screen.
  const activeRouteName = getActiveRouteNameFromState(state);
  if (activeRouteName === "AI") {
    return null; // Don't render the bottom tab bar on the AI screen.
  }

  const styles = StyleSheet.create({
    tabBar: {
      alignSelf: "center",
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: "row",
      paddingLeft: 5,
      height: SCREEN_HEIGHT / 14,
      elevation: 2,
      borderTopRightRadius: 30,
      borderTopLeftRadius: 30,
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0,
      backgroundColor: theme.colors.primary,
    },
    // Add other styles here
  });

  const getIconName = (routeName) => {
    if (routeName === "Home") {
      return "home";
    } else if (routeName === "Diary") {
      return "book";
    } else if (routeName === "Foods") {
      return "fast-food";
    } else if (routeName === "Workout") {
      return "barbell";
    }
    return "home"; // Default icon name
  };

  const onTabPress = (route, index) => {
    const isRouteFocused = state.index === index;

    // This will return the *deepest* route name in the focused tab
    const currentRouteName = getActiveRouteNameFromState(state);

    const event = navigation.emit({
      type: "tabPress",
      target: route.key,
      canPreventDefault: true,
    });

    if (!event.defaultPrevented) {
      if (isRouteFocused && route.name === "Home") {
        // If the tab is already focused and the deepest route is "Dashboard",
        // decide whether to reset or do nothing, etc.
        if (currentRouteName === "Dashboard") {
          // Already on the main Dashboard, do nothing
          return;
        } else {
          // We’re in a sub-route of Home, so reset back to 'Home'
          // navigation.reset({
          //   index: 0,
          //   routes: [{ name: "Home" }],
          // });
          navigation.navigate("Dashboard");
        }
      } else {
        // Default behavior
        navigation.navigate(route.name);
      }
    }
  };

  return (
    <View style={{ flexDirection: "row" }}>
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : route.name;
          const isFocused = state.index === index;

          return (
            <CustomTabButton
              key={route.key}
              label={label}
              isFocused={isFocused}
              onPress={() => onTabPress(route, index)} // Use the custom onTabPress
              icon={getIconName(route.name)}
            />
          );
        })}
        {/* Switch Tab Button */}
        {/* <CustomTabButton
          label="Switch"
          isFocused={false}
          onPress={() => navigation.navigate("Social")}
          icon={"swap-horizontal"}
        /> */}
      </View>
    </View>
  );
};

export default CustomPersonalBottomTabBar;
