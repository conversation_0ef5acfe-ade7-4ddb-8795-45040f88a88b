import React, { useRef, useEffect } from "react";
import { View, TouchableOpacity, StyleSheet, Text } from "react-native";
import * as Animatable from "react-native-animatable";
import Ionicons from "@expo/vector-icons/Ionicons";
import { useThemeContext } from "../../context/ThemeContext.js";

const CustomTabButton = ({ label, isFocused, onPress, icon }) => {
  const { theme } = useThemeContext();

  const styles = StyleSheet.create({
    tab: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    btnContainer: {
      width: 45,
      height: 45,
      borderWidth: 4,
      borderRadius: 25,
      backgroundColor: "transparent",
      justifyContent: "center",
      alignItems: "center",
      borderColor: isFocused
        ? theme.colors.screenBackground
        : theme.colors.primary,
    },
    circle: {
      ...StyleSheet.absoluteFillObject,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.primary,
      borderRadius: 25,
    },
    text: {
      fontSize: 12,
      textAlign: "center",
      color: "black",
    },
  });

  const viewRef = useRef(null);
  const circleRef = useRef(null);
  const textRef = useRef(null);

  useEffect(() => {
    if (isFocused) {
      viewRef.current.animate({
        0: { scale: 1, translateY: 7 },
        1: { scale: 1.2, translateY: -14 },
      });
      circleRef.current.animate({
        0: { scale: 1 },
        1: { scale: 1 },
      });
      textRef.current.transitionTo({ scale: 1 });
    } else {
      viewRef.current.animate({
        0: { scale: 1.2, translateY: 0 },
        1: { scale: 1, translateY: 7 },
      });
      circleRef.current.animate({ 0: { scale: 1 }, 1: { scale: 0 } });
      textRef.current.transitionTo({ scale: 0 });
    }
  }, [isFocused]);

  return (
    <TouchableOpacity style={styles.tab} activeOpacity={1} onPress={onPress}>
      <Animatable.View style={styles.tab} ref={viewRef} duration={500}>
        <View style={styles.btnContainer}>
          <Animatable.View ref={circleRef} style={styles.circle} />

          {label === "Switch" ? (
            <Ionicons name={icon} size={26} color={"black"} />
          ) : (
            <Ionicons
              name={icon}
              size={26}
              color={isFocused ? "black" : "rgba(0, 0, 0, 0.3)"}
            />
          )}
        </View>
        <Animatable.Text ref={textRef} style={styles.text}>
          {label}
        </Animatable.Text>
      </Animatable.View>
    </TouchableOpacity>
  );
};

export default CustomTabButton;
