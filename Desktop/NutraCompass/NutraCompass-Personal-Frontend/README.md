# NutraCompass Personal

NutraCompass Personal is an all-in-one wellness, nutrition, and fitness tracking application designed to guide users in achieving their health goals and living healthier, more fulfilling lives. The app provides tools to log daily meals, follow nutritional programs, track workouts, and engage with fitness programs. Future enhancements include an AI-powered NutraCompass Personal Assistant that leverages expert knowledge (from nutritionists, fitness trainers, and doctors) to provide personalized guidance, plans and automate routine tasks.

## Features

- **Food Diary:**  
  Record meals, snacks, and beverages with detailed nutritional information (calories, macronutrients, and micronutrients).

- **Nutritional Programs:**  
  Access and follow personalized nutritional plans tailored to your dietary goals.

- **Workout Diary & Fitness Programs (In Development):**  
  Log workouts and follow structured fitness routines designed to improve strength, endurance, and overall health.

- **AI NutraCompass Personal Assistant (Planned):**  
  An intelligent chatbot that provides expert advice on nutrition, fitness, and wellness. The assistant will help answer questions, offer personalized feedback, and automate actions within the app.

- **Analytics & Tracking:**  
  Gain insights into user engagement and activity with built-in analytics (via Firebase Analytics).

## Tech Stack

- **Frontend:**

  - [React Native](https://reactnative.dev/) (managed workflow using Expo)
  - [Expo](https://docs.expo.dev/) with EAS Build for custom dev clients
  - [React Native Firebase Analytics](https://rnfirebase.io/analytics/usage) for tracking user events

- **Backend:**

  - [Express/Node.js](https://expressjs.com/) for API endpoints and server-side logic
  - [Firebase Admin SDK](https://firebase.google.com/docs/admin/setup) for backend operations
  - [Firestore](https://firebase.google.com/docs/firestore) for data storage
  - [Firebase Authentication](https://firebase.google.com/docs/auth) (if needed for future features)

- **Analytics:**
  - Firebase Analytics (via React Native Firebase Analytics) to automatically track sessions and custom user events.
