import "./src/config/firebase.js";
import React, { useEffect } from "react";
import { Platform } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { AuthProvider } from "./src/authentication/context/AuthContext.js";
import RootNavigation from "./src/navigation/index.js";
import { TimeProvider } from "./src/context/TimeContext.js";
import { ThemeProvider } from "./src/context/ThemeContext.js";
import { UserSettingsProvider } from "./src/features/Settings/context/UserSettingsContext.js";
import { PushNotificationProvider } from "./src/context/PushNotificationContext.js";
import { FeatureFlagsProvider } from "./src/context/FeatureFlagsContext.js";
import { MenuProvider } from "react-native-popup-menu";
import "intl";
import "intl/locale-data/jsonp/en";
import * as Notifications from "expo-notifications";

export default function App() {
  useEffect(() => {
    const configureChannels = async () => {
      if (Platform.OS === "android") {
        await Notifications.setNotificationChannelAsync("default", {
          name: "Default Channel",
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: "#FF231F7C",
        });
      }
    };
    configureChannels();
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <TimeProvider>
        <AuthProvider>
          <UserSettingsProvider>
            <FeatureFlagsProvider>
              <PushNotificationProvider>
                <ThemeProvider>
                  <MenuProvider>
                    {/* Root Navigation */}
                    <RootNavigation />
                  </MenuProvider>
                </ThemeProvider>
              </PushNotificationProvider>
            </FeatureFlagsProvider>
          </UserSettingsProvider>
        </AuthProvider>
      </TimeProvider>
    </GestureHandlerRootView>
  );
}
