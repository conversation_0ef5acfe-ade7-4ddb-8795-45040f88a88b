const { getDefaultConfig } = require("expo/metro-config");

const config = getDefaultConfig(__dirname);

// Add '.cjs', '.css', and '.svg' extensions to sourceExts
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  "cjs",
  "svg",
  "css",
];

// Ensure .svg and .css files are handled with the proper transformers
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve("react-native-svg-transformer"),
  assetPlugins: ["expo-asset/tools/hashAssetFiles"],
  // Use CSS transformer for `.css` files
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// Remove '.svg' from assetExts as it's now handled by react-native-svg-transformer
config.resolver.assetExts = config.resolver.assetExts.filter(
  (ext) => ext !== "svg"
);

module.exports = config;
