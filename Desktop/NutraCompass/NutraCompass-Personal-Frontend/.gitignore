# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

configs.txt

# dependencies
node_modules/

# Google Service files
src/config/GoogleService-Info-Dev.plist
src/config/GoogleService-Info-Prod.plist
src/config/google-services-dev.json
src/config/google-services-prod.json

# Expo
.expo/
dist/
web-build/

# Yarn
.yarn/
.yarn

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

.env*

# more
.o
.log

# Ignore Visual Studio temporary files, build results, and
# files generated by popular Visual Studio add-ons.

# User-specific files
.vs/
*.user
*.userosscache
*.suo
*.userprefs
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Mono Auto Generated Files
mono_crash.*

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

.env.development


# @generated expo-cli sync-b5df6a44d8735348b729920a7406b633cfb74d4c
# The following patterns were generated by expo-cli

# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/
/android
/ios

# Expo
.expo/
dist/
web-build/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# @end expo-cli