import {
  MD3LightTheme as DefaultTheme,
  MD3DarkTheme as DarkTheme,
} from "react-native-paper";

export const Default = {
  name: "Default",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(76, 175, 80, 1)", // green color
      secondary: "rgba(33, 150, 243, 1)", // blue color
      primaryLightOpacity: "rgba(76, 175, 80, 0.5)", // green With custom opacity
      secondaryLightOpacity: "rgba(33, 150, 243, 0.5)", // blue With custom opacity
      chartRemainingColor: "rgba(182, 224, 184, 1)", // Lighter green
      screenBackground: "rgba(20, 20, 20, 1)", // Converted from #1E1E1E
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Converted from #333333
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Converted from #2C2C2C
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)", // Converted from #666666
      primaryTextColor: "rgba(255, 255, 255, 1)", // Converted from #FFFFFF
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "#4CAF50", // Green color
      secondary: "#2196F3", // Blue color
      primaryLightOpacity: "rgba(76, 175, 80, 0.6)", // Adjusted manually
      secondaryLightOpacity: "rgba(33, 150, 243, 0.6)", // Adjusted manually
      chartRemainingColor: "rgba(182, 224, 184, 1)", // Lighter green
      screenBackground: "#E5E5E5",
      sectionBackgroundColor: "#F5F5F5",
      sectionBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardBackgroundColor: "#FFFFFF",
      cardBackgroundColorLowOpacity: "rgba(255, 255, 255, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(0, 0, 0, 0.1)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#212121",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const BlossomPink = {
  name: "Blossom Pink",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(255, 105, 180, 1)", // HotPink
      secondary: "rgba(255, 182, 193, 1)", // LightPink
      primaryLightOpacity: "rgba(255, 105, 180, 0.5)", // Semi transparent HotPink
      secondaryLightOpacity: "rgba(255, 182, 193, 0.5)", // Semi transparent LightPink
      chartRemainingColor: "rgba(255, 205, 224, 1)", // Lighter HotPink
      screenBackground: "rgba(20, 20, 20, 1)", // Deep dark background for contrast
      sectionBackgroundColor: "rgba(255, 192, 203, 1)", // Pink
      cardBackgroundColor: "rgba(255, 160, 122, 1)", // LightSalmon
      sectionBackgroundColorLowOpacity: "rgba(255, 192, 203, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(255, 160, 122, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)", // Light shadow for depth
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(255, 99, 71, 1)", // Tomato
      primaryTextColor: "rgba(255, 255, 255, 1)", // White for text
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(255, 182, 193, 1)", // LightPink for primary elements
      secondary: "rgba(255, 192, 203, 1)", // Pink for secondary elements
      primaryLightOpacity: "rgba(255, 182, 193, 0.5)", // LightPink for primary elements
      secondaryLightOpacity: "rgba(255, 192, 203, 0.5)", // Pink for secondary elements
      chartRemainingColor: "rgba(255, 205, 224, 1)", // Lighter HotPink
      screenBackground: "#E5E5E5", // Matte gray for a clean look
      sectionBackgroundColor: "rgba(255, 105, 180, 1)", // HotPink for sections
      cardBackgroundColor: "#F2F2F2", // Light gray for cards, providing soft contrast
      sectionBackgroundColorLowOpacity: "rgba(255, 192, 203, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(242, 242, 242, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)", // Subtle shadow for depth
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "rgba(255, 105, 180, 1)", // HotPink for card borders
      primaryTextColor: "#000000", // Black for text for readability
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const LavenderDream = {
  name: "Lavender Dream",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(138, 43, 226, 1)", // BlueViolet
      secondary: "rgba(153, 50, 204, 1)", // DarkOrchid
      primaryLightOpacity: "rgba(138, 43, 226, 0.5)", // BlueViolet
      secondaryLightOpacity: "rgba(153, 50, 204, 0.5)", // DarkOrchid
      chartRemainingColor: "rgba(204, 153, 239, 1)", // Lighter BlueViolet
      screenBackground: "rgba(20, 20, 20, 1)", // Deep dark background for contrast
      sectionBackgroundColor: "rgba(128, 0, 128, 1)", // Purple
      cardBackgroundColor: "rgba(148, 0, 211, 1)", // DarkViolet
      sectionBackgroundColorLowOpacity: "rgba(128, 0, 128, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(148, 0, 211, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)", // Light shadow for depth
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(139, 0, 139, 1)", // DarkMagenta
      primaryTextColor: "rgba(255, 255, 255, 1)", // White for text
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(153, 50, 204, 1)", // DarkOrchid for primary elements
      secondary: "rgba(186, 85, 211, 1)", // MediumOrchid for secondary elements
      primaryLightOpacity: "rgba(153, 50, 204, 0.5)", // DarkOrchid for primary elements
      secondaryLightOpacity: "rgba(186, 85, 211, 0.5)", // MediumOrchid for secondary elements
      chartRemainingColor: "rgba(204, 153, 239, 1)", // Lighter BlueViolet
      screenBackground: "#E5E5E5", // Matte gray for a clean look
      sectionBackgroundColor: "rgba(128, 0, 128, 1)", // Purple for sections
      cardBackgroundColor: "#F2F2F2", // Light gray for cards, providing soft contrast
      sectionBackgroundColorLowOpacity: "rgba(128, 0, 128, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(242, 242, 242, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)", // Subtle shadow for depth
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "rgba(139, 0, 139, 1)", // DarkMagenta for card borders
      primaryTextColor: "#000000", // Black for text for readability
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const SereneAqua = {
  name: "Serene Aqua",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(0, 206, 209, 1)", // Teal color for primary elements
      secondary: "rgba(32, 178, 170, 1)", // Lighter teal for secondary elements
      primaryLightOpacity: "rgba(0, 206, 209, 0.5)", // Teal color for primary elements
      secondaryLightOpacity: "rgba(32, 178, 170, 0.5)", // Lighter teal for secondary elements
      chartRemainingColor: "rgba(153, 233, 234, 1)", // Lighter Teal
      screenBackground: "rgba(20, 20, 20, 1)", // Deep dark background for contrast
      sectionBackgroundColor: "rgba(0, 128, 128, 1)", // Dark teal for sections
      cardBackgroundColor: "rgba(0, 139, 139, 1)", // Darker teal for card backgrounds
      sectionBackgroundColorLowOpacity: "rgba(0, 128, 128, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(0, 139, 139, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)", // Light shadow for depth
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(70, 130, 180, 1)", // Steel Blue for card borders
      primaryTextColor: "rgba(255, 255, 255, 1)", // White for text
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(32, 178, 170, 1)", // Light Sea Green for primary elements
      secondary: "rgba(0, 255, 255, 1)", // Cyan/Aqua for secondary elements
      primaryLightOpacity: "rgba(32, 178, 170, 0.5)", // Light Sea Green for primary elements
      secondaryLightOpacity: "rgba(0, 255, 255, 0.5)", // Cyan/Aqua for secondary elements
      chartRemainingColor: "rgba(153, 233, 234, 1)", // Lighter Teal
      screenBackground: "#E5E5E5", // Matte gray for a clean look
      sectionBackgroundColor: "rgba(0, 128, 128, 1)", // Teal for sections
      cardBackgroundColor: "#F2F2F2", // Light gray for cards, providing soft contrast
      sectionBackgroundColorLowOpacity: "rgba(0, 128, 128, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(242, 242, 242, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)", // Subtle shadow for depth
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "rgba(95, 158, 160, 1)", // Cadet Blue for card borders
      primaryTextColor: "#000000", // Black for text for readability
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const SunsetGlow = {
  name: "Sunset Glow",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(255, 215, 0, 1)", // Rich Gold color
      secondary: "rgba(25, 42, 86, 1)", // Deep Blue color for secondary elements
      primaryLightOpacity: "rgba(255, 215, 0, 0.5)", // Gold with custom opacity for overlays
      secondaryLightOpacity: "rgba(255, 215, 0, 0.5)", // Gold for secondary elements with opacity
      chartRemainingColor: "rgba(255, 232, 153, 1)", // Lighter Gold
      screenBackground: "rgba(20, 20, 20, 1)", // Deep Black Screen Background
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Slightly lighter black for sections
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Even lighter black for cards
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(255, 215, 0, 1)", // Gold for card borders
      primaryTextColor: "rgba(255, 255, 255, 1)", // White for text
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(255, 215, 0, 1)", // Rich Gold color
      secondary: "rgba(25, 42, 86, 0.5)", // Deep Blue color for secondary elements
      primaryLightOpacity: "rgba(255, 215, 0, 0.6)", // Gold with custom opacity for overlays
      secondaryLightOpacity: "rgba(255, 215, 0, 0.6)", // Gold for secondary elements with opacity
      chartRemainingColor: "rgba(255, 232, 153, 1)", // Lighter Gold
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)", // Light background with low opacity
      cardBackgroundColor: "#F5F5F5", // Matte white background for cards
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)", // Lightened background with low opacity
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)", // Dark gray background with high opacity
      shadow: "rgba(240, 240, 240, 0.5)", // Shadow for depth
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#FFD700", // Gold for card borders
      primaryTextColor: "#000000", // Black for text
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const UrbanMidnight = {
  name: "Urban Midnight",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(52, 152, 219, 1)", // Bright Blue color
      secondary: "rgba(44, 62, 80, 1)", // Slate color for secondary elements
      primaryLightOpacity: "rgba(52, 152, 219, 0.5)", // Blue with custom opacity
      secondaryLightOpacity: "rgba(44, 62, 80, 0.5)", // Slate with custom opacity
      chartRemainingColor: "rgba(153, 204, 255, 1)", // Lighter Blue
      screenBackground: "rgba(20, 20, 20, 1)", // Matte Black Screen Background
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Consistent with Default
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Consistent with Default
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)",
      primaryTextColor: "rgba(255, 255, 255, 1)",
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(52, 152, 219, 1)", // Bright Blue color
      secondary: "rgba(44, 62, 80, 0.5)", // Slate color for secondary elements
      primaryLightOpacity: "rgba(52, 152, 219, 0.5)", // Blue with custom opacity
      secondaryLightOpacity: "rgba(44, 62, 80, 0.5)", // Slate with custom opacity
      chartRemainingColor: "rgba(153, 204, 255, 1)", // Lighter Blue
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)",
      cardBackgroundColor: "#F5F5F5", // Matte white background
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#000000",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const VerdantFields = {
  name: "Verdant Fields",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(0, 255, 0, 1)", // Vibrant Green color
      secondary: "rgba(44, 62, 80, 1)", // Slate color for secondary elements
      primaryLightOpacity: "rgba(0, 255, 0, 0.5)", // Green with custom opacity
      secondaryLightOpacity: "rgba(44, 62, 80, 0.5)", // Slate with custom opacity
      chartRemainingColor: "rgba(153, 255, 153, 1)", // Lighter Green
      screenBackground: "rgba(20, 20, 20, 1)", // Matte Black Screen Background
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Consistent with Default
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Consistent with Default
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)",
      primaryTextColor: "rgba(255, 255, 255, 1)",
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(0, 255, 0, 1)", // Vibrant Green color
      secondary: "rgba(44, 62, 80, 0.5)", // Slate color for secondary elements
      primaryLightOpacity: "rgba(0, 255, 0, 0.5)", // Green with custom opacity
      secondaryLightOpacity: "rgba(44, 62, 80, 0.5)", // Slate with custom opacity
      chartRemainingColor: "rgba(153, 255, 153, 1)", // Lighter Green
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)",
      cardBackgroundColor: "#F5F5F5", // Matte white background
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#000000",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const CrimsonTide = {
  name: "Crimson Tide",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(255, 0, 0, 1)", // Vibrant Red color
      secondary: "rgba(0, 128, 128, 1)", // Teal for secondary elements
      primaryLightOpacity: "rgba(255, 0, 0, 0.5)", // Vibrant Red with custom opacity
      secondaryLightOpacity: "rgba(139, 0, 0, 0.5)", // Dark Red with custom opacity
      chartRemainingColor: "rgba(255, 153, 153, 1)", // Lighter Red
      screenBackground: "rgba(20, 20, 20, 1)", // Matte Black Screen Background
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Consistent with Default
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Consistent with Default
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)",
      primaryTextColor: "rgba(255, 255, 255, 1)",
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(255, 0, 0, 1)", // Vibrant Red color
      secondary: "rgba(0, 128, 128, 0.5)", // Teal for secondary elements
      primaryLightOpacity: "rgba(255, 0, 0, 0.5)", // Vibrant Red with custom opacity
      secondaryLightOpacity: "rgba(139, 0, 0, 0.5)", // Dark Red with custom opacity
      chartRemainingColor: "rgba(255, 153, 153, 1)", // Lighter Red
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)",
      cardBackgroundColor: "#F5F5F5", // Matte white background
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#000000",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const NeonBlush = {
  name: "Neon Blush",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(255, 20, 147, 1)", // Hot Pink color
      secondary: "rgba(0, 31, 63, 1)", // Navy Blue for secondary elements
      primaryLightOpacity: "rgba(255, 20, 147, 0.5)", // Hot Pink with custom opacity
      secondaryLightOpacity: "rgba(199, 21, 133, 0.5)", // Darker Pink with custom opacity
      chartRemainingColor: "rgba(255, 153, 204, 1)", // Lighter Pink
      screenBackground: "rgba(20, 20, 20, 1)", // Matte Gray Screen Background as in Default
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Consistent with Default
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Consistent with Default
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)",
      primaryTextColor: "rgba(255, 255, 255, 1)",
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(255, 20, 147, 1)", // Hot Pink color
      secondary: "rgba(0, 31, 63, 0.5)", // Navy Blue for secondary elements
      primaryLightOpacity: "rgba(255, 20, 147, 0.5)", // Hot Pink with custom opacity
      secondaryLightOpacity: "rgba(199, 21, 133, 0.5)", // Darker Pink with custom opacity
      chartRemainingColor: "rgba(255, 153, 204, 1)", // Lighter Pink
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)",
      cardBackgroundColor: "#F5F5F5", // Matte white background
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#000000",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const CitrusSunset = {
  name: "Citrus Sunset",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(255, 69, 0, 1)", // Vibrant Orange color
      secondary: "rgba(139, 37, 0, 1)", // Darker Orange color
      primaryLightOpacity: "rgba(255, 69, 0, 0.5)", // Orange with custom opacity
      secondaryLightOpacity: "rgba(139, 37, 0, 0.5)", // Darker Orange with custom opacity
      chartRemainingColor: "rgba(255, 179, 128, 1)", // Lighter Orange
      screenBackground: "rgba(20, 20, 20, 1)", // Matte Gray Screen Background as in Default
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Consistent with Default
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Consistent with Default
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)",
      primaryTextColor: "rgba(255, 255, 255, 1)",
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(255, 69, 0, 1)", // Vibrant Orange color
      secondary: "rgba(139, 37, 0, 0.5)", // Darker Orange color
      primaryLightOpacity: "rgba(255, 69, 0, 0.5)", // Orange with custom opacity
      secondaryLightOpacity: "rgba(139, 37, 0, 0.5)", // Darker Orange with custom opacity
      chartRemainingColor: "rgba(255, 179, 128, 1)", // Lighter Orange
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)",
      cardBackgroundColor: "#F5F5F5", // Matte white background
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#000000",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const TropicalLagoon = {
  name: "Tropical Lagoon",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(0, 128, 128, 1)", // Teal color
      secondary: "rgba(0, 64, 64, 1)", // Darker Teal/Turquoise color
      primaryLightOpacity: "rgba(0, 128, 128, 0.5)", // Teal with custom opacity
      secondaryLightOpacity: "rgba(0, 64, 64, 0.5)", // Darker Teal/Turquoise with custom opacity
      chartRemainingColor: "rgba(128, 224, 224, 1)", // Lighter Teal
      screenBackground: "rgba(20, 20, 20, 1)", // Matte Gray Screen Background similar to Default
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Consistent with Default
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Consistent with Default
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)",
      primaryTextColor: "rgba(255, 255, 255, 1)",
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(0, 128, 128, 1)", // Teal color
      secondary: "rgba(0, 64, 64, 0.5)", // Darker Teal/Turquoise color
      primaryLightOpacity: "rgba(0, 128, 128, 0.5)", // Teal with custom opacity
      secondaryLightOpacity: "rgba(0, 64, 64, 0.5)", // Darker Teal/Turquoise with custom opacity
      chartRemainingColor: "rgba(128, 224, 224, 1)", // Lighter Teal
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)",
      cardBackgroundColor: "#F5F5F5", // Matte white background
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#000000",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const SilkenGray = {
  name: "Silken Gray",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(192, 192, 192, 1)", // Silver color
      secondary: "rgba(169, 169, 169, 1)", // Dark Silver color
      primaryLightOpacity: "rgba(192, 192, 192, 0.5)", // Silver with custom opacity
      secondaryLightOpacity: "rgba(169, 169, 169, 0.5)", // Dark Silver with custom opacity
      chartRemainingColor: "rgba(224, 224, 224, 1)", // Lighter Silver
      screenBackground: "rgba(20, 20, 20, 1)", // Matte Gray Screen Background
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Same as Default
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Same as Default
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)",
      primaryTextColor: "rgba(255, 255, 255, 1)",
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(192, 192, 192, 1)", // Silver color
      secondary: "rgba(169, 169, 169, 1)", // Dark Silver color
      primaryLightOpacity: "rgba(192, 192, 192, 0.5)", // Silver with custom opacity
      secondaryLightOpacity: "rgba(169, 169, 169, 0.5)", // Dark Silver with custom opacity
      chartRemainingColor: "rgba(224, 224, 224, 1)", // Lighter Silver
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)",
      cardBackgroundColor: "#F5F5F5", // Matte white background
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#000000",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};

export const RoyalViolet = {
  name: "Royal Violet",
  dark: {
    ...DarkTheme,
    myOwnProperty: true,
    colors: {
      ...DarkTheme.colors,
      primary: "rgba(156, 39, 176, 1)", // Purple color
      secondary: "rgba(233, 30, 99, 1)", // Pink color
      primaryLightOpacity: "rgba(156, 39, 176, 0.5)", // Purple with custom opacity
      secondaryLightOpacity: "rgba(233, 30, 99, 0.5)", // Pink with custom opacity
      chartRemainingColor: "rgba(204, 153, 229, 1)", // Lighter Purple
      screenBackground: "rgba(20, 20, 20, 1)", // Matte Gray Screen Background
      sectionBackgroundColor: "rgba(51, 51, 51, 1)", // Same as Default
      cardBackgroundColor: "rgba(44, 44, 44, 1)", // Same as Default
      sectionBackgroundColorLowOpacity: "rgba(51, 51, 51, 0.5)",
      cardBackgroundColorLowOpacity: "rgba(44, 44, 44, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(30, 30, 30, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "rgba(85, 85, 85, 1)", // Converted from #555555
      cardBorderColor: "rgba(102, 102, 102, 1)",
      primaryTextColor: "rgba(255, 255, 255, 1)",
      subTextColor: "rgba(176, 176, 176, 1)", // Converted from #B0B0B0
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
  light: {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      primary: "rgba(156, 39, 176, 1)", // Purple color
      secondary: "rgba(233, 30, 99, 1)", // Pink color
      primaryLightOpacity: "rgba(156, 39, 176, 0.5)", // Purple with custom opacity
      secondaryLightOpacity: "rgba(233, 30, 99, 0.5)", // Pink with custom opacity
      chartRemainingColor: "rgba(204, 153, 229, 1)", // Lighter Purple
      screenBackground: "#E5E5E5",
      sectionBackgroundColorLowOpacity: "rgba(224, 224, 224, 0.5)",
      cardBackgroundColor: "#F5F5F5",
      cardBackgroundColorLowOpacity: "rgba(245, 245, 245, 0.5)",
      cardDarkGrayBackgroundColor: "rgba(240, 240, 240, 0.9)",
      shadow: "rgba(240, 240, 240, 0.5)",
      sectionBorderColor: "#E0E0E0",
      cardBorderColor: "#CCCCCC",
      primaryTextColor: "#000000",
      subTextColor: "#757575",
    },
    dimensions: { sectionBorderRadius: 8, cardBorderRadius: 6 },
  },
};
