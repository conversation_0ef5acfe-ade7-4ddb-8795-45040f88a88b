export default ({ config }) => {
  // Use CONFIG_ENV to decide which config files to use.
  // Default to production if CONFIG_ENV is not defined.
  const isProd = process.env.CONFIG_ENV === "production";

  // Set the file paths based on the environment.
  const iosGoogleServicesFile = isProd
    ? "./src/config/GoogleService-Info-Prod.plist"
    : "./src/config/GoogleService-Info-Dev.plist";

  const androidGoogleServicesFile = isProd
    ? "./src/config/google-services-prod.json"
    : "./src/config/google-services-dev.json";

  const extraConfig = {
    eas: {
      projectId:
        process.env.EXPO_PUBLIC_EAS_PROJECT_ID ||
        "268ed217-15ee-43ed-877a-cb5b01b2a22c",
    },
    NutraCompass_API_URL:
      process.env.NUTRACOMPASS_API_URL ||
      process.env.EXPO_PUBLIC_NUTRACOMPASS_API_URL,
    // ---- Firebase variables with OR statements ----
    FIREBASE_API_KEY:
      process.env.FIREBASE_API_KEY || process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
    FIREBASE_AUTH_DOMAIN:
      process.env.FIREBASE_AUTH_DOMAIN ||
      process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
    FIREBASE_PROJECT_ID:
      process.env.FIREBASE_PROJECT_ID ||
      process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
    FIREBASE_STORAGE_BUCKET:
      process.env.FIREBASE_STORAGE_BUCKET ||
      process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
    FIREBASE_MESSAGING_SENDER_ID:
      process.env.FIREBASE_MESSAGING_SENDER_ID ||
      process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    FIREBASE_APP_ID:
      process.env.FIREBASE_APP_ID || process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
    FIREBASE_MEASUREMENT_ID:
      process.env.FIREBASE_MEASUREMENT_ID ||
      process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID,

    // ---- Google auth IDs ----
    GOOGLE_AUTH_WEB_CLIENT_ID:
      process.env.GOOGLE_AUTH_WEB_CLIENT_ID ||
      process.env.EXPO_PUBLIC_GOOGLE_AUTH_WEB_CLIENT_ID,
    GOOGLE_AUTH_IOS_CLIENT_ID:
      process.env.GOOGLE_AUTH_IOS_CLIENT_ID ||
      process.env.EXPO_PUBLIC_GOOGLE_AUTH_IOS_CLIENT_ID,

    // ---- Edamam API ----
    EDAMAM_APP_ID:
      process.env.EDAMAM_APP_ID || process.env.EXPO_PUBLIC_EDAMAM_APP_ID,
    EDAMAM_APP_KEY:
      process.env.EDAMAM_APP_KEY || process.env.EXPO_PUBLIC_EDAMAM_APP_KEY,
    EDAMAM_PARSER_BASE_URL:
      process.env.EDAMAM_PARSER_BASE_URL ||
      process.env.EXPO_PUBLIC_EDAMAM_PARSER_BASE_URL,
    EDAMAM_NUTRIENTS_BASE_URL:
      process.env.EDAMAM_NUTRIENTS_BASE_URL ||
      process.env.EXPO_PUBLIC_EDAMAM_NUTRIENTS_BASE_URL,

    // ---- RapidAPI ----
    RAPIDAPI_KEY:
      process.env.RAPIDAPI_KEY || process.env.EXPO_PUBLIC_RAPIDAPI_KEY,
    RAPIDAPI_HOST:
      process.env.RAPIDAPI_HOST || process.env.EXPO_PUBLIC_RAPIDAPI_HOST,
  };

  //console.log("Extra config:", JSON.stringify(extraConfig, null, 1));

  return {
    ...config,
    name: "NutraCompass",
    slug: "nutracompass",
    version: "1.0.1", // Incremented app version
    runtimeVersion: "1.0.1",
    orientation: "portrait",
    icon: "./assets/NutraCompass.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/brandmark-design.png",
      contentFit: "contain",
      backgroundColor: "#1E1E1E",
      resizeMode: "contain",
    },
    assetBundlePatterns: ["./assets/**/*"],
    ios: {
      supportsTablet: true,
      googleServicesFile: iosGoogleServicesFile,
      bundleIdentifier: "com.nutracompass.personal",
      buildNumber: "30", // Increment for every new build
      infoPlist: {
        CFBundleURLTypes: [
          {
            CFBundleURLSchemes: [
              "com.googleusercontent.apps.356020700499-ljocfsi93litfhpamtoof3v7t3l8ilqi",
              "com.googleusercontent.apps.356020700499-aippfvrmg3s9i8ltcm8psia0ei2lc11c",
            ],
          },
        ],
        ITSAppUsesNonExemptEncryption: false,
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      permissions: [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
      ],
      googleServicesFile: androidGoogleServicesFile,
      package: "com.nutracompass.personal",
      versionCode: 2, // Increment for every new build
    },
    web: {
      favicon: "./assets/favicon.png",
    },
    plugins: [
      ["expo-localization"],
      [
        "expo-camera",
        {
          cameraPermission: "Allow $(PRODUCT_NAME) to access your camera.",
        },
      ],
      [
        "expo-contacts",
        {
          contactsPermission: "Allow $(PRODUCT_NAME) to access your contacts.",
        },
      ],
      ["@react-native-firebase/app"],
      ["@react-native-firebase/crashlytics"],
      ["expo-notifications"],
      [
        "expo-build-properties",
        {
          ios: {
            deploymentTarget: "13.4",
            useFrameworks: "static",
          },
        },
      ],
      ["expo-image-picker"],
      ["expo-sensors"],
      ["expo-task-manager"],
      ["expo-updates"],
    ],
    extra: extraConfig,
    updates: {
      enabled: true,
      checkAutomatically: "ON_ERROR_RECOVERY",
      fallbackToCacheTimeout: 0,
      url: "https://u.expo.dev/268ed217-15ee-43ed-877a-cb5b01b2a22c", // Use an Expo Secret here if this value needs to be secure
    },
  };
};
