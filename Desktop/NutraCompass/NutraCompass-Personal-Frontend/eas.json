{"cli": {"version": ">= 9.1.0", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "ios": {"simulator": true}, "env": {"CONFIG_ENV": "development", "EAS_PROJECT_ID": "268ed217-15ee-43ed-877a-cb5b01b2a22c", "EAS_UPDATES_URL": "https://u.expo.dev/268ed217-15ee-43ed-877a-cb5b01b2a22c", "NUTRACOMPASS_API_URL": "https://nutracompass-personal-dev-2d9f635773f9.herokuapp.com", "FIREBASE_API_KEY": "AIzaSyDtkvoQ51JM4yXMAm0_h8hZHrhso4LNHeo", "FIREBASE_AUTH_DOMAIN": "nutracompass-individual.firebaseapp.com", "FIREBASE_PROJECT_ID": "nutracompass-individual", "FIREBASE_STORAGE_BUCKET": "nutracompass-individual.appspot.com", "FIREBASE_MESSAGING_SENDER_ID": "961917384290", "FIREBASE_APP_ID": "1:961917384290:web:1800d93679809c07abc33b", "FIREBASE_MEASUREMENT_ID": "G-438J2CJY12", "GOOGLE_AUTH_WEB_CLIENT_ID": "961917384290-4akireqpg59qqge26dqq52go9ijan6fd.apps.googleusercontent.com", "GOOGLE_AUTH_IOS_CLIENT_ID": "961917384290-e6rk6293v6km1vio6q7gpk5d6bb1c5q7.apps.googleusercontent.com", "EDAMAM_APP_ID": "b0413aad", "EDAMAM_APP_KEY": "********************************", "EDAMAM_PARSER_BASE_URL": "https://api.edamam.com/api/food-database/v2/parser", "EDAMAM_NUTRIENTS_BASE_URL": "https://api.edamam.com/api/food-database/v2/nutrients", "RAPIDAPI_KEY": "**************************************************", "RAPIDAPI_HOST": "exercisedb.p.rapidapi.com"}}, "preview": {"distribution": "internal", "channel": "preview", "env": {"CONFIG_ENV": "development", "EAS_PROJECT_ID": "268ed217-15ee-43ed-877a-cb5b01b2a22c", "EAS_UPDATES_URL": "https://u.expo.dev/268ed217-15ee-43ed-877a-cb5b01b2a22c", "NUTRACOMPASS_API_URL": "https://nutracompass-personal-dev-2d9f635773f9.herokuapp.com", "FIREBASE_API_KEY": "AIzaSyDtkvoQ51JM4yXMAm0_h8hZHrhso4LNHeo", "FIREBASE_AUTH_DOMAIN": "nutracompass-individual.firebaseapp.com", "FIREBASE_PROJECT_ID": "nutracompass-individual", "FIREBASE_STORAGE_BUCKET": "nutracompass-individual.appspot.com", "FIREBASE_MESSAGING_SENDER_ID": "961917384290", "FIREBASE_APP_ID": "1:961917384290:web:1800d93679809c07abc33b", "FIREBASE_MEASUREMENT_ID": "G-438J2CJY12", "GOOGLE_AUTH_WEB_CLIENT_ID": "961917384290-4akireqpg59qqge26dqq52go9ijan6fd.apps.googleusercontent.com", "GOOGLE_AUTH_IOS_CLIENT_ID": "961917384290-e6rk6293v6km1vio6q7gpk5d6bb1c5q7.apps.googleusercontent.com", "EDAMAM_APP_ID": "b0413aad", "EDAMAM_APP_KEY": "********************************", "EDAMAM_PARSER_BASE_URL": "https://api.edamam.com/api/food-database/v2/parser", "EDAMAM_NUTRIENTS_BASE_URL": "https://api.edamam.com/api/food-database/v2/nutrients", "RAPIDAPI_KEY": "**************************************************", "RAPIDAPI_HOST": "exercisedb.p.rapidapi.com"}}, "production": {"channel": "production", "env": {"CONFIG_ENV": "production", "EAS_PROJECT_ID": "@eas_project_id", "EAS_UPDATES_URL": "@eas_updates_url", "NUTRACOMPASS_API_URL": "@nutracompass_api_url", "FIREBASE_API_KEY": "@firebase_api_key", "FIREBASE_AUTH_DOMAIN": "@firebase_auth_domain", "FIREBASE_PROJECT_ID": "@firebase_project_id", "FIREBASE_STORAGE_BUCKET": "@firebase_storage_bucket", "FIREBASE_MESSAGING_SENDER_ID": "@firebase_messaging_sender_id", "FIREBASE_APP_ID": "@firebase_app_id", "FIREBASE_MEASUREMENT_ID": "@firebase_measurement_id", "GOOGLE_AUTH_WEB_CLIENT_ID": "@google_auth_web_client_id", "GOOGLE_AUTH_IOS_CLIENT_ID": "@google_auth_ios_client_id", "EDAMAM_APP_ID": "@edamam_app_id", "EDAMAM_APP_KEY": "@edamam_app_key", "EDAMAM_PARSER_BASE_URL": "@edamam_parser_base_url", "EDAMAM_NUTRIENTS_BASE_URL": "@edamam_nutrients_base_url", "RAPIDAPI_KEY": "@rapidapi_key", "RAPIDAPI_HOST": "@rapidapi_host"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "1234567890", "appleTeamId": "AB12XYZ34S"}}}}