2025-02-16T00:14:13.577Z - Transforming: node_modules/expo/AppEntry.js
2025-02-16T00:14:14.036Z - Transforming: node_modules/expo/src/launch/registerRootComponent.tsx
2025-02-16T00:14:14.036Z - Transforming: node_modules/@babel/runtime/helpers/interopRequireDefault.js
2025-02-16T00:14:14.036Z - Transforming: App.js
2025-02-16T00:14:14.260Z - Transforming: node_modules/expo/src/launch/withDevTools.ios.tsx
2025-02-16T00:14:14.304Z - Transforming: node_modules/react/index.js
2025-02-16T00:14:14.304Z - Transforming: node_modules/react-native-gesture-handler/src/index.ts
2025-02-16T00:14:14.307Z - Transforming: node_modules/@babel/runtime/helpers/asyncToGenerator.js
2025-02-16T00:14:14.323Z - Transforming: src/config/firebase.js
2025-02-16T00:14:14.329Z - Transforming: src/authentication/context/AuthContext.js
2025-02-16T00:14:14.342Z - Transforming: src/navigation/index.js
2025-02-16T00:14:14.366Z - Transforming: src/context/TimeContext.js
2025-02-16T00:14:14.414Z - Transforming: src/context/ThemeContext.js
2025-02-16T00:14:14.446Z - Transforming: src/features/Settings/context/UserSettingsContext.js
2025-02-16T00:14:14.471Z - Transforming: src/context/FeatureFlagsContext.js
2025-02-16T00:14:14.524Z - Transforming: node_modules/react-native-popup-menu/build/rnpm.js
2025-02-16T00:14:14.549Z - Transforming: node_modules/react-native/index.js
2025-02-16T00:14:14.555Z - Transforming: src/context/PushNotificationContext.js
2025-02-16T00:14:14.574Z - Transforming: node_modules/expo/src/Expo.fx.tsx
2025-02-16T00:14:14.603Z - Transforming: node_modules/intl/index.js
2025-02-16T00:14:14.609Z - Transforming: node_modules/expo-notifications/build/index.js
2025-02-16T00:14:14.647Z - Transforming: node_modules/intl/locale-data/jsonp/en.js
2025-02-16T00:14:14.700Z - Transforming: node_modules/react-native-css-interop/dist/runtime/jsx-runtime.js
2025-02-16T00:14:14.723Z - Transforming: node_modules/react/cjs/react.development.js
2025-02-16T00:14:14.723Z - Transforming: node_modules/metro-runtime/src/modules/empty-module.js
2025-02-16T00:14:14.727Z - Transforming: node_modules/expo/src/environment/DevLoadingView.tsx
2025-02-16T00:14:14.749Z - Transforming: node_modules/expo/src/environment/ExpoGo.ts
2025-02-16T00:14:14.783Z - Transforming: node_modules/expo-keep-awake/build/index.js
2025-02-16T00:14:14.849Z - Transforming: node_modules/@react-native-async-storage/async-storage/src/index.ts
2025-02-16T00:14:14.860Z - Transforming: node_modules/firebase/app/dist/esm/index.esm.js
2025-02-16T00:14:14.879Z - Transforming: node_modules/firebase/analytics/dist/esm/index.esm.js
2025-02-16T00:14:14.907Z - Transforming: node_modules/firebase/firestore/dist/esm/index.esm.js
2025-02-16T00:14:14.913Z - Transforming: node_modules/firebase/auth/dist/esm/index.esm.js
2025-02-16T00:14:14.920Z - Transforming: node_modules/firebase/storage/dist/esm/index.esm.js
2025-02-16T00:14:14.929Z - Transforming: configs.js
2025-02-16T00:14:14.932Z - Transforming: node_modules/react-native-gesture-handler/src/init.ts
2025-02-16T00:14:14.956Z - Transforming: node_modules/react-native-gesture-handler/src/State.ts
2025-02-16T00:14:14.959Z - Transforming: node_modules/react-native-gesture-handler/src/Directions.ts
2025-02-16T00:14:14.969Z - Transforming: node_modules/react-native-gesture-handler/src/PointerType.ts
2025-02-16T00:14:14.976Z - Transforming: node_modules/react-native-gesture-handler/src/components/gestureHandlerRootHOC.tsx
2025-02-16T00:14:14.986Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestureHandlerCommon.ts
2025-02-16T00:14:14.987Z - Transforming: node_modules/react-native-gesture-handler/src/components/GestureHandlerRootView.tsx
2025-02-16T00:14:15.016Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/TapGestureHandler.ts
2025-02-16T00:14:15.026Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/PinchGestureHandler.ts
2025-02-16T00:14:15.031Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/RotationGestureHandler.ts
2025-02-16T00:14:15.036Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/ForceTouchGestureHandler.ts
2025-02-16T00:14:15.039Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/LongPressGestureHandler.ts
2025-02-16T00:14:15.046Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/createNativeWrapper.tsx
2025-02-16T00:14:15.051Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/PanGestureHandler.ts
2025-02-16T00:14:15.083Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/FlingGestureHandler.ts
2025-02-16T00:14:15.090Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/index.tsx
2025-02-16T00:14:15.095Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/NativeViewGestureHandler.ts
2025-02-16T00:14:15.106Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/gestureObjects.ts
2025-02-16T00:14:15.110Z - Transforming: node_modules/react-native-gesture-handler/src/components/GestureButtons.tsx
2025-02-16T00:14:15.144Z - Transforming: node_modules/react-native-gesture-handler/src/components/touchables/index.ts
2025-02-16T00:14:15.149Z - Transforming: node_modules/react-native-gesture-handler/src/components/GestureComponents.tsx
2025-02-16T00:14:15.155Z - Transforming: node_modules/react-native-gesture-handler/src/components/DrawerLayout.tsx
2025-02-16T00:14:15.187Z - Transforming: node_modules/react-native-gesture-handler/src/components/Swipeable.tsx
2025-02-16T00:14:15.209Z - Transforming: node_modules/react-native-gesture-handler/src/components/Pressable/index.ts
2025-02-16T00:14:15.219Z - Transforming: node_modules/react-native-gesture-handler/src/EnableNewWebImplementation.ts
2025-02-16T00:14:15.228Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/hoverGesture.ts
2025-02-16T00:14:15.234Z - Transforming: node_modules/@babel/runtime/helpers/slicedToArray.js
2025-02-16T00:14:15.245Z - Transforming: src/navigation/authStack.js
2025-02-16T00:14:15.270Z - Transforming: src/navigation/userStack.js
2025-02-16T00:14:15.277Z - Transforming: node_modules/react-native-paper/src/index.tsx
2025-02-16T00:14:15.326Z - Transforming: src/features/FoodDiary/context/FoodLogContext.js
2025-02-16T00:14:15.351Z - Transforming: src/features/StepsLog/context/StepsLogContext.js
2025-02-16T00:14:15.373Z - Transforming: src/features/FoodMenu/context/FoodMenuContext.js
2025-02-16T00:14:15.395Z - Transforming: src/features/NutritionalProgram/context/NutritionProgramContext.js
2025-02-16T00:14:15.425Z - Transforming: src/features/SocialMedia/context/FriendManagementContext.js
2025-02-16T00:14:15.435Z - Transforming: themes.js
2025-02-16T00:14:15.485Z - Transforming: node_modules/intl/lib/core.js
2025-02-16T00:14:15.526Z - Transforming: node_modules/expo/src/Expo.ts
2025-02-16T00:14:15.532Z - Transforming: node_modules/expo-notifications/build/getExpoPushTokenAsync.js
2025-02-16T00:14:15.554Z - Transforming: node_modules/expo-notifications/build/unregisterForNotificationsAsync.js
2025-02-16T00:14:15.562Z - Transforming: node_modules/expo-notifications/build/getDevicePushTokenAsync.js
2025-02-16T00:14:15.568Z - Transforming: node_modules/expo-notifications/build/presentNotificationAsync.js
2025-02-16T00:14:15.574Z - Transforming: node_modules/expo-notifications/build/getPresentedNotificationsAsync.js
2025-02-16T00:14:15.583Z - Transforming: node_modules/expo-notifications/build/dismissNotificationAsync.js
2025-02-16T00:14:15.596Z - Transforming: node_modules/expo-notifications/build/dismissAllNotificationsAsync.js
2025-02-16T00:14:15.598Z - Transforming: node_modules/expo-notifications/build/getNotificationChannelAsync.js
2025-02-16T00:14:15.602Z - Transforming: node_modules/expo-notifications/build/deleteNotificationChannelAsync.js
2025-02-16T00:14:15.603Z - Transforming: node_modules/expo-notifications/build/getNotificationChannelsAsync.js
2025-02-16T00:14:15.608Z - Transforming: node_modules/expo-notifications/build/setNotificationChannelAsync.js
2025-02-16T00:14:15.609Z - Transforming: node_modules/expo-notifications/build/getNotificationChannelGroupsAsync.js
2025-02-16T00:14:15.613Z - Transforming: node_modules/expo-notifications/build/setNotificationChannelGroupAsync.js
2025-02-16T00:14:15.614Z - Transforming: node_modules/expo-notifications/build/deleteNotificationChannelGroupAsync.js
2025-02-16T00:14:15.618Z - Transforming: node_modules/expo-notifications/build/setBadgeCountAsync.js
2025-02-16T00:14:15.618Z - Transforming: node_modules/expo-notifications/build/getNotificationChannelGroupAsync.js
2025-02-16T00:14:15.623Z - Transforming: node_modules/expo-notifications/build/getAllScheduledNotificationsAsync.js
2025-02-16T00:14:15.623Z - Transforming: node_modules/expo-notifications/build/scheduleNotificationAsync.js
2025-02-16T00:14:15.628Z - Transforming: node_modules/expo-notifications/build/getBadgeCountAsync.js
2025-02-16T00:14:15.633Z - Transforming: node_modules/expo-notifications/build/cancelScheduledNotificationAsync.js
2025-02-16T00:14:15.638Z - Transforming: node_modules/expo-notifications/build/getNotificationCategoriesAsync.js
2025-02-16T00:14:15.644Z - Transforming: node_modules/expo-notifications/build/cancelAllScheduledNotificationsAsync.js
2025-02-16T00:14:15.648Z - Transforming: node_modules/expo-notifications/build/setNotificationCategoryAsync.js
2025-02-16T00:14:15.650Z - Transforming: node_modules/expo-notifications/build/useLastNotificationResponse.js
2025-02-16T00:14:15.653Z - Transforming: node_modules/expo-notifications/build/DevicePushTokenAutoRegistration.fx.js
2025-02-16T00:14:15.658Z - Transforming: node_modules/expo-notifications/build/deleteNotificationCategoryAsync.js
2025-02-16T00:14:15.663Z - Transforming: node_modules/expo-notifications/build/registerTaskAsync.js
2025-02-16T00:14:15.668Z - Transforming: node_modules/expo-notifications/build/unregisterTaskAsync.js
2025-02-16T00:14:15.670Z - Transforming: node_modules/expo-notifications/build/getNextTriggerDateAsync.js
2025-02-16T00:14:15.674Z - Transforming: node_modules/expo-notifications/build/TokenEmitter.js
2025-02-16T00:14:15.676Z - Transforming: node_modules/expo-notifications/build/NotificationsHandler.js
2025-02-16T00:14:15.682Z - Transforming: node_modules/expo-notifications/build/NotificationChannelGroupManager.types.js
2025-02-16T00:14:15.686Z - Transforming: node_modules/expo-notifications/build/NotificationChannelManager.types.js
2025-02-16T00:14:15.687Z - Transforming: node_modules/expo-notifications/build/NotificationPermissions.js
2025-02-16T00:14:15.694Z - Transforming: node_modules/expo-notifications/build/NotificationsEmitter.js
2025-02-16T00:14:15.694Z - Transforming: node_modules/expo-notifications/build/NotificationPermissions.types.js
2025-02-16T00:14:15.699Z - Transforming: node_modules/expo-notifications/build/Notifications.types.js
2025-02-16T00:14:15.705Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/index.ts
2025-02-16T00:14:15.706Z - Transforming: node_modules/@react-navigation/native/src/index.tsx
2025-02-16T00:14:15.707Z - Transforming: node_modules/expo-notifications/build/Tokens.types.js
2025-02-16T00:14:15.711Z - Transforming: node_modules/react/jsx-runtime.js
2025-02-16T00:14:15.711Z - Transforming: node_modules/react-native-css-interop/dist/runtime/wrap-jsx.js
2025-02-16T00:14:15.716Z - Transforming: src/utils/registerForPushNotificationsAsync.js
2025-02-16T00:14:15.735Z - Transforming: node_modules/@babel/runtime/helpers/defineProperty.js
2025-02-16T00:14:15.739Z - Transforming: node_modules/expo-modules-core/src/index.ts
2025-02-16T00:14:15.743Z - Transforming: node_modules/expo-keep-awake/build/ExpoKeepAwake.js
2025-02-16T00:14:15.749Z - Transforming: node_modules/expo-keep-awake/build/KeepAwake.types.js
2025-02-16T00:14:15.754Z - Transforming: node_modules/expo-modules-core/src/LegacyEventEmitter.ts
2025-02-16T00:14:15.758Z - Transforming: node_modules/expo/src/environment/DevLoadingViewNativeModule.native.ts
2025-02-16T00:14:15.761Z - Transforming: node_modules/expo/src/environment/getInitialSafeArea.native.ts
2025-02-16T00:14:15.764Z - Transforming: node_modules/@react-native-async-storage/async-storage/src/AsyncStorage.native.ts
2025-02-16T00:14:15.766Z - Transforming: node_modules/@react-native-async-storage/async-storage/src/hooks.ts
2025-02-16T00:14:15.773Z - Transforming: node_modules/firebase/node_modules/@firebase/analytics/dist/esm/index.esm2017.js
2025-02-16T00:14:15.779Z - Transforming: node_modules/@firebase/app/dist/esm/index.esm2017.js
2025-02-16T00:14:15.795Z - Transforming: node_modules/@firebase/firestore/dist/index.rn.js
2025-02-16T00:14:15.829Z - Transforming: node_modules/@firebase/auth/dist/rn/index.js
2025-02-16T00:14:15.884Z - Transforming: node_modules/@firebase/storage/dist/index.esm2017.js
2025-02-16T00:14:15.885Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/eventReceiver.ts
2025-02-16T00:14:15.934Z - Transforming: node_modules/react-native-gesture-handler/src/RNGestureHandlerModule.ts
2025-02-16T00:14:15.939Z - Transforming: node_modules/react-native-gesture-handler/src/utils.ts
2025-02-16T00:14:15.967Z - Transforming: node_modules/expo-constants/build/Constants.js
2025-02-16T00:14:15.988Z - Transforming: node_modules/@babel/runtime/helpers/toConsumableArray.js
2025-02-16T00:14:15.991Z - Transforming: node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js
2025-02-16T00:14:15.993Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/createHandler.tsx
2025-02-16T00:14:16.001Z - Transforming: node_modules/react-native-gesture-handler/src/GestureHandlerRootViewContext.ts
2025-02-16T00:14:16.008Z - Transforming: node_modules/@babel/runtime/helpers/objectWithoutProperties.js
2025-02-16T00:14:16.014Z - Transforming: node_modules/@babel/runtime/helpers/possibleConstructorReturn.js
2025-02-16T00:14:16.018Z - Transforming: node_modules/@babel/runtime/helpers/classCallCheck.js
2025-02-16T00:14:16.025Z - Transforming: node_modules/@babel/runtime/helpers/createClass.js
2025-02-16T00:14:16.026Z - Transforming: node_modules/@babel/runtime/helpers/getPrototypeOf.js
2025-02-16T00:14:16.030Z - Transforming: node_modules/@babel/runtime/helpers/inherits.js
2025-02-16T00:14:16.030Z - Transforming: node_modules/react-native-gesture-handler/src/PlatformConstants.ts
2025-02-16T00:14:16.035Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useAnimatedGesture.ts
2025-02-16T00:14:16.039Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/attachHandlers.ts
2025-02-16T00:14:16.054Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/needsToReattach.ts
2025-02-16T00:14:16.061Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/utils.ts
2025-02-16T00:14:16.081Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useDetectorUpdater.ts
2025-02-16T00:14:16.092Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/dropHandlers.ts
2025-02-16T00:14:16.097Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useViewRefHandler.ts
2025-02-16T00:14:16.102Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/Wrap.tsx
2025-02-16T00:14:16.112Z - Transforming: node_modules/react-native-gesture-handler/src/components/touchables/TouchableWithoutFeedback.tsx
2025-02-16T00:14:16.121Z - Transforming: node_modules/react-native-gesture-handler/src/components/touchables/TouchableOpacity.tsx
2025-02-16T00:14:16.147Z - Transforming: node_modules/react-native-gesture-handler/src/components/touchables/TouchableHighlight.tsx
2025-02-16T00:14:16.148Z - Transforming: node_modules/react-native-gesture-handler/src/components/touchables/TouchableNativeFeedback.tsx
2025-02-16T00:14:16.151Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/flingGesture.ts
2025-02-16T00:14:16.162Z - Transforming: node_modules/@babel/runtime/helpers/construct.js
2025-02-16T00:14:16.166Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/longPressGesture.ts
2025-02-16T00:14:16.176Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/gestureComposition.ts
2025-02-16T00:14:16.194Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/forceTouchGesture.ts
2025-02-16T00:14:16.197Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/panGesture.ts
2025-02-16T00:14:16.215Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/tapGesture.ts
2025-02-16T00:14:16.222Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/rotationGesture.ts
2025-02-16T00:14:16.223Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/nativeGesture.ts
2025-02-16T00:14:16.231Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/pinchGesture.ts
2025-02-16T00:14:16.233Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/manualGesture.ts
2025-02-16T00:14:16.249Z - Transforming: node_modules/react-native-gesture-handler/src/components/Pressable/Pressable.tsx
2025-02-16T00:14:16.251Z - Transforming: node_modules/expo/src/winter/index.ts
2025-02-16T00:14:16.254Z - Transforming: node_modules/expo/src/errors/ExpoErrorManager.native.ts
2025-02-16T00:14:16.261Z - Transforming: node_modules/expo/src/errors/AppEntryNotFound.tsx
2025-02-16T00:14:16.270Z - Transforming: node_modules/expo-asset/build/index.js
2025-02-16T00:14:16.275Z - Transforming: node_modules/@babel/runtime/helpers/iterableToArrayLimit.js
2025-02-16T00:14:16.279Z - Transforming: node_modules/@babel/runtime/helpers/arrayWithHoles.js
2025-02-16T00:14:16.282Z - Transforming: node_modules/@babel/runtime/helpers/nonIterableRest.js
2025-02-16T00:14:16.285Z - Transforming: node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js
2025-02-16T00:14:16.291Z - Transforming: node_modules/react-native-gesture-handler/src/components/GestureHandlerButton.tsx
2025-02-16T00:14:16.295Z - Transforming: node_modules/@react-navigation/stack/src/index.tsx
2025-02-16T00:14:16.297Z - Transforming: src/screens/Welcome.js
2025-02-16T00:14:16.308Z - Transforming: src/screens/Signup.js
2025-02-16T00:14:16.321Z - Transforming: src/screens/Signin.js
2025-02-16T00:14:16.424Z - Transforming: node_modules/react-native/Libraries/Utilities/warnOnce.js
2025-02-16T00:14:16.449Z - Transforming: node_modules/react-native/Libraries/Core/registerCallableModule.js
2025-02-16T00:14:16.454Z - Transforming: node_modules/react-native/Libraries/Components/AccessibilityInfo/AccessibilityInfo.js
2025-02-16T00:14:16.455Z - Transforming: node_modules/react-native/Libraries/Components/ActivityIndicator/ActivityIndicator.js
2025-02-16T00:14:16.482Z - Transforming: node_modules/invariant/browser.js
2025-02-16T00:14:16.488Z - Transforming: node_modules/react-native/Libraries/Components/DrawerAndroid/DrawerLayoutAndroid.js
2025-02-16T00:14:16.492Z - Transforming: node_modules/react-native/Libraries/Lists/FlatList.js
2025-02-16T00:14:16.496Z - Transforming: node_modules/react-native/Libraries/Image/Image.ios.js
2025-02-16T00:14:16.518Z - Transforming: node_modules/react-native/Libraries/Image/ImageBackground.js
2025-02-16T00:14:16.533Z - Transforming: node_modules/react-native/Libraries/Components/Keyboard/KeyboardAvoidingView.js
2025-02-16T00:14:16.540Z - Transforming: node_modules/react-native/Libraries/Components/Button.js
2025-02-16T00:14:16.583Z - Transforming: node_modules/react-native/Libraries/Modal/Modal.js
2025-02-16T00:14:16.617Z - Transforming: node_modules/react-native/Libraries/Components/ProgressBarAndroid/ProgressBarAndroid.js
2025-02-16T00:14:16.622Z - Transforming: node_modules/react-native/Libraries/Components/RefreshControl/RefreshControl.js
2025-02-16T00:14:16.632Z - Transforming: node_modules/react-native/Libraries/Components/SafeAreaView/SafeAreaView.js
2025-02-16T00:14:16.641Z - Transforming: node_modules/react-native/Libraries/Components/ScrollView/ScrollView.js
2025-02-16T00:14:16.653Z - Transforming: node_modules/react-native/Libraries/Lists/SectionList.js
2025-02-16T00:14:16.688Z - Transforming: node_modules/react-native/Libraries/Components/StatusBar/StatusBar.js
2025-02-16T00:14:16.699Z - Transforming: node_modules/react-native/Libraries/Components/Pressable/Pressable.js
2025-02-16T00:14:16.710Z - Transforming: node_modules/react-native/Libraries/Components/TextInput/TextInput.js
2025-02-16T00:14:16.740Z - Transforming: node_modules/react-native/Libraries/Components/TextInput/InputAccessoryView.js
2025-02-16T00:14:16.748Z - Transforming: node_modules/react-native/Libraries/Components/Switch/Switch.js
2025-02-16T00:14:16.751Z - Transforming: node_modules/react-native/Libraries/Text/Text.js
2025-02-16T00:14:16.785Z - Transforming: node_modules/react-native/Libraries/Components/Touchable/TouchableOpacity.js
2025-02-16T00:14:16.804Z - Transforming: node_modules/react-native/Libraries/Components/Touchable/Touchable.js
2025-02-16T00:14:16.829Z - Transforming: node_modules/react-native/Libraries/Components/View/View.js
2025-02-16T00:14:16.845Z - Transforming: node_modules/react-native/Libraries/Lists/VirtualizedList.js
2025-02-16T00:14:16.850Z - Transforming: node_modules/react-native/Libraries/Lists/VirtualizedSectionList.js
2025-02-16T00:14:16.853Z - Transforming: node_modules/react-native/Libraries/Components/Touchable/TouchableHighlight.js
2025-02-16T00:14:16.855Z - Transforming: node_modules/react-native/Libraries/ActionSheetIOS/ActionSheetIOS.js
2025-02-16T00:14:16.864Z - Transforming: node_modules/react-native/Libraries/Animated/Animated.js
2025-02-16T00:14:16.866Z - Transforming: node_modules/react-native/Libraries/Alert/Alert.js
2025-02-16T00:14:16.871Z - Transforming: node_modules/react-native/Libraries/Utilities/Appearance.js
2025-02-16T00:14:16.880Z - Transforming: node_modules/react-native/Libraries/ReactNative/AppRegistry.js
2025-02-16T00:14:16.896Z - Transforming: node_modules/react-native/Libraries/AppState/AppState.js
2025-02-16T00:14:16.906Z - Transforming: node_modules/react-native/Libraries/Components/Clipboard/Clipboard.js
2025-02-16T00:14:16.910Z - Transforming: node_modules/react-native/Libraries/Utilities/BackHandler.ios.js
2025-02-16T00:14:16.915Z - Transforming: node_modules/react-native/Libraries/Utilities/DevSettings.js
2025-02-16T00:14:16.918Z - Transforming: node_modules/react-native/Libraries/Utilities/Dimensions.js
2025-02-16T00:14:16.927Z - Transforming: node_modules/react-native/Libraries/ReactNative/RendererProxy.js
2025-02-16T00:14:16.927Z - Transforming: node_modules/react-native/Libraries/Animated/Easing.js
2025-02-16T00:14:16.927Z - Transforming: node_modules/react-native/Libraries/ReactNative/I18nManager.js
2025-02-16T00:14:16.934Z - Transforming: node_modules/react-native/Libraries/Components/Touchable/TouchableWithoutFeedback.js
2025-02-16T00:14:16.938Z - Transforming: node_modules/react-native/Libraries/Components/Keyboard/Keyboard.js
2025-02-16T00:14:16.939Z - Transforming: node_modules/react-native/Libraries/Interaction/InteractionManager.js
2025-02-16T00:14:16.949Z - Transforming: node_modules/react-native/Libraries/Linking/Linking.js
2025-02-16T00:14:16.961Z - Transforming: node_modules/react-native/Libraries/LogBox/LogBox.js
2025-02-16T00:14:16.970Z - Transforming: node_modules/react-native/Libraries/Utilities/DeviceInfo.js
2025-02-16T00:14:16.975Z - Transforming: node_modules/react-native/Libraries/NativeModules/specs/NativeDialogManagerAndroid.js
2025-02-16T00:14:16.976Z - Transforming: node_modules/react-native/Libraries/EventEmitter/NativeEventEmitter.js
2025-02-16T00:14:16.988Z - Transforming: node_modules/react-native/Libraries/Network/RCTNetworking.ios.js
2025-02-16T00:14:16.989Z - Transforming: node_modules/react-native/Libraries/Interaction/PanResponder.js
2025-02-16T00:14:17.005Z - Transforming: node_modules/react-native/Libraries/LayoutAnimation/LayoutAnimation.js
2025-02-16T00:14:17.010Z - Transforming: node_modules/react-native/Libraries/PushNotificationIOS/PushNotificationIOS.js
2025-02-16T00:14:17.021Z - Transforming: node_modules/react-native/Libraries/Settings/Settings.ios.js
2025-02-16T00:14:17.024Z - Transforming: node_modules/react-native/Libraries/StyleSheet/StyleSheet.js
2025-02-16T00:14:17.029Z - Transforming: node_modules/react-native/Libraries/Share/Share.js
2025-02-16T00:14:17.033Z - Transforming: node_modules/react-native/Libraries/Performance/Systrace.js
2025-02-16T00:14:17.037Z - Transforming: node_modules/react-native/Libraries/PermissionsAndroid/PermissionsAndroid.js
2025-02-16T00:14:17.041Z - Transforming: node_modules/react-native/Libraries/TurboModule/TurboModuleRegistry.js
2025-02-16T00:14:17.049Z - Transforming: node_modules/react-native/Libraries/ReactNative/UIManager.js
2025-02-16T00:14:17.050Z - Transforming: node_modules/react-native/Libraries/Animated/useAnimatedValue.js
2025-02-16T00:14:17.055Z - Transforming: node_modules/react-native/Libraries/Utilities/PixelRatio.js
2025-02-16T00:14:17.057Z - Transforming: node_modules/react-native/Libraries/Utilities/useWindowDimensions.js
2025-02-16T00:14:17.059Z - Transforming: node_modules/react-native/Libraries/Components/ToastAndroid/ToastAndroid.js
2025-02-16T00:14:17.062Z - Transforming: node_modules/react-native/Libraries/Vibration/Vibration.js
2025-02-16T00:14:17.064Z - Transforming: node_modules/react-native/Libraries/UTFSequence.js
2025-02-16T00:14:17.066Z - Transforming: node_modules/react-native/Libraries/Utilities/useColorScheme.js
2025-02-16T00:14:17.068Z - Transforming: node_modules/react-native/Libraries/Components/Touchable/TouchableNativeFeedback.js
2025-02-16T00:14:17.071Z - Transforming: node_modules/react-native/Libraries/YellowBox/YellowBoxDeprecated.js
2025-02-16T00:14:17.075Z - Transforming: node_modules/react-native/Libraries/EventEmitter/RCTDeviceEventEmitter.js
2025-02-16T00:14:17.076Z - Transforming: node_modules/react-native/Libraries/StyleSheet/PlatformColorValueTypesIOS.ios.js
2025-02-16T00:14:17.092Z - Transforming: node_modules/react-native/Libraries/EventEmitter/RCTNativeAppEventEmitter.js
2025-02-16T00:14:17.092Z - Transforming: node_modules/react-native/Libraries/BatchedBridge/NativeModules.js
2025-02-16T00:14:17.095Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processColor.js
2025-02-16T00:14:17.100Z - Transforming: node_modules/react-native/Libraries/ReactNative/requireNativeComponent.js
2025-02-16T00:14:17.106Z - Transforming: node_modules/react-native/Libraries/StyleSheet/PlatformColorValueTypes.ios.js
2025-02-16T00:14:17.107Z - Transforming: node_modules/react-native/Libraries/ReactNative/RootTag.js
2025-02-16T00:14:17.113Z - Transforming: node_modules/react-native/Libraries/Utilities/Platform.ios.js
2025-02-16T00:14:17.114Z - Transforming: node_modules/@babel/runtime/helpers/get.js
2025-02-16T00:14:17.116Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/gesture.ts
2025-02-16T00:14:17.119Z - Transforming: node_modules/@react-navigation/material-top-tabs/src/index.tsx
2025-02-16T00:14:17.121Z - Transforming: src/screens/SplashScreen.js
2025-02-16T00:14:17.124Z - Transforming: src/features/AIAgent/screens/NutraCompassAIScreen.js
2025-02-16T00:14:17.128Z - Transforming: src/screens/Dashboard.js
2025-02-16T00:14:17.160Z - Transforming: src/screens/WorkoutDiary.js
2025-02-16T00:14:17.173Z - Transforming: src/screens/Goals.js
2025-02-16T00:14:17.176Z - Transforming: src/screens/FoodDiary.js
2025-02-16T00:14:17.192Z - Transforming: src/features/ThemeChanger/screens/ThemesScreen.js
2025-02-16T00:14:17.196Z - Transforming: src/screens/Settings.js
2025-02-16T00:14:17.202Z - Transforming: src/features/FoodMenu/screens/CustomMealsScreen.js
2025-02-16T00:14:17.209Z - Transforming: src/features/SocialMedia/MyProfile/screens/MyProfileScreen.js
2025-02-16T00:14:17.211Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/index.tsx
2025-02-16T00:14:17.225Z - Transforming: src/features/SocialMedia/MyAccomplishments/screens/MyAccomplishmentsScreen.js
2025-02-16T00:14:17.232Z - Transforming: node_modules/@react-navigation/drawer/src/index.tsx
2025-02-16T00:14:17.242Z - Transforming: src/screens/Foods.js
2025-02-16T00:14:17.300Z - Transforming: src/features/SocialMedia/Messages/screens/ChatScreen.js
2025-02-16T00:14:17.313Z - Transforming: src/features/SocialMedia/Messages/screens/SelectFriend.js
2025-02-16T00:14:17.337Z - Transforming: src/features/SocialMedia/MyLibrary/screens/MyLibraryScreen.js
2025-02-16T00:14:17.339Z - Transforming: src/features/SocialMedia/Messages/screens/TextScreen.js
2025-02-16T00:14:17.350Z - Transforming: src/features/SocialMedia/Messages/screens/AddFriend.js
2025-02-16T00:14:17.377Z - Transforming: src/navigation/components/CustomTopTabBar.js
2025-02-16T00:14:17.383Z - Transforming: src/navigation/components/CustomSocialBottomTabBar.js
2025-02-16T00:14:17.405Z - Transforming: src/navigation/components/CustomDrawerContent.js
2025-02-16T00:14:17.408Z - Transforming: src/features/SocialMedia/SocialSettings/screens/SocialSettingsScreen.js
2025-02-16T00:14:17.421Z - Transforming: src/features/Settings/screens/NotificationSettings.js
2025-02-16T00:14:17.432Z - Transforming: src/features/Settings/screens/NutritionSettings.js
2025-02-16T00:14:17.440Z - Transforming: src/features/Settings/screens/AccountSettings.js
2025-02-16T00:14:17.451Z - Transforming: src/features/NutritionalProgram/screens/NutritionalProgramScreen.js
2025-02-16T00:14:17.482Z - Transforming: src/features/SocialMedia/MarketPlace/screens/MarketPlaceScreen.js
2025-02-16T00:14:17.484Z - Transforming: src/navigation/components/CustomPersonalBottomTabBar.js
2025-02-16T00:14:17.491Z - Transforming: src/features/Settings/screens/ProfileSettings.js
2025-02-16T00:14:17.497Z - Transforming: node_modules/react-native-paper/src/styles/themes/v3/tokens.tsx
2025-02-16T00:14:17.513Z - Transforming: node_modules/react-native-paper/src/core/theming.tsx
2025-02-16T00:14:17.527Z - Transforming: node_modules/react-native-paper/src/styles/themes/index.ts
2025-02-16T00:14:17.532Z - Transforming: node_modules/react-native-paper/src/core/PaperProvider.tsx
2025-02-16T00:14:17.538Z - Transforming: node_modules/react-native-paper/src/styles/shadow.tsx
2025-02-16T00:14:17.547Z - Transforming: node_modules/react-native-paper/src/styles/fonts.tsx
2025-02-16T00:14:17.556Z - Transforming: node_modules/react-native-paper/src/components/Avatar/Avatar.tsx
2025-02-16T00:14:17.558Z - Transforming: node_modules/react-native-paper/src/components/Drawer/Drawer.tsx
2025-02-16T00:14:17.562Z - Transforming: node_modules/react-native-paper/src/components/List/List.tsx
2025-02-16T00:14:17.565Z - Transforming: node_modules/react-native-paper/src/styles/themes/v2/colors.tsx
2025-02-16T00:14:17.567Z - Transforming: node_modules/react-native-paper/src/styles/overlay.tsx
2025-02-16T00:14:17.570Z - Transforming: node_modules/react-native-paper/src/components/FAB/AnimatedFAB.tsx
2025-02-16T00:14:17.579Z - Transforming: node_modules/react-native-paper/src/components/Badge.tsx
2025-02-16T00:14:17.579Z - Transforming: node_modules/react-native-paper/src/components/ActivityIndicator.tsx
2025-02-16T00:14:17.599Z - Transforming: node_modules/react-native-paper/src/components/Banner.tsx
2025-02-16T00:14:17.622Z - Transforming: node_modules/react-native-paper/src/components/BottomNavigation/BottomNavigation.tsx
2025-02-16T00:14:17.630Z - Transforming: node_modules/react-native-paper/src/components/Button/Button.tsx
2025-02-16T00:14:17.642Z - Transforming: node_modules/react-native-paper/src/components/Card/Card.tsx
2025-02-16T00:14:17.678Z - Transforming: node_modules/react-native-paper/src/components/Checkbox/index.ts
2025-02-16T00:14:17.684Z - Transforming: node_modules/react-native-paper/src/components/DataTable/DataTable.tsx
2025-02-16T00:14:17.690Z - Transforming: node_modules/react-native-paper/src/components/Dialog/Dialog.tsx
2025-02-16T00:14:17.695Z - Transforming: node_modules/react-native-paper/src/components/Divider.tsx
2025-02-16T00:14:17.710Z - Transforming: node_modules/react-native-paper/src/components/FAB/index.ts
2025-02-16T00:14:17.713Z - Transforming: node_modules/react-native-paper/src/components/HelperText/HelperText.tsx
2025-02-16T00:14:17.719Z - Transforming: node_modules/react-native-paper/src/components/Icon.tsx
2025-02-16T00:14:17.734Z - Transforming: node_modules/react-native-paper/src/components/Menu/Menu.tsx
2025-02-16T00:14:17.735Z - Transforming: node_modules/react-native-paper/src/components/IconButton/IconButton.tsx
2025-02-16T00:14:17.744Z - Transforming: node_modules/react-native-paper/src/components/Portal/Portal.tsx
2025-02-16T00:14:17.751Z - Transforming: node_modules/react-native-paper/src/components/ProgressBar.tsx
2025-02-16T00:14:17.758Z - Transforming: node_modules/react-native-paper/src/components/RadioButton/index.ts
2025-02-16T00:14:17.764Z - Transforming: node_modules/react-native-paper/src/components/Searchbar.tsx
2025-02-16T00:14:17.782Z - Transforming: node_modules/react-native-paper/src/components/Snackbar.tsx
2025-02-16T00:14:17.794Z - Transforming: node_modules/react-native-paper/src/components/Chip/Chip.tsx
2025-02-16T00:14:17.797Z - Transforming: node_modules/react-native-paper/src/components/Surface.tsx
2025-02-16T00:14:17.810Z - Transforming: node_modules/react-native-paper/src/components/Appbar/index.ts
2025-02-16T00:14:17.815Z - Transforming: node_modules/react-native-paper/src/components/TouchableRipple/TouchableRipple.native.tsx
2025-02-16T00:14:17.833Z - Transforming: node_modules/react-native-paper/src/components/TextInput/TextInput.tsx
2025-02-16T00:14:17.849Z - Transforming: node_modules/react-native-paper/src/components/ToggleButton/index.ts
2025-02-16T00:14:17.851Z - Transforming: node_modules/react-native-paper/src/components/SegmentedButtons/SegmentedButtons.tsx
2025-02-16T00:14:17.853Z - Transforming: node_modules/react-native-paper/src/components/Typography/v2/index.ts
2025-02-16T00:14:17.859Z - Transforming: node_modules/react-native-paper/src/components/Typography/Text.tsx
2025-02-16T00:14:17.862Z - Transforming: node_modules/react-native-paper/src/components/Tooltip/Tooltip.tsx
2025-02-16T00:14:17.878Z - Transforming: node_modules/react-native-paper/src/components/Switch/Switch.tsx
2025-02-16T00:14:17.888Z - Transforming: node_modules/react-native-paper/src/components/Modal.tsx
2025-02-16T00:14:17.890Z - Transforming: node_modules/expo-sensors/build/index.js
2025-02-16T00:14:17.895Z - Transforming: node_modules/axios/index.js
2025-02-16T00:14:17.903Z - Transforming: node_modules/react-native-uuid/dist/index.js
2025-02-16T00:14:17.907Z - Transforming: node_modules/expo/src/hooks/useEvent.ts
2025-02-16T00:14:17.908Z - Transforming: node_modules/expo-notifications/build/PushTokenManager.native.js
2025-02-16T00:14:17.912Z - Transforming: node_modules/expo-notifications/build/warnOfExpoGoPushUsage.js
2025-02-16T00:14:17.918Z - Transforming: node_modules/expo-notifications/build/NotificationPresenterModule.native.js
2025-02-16T00:14:17.922Z - Transforming: node_modules/expo-notifications/build/utils/mapNotificationResponse.js
2025-02-16T00:14:17.923Z - Transforming: node_modules/expo-notifications/build/ServerRegistrationModule.native.js
2025-02-16T00:14:17.931Z - Transforming: node_modules/expo-application/build/Application.js
2025-02-16T00:14:17.938Z - Transforming: node_modules/expo-notifications/build/NotificationScheduler.native.js
2025-02-16T00:14:17.947Z - Transforming: node_modules/expo-notifications/build/BadgeModule.native.js
2025-02-16T00:14:17.947Z - Transforming: node_modules/expo-notifications/build/NotificationCategoriesModule.native.js
2025-02-16T00:14:17.948Z - Transforming: node_modules/abort-controller/polyfill.mjs
2025-02-16T00:14:17.959Z - Transforming: node_modules/expo-notifications/build/BackgroundNotificationTasksModule.native.js
2025-02-16T00:14:17.959Z - Transforming: node_modules/expo-contacts/build/Contacts.js
2025-02-16T00:14:17.968Z - Transforming: node_modules/expo-notifications/build/NotificationPermissionsModule.native.js
2025-02-16T00:14:17.972Z - Transforming: node_modules/expo-notifications/build/NotificationsHandlerModule.native.js
2025-02-16T00:14:17.976Z - Transforming: node_modules/expo-notifications/build/NotificationsEmitterModule.native.js
2025-02-16T00:14:17.981Z - Transforming: node_modules/expo-notifications/build/utils/updateDevicePushTokenAsync.js
2025-02-16T00:14:17.982Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/signIn/GoogleSignin.ts
2025-02-16T00:14:17.982Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/buttons/GoogleSigninButton.tsx
2025-02-16T00:14:17.985Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/errors/errorCodes.ts
2025-02-16T00:14:17.998Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/types.ts
2025-02-16T00:14:17.999Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/functions.ts
2025-02-16T00:14:18.002Z - Transforming: node_modules/react-native-css-interop/dist/runtime/api.native.js
2025-02-16T00:14:18.003Z - Transforming: node_modules/react-native-css-interop/dist/runtime/third-party-libs/react-native-safe-area-context.native.js
2025-02-16T00:14:18.006Z - Transforming: node_modules/react-native-css-interop/dist/runtime/components.js
2025-02-16T00:14:18.009Z - Transforming: node_modules/@babel/runtime/helpers/toPropertyKey.js
2025-02-16T00:14:18.011Z - Transforming: node_modules/@react-navigation/native/src/Link.tsx
2025-02-16T00:14:18.016Z - Transforming: node_modules/@react-navigation/native/src/LinkingContext.tsx
2025-02-16T00:14:18.016Z - Transforming: node_modules/expo-device/build/Device.js
2025-02-16T00:14:18.016Z - Transforming: node_modules/@react-navigation/native/src/NavigationContainer.tsx
2025-02-16T00:14:18.023Z - Transforming: node_modules/@react-navigation/native/src/ServerContainer.tsx
2025-02-16T00:14:18.025Z - Transforming: node_modules/@react-navigation/native/src/theming/DarkTheme.tsx
2025-02-16T00:14:18.029Z - Transforming: node_modules/@react-navigation/native/src/theming/DefaultTheme.tsx
2025-02-16T00:14:18.033Z - Transforming: node_modules/@react-navigation/native/src/theming/useTheme.tsx
2025-02-16T00:14:18.033Z - Transforming: node_modules/@react-navigation/native/src/theming/ThemeProvider.tsx
2025-02-16T00:14:18.036Z - Transforming: node_modules/@react-navigation/native/src/types.tsx
2025-02-16T00:14:18.041Z - Transforming: node_modules/@react-navigation/native/src/useLinkProps.tsx
2025-02-16T00:14:18.041Z - Transforming: node_modules/@react-navigation/native/src/useLinkTo.tsx
2025-02-16T00:14:18.042Z - Transforming: node_modules/@react-navigation/native/src/useLinkBuilder.tsx
2025-02-16T00:14:18.055Z - Transforming: node_modules/@react-navigation/native/src/useScrollToTop.tsx
2025-02-16T00:14:18.066Z - Transforming: node_modules/@react-navigation/core/src/index.tsx
2025-02-16T00:14:18.068Z - Transforming: node_modules/react/cjs/react-jsx-runtime.development.js
2025-02-16T00:14:18.082Z - Transforming: node_modules/dayjs/dayjs.min.js
2025-02-16T00:14:18.091Z - Transforming: node_modules/expo-modules-core/src/EventEmitter.ts
2025-02-16T00:14:18.095Z - Transforming: node_modules/expo-modules-core/src/NativeModule.ts
2025-02-16T00:14:18.097Z - Transforming: node_modules/expo-modules-core/src/NativeViewManagerAdapter.native.tsx
2025-02-16T00:14:18.097Z - Transforming: node_modules/expo-modules-core/src/NativeModulesProxy.native.ts
2025-02-16T00:14:18.103Z - Transforming: node_modules/expo-modules-core/src/SharedObject.ts
2025-02-16T00:14:18.107Z - Transforming: node_modules/expo-modules-core/src/SharedRef.ts
2025-02-16T00:14:18.107Z - Transforming: node_modules/expo-modules-core/src/errors/UnavailabilityError.ts
2025-02-16T00:14:18.113Z - Transforming: node_modules/expo-modules-core/src/errors/CodedError.ts
2025-02-16T00:14:18.115Z - Transforming: node_modules/expo-modules-core/src/Platform.ts
2025-02-16T00:14:18.119Z - Transforming: node_modules/expo-modules-core/src/sweet/setUpErrorManager.fx.ts
2025-02-16T00:14:18.120Z - Transforming: node_modules/expo-modules-core/src/web/index.ts
2025-02-16T00:14:18.121Z - Transforming: node_modules/expo-modules-core/src/requireNativeModule.ts
2025-02-16T00:14:18.126Z - Transforming: node_modules/expo-modules-core/src/registerWebModule.ts
2025-02-16T00:14:18.126Z - Transforming: node_modules/expo-modules-core/src/uuid/index.ts
2025-02-16T00:14:18.127Z - Transforming: node_modules/expo-modules-core/src/TypedArrays.types.ts
2025-02-16T00:14:18.130Z - Transforming: node_modules/expo-modules-core/src/PermissionsInterface.ts
2025-02-16T00:14:18.131Z - Transforming: node_modules/expo-modules-core/src/PermissionsHook.ts
2025-02-16T00:14:18.131Z - Transforming: node_modules/expo-modules-core/src/Refs.ts
2025-02-16T00:14:18.134Z - Transforming: node_modules/expo-modules-core/src/hooks/useReleasingSharedObject.ts
2025-02-16T00:14:18.134Z - Transforming: node_modules/expo-modules-core/src/reload.ts
2025-02-16T00:14:18.139Z - Transforming: node_modules/@react-native-async-storage/async-storage/src/helpers.ts
2025-02-16T00:14:18.144Z - Transforming: node_modules/@react-native-async-storage/async-storage/src/RCTAsyncStorage.ts
2025-02-16T00:14:18.147Z - Transforming: node_modules/@firebase/auth/dist/rn/index-2f66320e.js
2025-02-16T00:14:18.148Z - Transforming: node_modules/tslib/tslib.js
2025-02-16T00:14:18.171Z - Transforming: node_modules/@firebase/util/dist/index.esm2017.js
2025-02-16T00:14:18.194Z - Transforming: node_modules/@firebase/auth/node_modules/@firebase/component/dist/esm/index.esm2017.js
2025-02-16T00:14:18.201Z - Transforming: node_modules/@firebase/logger/dist/index.cjs.js
2025-02-16T00:14:18.227Z - Transforming: node_modules/react-native-gesture-handler/src/TouchEventType.ts
2025-02-16T00:14:18.230Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/handlersRegistry.ts
2025-02-16T00:14:18.237Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/gestureStateManager.ts
2025-02-16T00:14:18.257Z - Transforming: node_modules/react-native-gesture-handler/src/specs/NativeRNGestureHandlerModule.ts
2025-02-16T00:14:18.260Z - Transforming: node_modules/expo-constants/build/Constants.types.js
2025-02-16T00:14:18.261Z - Transforming: node_modules/expo-constants/build/ExponentConstants.js
2025-02-16T00:14:18.265Z - Transforming: node_modules/@babel/runtime/helpers/arrayWithoutHoles.js
2025-02-16T00:14:18.266Z - Transforming: node_modules/@babel/runtime/helpers/iterableToArray.js
2025-02-16T00:14:18.268Z - Transforming: node_modules/@babel/runtime/helpers/nonIterableSpread.js
2025-02-16T00:14:18.270Z - Transforming: node_modules/react-is/index.js
2025-02-16T00:14:18.270Z - Transforming: node_modules/@firebase/app/node_modules/@firebase/component/dist/esm/index.esm2017.js
2025-02-16T00:14:18.273Z - Transforming: node_modules/idb/build/index.cjs
2025-02-16T00:14:18.287Z - Transforming: node_modules/@babel/runtime/helpers/assertThisInitialized.js
2025-02-16T00:14:18.291Z - Transforming: node_modules/@babel/runtime/helpers/typeof.js
2025-02-16T00:14:18.291Z - Transforming: node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js
2025-02-16T00:14:18.295Z - Transforming: node_modules/@babel/runtime/helpers/setPrototypeOf.js
2025-02-16T00:14:18.295Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/utils.ts
2025-02-16T00:14:18.304Z - Transforming: node_modules/react-native-gesture-handler/src/ActionType.ts
2025-02-16T00:14:18.304Z - Transforming: node_modules/react-native-gesture-handler/src/ghQueueMicrotask.ts
2025-02-16T00:14:18.313Z - Transforming: node_modules/firebase/node_modules/@firebase/component/dist/esm/index.esm2017.js
2025-02-16T00:14:18.313Z - Transforming: node_modules/firebase/node_modules/@firebase/installations/dist/esm/index.esm2017.js
2025-02-16T00:14:18.348Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/updateHandlers.ts
2025-02-16T00:14:18.359Z - Transforming: node_modules/react-native-gesture-handler/src/getShadowNodeFromRef.ts
2025-02-16T00:14:18.363Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/gestures/reanimatedWrapper.ts
2025-02-16T00:14:18.375Z - Transforming: node_modules/react-native-gesture-handler/src/components/touchables/GenericTouchable.tsx
2025-02-16T00:14:18.392Z - Transforming: node_modules/react-native-gesture-handler/src/getReactNativeVersion.ts
2025-02-16T00:14:18.395Z - Transforming: node_modules/react-native-gesture-handler/src/RNRenderer.ts
2025-02-16T00:14:18.401Z - Transforming: node_modules/@babel/runtime/helpers/isNativeReflectConstruct.js
2025-02-16T00:14:18.402Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/customDirectEventTypes.ts
2025-02-16T00:14:18.403Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/getNextHandlerTag.ts
2025-02-16T00:14:18.405Z - Transforming: node_modules/react-native-gesture-handler/src/handlers/PressabilityDebugView.tsx
2025-02-16T00:14:18.405Z - Transforming: node_modules/expo/src/winter/runtime.native.ts
2025-02-16T00:14:18.406Z - Transforming: node_modules/expo-asset/build/Asset.fx.js
2025-02-16T00:14:18.408Z - Transforming: node_modules/expo-asset/build/Asset.js
2025-02-16T00:14:18.410Z - Transforming: node_modules/expo-asset/build/AssetHooks.js
2025-02-16T00:14:18.411Z - Transforming: node_modules/@babel/runtime/helpers/arrayLikeToArray.js
2025-02-16T00:14:18.414Z - Transforming: node_modules/react-native-gesture-handler/src/specs/RNGestureHandlerButtonNativeComponent.ts
2025-02-16T00:14:18.416Z - Transforming: node_modules/@react-navigation/stack/src/TransitionConfigs/CardStyleInterpolators.tsx
2025-02-16T00:14:18.441Z - Transforming: node_modules/@react-navigation/stack/src/TransitionConfigs/HeaderStyleInterpolators.tsx
2025-02-16T00:14:18.467Z - Transforming: node_modules/@react-navigation/stack/src/TransitionConfigs/TransitionPresets.tsx
2025-02-16T00:14:18.467Z - Transforming: node_modules/@react-navigation/stack/src/TransitionConfigs/TransitionSpecs.tsx
2025-02-16T00:14:18.471Z - Transforming: node_modules/@react-navigation/stack/src/navigators/createStackNavigator.tsx
2025-02-16T00:14:18.474Z - Transforming: node_modules/@react-navigation/stack/src/views/Header/Header.tsx
2025-02-16T00:14:18.482Z - Transforming: node_modules/@react-navigation/stack/src/views/Stack/StackView.tsx
2025-02-16T00:14:18.486Z - Transforming: node_modules/@react-navigation/stack/src/utils/CardAnimationContext.tsx
2025-02-16T00:14:18.493Z - Transforming: node_modules/@react-navigation/stack/src/utils/GestureHandlerRefContext.tsx
2025-02-16T00:14:18.498Z - Transforming: node_modules/@react-navigation/stack/src/utils/useCardAnimation.tsx
2025-02-16T00:14:18.504Z - Transforming: node_modules/@react-navigation/stack/src/utils/useGestureHandlerRef.tsx
2025-02-16T00:14:18.505Z - Transforming: node_modules/expo-image/src/index.ts
2025-02-16T00:14:18.511Z - Transforming: node_modules/react-native-reanimated-carousel/src/index.tsx
2025-02-16T00:14:18.515Z - Transforming: node_modules/expo-linear-gradient/build/LinearGradient.js
2025-02-16T00:14:18.517Z - Transforming: node_modules/expo-haptics/src/Haptics.ts
2025-02-16T00:14:18.526Z - Transforming: src/screens/styles/welcomeScreenStyles.js
2025-02-16T00:14:18.530Z - Transforming: src/authentication/components/LoginModal.js
2025-02-16T00:14:18.553Z - Transforming: node_modules/@expo/vector-icons/Feather.js
2025-02-16T00:14:18.559Z - Transforming: src/screens/styles/signinScreenStyles.js
2025-02-16T00:14:18.562Z - Transforming: src/authentication/components/ResetPasswordModal.js
2025-02-16T00:14:18.566Z - Transforming: node_modules/react-native/Libraries/BatchedBridge/BatchedBridge.js
2025-02-16T00:14:18.570Z - Transforming: node_modules/react-native-gesture-handler/src/components/Pressable/utils.ts
2025-02-16T00:14:18.584Z - Transforming: node_modules/@expo/vector-icons/MaterialCommunityIcons.js
2025-02-16T00:14:18.588Z - Transforming: src/screens/styles/signupScreenStyles.js
2025-02-16T00:14:18.595Z - Transforming: node_modules/@expo/vector-icons/build/IconsLazy.js
2025-02-16T00:14:18.608Z - Transforming: src/authentication/components/AboutYouSection.js
2025-02-16T00:14:18.635Z - Transforming: src/authentication/components/ActivityLevelSection.js
2025-02-16T00:14:18.637Z - Transforming: src/authentication/components/SetAGoalSection.js
2025-02-16T00:14:18.703Z - Transforming: src/authentication/components/AccountDetailsSection.js
2025-02-16T00:14:18.851Z - Transforming: src/authentication/components/BodyTypeSection.js
2025-02-16T00:14:18.944Z - Transforming: node_modules/react-native/Libraries/Components/UnimplementedViews/UnimplementedView.js
2025-02-16T00:14:18.954Z - Transforming: node_modules/react-native/Libraries/Components/AccessibilityInfo/legacySendAccessibilityEvent.ios.js
2025-02-16T00:14:18.961Z - Transforming: node_modules/react-native/Libraries/Components/AccessibilityInfo/NativeAccessibilityInfo.js
2025-02-16T00:14:18.968Z - Transforming: node_modules/react-native/Libraries/Components/AccessibilityInfo/NativeAccessibilityManager.js
2025-02-16T00:14:18.976Z - Transforming: node_modules/react-native/Libraries/Components/ActivityIndicator/ActivityIndicatorViewNativeComponent.js
2025-02-16T00:14:18.978Z - Transforming: node_modules/@firebase/storage/node_modules/@firebase/component/dist/esm/index.esm2017.js
2025-02-16T00:14:18.995Z - Transforming: node_modules/react-native/Libraries/StyleSheet/flattenStyle.js
2025-02-16T00:14:19.004Z - Transforming: node_modules/react-native/Libraries/Image/ImageAnalyticsTagContext.js
2025-02-16T00:14:19.011Z - Transforming: node_modules/react-native/Libraries/Image/ImageInjection.js
2025-02-16T00:14:19.014Z - Transforming: node_modules/react-native/Libraries/Image/ImageSourceUtils.js
2025-02-16T00:14:19.024Z - Transforming: node_modules/react-native/Libraries/Image/ImageUtils.js
2025-02-16T00:14:19.024Z - Transforming: node_modules/react-native/Libraries/Image/ImageViewNativeComponent.js
2025-02-16T00:14:19.026Z - Transforming: node_modules/react-native/Libraries/Image/NativeImageLoaderIOS.js
2025-02-16T00:14:19.028Z - Transforming: node_modules/react-native/Libraries/Image/resolveAssetSource.js
2025-02-16T00:14:19.032Z - Transforming: node_modules/react-native/Libraries/Components/SafeAreaView/RCTSafeAreaViewNativeComponent.js
2025-02-16T00:14:19.035Z - Transforming: node_modules/react-native/Libraries/Components/RefreshControl/AndroidSwipeRefreshLayoutNativeComponent.js
2025-02-16T00:14:19.038Z - Transforming: node_modules/react-native/Libraries/Components/RefreshControl/PullToRefreshViewNativeComponent.js
2025-02-16T00:14:19.041Z - Transforming: node_modules/@react-native/virtualized-lists/index.js
2025-02-16T00:14:19.045Z - Transforming: node_modules/react-native/Libraries/ReactNative/AppContainer.js
2025-02-16T00:14:19.045Z - Transforming: node_modules/react-native/Libraries/Modal/RCTModalHostViewNativeComponent.js
2025-02-16T00:14:19.046Z - Transforming: node_modules/react-native/Libraries/Modal/ModalInjection.js
2025-02-16T00:14:19.052Z - Transforming: node_modules/react-native/src/private/featureflags/ReactNativeFeatureFlags.js
2025-02-16T00:14:19.052Z - Transforming: node_modules/memoize-one/dist/memoize-one.cjs.js
2025-02-16T00:14:19.053Z - Transforming: node_modules/react-native/Libraries/Modal/NativeModalManager.js
2025-02-16T00:14:19.060Z - Transforming: node_modules/react-native/Libraries/Utilities/differ/deepDiffer.js
2025-02-16T00:14:19.067Z - Transforming: node_modules/react-native/Libraries/Components/StatusBar/NativeStatusBarManagerAndroid.js
2025-02-16T00:14:19.068Z - Transforming: node_modules/react-native/Libraries/Components/StatusBar/NativeStatusBarManagerIOS.js
2025-02-16T00:14:19.075Z - Transforming: node_modules/react-native/Libraries/Components/TextInput/RCTInputAccessoryViewNativeComponent.js
2025-02-16T00:14:19.077Z - Transforming: node_modules/react-native/Libraries/Pressability/PressabilityDebug.js
2025-02-16T00:14:19.081Z - Transforming: node_modules/react-native/Libraries/Components/Pressable/useAndroidRippleForView.js
2025-02-16T00:14:19.092Z - Transforming: node_modules/react-native/Libraries/Pressability/usePressability.js
2025-02-16T00:14:19.098Z - Transforming: node_modules/react-native/Libraries/Components/Switch/AndroidSwitchNativeComponent.js
2025-02-16T00:14:19.101Z - Transforming: node_modules/react-native/Libraries/Components/Switch/SwitchNativeComponent.js
2025-02-16T00:14:19.103Z - Transforming: node_modules/react-native/Libraries/Utilities/useMergeRefs.js
2025-02-16T00:14:19.109Z - Transforming: node_modules/react-native/Libraries/Text/TextAncestor.js
2025-02-16T00:14:19.115Z - Transforming: node_modules/react-native/Libraries/Text/TextNativeComponent.js
2025-02-16T00:14:19.119Z - Transforming: node_modules/react-native/Libraries/Components/TextInput/TextInputState.js
2025-02-16T00:14:19.121Z - Transforming: node_modules/nullthrows/nullthrows.js
2025-02-16T00:14:19.127Z - Transforming: node_modules/react-native/Libraries/Components/TextInput/AndroidTextInputNativeComponent.js
2025-02-16T00:14:19.130Z - Transforming: node_modules/react-native/Libraries/Components/TextInput/RCTMultilineTextInputNativeComponent.js
2025-02-16T00:14:19.132Z - Transforming: node_modules/react-native/Libraries/Components/TextInput/RCTSingelineTextInputNativeComponent.js
2025-02-16T00:14:19.142Z - Transforming: node_modules/react-native/Libraries/Pressability/Pressability.js
2025-02-16T00:14:19.147Z - Transforming: node_modules/react-native/Libraries/Components/View/ViewNativeComponent.js
2025-02-16T00:14:19.149Z - Transforming: node_modules/react-native/Libraries/ActionSheetIOS/NativeActionSheetManager.js
2025-02-16T00:14:19.156Z - Transforming: node_modules/react-native/Libraries/Animated/AnimatedMock.js
2025-02-16T00:14:19.158Z - Transforming: node_modules/react-native/Libraries/Animated/AnimatedImplementation.js
2025-02-16T00:14:19.173Z - Transforming: node_modules/react-native/Libraries/Animated/components/AnimatedFlatList.js
2025-02-16T00:14:19.185Z - Transforming: node_modules/react-native/Libraries/Animated/components/AnimatedImage.js
2025-02-16T00:14:19.193Z - Transforming: node_modules/react-native/Libraries/Animated/components/AnimatedView.js
2025-02-16T00:14:19.331Z - Transforming: node_modules/react-native/Libraries/Animated/components/AnimatedScrollView.js
2025-02-16T00:14:19.470Z - Transforming: node_modules/react-native/Libraries/Animated/components/AnimatedText.js
2025-02-16T00:14:19.608Z - Transforming: node_modules/react-native/Libraries/Animated/components/AnimatedSectionList.js
2025-02-16T00:14:19.640Z - Transforming: node_modules/react-native/Libraries/vendor/emitter/EventEmitter.js
2025-02-16T00:14:19.688Z - Transforming: node_modules/react-native/Libraries/Utilities/DebugEnvironment.js
2025-02-16T00:14:19.710Z - Transforming: node_modules/react-native/Libraries/Utilities/NativeAppearance.js
2025-02-16T00:14:19.723Z - Transforming: node_modules/react-native/Libraries/Components/Sound/SoundManager.js
2025-02-16T00:14:19.744Z - Transforming: node_modules/react-native/Libraries/Components/Touchable/BoundingDimensions.js
2025-02-16T00:14:19.757Z - Transforming: node_modules/react-native/Libraries/Components/Touchable/Position.js
2025-02-16T00:14:19.763Z - Transforming: node_modules/react-native/Libraries/Utilities/logError.js
2025-02-16T00:14:19.765Z - Transforming: node_modules/react-native/Libraries/Components/Clipboard/NativeClipboard.js
2025-02-16T00:14:19.906Z - Transforming: node_modules/react-native/Libraries/AppState/NativeAppState.js
2025-02-16T00:14:19.965Z - Transforming: node_modules/react-native/Libraries/Alert/RCTAlertManager.ios.js
2025-02-16T00:14:19.971Z - Transforming: node_modules/react-native/Libraries/NativeModules/specs/NativeDevSettings.js
2025-02-16T00:14:20.014Z - Transforming: node_modules/react-native/Libraries/Utilities/NativeDeviceInfo.js
2025-02-16T00:14:20.036Z - Transforming: node_modules/react-native/Libraries/ReactNative/NativeI18nManager.js
2025-02-16T00:14:20.037Z - Transforming: node_modules/react-native/Libraries/ReactNative/RendererImplementation.js
2025-02-16T00:14:20.073Z - Transforming: node_modules/react-native/Libraries/Animated/bezier.js
2025-02-16T00:14:20.076Z - Transforming: node_modules/react-native/Libraries/Utilities/infoLog.js
2025-02-16T00:14:20.087Z - Transforming: node_modules/react-native/Libraries/Interaction/TaskQueue.js
2025-02-16T00:14:20.090Z - Transforming: node_modules/react-native/Libraries/Linking/NativeIntentAndroid.js
2025-02-16T00:14:20.120Z - Transforming: node_modules/react-native/Libraries/Linking/NativeLinkingManager.js
2025-02-16T00:14:20.121Z - Transforming: node_modules/react-native/Libraries/BugReporting/BugReporting.js
2025-02-16T00:14:20.227Z - Transforming: node_modules/react-native/Libraries/Utilities/SceneTracker.js
2025-02-16T00:14:20.233Z - Transforming: node_modules/react-native/Libraries/Utilities/createPerformanceLogger.js
2025-02-16T00:14:20.250Z - Transforming: node_modules/react-native/Libraries/ReactNative/DisplayMode.js
2025-02-16T00:14:20.270Z - Transforming: node_modules/react-native/Libraries/ReactNative/HeadlessJsTaskError.js
2025-02-16T00:14:20.290Z - Transforming: node_modules/react-native/Libraries/ReactNative/renderApplication.js
2025-02-16T00:14:20.309Z - Transforming: node_modules/react-native/Libraries/ReactNative/NativeHeadlessJsTaskSupport.js
2025-02-16T00:14:20.334Z - Transforming: node_modules/react-native/Libraries/LogBox/LogBoxInspectorContainer.js
2025-02-16T00:14:20.351Z - Transforming: node_modules/@babel/runtime/helpers/classPrivateFieldLooseKey.js
2025-02-16T00:14:20.356Z - Transforming: node_modules/react-native/src/private/components/HScrollViewNativeComponents.js
2025-02-16T00:14:20.360Z - Transforming: node_modules/@babel/runtime/helpers/classPrivateFieldLooseBase.js
2025-02-16T00:14:20.370Z - Transforming: node_modules/react-native/src/private/components/VScrollViewNativeComponents.js
2025-02-16T00:14:20.372Z - Transforming: node_modules/react-native/Libraries/Interaction/FrameRateLogger.js
2025-02-16T00:14:20.393Z - Transforming: node_modules/react-native/Libraries/StyleSheet/splitLayoutProps.js
2025-02-16T00:14:20.414Z - Transforming: node_modules/react-native/Libraries/Utilities/dismissKeyboard.js
2025-02-16T00:14:20.419Z - Transforming: node_modules/react-native/Libraries/Components/ScrollView/processDecelerationRate.js
2025-02-16T00:14:20.427Z - Transforming: node_modules/react-native/Libraries/Components/ScrollView/ScrollViewContext.js
2025-02-16T00:14:20.433Z - Transforming: node_modules/react-native/Libraries/Components/ScrollView/ScrollViewStickyHeader.js
2025-02-16T00:14:20.463Z - Transforming: node_modules/react-native/Libraries/Components/Keyboard/NativeKeyboardObserver.js
2025-02-16T00:14:20.474Z - Transforming: node_modules/react-native/Libraries/Interaction/TouchHistoryMath.js
2025-02-16T00:14:20.475Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeDialogManagerAndroid.js
2025-02-16T00:14:20.475Z - Transforming: node_modules/react-native/Libraries/Components/ScrollView/ScrollViewCommands.js
2025-02-16T00:14:20.486Z - Transforming: node_modules/react-native/Libraries/Network/convertRequestBody.js
2025-02-16T00:14:20.490Z - Transforming: node_modules/react-native/Libraries/Network/NativeNetworkingIOS.js
2025-02-16T00:14:20.491Z - Transforming: node_modules/react-native/Libraries/ReactNative/FabricUIManager.js
2025-02-16T00:14:20.496Z - Transforming: node_modules/react-native/Libraries/Settings/NativeSettingsManager.js
2025-02-16T00:14:20.497Z - Transforming: node_modules/react-native/src/private/styles/composeStyles.js
2025-02-16T00:14:20.502Z - Transforming: node_modules/react-native/Libraries/Components/View/ReactNativeStyleAttributes.js
2025-02-16T00:14:20.508Z - Transforming: node_modules/react-native/Libraries/Utilities/RCTLog.js
2025-02-16T00:14:20.508Z - Transforming: node_modules/react-native/Libraries/LogBox/Data/parseLogBoxLog.js
2025-02-16T00:14:20.508Z - Transforming: node_modules/react-native/Libraries/Share/NativeShareModule.js
2025-02-16T00:14:20.542Z - Transforming: node_modules/react-native/Libraries/LogBox/Data/LogBoxData.js
2025-02-16T00:14:20.543Z - Transforming: node_modules/react-native/Libraries/NativeModules/specs/NativeLogBox.js
2025-02-16T00:14:20.548Z - Transforming: node_modules/react-native/Libraries/PermissionsAndroid/NativePermissionsAndroid.js
2025-02-16T00:14:20.550Z - Transforming: node_modules/react-native/Libraries/ReactNative/BridgelessUIManager.js
2025-02-16T00:14:20.561Z - Transforming: node_modules/react-native/Libraries/ReactNative/PaperUIManager.js
2025-02-16T00:14:20.593Z - Transforming: node_modules/react-native/Libraries/Utilities/deepFreezeAndThrowOnMutationInDev.js
2025-02-16T00:14:20.596Z - Transforming: node_modules/react-native/Libraries/Vibration/NativeVibration.js
2025-02-16T00:14:20.600Z - Transforming: node_modules/react-native/Libraries/PushNotificationIOS/NativePushNotificationManagerIOS.js
2025-02-16T00:14:20.603Z - Transforming: node_modules/react-native/Libraries/StyleSheet/normalizeColor.js
2025-02-16T00:14:20.606Z - Transforming: node_modules/react-native/Libraries/Renderer/shims/createReactNativeComponentClass.js
2025-02-16T00:14:20.607Z - Transforming: node_modules/react-native/Libraries/ReactNative/getNativeComponentAttributes.js
2025-02-16T00:14:20.623Z - Transforming: node_modules/react-native/Libraries/Utilities/defineLazyObjectProperty.js
2025-02-16T00:14:20.624Z - Transforming: node_modules/react-native/Libraries/Utilities/NativePlatformConstantsIOS.js
2025-02-16T00:14:20.624Z - Transforming: node_modules/@babel/runtime/helpers/superPropBase.js
2025-02-16T00:14:20.629Z - Transforming: node_modules/@react-navigation/material-top-tabs/src/navigators/createMaterialTopTabNavigator.tsx
2025-02-16T00:14:20.631Z - Transforming: node_modules/@react-navigation/material-top-tabs/src/views/MaterialTopTabBar.tsx
2025-02-16T00:14:20.631Z - Transforming: node_modules/@react-navigation/material-top-tabs/src/views/MaterialTopTabView.tsx
2025-02-16T00:14:20.633Z - Transforming: src/features/AIAgent/components/AiChatHeader.js
2025-02-16T00:14:20.641Z - Transforming: src/features/AIAgent/components/AiInputBar.js
2025-02-16T00:14:20.671Z - Transforming: src/features/AIAgent/components/CustomKeyboard.js
2025-02-16T00:14:20.673Z - Transforming: src/features/AIAgent/components/AiMessagesList.js
2025-02-16T00:14:20.677Z - Transforming: src/screens/styles/goalsScreenStyles.js
2025-02-16T00:14:20.685Z - Transforming: src/features/WorkoutDiary/components/TabModal.js
2025-02-16T00:14:20.688Z - Transforming: src/features/WorkoutDiary/CreatePlan/CreateEditContent.js
2025-02-16T00:14:20.701Z - Transforming: src/screens/styles/workoutDiaryScreenStyles.js
2025-02-16T00:14:20.702Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/WorkutPlanScreen.js
2025-02-16T00:14:20.704Z - Transforming: src/screens/styles/settingsScreenStyles.js
2025-02-16T00:14:20.712Z - Transforming: src/features/ThemeChanger/components/ThemeDisplay.js
2025-02-16T00:14:20.713Z - Transforming: src/features/ThemeChanger/components/ThemeSelector.js
2025-02-16T00:14:20.716Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/views/BottomTabBar.tsx
2025-02-16T00:14:20.735Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/navigators/createBottomTabNavigator.tsx
2025-02-16T00:14:20.741Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/utils/BottomTabBarHeightCallbackContext.tsx
2025-02-16T00:14:20.750Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/views/BottomTabView.tsx
2025-02-16T00:14:20.751Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/utils/BottomTabBarHeightContext.tsx
2025-02-16T00:14:20.759Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/utils/useBottomTabBarHeight.tsx
2025-02-16T00:14:20.765Z - Transforming: src/features/SocialMedia/components/OpenDrawerToggle.js
2025-02-16T00:14:20.780Z - Transforming: node_modules/@react-navigation/drawer/src/navigators/createDrawerNavigator.tsx
2025-02-16T00:14:20.785Z - Transforming: node_modules/@react-navigation/drawer/src/views/DrawerContent.tsx
2025-02-16T00:14:20.790Z - Transforming: node_modules/@react-navigation/drawer/src/views/DrawerContentScrollView.tsx
2025-02-16T00:14:20.802Z - Transforming: node_modules/@react-navigation/drawer/src/views/DrawerItemList.tsx
2025-02-16T00:14:20.809Z - Transforming: node_modules/@react-navigation/drawer/src/views/DrawerToggleButton.tsx
2025-02-16T00:14:20.812Z - Transforming: node_modules/@react-navigation/drawer/src/views/DrawerItem.tsx
2025-02-16T00:14:20.817Z - Transforming: node_modules/@react-navigation/drawer/src/views/DrawerView.tsx
2025-02-16T00:14:20.820Z - Transforming: node_modules/@react-navigation/drawer/src/utils/DrawerGestureContext.tsx
2025-02-16T00:14:20.833Z - Transforming: node_modules/@react-navigation/drawer/src/utils/DrawerProgressContext.tsx
2025-02-16T00:14:20.836Z - Transforming: node_modules/@react-navigation/drawer/src/utils/DrawerStatusContext.tsx
2025-02-16T00:14:20.838Z - Transforming: node_modules/@react-navigation/drawer/src/utils/getDrawerStatusFromState.tsx
2025-02-16T00:14:20.841Z - Transforming: node_modules/@react-navigation/drawer/src/utils/useDrawerProgress.tsx
2025-02-16T00:14:20.842Z - Transforming: node_modules/@react-navigation/drawer/src/utils/useDrawerStatus.tsx
2025-02-16T00:14:20.844Z - Transforming: src/components/LinearGradientCard.js
2025-02-16T00:14:20.846Z - Transforming: src/features/SocialMedia/Messages/components/AddFriendModal.js
2025-02-16T00:14:20.847Z - Transforming: src/features/SocialMedia/components/ProfilePicture.js
2025-02-16T00:14:20.848Z - Transforming: src/features/FoodMenu/components/CreateCustomMealModal.js
2025-02-16T00:14:21.037Z - Transforming: node_modules/@expo/vector-icons/Ionicons.js
2025-02-16T00:14:21.056Z - Transforming: src/features/SocialMedia/Messages/screens/styles/chatScreenStyles.js
2025-02-16T00:14:21.064Z - Transforming: src/features/SocialMedia/Messages/DummyData/FriendData.js
2025-02-16T00:14:21.081Z - Transforming: src/features/SocialMedia/Messages/screens/styles/AddFriendStyles.js
2025-02-16T00:14:21.092Z - Transforming: src/features/SocialMedia/Messages/screens/styles/selectFriendStyles.js
2025-02-16T00:14:21.106Z - Transforming: src/screens/styles/dashboardScreenStyles.js
2025-02-16T00:14:21.127Z - Transforming: src/components/StepsAndDistanceGoalModal.js
2025-02-16T00:14:21.151Z - Transforming: src/features/FoodDiary/components/WaterGoalModal.js
2025-02-16T00:14:21.155Z - Transforming: src/components/DatePickerModal.js
2025-02-16T00:14:21.191Z - Transforming: src/components/ProgressRing.js
2025-02-16T00:14:21.206Z - Transforming: src/features/FoodDiary/components/NutritionGoalsAlignmentModal.js
2025-02-16T00:14:21.232Z - Transforming: src/features/NutritionalProgram/components/NutritionalProgramCard.js
2025-02-16T00:14:21.242Z - Transforming: src/components/ThreeLevelProgressRing.js
2025-02-16T00:14:21.256Z - Transforming: src/navigation/components/CustomTabButton.js
2025-02-16T00:14:21.260Z - Transforming: src/features/Settings/screens/styles/nutritionSettingsStyles.js
2025-02-16T00:14:21.268Z - Transforming: src/features/FoodDiary/components/FoodEntryModal.js
2025-02-16T00:14:21.271Z - Transforming: src/features/FoodDiary/components/MealSectionCustomizationModal.js
2025-02-16T00:14:21.290Z - Transforming: src/features/FoodDiary/components/DailyNutritionGoalsCustomizationModal.js
2025-02-16T00:14:21.300Z - Transforming: src/features/FoodDiary/components/DailyNutritionGoalsCalculationModal.js
2025-02-16T00:14:21.301Z - Transforming: src/screens/styles/foodDiaryScreenStyles.js
2025-02-16T00:14:21.327Z - Transforming: src/features/FoodDiary/components/FoodNutrientModal.js
2025-02-16T00:14:21.370Z - Transforming: src/features/FoodDiary/components/SwipeableFoodEntryListItem.js
2025-02-16T00:14:21.393Z - Transforming: src/components/DateSelector.js
2025-02-16T00:14:21.468Z - Transforming: src/features/FoodDiary/components/FabGroupMenu.js
2025-02-16T00:14:21.480Z - Transforming: src/features/FoodDiary/components/CarouselRenderItemComponent.js
2025-02-16T00:14:21.482Z - Transforming: src/features/FoodDiary/components/CarouselWithIndicators.js
2025-02-16T00:14:21.490Z - Transforming: src/features/FoodDiary/components/MealSectionMenuOptions/QuickAddModal.js
2025-02-16T00:14:21.497Z - Transforming: src/features/FoodDiary/components/MealSectionMenuOptions/CopyMealSectionFromDateModal.js
2025-02-16T00:14:21.501Z - Transforming: src/features/FoodDiary/components/WaterLogEntryModal.js
2025-02-16T00:14:21.532Z - Transforming: src/features/NutritionalProgram/views/CreateProgram.js
2025-02-16T00:14:21.543Z - Transforming: src/features/NutritionalProgram/views/ProgramActive.js
2025-02-16T00:14:21.568Z - Transforming: src/features/NutritionalProgram/views/ProgramAll.js
2025-02-16T00:14:21.595Z - Transforming: src/features/NutritionalProgram/views/ProgramCheckpoints.js
2025-02-16T00:14:21.696Z - Transforming: src/features/NutritionalProgram/views/ProgramMealPlan.js
2025-02-16T00:14:21.704Z - Transforming: src/features/NutritionalProgram/components/BasicProgramModal.js
2025-02-16T00:14:21.715Z - Transforming: src/features/NutritionalProgram/components/AICreateMealPlanModal.js
2025-02-16T00:14:21.720Z - Transforming: src/features/NutritionalProgram/components/VoiceAssistedProgramCreationModal.js
2025-02-16T00:14:21.738Z - Transforming: src/features/Settings/screens/styles/notificationSettingsStyles.js
2025-02-16T00:14:21.752Z - Transforming: node_modules/react-native-modal-datetime-picker/src/index.js
2025-02-16T00:14:21.756Z - Transforming: node_modules/react-native-paper/src/styles/themes/v3/LightTheme.tsx
2025-02-16T00:14:21.772Z - Transforming: node_modules/react-native-paper/src/styles/themes/v3/DarkTheme.tsx
2025-02-16T00:14:21.774Z - Transforming: node_modules/react-native-paper/src/styles/themes/v2/LightTheme.tsx
2025-02-16T00:14:21.780Z - Transforming: node_modules/react-native-paper/src/styles/themes/v2/DarkTheme.tsx
2025-02-16T00:14:21.781Z - Transforming: src/features/Settings/screens/styles/accountSettingsStyles.js
2025-02-16T00:14:21.787Z - Transforming: node_modules/@callstack/react-theme-provider/lib/index.js
2025-02-16T00:14:21.787Z - Transforming: node_modules/react-native-paper/node_modules/color/index.js
2025-02-16T00:14:21.792Z - Transforming: node_modules/react-native-paper/src/core/SafeAreaProviderCompat.tsx
2025-02-16T00:14:21.801Z - Transforming: node_modules/react-native-paper/src/core/settings.tsx
2025-02-16T00:14:21.807Z - Transforming: node_modules/react-native-paper/src/components/MaterialCommunityIcon.tsx
2025-02-16T00:14:21.810Z - Transforming: node_modules/react-native-paper/src/components/Portal/PortalHost.tsx
2025-02-16T00:14:21.830Z - Transforming: node_modules/react-native-paper/src/utils/addEventListener.tsx
2025-02-16T00:14:21.832Z - Transforming: node_modules/react-native-paper/src/components/Avatar/AvatarIcon.tsx
2025-02-16T00:14:21.834Z - Transforming: node_modules/react-native-paper/src/components/Avatar/AvatarImage.tsx
2025-02-16T00:14:21.845Z - Transforming: node_modules/react-native-paper/src/components/Avatar/AvatarText.tsx
2025-02-16T00:14:21.857Z - Transforming: node_modules/react-native-paper/src/components/Drawer/DrawerCollapsedItem.tsx
2025-02-16T00:14:21.857Z - Transforming: node_modules/react-native-paper/src/components/Drawer/DrawerItem.tsx
2025-02-16T00:14:21.870Z - Transforming: node_modules/react-native-paper/src/components/Drawer/DrawerSection.tsx
2025-02-16T00:14:21.879Z - Transforming: node_modules/react-native-paper/src/components/List/ListAccordion.tsx
2025-02-16T00:14:21.887Z - Transforming: node_modules/react-native-paper/src/components/List/ListAccordionGroup.tsx
2025-02-16T00:14:21.889Z - Transforming: node_modules/react-native-paper/src/components/List/ListIcon.tsx
2025-02-16T00:14:21.897Z - Transforming: node_modules/react-native-paper/src/components/List/ListItem.tsx
2025-02-16T00:14:21.908Z - Transforming: node_modules/react-native-paper/src/components/List/ListSection.tsx
2025-02-16T00:14:21.916Z - Transforming: node_modules/react-native-paper/src/components/List/ListSubheader.tsx
2025-02-16T00:14:21.924Z - Transforming: node_modules/react-native-paper/src/components/List/ListImage.tsx
2025-02-16T00:14:21.926Z - Transforming: node_modules/react-native-paper/src/utils/getContrastingColor.tsx
2025-02-16T00:14:21.927Z - Transforming: node_modules/react-native-paper/node_modules/use-latest-callback/lib/src/index.js
2025-02-16T00:14:21.932Z - Transforming: src/features/Settings/screens/styles/profileSettingsStyles.js
2025-02-16T00:14:21.934Z - Transforming: src/authentication/components/CustomSexPickerModal.js
2025-02-16T00:14:21.935Z - Transforming: src/authentication/components/CustomWeightInputModal.js
2025-02-16T00:14:21.942Z - Transforming: src/authentication/components/CustomHeightPickerModal.js
2025-02-16T00:14:21.944Z - Transforming: src/authentication/components/CustomDatePickerModal.js
2025-02-16T00:14:21.951Z - Transforming: node_modules/react-native-paper/src/components/FAB/utils.ts
2025-02-16T00:14:21.957Z - Transforming: node_modules/react-native-paper/src/components/Typography/AnimatedText.tsx
2025-02-16T00:14:21.968Z - Transforming: node_modules/react-native-paper/src/components/Checkbox/Checkbox.tsx
2025-02-16T00:14:21.987Z - Transforming: node_modules/react-native-paper/src/components/Checkbox/CheckboxAndroid.tsx
2025-02-16T00:14:21.993Z - Transforming: node_modules/react-native-paper/src/components/Checkbox/CheckboxIOS.tsx
2025-02-16T00:14:22.000Z - Transforming: node_modules/react-native-paper/src/components/Checkbox/CheckboxItem.tsx
2025-02-16T00:14:22.002Z - Transforming: node_modules/react-native-paper/src/components/DataTable/DataTableCell.tsx
2025-02-16T00:14:22.020Z - Transforming: node_modules/react-native-paper/src/components/DataTable/DataTableHeader.tsx
2025-02-16T00:14:22.023Z - Transforming: node_modules/react-native-paper/src/components/DataTable/DataTablePagination.tsx
2025-02-16T00:14:22.023Z - Transforming: node_modules/react-native-paper/src/components/DataTable/DataTableRow.tsx
2025-02-16T00:14:22.030Z - Transforming: node_modules/react-native-paper/src/components/DataTable/DataTableTitle.tsx
2025-02-16T00:14:22.036Z - Transforming: node_modules/react-native-paper/src/utils/forwardRef.tsx
2025-02-16T00:14:22.041Z - Transforming: node_modules/react-native-paper/src/components/Button/utils.tsx
2025-02-16T00:14:22.041Z - Transforming: node_modules/react-native-paper/src/utils/hasTouchHandler.tsx
2025-02-16T00:14:22.050Z - Transforming: node_modules/react-native-paper/src/utils/splitStyles.ts
2025-02-16T00:14:22.064Z - Transforming: node_modules/react-native-safe-area-context/src/index.tsx
2025-02-16T00:14:22.069Z - Transforming: node_modules/react-native-paper/src/components/Dialog/DialogActions.tsx
2025-02-16T00:14:22.073Z - Transforming: node_modules/react-native-paper/src/components/Dialog/DialogContent.tsx
2025-02-16T00:14:22.074Z - Transforming: node_modules/react-native-paper/src/components/Dialog/DialogIcon.tsx
2025-02-16T00:14:22.081Z - Transforming: node_modules/react-native-paper/src/components/Dialog/DialogScrollArea.tsx
2025-02-16T00:14:22.085Z - Transforming: node_modules/react-native-paper/src/components/Dialog/DialogTitle.tsx
2025-02-16T00:14:22.085Z - Transforming: node_modules/react-native-paper/src/components/FAB/FAB.tsx
2025-02-16T00:14:22.087Z - Transforming: node_modules/react-native-paper/src/components/FAB/FABGroup.tsx
2025-02-16T00:14:22.098Z - Transforming: node_modules/react-native-paper/src/components/BottomNavigation/BottomNavigationBar.tsx
2025-02-16T00:14:22.132Z - Transforming: node_modules/react-native-css-interop/dist/index.js
2025-02-16T00:14:22.141Z - Transforming: node_modules/react-native-paper/src/components/BottomNavigation/BottomNavigationRouteScreen.tsx
2025-02-16T00:14:22.150Z - Transforming: node_modules/react-native-paper/src/utils/useAnimatedValueArray.tsx
2025-02-16T00:14:22.152Z - Transforming: node_modules/react-native-paper/src/components/HelperText/utils.ts
2025-02-16T00:14:22.156Z - Transforming: node_modules/react-native-paper/src/components/Card/CardContent.tsx
2025-02-16T00:14:22.157Z - Transforming: node_modules/react-native-paper/src/components/Card/CardCover.tsx
2025-02-16T00:14:22.165Z - Transforming: node_modules/react-native-paper/src/components/Card/CardTitle.tsx
2025-02-16T00:14:22.168Z - Transforming: node_modules/react-native-paper/src/components/Card/utils.tsx
2025-02-16T00:14:22.176Z - Transforming: node_modules/react-native-paper/src/components/Card/CardActions.tsx
2025-02-16T00:14:22.182Z - Transforming: node_modules/react-native-paper/src/components/RadioButton/RadioButton.tsx
2025-02-16T00:14:22.188Z - Transforming: node_modules/react-native-paper/src/components/RadioButton/RadioButtonAndroid.tsx
2025-02-16T00:14:22.194Z - Transforming: node_modules/react-native-paper/src/components/RadioButton/RadioButtonGroup.tsx
2025-02-16T00:14:22.199Z - Transforming: node_modules/react-native-paper/src/components/RadioButton/RadioButtonIOS.tsx
2025-02-16T00:14:22.202Z - Transforming: node_modules/react-native-paper/src/components/RadioButton/RadioButtonItem.tsx
2025-02-16T00:14:22.222Z - Transforming: node_modules/react-native-paper/src/components/Portal/PortalConsumer.tsx
2025-02-16T00:14:22.228Z - Transforming: node_modules/react-native-paper/src/components/CrossFadeIcon.tsx
2025-02-16T00:14:22.238Z - Transforming: node_modules/react-native-paper/src/components/IconButton/utils.ts
2025-02-16T00:14:22.252Z - Transforming: node_modules/react-native-paper/src/components/Appbar/Appbar.tsx
2025-02-16T00:14:22.269Z - Transforming: node_modules/react-native-paper/src/components/Appbar/AppbarAction.tsx
2025-02-16T00:14:22.330Z - Transforming: node_modules/react-native-paper/src/components/Appbar/AppbarBackAction.tsx
2025-02-16T00:14:22.351Z - Transforming: node_modules/react-native-paper/src/components/Appbar/AppbarContent.tsx
2025-02-16T00:14:22.361Z - Transforming: node_modules/react-native-paper/src/components/Appbar/AppbarHeader.tsx
2025-02-16T00:14:22.384Z - Transforming: node_modules/react-native-paper/src/components/Chip/helpers.tsx
2025-02-16T00:14:22.407Z - Transforming: node_modules/react-native-paper/src/components/ToggleButton/ToggleButton.tsx
2025-02-16T00:14:22.416Z - Transforming: node_modules/react-native-paper/src/components/ToggleButton/ToggleButtonGroup.tsx
2025-02-16T00:14:22.428Z - Transforming: node_modules/react-native-paper/src/components/ToggleButton/ToggleButtonRow.tsx
2025-02-16T00:14:22.430Z - Transforming: node_modules/react-native-paper/src/components/TouchableRipple/utils.ts
2025-02-16T00:14:22.431Z - Transforming: node_modules/react-native-paper/src/components/TouchableRipple/Pressable.tsx
2025-02-16T00:14:22.437Z - Transforming: node_modules/react-native-paper/src/components/Typography/v2/Caption.tsx
2025-02-16T00:14:22.440Z - Transforming: node_modules/react-native-paper/src/components/Typography/v2/Headline.tsx
2025-02-16T00:14:22.443Z - Transforming: node_modules/react-native-paper/src/components/Typography/v2/Paragraph.tsx
2025-02-16T00:14:22.447Z - Transforming: node_modules/react-native-paper/src/components/Typography/v2/Subheading.tsx
2025-02-16T00:14:22.452Z - Transforming: node_modules/react-native-paper/src/components/Typography/v2/Title.tsx
2025-02-16T00:14:22.453Z - Transforming: node_modules/react-native-paper/src/components/SegmentedButtons/SegmentedButtonItem.tsx
2025-02-16T00:14:22.460Z - Transforming: node_modules/react-native-paper/src/components/Switch/utils.ts
2025-02-16T00:14:22.462Z - Transforming: node_modules/react-native-paper/src/components/Typography/v2/StyledText.tsx
2025-02-16T00:14:22.462Z - Transforming: node_modules/react-native-paper/src/components/SegmentedButtons/utils.ts
2025-02-16T00:14:22.481Z - Transforming: node_modules/react-native-paper/src/components/Menu/MenuItem.tsx
2025-02-16T00:14:22.482Z - Transforming: node_modules/react-native-paper/src/constants.tsx
2025-02-16T00:14:22.502Z - Transforming: node_modules/react-native-paper/src/types.tsx
2025-02-16T00:14:22.504Z - Transforming: node_modules/react-native-paper/src/utils/BackHandler/BackHandler.native.tsx
2025-02-16T00:14:22.513Z - Transforming: node_modules/react-native-paper/src/components/Tooltip/utils.ts
2025-02-16T00:14:22.515Z - Transforming: node_modules/axios/lib/axios.js
2025-02-16T00:14:22.521Z - Transforming: node_modules/expo-sensors/build/Accelerometer.js
2025-02-16T00:14:22.522Z - Transforming: node_modules/expo-sensors/build/Pedometer.js
2025-02-16T00:14:22.539Z - Transforming: node_modules/expo-sensors/build/Barometer.js
2025-02-16T00:14:22.545Z - Transforming: node_modules/expo-sensors/build/DeviceSensor.js
2025-02-16T00:14:22.548Z - Transforming: node_modules/expo-sensors/build/DeviceMotion.js
2025-02-16T00:14:22.561Z - Transforming: node_modules/expo-sensors/build/Gyroscope.js
2025-02-16T00:14:22.564Z - Transforming: node_modules/expo-sensors/build/Magnetometer.js
2025-02-16T00:14:22.570Z - Transforming: node_modules/expo-sensors/build/MagnetometerUncalibrated.js
2025-02-16T00:14:22.585Z - Transforming: node_modules/expo-sensors/build/LightSensor.js
2025-02-16T00:14:22.589Z - Transforming: node_modules/react-native-uuid/dist/parse.js
2025-02-16T00:14:22.594Z - Transforming: node_modules/react-native-uuid/dist/validate.js
2025-02-16T00:14:22.605Z - Transforming: node_modules/react-native-uuid/dist/version.js
2025-02-16T00:14:22.606Z - Transforming: node_modules/react-native-uuid/dist/unparse.js
2025-02-16T00:14:22.614Z - Transforming: node_modules/react-native-uuid/dist/v1.js
2025-02-16T00:14:22.615Z - Transforming: node_modules/react-native-uuid/dist/v3.js
2025-02-16T00:14:22.622Z - Transforming: node_modules/react-native-uuid/dist/v4.js
2025-02-16T00:14:22.641Z - Transforming: node_modules/react-native-uuid/dist/v5.js
2025-02-16T00:14:22.647Z - Transforming: node_modules/react-native-uuid/dist/utils.js
2025-02-16T00:14:22.649Z - Transforming: node_modules/react-native-paper/src/utils/useAnimatedValue.tsx
2025-02-16T00:14:22.651Z - Transforming: node_modules/abort-controller/dist/abort-controller.mjs
2025-02-16T00:14:22.651Z - Transforming: node_modules/expo-application/build/Application.types.js
2025-02-16T00:14:22.655Z - Transforming: node_modules/expo-application/build/ExpoApplication.js
2025-02-16T00:14:22.657Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Adornment/TextInputAffix.tsx
2025-02-16T00:14:22.658Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Adornment/TextInputIcon.tsx
2025-02-16T00:14:22.660Z - Transforming: node_modules/react-native-paper/src/components/TextInput/TextInputFlat.tsx
2025-02-16T00:14:22.661Z - Transforming: node_modules/react-native-paper/src/utils/roundLayoutSize.ts
2025-02-16T00:14:22.672Z - Transforming: node_modules/react-native-paper/src/components/TextInput/TextInputOutlined.tsx
2025-02-16T00:14:22.692Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/spec/NativeGoogleSignin.ts
2025-02-16T00:14:22.692Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/spec/SignInButtonNativeComponent.ts
2025-02-16T00:14:22.698Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/buttons/statics.ts
2025-02-16T00:14:22.700Z - Transforming: node_modules/@ide/backoff/build/backoff.js
2025-02-16T00:14:22.701Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/constants.ts
2025-02-16T00:14:22.706Z - Transforming: node_modules/@react-native-google-signin/google-signin/src/translateNativeRejection.ts
2025-02-16T00:14:22.710Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/api.js
2025-02-16T00:14:22.710Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/styles.js
2025-02-16T00:14:22.740Z - Transforming: node_modules/@babel/runtime/helpers/toPrimitive.js
2025-02-16T00:14:22.750Z - Transforming: node_modules/@react-navigation/native/src/ServerContext.tsx
2025-02-16T00:14:22.764Z - Transforming: node_modules/@react-navigation/native/src/useBackButton.native.tsx
2025-02-16T00:14:22.764Z - Transforming: node_modules/@react-navigation/native/src/useDocumentTitle.native.tsx
2025-02-16T00:14:22.777Z - Transforming: node_modules/@react-navigation/native/src/useLinking.native.tsx
2025-02-16T00:14:22.778Z - Transforming: node_modules/@react-navigation/native/src/useThenable.tsx
2025-02-16T00:14:22.791Z - Transforming: node_modules/@react-navigation/native/src/theming/ThemeContext.tsx
2025-02-16T00:14:22.797Z - Transforming: node_modules/@react-navigation/core/src/BaseNavigationContainer.tsx
2025-02-16T00:14:22.798Z - Transforming: node_modules/@react-navigation/core/src/createNavigationContainerRef.tsx
2025-02-16T00:14:22.800Z - Transforming: node_modules/@react-navigation/core/src/CurrentRenderContext.tsx
2025-02-16T00:14:22.801Z - Transforming: node_modules/@react-navigation/core/src/createNavigatorFactory.tsx
2025-02-16T00:14:22.808Z - Transforming: node_modules/@react-navigation/core/src/findFocusedRoute.tsx
2025-02-16T00:14:22.817Z - Transforming: node_modules/@react-navigation/core/src/getActionFromState.tsx
2025-02-16T00:14:22.817Z - Transforming: node_modules/@react-navigation/core/src/getFocusedRouteNameFromRoute.tsx
2025-02-16T00:14:22.819Z - Transforming: node_modules/@react-navigation/core/src/getPathFromState.tsx
2025-02-16T00:14:22.832Z - Transforming: node_modules/@react-navigation/core/src/getStateFromPath.tsx
2025-02-16T00:14:22.839Z - Transforming: node_modules/@react-navigation/core/src/NavigationContainerRefContext.tsx
2025-02-16T00:14:22.848Z - Transforming: node_modules/@react-navigation/core/src/NavigationContext.tsx
2025-02-16T00:14:22.848Z - Transforming: node_modules/@react-navigation/core/src/NavigationHelpersContext.tsx
2025-02-16T00:14:22.854Z - Transforming: node_modules/@react-navigation/core/src/NavigationRouteContext.tsx
2025-02-16T00:14:22.854Z - Transforming: node_modules/@react-navigation/core/src/PreventRemoveContext.tsx
2025-02-16T00:14:22.859Z - Transforming: node_modules/@react-navigation/core/src/PreventRemoveProvider.tsx
2025-02-16T00:14:22.863Z - Transforming: node_modules/@react-navigation/core/src/types.tsx
2025-02-16T00:14:22.890Z - Transforming: node_modules/@react-navigation/core/src/useFocusEffect.tsx
2025-02-16T00:14:22.894Z - Transforming: node_modules/@react-navigation/core/src/useIsFocused.tsx
2025-02-16T00:14:22.906Z - Transforming: node_modules/@react-navigation/core/src/useNavigation.tsx
2025-02-16T00:14:22.909Z - Transforming: node_modules/@react-navigation/core/src/useNavigationBuilder.tsx
2025-02-16T00:14:22.920Z - Transforming: node_modules/@react-navigation/core/src/useNavigationContainerRef.tsx
2025-02-16T00:14:22.924Z - Transforming: node_modules/@react-navigation/core/src/useNavigationState.tsx
2025-02-16T00:14:22.934Z - Transforming: node_modules/@react-navigation/core/src/usePreventRemove.tsx
2025-02-16T00:14:22.939Z - Transforming: node_modules/@react-navigation/core/src/usePreventRemoveContext.tsx
2025-02-16T00:14:22.944Z - Transforming: node_modules/@react-navigation/core/src/useRoute.tsx
2025-02-16T00:14:22.955Z - Transforming: node_modules/@react-navigation/core/src/validatePathConfig.tsx
2025-02-16T00:14:22.962Z - Transforming: node_modules/@react-navigation/routers/src/index.tsx
2025-02-16T00:14:22.969Z - Transforming: node_modules/expo-device/build/Device.types.js
2025-02-16T00:14:22.974Z - Transforming: node_modules/expo-device/build/ExpoDevice.js
2025-02-16T00:14:22.981Z - Transforming: node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts
2025-02-16T00:14:22.981Z - Transforming: node_modules/expo-contacts/build/ExpoContacts.js
2025-02-16T00:14:22.984Z - Transforming: node_modules/react-native/Libraries/NativeComponent/NativeComponentRegistry.js
2025-02-16T00:14:22.987Z - Transforming: node_modules/@babel/runtime/helpers/wrapNativeSuper.js
2025-02-16T00:14:22.987Z - Transforming: node_modules/expo-modules-core/src/environment/browser.ts
2025-02-16T00:14:22.991Z - Transforming: node_modules/expo-modules-core/src/sweet/NativeErrorManager.ts
2025-02-16T00:14:22.995Z - Transforming: node_modules/expo-modules-core/src/uuid/uuid.ts
2025-02-16T00:14:22.996Z - Transforming: node_modules/@react-native-async-storage/async-storage/src/shouldFallbackToLegacyNativeModule.ts
2025-02-16T00:14:23.007Z - Transforming: node_modules/react-is/cjs/react-is.development.js
2025-02-16T00:14:23.007Z - Transforming: node_modules/idb/build/wrap-idb-value.cjs
2025-02-16T00:14:23.008Z - Transforming: node_modules/react-native/Libraries/Renderer/shims/ReactFabric.js
2025-02-16T00:14:23.016Z - Transforming: node_modules/react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance.js
2025-02-16T00:14:23.023Z - Transforming: node_modules/react-native-reanimated/src/index.ts
2025-02-16T00:14:23.024Z - Transforming: node_modules/@babel/runtime/helpers/readOnlyError.js
2025-02-16T00:14:23.031Z - Transforming: node_modules/react-native/Libraries/Renderer/shims/ReactNative.js
2025-02-16T00:14:23.033Z - Transforming: node_modules/react-native/Libraries/Renderer/shims/ReactNativeViewConfigRegistry.js
2025-02-16T00:14:23.035Z - Transforming: node_modules/react-native/Libraries/Utilities/PolyfillFunctions.js
2025-02-16T00:14:23.035Z - Transforming: node_modules/expo/src/winter/FormData.ts
2025-02-16T00:14:23.040Z - Transforming: node_modules/expo/src/winter/TextDecoder.ts
2025-02-16T00:14:23.047Z - Transforming: node_modules/expo/src/winter/url.ts
2025-02-16T00:14:23.054Z - Transforming: node_modules/expo-asset/build/PlatformUtils.js
2025-02-16T00:14:23.064Z - Transforming: node_modules/expo-asset/build/resolveAssetSource.native.js
2025-02-16T00:14:23.074Z - Transforming: node_modules/expo-asset/build/AssetUris.js
2025-02-16T00:14:23.082Z - Transforming: node_modules/expo-asset/build/ExpoAsset.js
2025-02-16T00:14:23.088Z - Transforming: node_modules/expo-asset/build/AssetSources.js
2025-02-16T00:14:23.092Z - Transforming: node_modules/expo-asset/build/ImageAssets.js
2025-02-16T00:14:23.098Z - Transforming: node_modules/expo-asset/build/LocalAssets.js
2025-02-16T00:14:23.103Z - Transforming:  polyfill:assets-registry
2025-02-16T00:14:23.110Z - Transforming: node_modules/@react-navigation/stack/src/utils/conditional.tsx
2025-02-16T00:14:23.111Z - Transforming: node_modules/warn-once/index.js
2025-02-16T00:14:23.117Z - Transforming: node_modules/@react-navigation/stack/src/utils/debounce.tsx
2025-02-16T00:14:23.119Z - Transforming: node_modules/@react-navigation/elements/src/index.tsx
2025-02-16T00:14:23.119Z - Transforming: node_modules/@react-navigation/stack/src/utils/ModalPresentationContext.tsx
2025-02-16T00:14:23.123Z - Transforming: node_modules/@react-navigation/stack/src/views/Header/HeaderSegment.tsx
2025-02-16T00:14:23.129Z - Transforming: node_modules/expo-image/src/Image.tsx
2025-02-16T00:14:23.137Z - Transforming: node_modules/expo-image/src/Image.types.ts
2025-02-16T00:14:23.142Z - Transforming: node_modules/expo-image/src/ImageBackground.tsx
2025-02-16T00:14:23.163Z - Transforming: node_modules/expo-image/src/useImage.ts
2025-02-16T00:14:23.164Z - Transforming: node_modules/react-native-reanimated-carousel/src/Carousel.tsx
2025-02-16T00:14:23.166Z - Transforming: node_modules/react-native-reanimated-carousel/src/layouts/stack.ts
2025-02-16T00:14:23.170Z - Transforming: node_modules/expo-linear-gradient/build/NativeLinearGradient.ios.js
2025-02-16T00:14:23.176Z - Transforming: node_modules/react-native/Libraries/Utilities/codegenNativeComponent.js
2025-02-16T00:14:23.188Z - Transforming: node_modules/expo-haptics/src/Haptics.types.ts
2025-02-16T00:14:23.189Z - Transforming: node_modules/expo-haptics/src/ExpoHaptics.ts
2025-02-16T00:14:23.206Z - Transforming: node_modules/@expo/vector-icons/build/MaterialCommunityIcons.js
2025-02-16T00:14:23.207Z - Transforming: node_modules/react-native/Libraries/BatchedBridge/MessageQueue.js
2025-02-16T00:14:23.206Z - Transforming: node_modules/@expo/vector-icons/build/Feather.js
2025-02-16T00:14:23.219Z - Transforming: node_modules/@expo/vector-icons/build/Entypo.js
2025-02-16T00:14:23.222Z - Transforming: node_modules/@expo/vector-icons/build/AntDesign.js
2025-02-16T00:14:23.223Z - Transforming: node_modules/@expo/vector-icons/build/EvilIcons.js
2025-02-16T00:14:23.227Z - Transforming: node_modules/@expo/vector-icons/build/Fontisto.js
2025-02-16T00:14:23.228Z - Transforming: node_modules/@expo/vector-icons/build/FontAwesome.js
2025-02-16T00:14:23.232Z - Transforming: node_modules/@expo/vector-icons/build/FontAwesome5.js
2025-02-16T00:14:23.233Z - Transforming: node_modules/@expo/vector-icons/build/FontAwesome6.js
2025-02-16T00:14:23.236Z - Transforming: node_modules/@expo/vector-icons/build/Foundation.js
2025-02-16T00:14:23.239Z - Transforming: node_modules/@expo/vector-icons/build/Ionicons.js
2025-02-16T00:14:23.241Z - Transforming: node_modules/@expo/vector-icons/build/Octicons.js
2025-02-16T00:14:23.246Z - Transforming: node_modules/@expo/vector-icons/build/Zocial.js
2025-02-16T00:14:23.249Z - Transforming: node_modules/@expo/vector-icons/build/SimpleLineIcons.js
2025-02-16T00:14:23.252Z - Transforming: node_modules/@expo/vector-icons/build/MaterialIcons.js
2025-02-16T00:14:23.259Z - Transforming: node_modules/@expo/vector-icons/build/createMultiStyleIconSet.js
2025-02-16T00:14:23.263Z - Transforming: node_modules/@expo/vector-icons/build/createIconSetFromIcoMoon.js
2025-02-16T00:14:23.272Z - Transforming: node_modules/@expo/vector-icons/build/createIconSet.js
2025-02-16T00:14:23.272Z - Transforming: node_modules/@expo/vector-icons/build/createIconSetFromFontello.js
2025-02-16T00:14:23.278Z - Transforming: node_modules/@react-navigation/stack/src/views/GestureHandler.ios.tsx
2025-02-16T00:14:23.284Z - Transforming: node_modules/@react-navigation/stack/src/views/Header/HeaderContainer.tsx
2025-02-16T00:14:23.329Z - Transforming: node_modules/@react-navigation/stack/src/views/Stack/CardStack.tsx
2025-02-16T00:14:23.347Z - Transforming: node_modules/@react-native-community/slider/dist/Slider.js
2025-02-16T00:14:23.380Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeAccessibilityInfo.js
2025-02-16T00:14:23.386Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeAccessibilityManager.js
2025-02-16T00:14:23.389Z - Transforming: node_modules/react-native/src/private/specs/components/ActivityIndicatorViewNativeComponent.js
2025-02-16T00:14:23.393Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeImageLoaderIOS.js
2025-02-16T00:14:23.396Z - Transforming: node_modules/react-native/Libraries/NativeComponent/ViewConfigIgnore.js
2025-02-16T00:14:23.400Z - Transforming: node_modules/react-native/Libraries/Utilities/differ/insetsDiffer.js
2025-02-16T00:14:23.403Z - Transforming: node_modules/react-native/Libraries/Utilities/codegenNativeCommands.js
2025-02-16T00:14:23.405Z - Transforming: node_modules/react-native/Libraries/NativeModules/specs/NativeSourceCode.js
2025-02-16T00:14:23.407Z - Transforming: node_modules/react-native/Libraries/Image/AssetSourceResolver.js
2025-02-16T00:14:23.411Z - Transforming: node_modules/react-native/Libraries/Image/AssetUtils.js
2025-02-16T00:14:23.422Z - Transforming: node_modules/react-native/src/private/specs/components/RCTSafeAreaViewNativeComponent.js
2025-02-16T00:14:23.428Z - Transforming: node_modules/react-native/src/private/specs/components/AndroidSwipeRefreshLayoutNativeComponent.js
2025-02-16T00:14:23.438Z - Transforming: node_modules/react-native/src/private/specs/components/PullToRefreshViewNativeComponent.js
2025-02-16T00:14:23.497Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/VirtualizeUtils.js
2025-02-16T00:14:23.516Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/VirtualizedList.js
2025-02-16T00:14:23.563Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/VirtualizedSectionList.js
2025-02-16T00:14:23.655Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/ViewabilityHelper.js
2025-02-16T00:14:23.674Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/VirtualizedListContext.js
2025-02-16T00:14:23.690Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/FillRateHelper.js
2025-02-16T00:14:23.692Z - Transforming: node_modules/react-native/Libraries/ReactNative/AppContainer-prod.js
2025-02-16T00:14:23.697Z - Transforming: node_modules/react-native/Libraries/ReactNative/AppContainer-dev.js
2025-02-16T00:14:23.715Z - Transforming: node_modules/react-native/src/private/specs/components/RCTModalHostViewNativeComponent.js
2025-02-16T00:14:23.738Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeModalManager.js
2025-02-16T00:14:23.753Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeStatusBarManagerIOS.js
2025-02-16T00:14:23.758Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeStatusBarManagerAndroid.js
2025-02-16T00:14:23.773Z - Transforming: node_modules/react-native/src/private/specs/components/RCTInputAccessoryViewNativeComponent.js
2025-02-16T00:14:23.775Z - Transforming: node_modules/react-native/Libraries/StyleSheet/Rect.js
2025-02-16T00:14:23.781Z - Transforming: node_modules/react-native/src/private/specs/components/AndroidSwitchNativeComponent.js
2025-02-16T00:14:23.787Z - Transforming: node_modules/react-native/src/private/specs/components/SwitchNativeComponent.js
2025-02-16T00:14:23.796Z - Transforming: node_modules/react-native/src/private/featureflags/ReactNativeFeatureFlagsBase.js
2025-02-16T00:14:23.806Z - Transforming: node_modules/react-native/Libraries/NativeComponent/ViewConfig.js
2025-02-16T00:14:23.813Z - Transforming: node_modules/react-native/Libraries/Components/TextInput/RCTTextInputViewConfig.js
2025-02-16T00:14:23.814Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeActionSheetManager.js
2025-02-16T00:14:23.815Z - Transforming: node_modules/react-native/Libraries/Animated/createAnimatedComponent.js
2025-02-16T00:14:23.822Z - Transforming: node_modules/react-native/Libraries/Animated/AnimatedEvent.js
2025-02-16T00:14:23.825Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedColor.js
2025-02-16T00:14:23.839Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedInterpolation.js
2025-02-16T00:14:23.840Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedNode.js
2025-02-16T00:14:23.844Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedValue.js
2025-02-16T00:14:23.860Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedValueXY.js
2025-02-16T00:14:23.877Z - Transforming: node_modules/react-native/Libraries/Animated/animations/DecayAnimation.js
2025-02-16T00:14:23.885Z - Transforming: node_modules/react-native/Libraries/Animated/animations/SpringAnimation.js
2025-02-16T00:14:23.892Z - Transforming: node_modules/react-native/Libraries/Animated/animations/TimingAnimation.js
2025-02-16T00:14:23.900Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedAddition.js
2025-02-16T00:14:23.916Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedDiffClamp.js
2025-02-16T00:14:23.922Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedDivision.js
2025-02-16T00:14:23.939Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedMultiplication.js
2025-02-16T00:14:23.948Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedSubtraction.js
2025-02-16T00:14:23.948Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedModulo.js
2025-02-16T00:14:23.960Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedTracking.js
2025-02-16T00:14:23.962Z - Transforming: node_modules/react-native/Libraries/Components/Sound/NativeSoundManager.js
2025-02-16T00:14:23.964Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeAppearance.js
2025-02-16T00:14:23.965Z - Transforming: node_modules/react-native/Libraries/Animated/useAnimatedProps.js
2025-02-16T00:14:23.971Z - Transforming: node_modules/react-native/Libraries/Components/Touchable/PooledClass.js
2025-02-16T00:14:23.972Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeAppState.js
2025-02-16T00:14:23.979Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeDevSettings.js
2025-02-16T00:14:23.979Z - Transforming: node_modules/react-native/Libraries/Alert/NativeAlertManager.js
2025-02-16T00:14:23.983Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeDeviceInfo.js
2025-02-16T00:14:23.985Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeI18nManager.js
2025-02-16T00:14:23.988Z - Transforming: node_modules/react-native/src/private/renderer/errorhandling/ErrorHandlers.js
2025-02-16T00:14:23.991Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeIntentAndroid.js
2025-02-16T00:14:23.991Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeClipboard.js
2025-02-16T00:14:23.995Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeLinkingManager.js
2025-02-16T00:14:23.997Z - Transforming: node_modules/react-native/Libraries/Pressability/PressabilityPerformanceEventEmitter.js
2025-02-16T00:14:23.999Z - Transforming: node_modules/react-native/Libraries/ReactNative/ReactNativeFeatureFlags.js
2025-02-16T00:14:24.001Z - Transforming: node_modules/react-native/Libraries/Pressability/HoverState.js
2025-02-16T00:14:24.001Z - Transforming: node_modules/react-native/Libraries/NativeModules/specs/NativeRedBox.js
2025-02-16T00:14:24.002Z - Transforming: node_modules/react-native/Libraries/BugReporting/NativeBugReporting.js
2025-02-16T00:14:24.004Z - Transforming: node_modules/react-native/Libraries/BugReporting/dumpReactTree.js
2025-02-16T00:14:24.007Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeHeadlessJsTaskSupport.js
2025-02-16T00:14:24.008Z - Transforming: node_modules/react-native/Libraries/Utilities/GlobalPerformanceLogger.js
2025-02-16T00:14:24.008Z - Transforming: node_modules/react-native/Libraries/Utilities/PerformanceLoggerContext.js
2025-02-16T00:14:24.014Z - Transforming: node_modules/react-native/Libraries/ReactNative/getCachedComponentWithDebugName.js
2025-02-16T00:14:24.016Z - Transforming: node_modules/react-native/Libraries/Interaction/NativeFrameRateLogger.js
2025-02-16T00:14:24.016Z - Transforming: node_modules/react-native/Libraries/Components/ScrollView/ScrollContentViewNativeComponent.js
2025-02-16T00:14:24.017Z - Transforming: node_modules/react-native/Libraries/Components/ScrollView/ScrollViewNativeComponent.js
2025-02-16T00:14:24.022Z - Transforming: node_modules/react-native/src/private/components/useSyncOnScroll.js
2025-02-16T00:14:24.022Z - Transforming: node_modules/react-native/Libraries/Components/ScrollView/AndroidHorizontalScrollViewNativeComponent.js
2025-02-16T00:14:24.026Z - Transforming: node_modules/react-native/src/private/specs/components/AndroidHorizontalScrollContentViewNativeComponent.js
2025-02-16T00:14:24.032Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspector.js
2025-02-16T00:14:24.032Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeKeyboardObserver.js
2025-02-16T00:14:24.033Z - Transforming: node_modules/react-native/Libraries/Blob/Blob.js
2025-02-16T00:14:24.038Z - Transforming: node_modules/react-native/Libraries/Utilities/binaryToBase64.js
2025-02-16T00:14:24.039Z - Transforming: node_modules/react-native/Libraries/Network/FormData.js
2025-02-16T00:14:24.044Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeNetworkingIOS.js
2025-02-16T00:14:24.046Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeSettingsManager.js
2025-02-16T00:14:24.047Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeShareModule.js
2025-02-16T00:14:24.049Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processAspectRatio.js
2025-02-16T00:14:24.055Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processBackgroundImage.js
2025-02-16T00:14:24.055Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processBoxShadow.js
2025-02-16T00:14:24.056Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processFilter.js
2025-02-16T00:14:24.068Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processFontVariant.js
2025-02-16T00:14:24.068Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processTransform.js
2025-02-16T00:14:24.073Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processTransformOrigin.js
2025-02-16T00:14:24.084Z - Transforming: node_modules/react-native/Libraries/Utilities/differ/sizesDiffer.js
2025-02-16T00:14:24.085Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeLogBox.js
2025-02-16T00:14:24.088Z - Transforming: node_modules/react-native/src/private/specs/modules/NativePermissionsAndroid.js
2025-02-16T00:14:24.094Z - Transforming: node_modules/react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstanceUtils.js
2025-02-16T00:14:24.097Z - Transforming: node_modules/react-native/Libraries/NativeComponent/NativeComponentRegistryUnstable.js
2025-02-16T00:14:24.097Z - Transforming: node_modules/react-native/Libraries/ReactNative/NativeUIManager.js
2025-02-16T00:14:24.099Z - Transforming: node_modules/react-native/Libraries/ReactNative/UIManagerProperties.js
2025-02-16T00:14:24.101Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeVibration.js
2025-02-16T00:14:24.103Z - Transforming: node_modules/react-native/Libraries/Core/Devtools/parseErrorStack.js
2025-02-16T00:14:24.108Z - Transforming: node_modules/ansi-regex/index.js
2025-02-16T00:14:24.108Z - Transforming: node_modules/react-native/Libraries/ReactPrivate/ReactNativePrivateInterface.js
2025-02-16T00:14:24.109Z - Transforming: node_modules/react-native/src/private/specs/modules/NativePushNotificationManagerIOS.js
2025-02-16T00:14:24.115Z - Transforming: node_modules/react-native/Libraries/Utilities/stringifySafe.js
2025-02-16T00:14:24.117Z - Transforming: node_modules/react-native/src/private/fusebox/FuseboxSessionObserver.js
2025-02-16T00:14:24.117Z - Transforming: node_modules/react-native/Libraries/LogBox/Data/LogBoxLog.js
2025-02-16T00:14:24.124Z - Transforming: node_modules/react-native/Libraries/Core/ExceptionsManager.js
2025-02-16T00:14:24.126Z - Transforming: node_modules/react-native/src/private/specs/modules/NativePlatformConstantsIOS.js
2025-02-16T00:14:24.131Z - Transforming: node_modules/react-native/Libraries/StyleSheet/processColorArray.js
2025-02-16T00:14:24.133Z - Transforming: node_modules/react-native/Libraries/Utilities/differ/matricesDiffer.js
2025-02-16T00:14:24.136Z - Transforming: node_modules/@react-native/normalize-colors/index.js
2025-02-16T00:14:24.136Z - Transforming: node_modules/react-native/Libraries/Utilities/differ/pointsDiffer.js
2025-02-16T00:14:24.137Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/index.js
2025-02-16T00:14:24.148Z - Transforming: src/features/AIAgent/components/AiMessagesBubble.js
2025-02-16T00:14:24.149Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/styles/WorkoutPlanScreenStyles.js
2025-02-16T00:14:24.150Z - Transforming: node_modules/color/index.js
2025-02-16T00:14:24.160Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/PlanDetails/PlanDetailsScreen.js
2025-02-16T00:14:24.170Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/dummydata.js
2025-02-16T00:14:24.180Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/utils/useIsKeyboardShown.tsx
2025-02-16T00:14:24.182Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/views/BottomTabItem.tsx
2025-02-16T00:14:24.190Z - Transforming: node_modules/@react-navigation/drawer/src/utils/DrawerPositionContext.tsx
2025-02-16T00:14:24.199Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/views/ScreenFallback.tsx
2025-02-16T00:14:24.213Z - Transforming: src/features/WorkoutDiary/CreatePlan/styles/CreateEditContentStyles.js
2025-02-16T00:14:24.216Z - Transforming: src/features/WorkoutDiary/CreatePlan/NewPlan/NewProgramScreen.js
2025-02-16T00:14:24.219Z - Transforming: src/features/WorkoutDiary/CreatePlan/NewPlan/NewWorkoutScreen.js
2025-02-16T00:14:24.224Z - Transforming: src/features/WorkoutDiary/CreatePlan/EditPlan/EditPlanScreen.js
2025-02-16T00:14:24.236Z - Transforming: node_modules/@react-navigation/drawer/src/utils/addCancelListener.native.tsx
2025-02-16T00:14:24.245Z - Transforming: node_modules/@react-navigation/drawer/src/views/GestureHandler.ios.tsx
2025-02-16T00:14:24.250Z - Transforming: node_modules/@react-navigation/drawer/src/views/ScreenFallback.tsx
2025-02-16T00:14:24.256Z - Transforming: node_modules/@react-navigation/drawer/src/views/legacy/Drawer.tsx
2025-02-16T00:14:24.263Z - Transforming: node_modules/@react-navigation/drawer/src/views/modern/Drawer.tsx
2025-02-16T00:14:24.276Z - Transforming: node_modules/expo-image-picker/build/ImagePicker.js
2025-02-16T00:14:24.283Z - Transforming: src/components/CustomImageUploadAlert.js
2025-02-16T00:14:24.309Z - Transforming: node_modules/@react-native-community/datetimepicker/src/index.js
2025-02-16T00:14:24.315Z - Transforming: node_modules/react-native-modal/dist/index.js
2025-02-16T00:14:24.319Z - Transforming: src/features/SocialMedia/Messages/components/styles/addFriendModalStyles.js
2025-02-16T00:14:24.328Z - Transforming: node_modules/lodash.debounce/index.js
2025-02-16T00:14:24.333Z - Transforming: src/features/SocialMedia/Messages/components/UserOptionsModal.js
2025-02-16T00:14:24.338Z - Transforming: node_modules/@expo/vector-icons/AntDesign.js
2025-02-16T00:14:24.359Z - Transforming: node_modules/react-native-svg/src/index.ts
2025-02-16T00:14:24.362Z - Transforming: node_modules/prop-types/index.js
2025-02-16T00:14:24.367Z - Transforming: node_modules/react-native-animatable/index.js
2025-02-16T00:14:24.368Z - Transforming: node_modules/react-native-circular-progress/index.js
2025-02-16T00:14:24.372Z - Transforming: src/features/FoodDiary/components/styles/mealSectionCustomizationModalStyles.js
2025-02-16T00:14:24.379Z - Transforming: src/features/FoodDiary/components/SwipeableFoodEntryItem.js
2025-02-16T00:14:24.385Z - Transforming: src/features/FoodDiary/components/styles/foodEntryModalStyles.js
2025-02-16T00:14:24.393Z - Transforming: src/features/FoodDiary/api/EdamamFoodDB/edamamMethods.js
2025-02-16T00:14:24.411Z - Transforming: src/features/FoodDiary/components/BarcodeScanner.js
2025-02-16T00:14:24.441Z - Transforming: src/features/FoodDiary/components/DigitalFoodScanner.js
2025-02-16T00:14:24.449Z - Transforming: src/features/FoodDiary/components/styles/dailyNutritionGoalsCustomizationModalStyles.js
2025-02-16T00:14:24.459Z - Transforming: src/features/FoodDiary/components/NutritionGoalsAlertModal.js
2025-02-16T00:14:24.464Z - Transforming: src/features/FoodDiary/components/MacroSettingsModal.js
2025-02-16T00:14:24.466Z - Transforming: src/features/FoodDiary/components/CaloriesAndWaterProgressSection.js
2025-02-16T00:14:24.476Z - Transforming: src/features/FoodDiary/components/MacroProgressSection.js
2025-02-16T00:14:24.485Z - Transforming: src/features/FoodDiary/components/DailyNutritionPerformanceSummary.js.js
2025-02-16T00:14:24.492Z - Transforming: node_modules/react-native-calendars/src/index.ts
2025-02-16T00:14:24.501Z - Transforming: node_modules/@react-native-picker/picker/js/index.js
2025-02-16T00:14:24.503Z - Transforming: src/hooks/useUpgrade.js
2025-02-16T00:14:24.510Z - Transforming: src/features/NutritionalProgram/views/components/CategoriesGrid.js
2025-02-16T00:14:24.511Z - Transforming: src/features/NutritionalProgram/components/ProgramTimelineLineChart.js
2025-02-16T00:14:24.513Z - Transforming: src/features/NutritionalProgram/views/components/ProgramCard.js
2025-02-16T00:14:24.517Z - Transforming: src/features/FoodDiary/components/styles/dailyNutritionGoalsCalculationModalStyles.js
2025-02-16T00:14:24.529Z - Transforming: src/features/NutritionalProgram/components/ProgressPicture.js
2025-02-16T00:14:24.542Z - Transforming: node_modules/lodash.isequal/index.js
2025-02-16T00:14:24.547Z - Transforming: node_modules/react-native-modal-datetime-picker/src/DateTimePickerModal.ios.js
2025-02-16T00:14:24.548Z - Transforming: src/features/NutritionalProgram/components/GeneratedMealPlanView.js
2025-02-16T00:14:24.558Z - Transforming: node_modules/@callstack/react-theme-provider/lib/createTheming.js
2025-02-16T00:14:24.559Z - Transforming: node_modules/dayjs/plugin/customParseFormat.js
2025-02-16T00:14:24.568Z - Transforming: node_modules/color-string/index.js
2025-02-16T00:14:24.577Z - Transforming: node_modules/color-convert/index.js
2025-02-16T00:14:24.584Z - Transforming: node_modules/react-native-paper/src/components/Portal/PortalManager.tsx
2025-02-16T00:14:24.594Z - Transforming: src/features/FoodDiary/components/styles/foodNutrientModalStyles.js
2025-02-16T00:14:24.598Z - Transforming: node_modules/react-native-paper/src/components/List/utils.ts
2025-02-16T00:14:24.603Z - Transforming: node_modules/react-native-paper/node_modules/use-latest-callback/lib/src/useIsomorphicLayoutEffect.native.js
2025-02-16T00:14:24.606Z - Transforming: node_modules/react-native-paper/src/components/Checkbox/utils.ts
2025-02-16T00:14:24.608Z - Transforming: node_modules/react-native-safe-area-context/src/SafeAreaContext.tsx
2025-02-16T00:14:24.609Z - Transforming: node_modules/react-native-safe-area-context/src/SafeAreaView.tsx
2025-02-16T00:14:24.614Z - Transforming: node_modules/react-native-safe-area-context/src/InitialWindow.native.ts
2025-02-16T00:14:24.618Z - Transforming: node_modules/react-native-safe-area-context/src/SafeArea.types.ts
2025-02-16T00:14:24.620Z - Transforming: node_modules/react-native-css-interop/dist/doctor.native.js
2025-02-16T00:14:24.622Z - Transforming: node_modules/react-native-css-interop/dist/runtime/index.native.js
2025-02-16T00:14:24.624Z - Transforming: node_modules/react-native-paper/src/components/RadioButton/utils.ts
2025-02-16T00:14:24.625Z - Transforming: node_modules/react-native-paper/src/components/BottomNavigation/utils.ts
2025-02-16T00:14:24.627Z - Transforming: node_modules/react-native-paper/src/utils/useIsKeyboardShown.tsx
2025-02-16T00:14:24.630Z - Transforming: node_modules/react-native-paper/src/utils/useLayout.tsx
2025-02-16T00:14:24.632Z - Transforming: node_modules/react-native-paper/src/components/Appbar/AppbarBackIcon.tsx
2025-02-16T00:14:24.635Z - Transforming: node_modules/react-native-paper/src/components/Appbar/utils.ts
2025-02-16T00:14:24.639Z - Transforming: node_modules/react-native-chart-kit/dist/index.js
2025-02-16T00:14:24.645Z - Transforming: node_modules/react-native-paper/src/components/ToggleButton/utils.ts
2025-02-16T00:14:24.649Z - Transforming: node_modules/react-native-paper/src/components/Typography/v2/Text.tsx
2025-02-16T00:14:24.650Z - Transforming: node_modules/react-native-paper/src/components/Menu/utils.ts
2025-02-16T00:14:24.657Z - Transforming: node_modules/axios/lib/utils.js
2025-02-16T00:14:24.659Z - Transforming: node_modules/axios/lib/helpers/bind.js
2025-02-16T00:14:24.662Z - Transforming: node_modules/axios/lib/core/Axios.js
2025-02-16T00:14:24.674Z - Transforming: node_modules/axios/lib/core/mergeConfig.js
2025-02-16T00:14:24.675Z - Transforming: node_modules/axios/lib/defaults/index.js
2025-02-16T00:14:24.687Z - Transforming: node_modules/axios/lib/helpers/formDataToJSON.js
2025-02-16T00:14:24.688Z - Transforming: node_modules/axios/lib/cancel/CanceledError.js
2025-02-16T00:14:24.696Z - Transforming: node_modules/axios/lib/cancel/CancelToken.js
2025-02-16T00:14:24.699Z - Transforming: node_modules/axios/lib/cancel/isCancel.js
2025-02-16T00:14:24.699Z - Transforming: node_modules/axios/lib/env/data.js
2025-02-16T00:14:24.703Z - Transforming: node_modules/axios/lib/helpers/toFormData.js
2025-02-16T00:14:24.718Z - Transforming: node_modules/axios/lib/core/AxiosError.js
2025-02-16T00:14:24.718Z - Transforming: node_modules/axios/lib/helpers/spread.js
2025-02-16T00:14:24.719Z - Transforming: node_modules/axios/lib/helpers/isAxiosError.js
2025-02-16T00:14:24.723Z - Transforming: node_modules/axios/lib/adapters/adapters.js
2025-02-16T00:14:24.724Z - Transforming: node_modules/axios/lib/core/AxiosHeaders.js
2025-02-16T00:14:24.732Z - Transforming: node_modules/axios/lib/helpers/HttpStatusCode.js
2025-02-16T00:14:24.735Z - Transforming: node_modules/expo-sensors/build/ExponentAccelerometer.js
2025-02-16T00:14:24.740Z - Transforming: node_modules/expo-sensors/build/ExponentPedometer.js
2025-02-16T00:14:24.741Z - Transforming: node_modules/expo-sensors/build/ExpoBarometer.js
2025-02-16T00:14:24.742Z - Transforming: node_modules/expo-sensors/build/ExponentGyroscope.js
2025-02-16T00:14:24.744Z - Transforming: node_modules/expo-sensors/build/ExponentMagnetometer.js
2025-02-16T00:14:24.745Z - Transforming: node_modules/expo-sensors/build/ExponentDeviceMotion.js
2025-02-16T00:14:24.745Z - Transforming: node_modules/react-native-uuid/dist/regex.js
2025-02-16T00:14:24.748Z - Transforming: node_modules/react-native-uuid/dist/v35.js
2025-02-16T00:14:24.748Z - Transforming: node_modules/expo-sensors/build/ExponentMagnetometerUncalibrated.js
2025-02-16T00:14:24.748Z - Transforming: node_modules/react-native-uuid/dist/md5.js
2025-02-16T00:14:24.758Z - Transforming: node_modules/react-native-uuid/dist/rng.js
2025-02-16T00:14:24.758Z - Transforming: node_modules/react-native-uuid/dist/sha1.js
2025-02-16T00:14:24.765Z - Transforming: node_modules/react-native-uuid/dist/stringify.js
2025-02-16T00:14:24.770Z - Transforming: node_modules/expo-sensors/build/ExpoLightSensor.ios.js
2025-02-16T00:14:24.774Z - Transforming: node_modules/react-native-paper/src/utils/useLazyRef.tsx
2025-02-16T00:14:24.774Z - Transforming: node_modules/react-native-paper/src/components/TextInput/constants.tsx
2025-02-16T00:14:24.782Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Adornment/utils.ts
2025-02-16T00:14:24.783Z - Transforming: node_modules/react-native-paper/src/components/TextInput/helpers.tsx
2025-02-16T00:14:24.784Z - Transforming: node_modules/event-target-shim/dist/event-target-shim.mjs
2025-02-16T00:14:24.791Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Adornment/enums.tsx
2025-02-16T00:14:24.799Z - Transforming: node_modules/assert/build/assert.js
2025-02-16T00:14:24.801Z - Transforming: node_modules/react-native-css-interop/dist/shared.js
2025-02-16T00:14:24.816Z - Transforming: node_modules/react-native-css-interop/dist/runtime/observable.js
2025-02-16T00:14:24.821Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/appearance-observables.js
2025-02-16T00:14:24.823Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/globals.js
2025-02-16T00:14:24.825Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/unit-observables.js
2025-02-16T00:14:24.828Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/native-interop.js
2025-02-16T00:14:24.829Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/unwrap-components.js
2025-02-16T00:14:24.835Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/stylesheet.js
2025-02-16T00:14:24.841Z - Transforming: node_modules/react-native-css-interop/dist/runtime/config.js
2025-02-16T00:14:24.841Z - Transforming: node_modules/@babel/runtime/helpers/objectDestructuringEmpty.js
2025-02-16T00:14:24.850Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Adornment/TextInputAdornment.tsx
2025-02-16T00:14:24.851Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Addons/Outline.tsx
2025-02-16T00:14:24.851Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Label/LabelBackground.tsx
2025-02-16T00:14:24.860Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Addons/Underline.tsx
2025-02-16T00:14:24.861Z - Transforming: node_modules/react-native-paper/src/components/TextInput/Label/InputLabel.tsx
2025-02-16T00:14:24.863Z - Transforming: node_modules/@react-navigation/core/src/Group.tsx
2025-02-16T00:14:24.869Z - Transforming: node_modules/@react-navigation/core/src/Screen.tsx
2025-02-16T00:14:24.872Z - Transforming: node_modules/@react-navigation/native/src/extractPathFromURL.tsx
2025-02-16T00:14:24.877Z - Transforming: node_modules/@react-navigation/core/src/useRouteCache.tsx
2025-02-16T00:14:24.881Z - Transforming: node_modules/nanoid/non-secure/index.js
2025-02-16T00:14:24.883Z - Transforming: node_modules/use-latest-callback/lib/src/index.js
2025-02-16T00:14:24.887Z - Transforming: node_modules/@react-navigation/core/src/fromEntries.tsx
2025-02-16T00:14:24.887Z - Transforming: node_modules/query-string/index.js
2025-02-16T00:14:24.887Z - Transforming: node_modules/@react-navigation/core/src/checkDuplicateRouteNames.tsx
2025-02-16T00:14:24.891Z - Transforming: node_modules/@react-navigation/core/src/checkSerializable.tsx
2025-02-16T00:14:24.892Z - Transforming: node_modules/@react-navigation/core/src/EnsureSingleNavigator.tsx
2025-02-16T00:14:24.892Z - Transforming: node_modules/@react-navigation/core/src/NavigationBuilderContext.tsx
2025-02-16T00:14:24.895Z - Transforming: node_modules/@react-navigation/core/src/NavigationStateContext.tsx
2025-02-16T00:14:24.902Z - Transforming: node_modules/@react-navigation/core/src/useChildListeners.tsx
2025-02-16T00:14:24.902Z - Transforming: node_modules/@react-navigation/core/src/UnhandledActionContext.tsx
2025-02-16T00:14:24.903Z - Transforming: node_modules/@react-navigation/core/src/useEventEmitter.tsx
2025-02-16T00:14:24.904Z - Transforming: node_modules/@react-navigation/core/src/useKeyedChildListeners.tsx
2025-02-16T00:14:24.908Z - Transforming: node_modules/@react-navigation/core/src/useOptionsGetters.tsx
2025-02-16T00:14:24.912Z - Transforming: node_modules/@react-navigation/core/src/useSyncState.tsx
2025-02-16T00:14:24.918Z - Transforming: node_modules/@react-navigation/core/src/useScheduleUpdate.tsx
2025-02-16T00:14:24.920Z - Transforming: node_modules/escape-string-regexp/index.js
2025-02-16T00:14:24.923Z - Transforming: node_modules/@babel/runtime/helpers/isNativeFunction.js
2025-02-16T00:14:24.929Z - Transforming: node_modules/react-native/Libraries/NativeComponent/StaticViewConfigValidator.js
2025-02-16T00:14:24.930Z - Transforming: node_modules/expo-modules-core/src/uuid/lib/bytesToUuid.ts
2025-02-16T00:14:24.930Z - Transforming: node_modules/expo-modules-core/src/uuid/uuid.types.ts
2025-02-16T00:14:24.936Z - Transforming: node_modules/@react-navigation/routers/src/BaseRouter.tsx
2025-02-16T00:14:24.937Z - Transforming: node_modules/react-native/Libraries/Renderer/implementations/ReactFabric-dev.js
2025-02-16T00:14:24.942Z - Transforming: node_modules/@react-navigation/routers/src/DrawerRouter.tsx
2025-02-16T00:14:24.943Z - Transforming: node_modules/@react-navigation/routers/src/StackRouter.tsx
2025-02-16T00:14:24.949Z - Transforming: node_modules/@react-navigation/routers/src/TabRouter.tsx
2025-02-16T00:14:24.958Z - Transforming: node_modules/@react-navigation/routers/src/types.tsx
2025-02-16T00:14:24.964Z - Transforming: node_modules/@react-navigation/routers/src/CommonActions.tsx
2025-02-16T00:14:24.974Z - Transforming: node_modules/react-native/src/private/webapis/dom/nodes/ReactNativeElement.js
2025-02-16T00:14:24.982Z - Transforming: node_modules/react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricHostComponent.js
2025-02-16T00:14:24.992Z - Transforming: node_modules/react-native/src/private/webapis/dom/nodes/ReadOnlyText.js
2025-02-16T00:14:25.000Z - Transforming: node_modules/react-native/Libraries/Renderer/implementations/ReactNativeRenderer-dev.js
2025-02-16T00:14:25.019Z - Transforming: node_modules/whatwg-url-without-unicode/index.js
2025-02-16T00:14:25.032Z - Transforming: node_modules/@react-navigation/core/src/isArrayEqual.tsx
2025-02-16T00:14:25.043Z - Transforming: node_modules/@react-navigation/core/src/isRecordEqual.tsx
2025-02-16T00:14:25.043Z - Transforming: node_modules/@react-navigation/core/src/useComponent.tsx
2025-02-16T00:14:25.045Z - Transforming: node_modules/@react-navigation/core/src/useCurrentRender.tsx
2025-02-16T00:14:25.051Z - Transforming: node_modules/@react-navigation/core/src/useDescriptors.tsx
2025-02-16T00:14:25.082Z - Transforming: node_modules/@react-navigation/core/src/useFocusedListenersChildrenAdapter.tsx
2025-02-16T00:14:25.083Z - Transforming: node_modules/@react-navigation/core/src/useNavigationHelpers.tsx
2025-02-16T00:14:25.098Z - Transforming: node_modules/@react-navigation/core/src/useOnGetState.tsx
2025-02-16T00:14:25.100Z - Transforming: node_modules/@react-navigation/core/src/useOnRouteFocus.tsx
2025-02-16T00:14:25.102Z - Transforming: node_modules/@react-navigation/core/src/useRegisterNavigator.tsx
2025-02-16T00:14:25.118Z - Transforming: node_modules/@react-navigation/core/src/useOnAction.tsx
2025-02-16T00:14:25.118Z - Transforming: node_modules/@react-navigation/core/src/useFocusEvents.tsx
2025-02-16T00:14:25.118Z - Transforming: node_modules/expo-asset/build/AssetSourceResolver.native.js
2025-02-16T00:14:25.135Z - Transforming: node_modules/react-native-reanimated/src/publicGlobals.ts
2025-02-16T00:14:25.140Z - Transforming: node_modules/react-native-reanimated/src/Animated.ts
2025-02-16T00:14:25.149Z - Transforming: node_modules/react-native-reanimated/src/ConfigHelper.ts
2025-02-16T00:14:25.158Z - Transforming: node_modules/react-native-reanimated/src/logger/index.ts
2025-02-16T00:14:25.160Z - Transforming: node_modules/react-native-reanimated/src/core.ts
2025-02-16T00:14:25.163Z - Transforming: node_modules/react-native-reanimated/src/hook/index.ts
2025-02-16T00:14:25.168Z - Transforming: node_modules/react-native-reanimated/src/animation/index.ts
2025-02-16T00:14:25.177Z - Transforming: node_modules/react-native-reanimated/src/interpolation.ts
2025-02-16T00:14:25.194Z - Transforming: node_modules/react-native-reanimated/src/interpolateColor.ts
2025-02-16T00:14:25.203Z - Transforming: node_modules/react-native-reanimated/src/Easing.ts
2025-02-16T00:14:25.261Z - Transforming: node_modules/react-native-reanimated/src/platformFunctions/index.ts
2025-02-16T00:14:25.266Z - Transforming: node_modules/react-native-reanimated/src/Colors.ts
2025-02-16T00:14:25.293Z - Transforming: node_modules/react-native-reanimated/src/PropAdapters.ts
2025-02-16T00:14:25.300Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/index.ts
2025-02-16T00:14:25.307Z - Transforming: node_modules/react-native-reanimated/src/isSharedValue.ts
2025-02-16T00:14:25.315Z - Transforming: node_modules/react-native-reanimated/src/commonTypes.ts
2025-02-16T00:14:25.334Z - Transforming: node_modules/react-native-reanimated/src/pluginUtils.ts
2025-02-16T00:14:25.343Z - Transforming: node_modules/react-native-reanimated/src/jestUtils.ts
2025-02-16T00:14:25.360Z - Transforming: node_modules/react-native-reanimated/src/component/LayoutAnimationConfig.tsx
2025-02-16T00:14:25.379Z - Transforming: node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx
2025-02-16T00:14:25.398Z - Transforming: node_modules/react-native-reanimated/src/component/ReducedMotionConfig.tsx
2025-02-16T00:14:25.407Z - Transforming: node_modules/react-native-reanimated/src/screenTransition/index.ts
2025-02-16T00:14:25.410Z - Transforming: node_modules/react-native-reanimated/src/mappers.ts
2025-02-16T00:14:25.412Z - Transforming: node_modules/@react-navigation/elements/src/Background.tsx
2025-02-16T00:14:25.425Z - Transforming: node_modules/@react-navigation/elements/src/Header/getDefaultHeaderHeight.tsx
2025-02-16T00:14:25.434Z - Transforming: node_modules/@react-navigation/elements/src/Header/getHeaderTitle.tsx
2025-02-16T00:14:25.439Z - Transforming: node_modules/@react-navigation/elements/src/Header/Header.tsx
2025-02-16T00:14:25.471Z - Transforming: node_modules/@react-navigation/elements/src/Header/HeaderBackButton.tsx
2025-02-16T00:14:25.490Z - Transforming: node_modules/@react-navigation/elements/src/Header/HeaderBackContext.tsx
2025-02-16T00:14:25.494Z - Transforming: node_modules/@react-navigation/elements/src/Header/HeaderBackground.tsx
2025-02-16T00:14:25.502Z - Transforming: node_modules/@react-navigation/elements/src/Header/HeaderHeightContext.tsx
2025-02-16T00:14:25.506Z - Transforming: node_modules/@react-navigation/elements/src/Header/HeaderShownContext.tsx
2025-02-16T00:14:25.510Z - Transforming: node_modules/@react-navigation/elements/src/Header/HeaderTitle.tsx
2025-02-16T00:14:25.515Z - Transforming: node_modules/@react-navigation/elements/src/Header/useHeaderHeight.tsx
2025-02-16T00:14:25.520Z - Transforming: node_modules/@react-navigation/elements/src/MissingIcon.tsx
2025-02-16T00:14:25.527Z - Transforming: node_modules/@react-navigation/elements/src/PlatformPressable.tsx
2025-02-16T00:14:25.533Z - Transforming: node_modules/@react-navigation/elements/src/ResourceSavingView.tsx
2025-02-16T00:14:25.539Z - Transforming: node_modules/@react-navigation/elements/src/SafeAreaProviderCompat.tsx
2025-02-16T00:14:25.543Z - Transforming: node_modules/@react-navigation/elements/src/Screen.tsx
2025-02-16T00:14:25.544Z - Transforming: node_modules/@react-navigation/elements/src/types.tsx
2025-02-16T00:14:25.560Z - Transforming: node_modules/@react-navigation/stack/src/utils/memoize.tsx
2025-02-16T00:14:25.572Z - Transforming: node_modules/expo-image/src/ImageModule.ts
2025-02-16T00:14:25.574Z - Transforming: node_modules/expo-image/src/ExpoImage.tsx
2025-02-16T00:14:25.576Z - Transforming: node_modules/expo-image/src/utils/resolveSources.tsx
2025-02-16T00:14:25.576Z - Transforming: node_modules/expo-image/src/utils.ts
2025-02-16T00:14:25.623Z - Transforming: node_modules/@expo/vector-icons/build/createIconSetFromFontAwesome5.js
2025-02-16T00:14:25.635Z - Transforming: node_modules/@expo/vector-icons/build/createIconSetFromFontAwesome6.js
2025-02-16T00:14:25.652Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useCarouselController.tsx
2025-02-16T00:14:25.652Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useAutoPlay.ts
2025-02-16T00:14:25.652Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useCommonVariables.ts
2025-02-16T00:14:25.655Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useInitProps.ts
2025-02-16T00:14:25.665Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useLayoutConfig.ts
2025-02-16T00:14:25.675Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/usePropsErrorBoundary.ts
2025-02-16T00:14:25.675Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useOnProgressChange.ts
2025-02-16T00:14:25.680Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useVisibleRanges.tsx
2025-02-16T00:14:25.683Z - Transforming: node_modules/react-native-reanimated-carousel/src/layouts/BaseLayout.tsx
2025-02-16T00:14:25.697Z - Transforming: node_modules/react-native-reanimated-carousel/src/store/index.ts
2025-02-16T00:14:25.699Z - Transforming: node_modules/react-native-reanimated-carousel/src/ScrollViewGesture.tsx
2025-02-16T00:14:25.701Z - Transforming: node_modules/react-native-reanimated-carousel/src/utils/computedWithAutoFillData.ts
2025-02-16T00:14:25.755Z - Transforming: node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/create-icon-set-from-fontello.js
2025-02-16T00:14:25.758Z - Transforming: node_modules/@react-navigation/stack/src/views/GestureHandlerNative.tsx
2025-02-16T00:14:25.760Z - Transforming: node_modules/expo-font/build/index.js
2025-02-16T00:14:25.762Z - Transforming: node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/create-icon-set.js
2025-02-16T00:14:25.765Z - Transforming: node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/icon-button.js
2025-02-16T00:14:25.767Z - Transforming: node_modules/react-native/Libraries/vendor/core/ErrorUtils.js
2025-02-16T00:14:25.776Z - Transforming: node_modules/@react-native-community/slider/dist/index.js
2025-02-16T00:14:25.780Z - Transforming: node_modules/@react-native-community/slider/dist/components/StepsIndicator.js
2025-02-16T00:14:25.782Z - Transforming: node_modules/@react-native-community/slider/dist/utils/styles.js
2025-02-16T00:14:25.794Z - Transforming: node_modules/@react-native-community/slider/dist/utils/constants.js
2025-02-16T00:14:25.799Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeSourceCode.js
2025-02-16T00:14:25.802Z - Transforming: node_modules/@react-native/assets-registry/path-support.js
2025-02-16T00:14:25.807Z - Transforming: node_modules/@react-navigation/stack/src/utils/findLastIndex.tsx
2025-02-16T00:14:25.811Z - Transforming: node_modules/@react-navigation/stack/src/utils/getDistanceForDirection.tsx
2025-02-16T00:14:25.816Z - Transforming: node_modules/@react-navigation/stack/src/views/Screens.tsx
2025-02-16T00:14:25.820Z - Transforming: node_modules/@react-navigation/stack/src/views/Stack/CardContainer.tsx
2025-02-16T00:14:25.821Z - Transforming: node_modules/@react-navigation/stack/src/views/Stack/Card.tsx
2025-02-16T00:14:25.853Z - Transforming: node_modules/@firebase/firestore/node_modules/@firebase/component/dist/esm/index.esm2017.js
2025-02-16T00:14:25.862Z - Transforming: node_modules/@firebase/webchannel-wrapper/dist/bloom-blob/esm/bloom_blob_es2018.js
2025-02-16T00:14:25.899Z - Transforming: node_modules/@firebase/webchannel-wrapper/dist/webchannel-blob/esm/webchannel_blob_es2018.js
2025-02-16T00:14:25.941Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/ListMetricsAggregator.js
2025-02-16T00:14:25.942Z - Transforming: node_modules/react-native/Libraries/Debugging/DebuggingOverlay.js
2025-02-16T00:14:25.960Z - Transforming: node_modules/react-native/Libraries/Debugging/useSubscribeToDebuggingOverlayRegistry.js
2025-02-16T00:14:25.966Z - Transforming: node_modules/react-native/Libraries/LogBox/LogBoxNotificationContainer.js
2025-02-16T00:14:25.967Z - Transforming: node_modules/react-native/Libraries/Inspector/Inspector.js
2025-02-16T00:14:25.971Z - Transforming: node_modules/react-native/Libraries/Inspector/ReactDevToolsOverlay.js
2025-02-16T00:14:25.983Z - Transforming: node_modules/react-native/src/private/featureflags/specs/NativeReactNativeFeatureFlags.js
2025-02-16T00:14:25.995Z - Transforming: node_modules/react-native/Libraries/NativeComponent/PlatformBaseViewConfig.js
2025-02-16T00:14:25.997Z - Transforming: node_modules/react-native/src/private/animated/NativeAnimatedHelper.js
2025-02-16T00:14:25.999Z - Transforming: node_modules/react-native/src/private/animated/NativeAnimatedValidation.js
2025-02-16T00:14:26.000Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedWithChildren.js
2025-02-16T00:14:26.008Z - Transforming: node_modules/react-native/Libraries/Animated/animations/Animation.js
2025-02-16T00:14:26.043Z - Transforming: node_modules/react-native/Libraries/Animated/SpringConfig.js
2025-02-16T00:14:26.043Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeSoundManager.js
2025-02-16T00:14:26.047Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeAlertManager.js
2025-02-16T00:14:26.051Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeBugReporting.js
2025-02-16T00:14:26.053Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeRedBox.js
2025-02-16T00:14:26.056Z - Transforming: node_modules/base64-js/index.js
2025-02-16T00:14:26.057Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeFrameRateLogger.js
2025-02-16T00:14:26.060Z - Transforming: node_modules/react-native/Libraries/Blob/BlobManager.js
2025-02-16T00:14:26.070Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorFooter.js
2025-02-16T00:14:26.072Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorBody.js
2025-02-16T00:14:26.089Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxStyle.js
2025-02-16T00:14:26.091Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorHeader.js
2025-02-16T00:14:26.093Z - Transforming: node_modules/@react-native/virtualized-lists/Interaction/Batchinator.js
2025-02-16T00:14:26.106Z - Transforming: node_modules/@react-native/virtualized-lists/Utilities/clamp.js
2025-02-16T00:14:26.110Z - Transforming: node_modules/@react-native/virtualized-lists/Utilities/infoLog.js
2025-02-16T00:14:26.110Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/CellRenderMask.js
2025-02-16T00:14:26.113Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/ChildListCollection.js
2025-02-16T00:14:26.114Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/StateSafePureComponent.js
2025-02-16T00:14:26.123Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/VirtualizedListCellRenderer.js
2025-02-16T00:14:26.154Z - Transforming: node_modules/@react-native/virtualized-lists/Lists/VirtualizedListProps.js
2025-02-16T00:14:26.159Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeUIManager.js
2025-02-16T00:14:26.164Z - Transforming: node_modules/stacktrace-parser/dist/stack-trace-parser.cjs.js
2025-02-16T00:14:26.168Z - Transforming: node_modules/react-native/Libraries/Core/Devtools/parseHermesStack.js
2025-02-16T00:14:26.173Z - Transforming: node_modules/react-native/Libraries/EventEmitter/RCTEventEmitter.js
2025-02-16T00:14:26.174Z - Transforming: node_modules/react-native/Libraries/Core/ReactFiberErrorDialog.js
2025-02-16T00:14:26.177Z - Transforming: node_modules/react-native/Libraries/Core/RawEventEmitter.js
2025-02-16T00:14:26.180Z - Transforming: node_modules/react-native/Libraries/Events/CustomEvent.js
2025-02-16T00:14:26.181Z - Transforming: node_modules/react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactNativeAttributePayload.js
2025-02-16T00:14:26.187Z - Transforming: node_modules/react-native/Libraries/Utilities/useRefEffect.js
2025-02-16T00:14:26.194Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedProps.js
2025-02-16T00:14:26.203Z - Transforming: node_modules/react-native/Libraries/Core/NativeExceptionsManager.js
2025-02-16T00:14:26.206Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/TabBar.js
2025-02-16T00:14:26.215Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/TabBarIndicator.js
2025-02-16T00:14:26.215Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/SceneMap.js
2025-02-16T00:14:26.236Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/TabBarItem.js
2025-02-16T00:14:26.240Z - Transforming: node_modules/react-native/Libraries/LogBox/Data/LogBoxSymbolication.js
2025-02-16T00:14:26.249Z - Transforming: node_modules/react-native-screens/src/index.tsx
2025-02-16T00:14:26.249Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/TabView.js
2025-02-16T00:14:26.262Z - Transforming: node_modules/@expo/vector-icons/MaterialIcons.js
2025-02-16T00:14:26.265Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/PlanDetails/styles/PlanDetailScreenStyles.js
2025-02-16T00:14:26.265Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/views/TabBarIcon.tsx
2025-02-16T00:14:26.272Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/PlanDetails/DayWorkoutDetailScreen.js
2025-02-16T00:14:26.273Z - Transforming: node_modules/color/node_modules/color-convert/index.js
2025-02-16T00:14:26.281Z - Transforming: node_modules/@react-navigation/drawer/src/views/GestureHandlerNative.tsx
2025-02-16T00:14:26.282Z - Transforming: src/features/WorkoutDiary/CreatePlan/EditPlan/styles/EditPlanScreenStyles.js
2025-02-16T00:14:26.288Z - Transforming: src/features/WorkoutDiary/CreatePlan/EditPlan/EditDaysScreen.js
2025-02-16T00:14:26.290Z - Transforming: node_modules/expo-image-picker/build/ExponentImagePicker.js
2025-02-16T00:14:26.290Z - Transforming: node_modules/expo-image-picker/build/utils.js
2025-02-16T00:14:26.295Z - Transforming: node_modules/@react-native-community/datetimepicker/src/datetimepicker.ios.js
2025-02-16T00:14:26.295Z - Transforming: node_modules/expo-image-picker/build/ImagePicker.types.js
2025-02-16T00:14:26.301Z - Transforming: node_modules/@react-native-community/datetimepicker/src/eventCreators.js
2025-02-16T00:14:26.305Z - Transforming: node_modules/@react-native-community/datetimepicker/src/DateTimePickerAndroid.js
2025-02-16T00:14:26.309Z - Transforming: node_modules/react-native-modal/dist/modal.js
2025-02-16T00:14:26.309Z - Transforming: src/features/WorkoutDiary/CreatePlan/NewPlan/styles/NewProgramScreenStyles.js
2025-02-16T00:14:26.323Z - Transforming: src/features/WorkoutDiary/CreatePlan/NewPlan/DayWorkouts/NewProgramWorkoutScreen.js
2025-02-16T00:14:26.325Z - Transforming: src/features/WorkoutDiary/components/AddWorkoutModal.js
2025-02-16T00:14:26.348Z - Transforming: src/features/WorkoutDiary/CreatePlan/NewPlan/styles/NewWorkoutScreenStyles.js
2025-02-16T00:14:26.366Z - Transforming: node_modules/prop-types/factoryWithTypeCheckers.js
2025-02-16T00:14:26.388Z - Transforming: node_modules/prop-types/factoryWithThrowingShims.js
2025-02-16T00:14:26.397Z - Transforming: node_modules/react-native-svg/src/ReactNativeSVG.ts
2025-02-16T00:14:26.410Z - Transforming: node_modules/react-native-circular-progress/src/CircularProgress.js
2025-02-16T00:14:26.414Z - Transforming: node_modules/react-native-circular-progress/src/AnimatedCircularProgress.js
2025-02-16T00:14:26.415Z - Transforming: node_modules/react-native-animatable/registry.js
2025-02-16T00:14:26.422Z - Transforming: node_modules/react-native-animatable/createAnimatableComponent.js
2025-02-16T00:14:26.442Z - Transforming: node_modules/react-native-animatable/definitions/index.js
2025-02-16T00:14:26.448Z - Transforming: node_modules/react-native-animatable/createAnimation.js
2025-02-16T00:14:26.452Z - Transforming: node_modules/@react-navigation/drawer/src/views/modern/Overlay.tsx
2025-02-16T00:14:26.453Z - Transforming: src/features/FoodDiary/components/styles/swipeableFoodEntryItemStyles.js
2025-02-16T00:14:26.457Z - Transforming: node_modules/expo-camera/build/index.js
2025-02-16T00:14:26.460Z - Transforming: src/features/FoodDiary/components/styles/digitalFoodScannerStyles.js
2025-02-16T00:14:26.473Z - Transforming: src/components/MacroPercentageSlider.js
2025-02-16T00:14:26.475Z - Transforming: src/features/FoodDiary/components/styles/barcodeScannerStyles.js
2025-02-16T00:14:26.484Z - Transforming: node_modules/react-native-calendars/src/calendar/index.js
2025-02-16T00:14:26.495Z - Transforming: node_modules/react-native-calendars/src/calendar-list/index.js
2025-02-16T00:14:26.504Z - Transforming: node_modules/react-native-calendars/src/calendar-list/new.js
2025-02-16T00:14:26.522Z - Transforming: node_modules/react-native-calendars/src/agenda/index.js
2025-02-16T00:14:26.537Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/index.js
2025-02-16T00:14:26.570Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/WeekCalendar/new.js
2025-02-16T00:14:26.604Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/agendaList.js
2025-02-16T00:14:26.605Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/Context/index.js
2025-02-16T00:14:26.616Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/Context/Provider.js
2025-02-16T00:14:26.623Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/asCalendarConsumer.js
2025-02-16T00:14:26.631Z - Transforming: node_modules/react-native-calendars/src/timeline/Timeline.js
2025-02-16T00:14:26.634Z - Transforming: node_modules/react-native-calendars/src/timeline-list/index.js
2025-02-16T00:14:26.670Z - Transforming: node_modules/react-native-calendars/src/services/index.js
2025-02-16T00:14:26.700Z - Transforming: node_modules/xdate/src/xdate.js
2025-02-16T00:14:26.701Z - Transforming: node_modules/react-native-calendars/src/Profiler.js
2025-02-16T00:14:26.703Z - Transforming: node_modules/@react-native-picker/picker/js/Picker.js
2025-02-16T00:14:26.707Z - Transforming: node_modules/@react-native-picker/picker/js/PickerIOS.ios.js
2025-02-16T00:14:26.717Z - Transforming: node_modules/@react-navigation/drawer/src/views/legacy/Overlay.tsx
2025-02-16T00:14:26.729Z - Transforming: node_modules/expo-checkbox/build/Checkbox.js
2025-02-16T00:14:26.732Z - Transforming: node_modules/color-convert/conversions.js
2025-02-16T00:14:26.740Z - Transforming: node_modules/color-convert/route.js
2025-02-16T00:14:26.741Z - Transforming: node_modules/@callstack/react-theme-provider/node_modules/deepmerge/dist/umd.js
2025-02-16T00:14:26.745Z - Transforming: node_modules/@callstack/react-theme-provider/lib/createThemeProvider.js
2025-02-16T00:14:26.757Z - Transforming: node_modules/@callstack/react-theme-provider/lib/createWithTheme.js
2025-02-16T00:14:26.760Z - Transforming: node_modules/color-name/index.js
2025-02-16T00:14:26.761Z - Transforming: node_modules/simple-swizzle/index.js
2025-02-16T00:14:26.763Z - Transforming: node_modules/react-native-safe-area-context/src/specs/NativeSafeAreaContext.ts
2025-02-16T00:14:26.770Z - Transforming: node_modules/react-native-safe-area-context/src/specs/NativeSafeAreaView.ts
2025-02-16T00:14:26.773Z - Transforming: node_modules/react-native-chart-kit/dist/PieChart.js
2025-02-16T00:14:26.773Z - Transforming: node_modules/react-native-chart-kit/dist/AbstractChart.js
2025-02-16T00:14:26.773Z - Transforming: node_modules/react-native-chart-kit/dist/BarChart.js
2025-02-16T00:14:26.783Z - Transforming: node_modules/react-native-chart-kit/dist/ProgressChart.js
2025-02-16T00:14:26.804Z - Transforming: node_modules/react-native-chart-kit/dist/StackedBarChart.js
2025-02-16T00:14:26.810Z - Transforming: node_modules/react-native-chart-kit/dist/contribution-graph/index.js
2025-02-16T00:14:26.821Z - Transforming: node_modules/react-native-chart-kit/dist/line-chart/index.js
2025-02-16T00:14:26.823Z - Transforming: node_modules/react-native-modal-datetime-picker/src/Modal.js
2025-02-16T00:14:26.824Z - Transforming: node_modules/react-native-modal-datetime-picker/src/utils.js
2025-02-16T00:14:26.828Z - Transforming: node_modules/react-native-safe-area-context/src/NativeSafeAreaProvider.tsx
2025-02-16T00:14:26.831Z - Transforming: node_modules/axios/lib/defaults/transitional.js
2025-02-16T00:14:26.837Z - Transforming: node_modules/axios/lib/helpers/toURLEncodedForm.js
2025-02-16T00:14:26.839Z - Transforming: node_modules/axios/lib/platform/index.js
2025-02-16T00:14:26.840Z - Transforming: node_modules/axios/lib/helpers/null.js
2025-02-16T00:14:26.847Z - Transforming: node_modules/axios/lib/helpers/buildURL.js
2025-02-16T00:14:26.851Z - Transforming: node_modules/axios/lib/core/InterceptorManager.js
2025-02-16T00:14:26.852Z - Transforming: node_modules/axios/lib/core/dispatchRequest.js
2025-02-16T00:14:26.855Z - Transforming: node_modules/axios/lib/helpers/validator.js
2025-02-16T00:14:26.856Z - Transforming: node_modules/axios/lib/core/buildFullPath.js
2025-02-16T00:14:26.860Z - Transforming: node_modules/axios/lib/adapters/xhr.js
2025-02-16T00:14:26.861Z - Transforming: node_modules/axios/lib/adapters/fetch.js
2025-02-16T00:14:26.863Z - Transforming: node_modules/axios/lib/helpers/parseHeaders.js
2025-02-16T00:14:26.864Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/resolvers/shared.js
2025-02-16T00:14:26.867Z - Transforming: node_modules/assert/build/internal/errors.js
2025-02-16T00:14:26.887Z - Transforming: node_modules/assert/build/internal/assert/assertion_error.js
2025-02-16T00:14:26.923Z - Transforming: node_modules/util/util.js
2025-02-16T00:14:26.924Z - Transforming: node_modules/object.assign/polyfill.js
2025-02-16T00:14:26.924Z - Transforming: node_modules/object-is/polyfill.js
2025-02-16T00:14:26.926Z - Transforming: node_modules/call-bind/callBound.js
2025-02-16T00:14:26.927Z - Transforming: node_modules/assert/build/internal/util/comparisons.js
2025-02-16T00:14:26.933Z - Transforming: node_modules/use-latest-callback/lib/src/useIsomorphicLayoutEffect.native.js
2025-02-16T00:14:26.934Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/conditions.js
2025-02-16T00:14:26.939Z - Transforming: node_modules/@babel/runtime/helpers/toArray.js
2025-02-16T00:14:26.940Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/resolve-value.js
2025-02-16T00:14:26.940Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/render-component.js
2025-02-16T00:14:26.943Z - Transforming: node_modules/react-native/Libraries/ReactNative/ReactFabricPublicInstance/warnForStyleProps.js
2025-02-16T00:14:26.952Z - Transforming: node_modules/react-native/src/private/webapis/dom/nodes/ReadOnlyCharacterData.js
2025-02-16T00:14:26.954Z - Transforming: node_modules/react-native/src/private/webapis/dom/nodes/ReadOnlyNode.js
2025-02-16T00:14:26.967Z - Transforming: node_modules/react-native/src/private/webapis/dom/nodes/specs/NativeDOM.js
2025-02-16T00:14:26.970Z - Transforming: node_modules/react-native/src/private/webapis/dom/nodes/ReadOnlyElement.js
2025-02-16T00:14:26.972Z - Transforming: node_modules/whatwg-url-without-unicode/lib/url-state-machine.js
2025-02-16T00:14:26.987Z - Transforming: node_modules/whatwg-url-without-unicode/webidl2js-wrapper.js
2025-02-16T00:14:26.992Z - Transforming: node_modules/whatwg-url-without-unicode/lib/urlencoded.js
2025-02-16T00:14:26.997Z - Transforming: node_modules/@react-navigation/core/src/SceneView.tsx
2025-02-16T00:14:26.999Z - Transforming: node_modules/@react-navigation/core/src/useNavigationCache.tsx
2025-02-16T00:14:27.003Z - Transforming: node_modules/strict-uri-encode/index.js
2025-02-16T00:14:27.008Z - Transforming: node_modules/decode-uri-component/index.js
2025-02-16T00:14:27.011Z - Transforming: node_modules/split-on-first/index.js
2025-02-16T00:14:27.014Z - Transforming: node_modules/filter-obj/index.js
2025-02-16T00:14:27.017Z - Transforming: node_modules/react-native-reanimated/src/component/Text.ts
2025-02-16T00:14:27.017Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/index.ts
2025-02-16T00:14:27.017Z - Transforming: node_modules/react-native-reanimated/src/component/View.ts
2025-02-16T00:14:27.018Z - Transforming: node_modules/react-native-reanimated/src/component/ScrollView.tsx
2025-02-16T00:14:27.019Z - Transforming: node_modules/react-native-reanimated/src/component/Image.ts
2025-02-16T00:14:27.024Z - Transforming: node_modules/react-native-reanimated/src/propsAllowlists.ts
2025-02-16T00:14:27.024Z - Transforming: node_modules/react-native-reanimated/src/component/FlatList.tsx
2025-02-16T00:14:27.025Z - Transforming: node_modules/react-native-reanimated/src/PlatformChecker.ts
2025-02-16T00:14:27.025Z - Transforming: node_modules/react-native-reanimated/src/errors.ts
2025-02-16T00:14:27.033Z - Transforming: node_modules/react-native-reanimated/src/logger/logger.ts
2025-02-16T00:14:27.032Z - Transforming: node_modules/react-native-reanimated/src/logger/LogBox.ts
2025-02-16T00:14:27.035Z - Transforming: node_modules/react-native-reanimated/src/hook/useAnimatedProps.ts
2025-02-16T00:14:27.044Z - Transforming: node_modules/react-native-reanimated/src/hook/useSharedValue.ts
2025-02-16T00:14:27.051Z - Transforming: node_modules/react-native-reanimated/src/hook/useReducedMotion.ts
2025-02-16T00:14:27.051Z - Transforming: node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts
2025-02-16T00:14:27.052Z - Transforming: node_modules/react-native-reanimated/src/hook/useAnimatedGestureHandler.ts
2025-02-16T00:14:27.052Z - Transforming: node_modules/react-native-reanimated/src/hook/useAnimatedReaction.ts
2025-02-16T00:14:27.065Z - Transforming: node_modules/react-native-reanimated/src/hook/useAnimatedRef.ts
2025-02-16T00:14:27.074Z - Transforming: node_modules/react-native-reanimated/src/hook/useAnimatedScrollHandler.ts
2025-02-16T00:14:27.075Z - Transforming: node_modules/react-native-reanimated/src/hook/useDerivedValue.ts
2025-02-16T00:14:27.081Z - Transforming: node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts
2025-02-16T00:14:27.088Z - Transforming: node_modules/react-native-reanimated/src/hook/useFrameCallback.ts
2025-02-16T00:14:27.091Z - Transforming: node_modules/react-native-reanimated/src/hook/useAnimatedKeyboard.ts
2025-02-16T00:14:27.092Z - Transforming: node_modules/react-native-reanimated/src/hook/useScrollViewOffset.ts
2025-02-16T00:14:27.097Z - Transforming: node_modules/react-native-reanimated/src/hook/useEvent.ts
2025-02-16T00:14:27.097Z - Transforming: node_modules/react-native-reanimated/src/hook/useHandler.ts
2025-02-16T00:14:27.102Z - Transforming: node_modules/react-native-reanimated/src/hook/useComposedEventHandler.ts
2025-02-16T00:14:27.104Z - Transforming: node_modules/react-native-reanimated/src/hook/useWorkletCallback.ts
2025-02-16T00:14:27.108Z - Transforming: node_modules/react-native-reanimated/src/animation/util.ts
2025-02-16T00:14:27.123Z - Transforming: node_modules/react-native-reanimated/src/animation/decay/index.ts
2025-02-16T00:14:27.127Z - Transforming: node_modules/react-native-reanimated/src/animation/clamp.ts
2025-02-16T00:14:27.141Z - Transforming: node_modules/react-native-reanimated/src/animation/spring.ts
2025-02-16T00:14:27.142Z - Transforming: node_modules/react-native-reanimated/src/animation/repeat.ts
2025-02-16T00:14:27.150Z - Transforming: node_modules/react-native-reanimated/src/animation/delay.ts
2025-02-16T00:14:27.156Z - Transforming: node_modules/react-native-reanimated/src/animation/sequence.ts
2025-02-16T00:14:27.176Z - Transforming: node_modules/react-native-reanimated/src/animation/timing.ts
2025-02-16T00:14:27.191Z - Transforming: node_modules/react-native-reanimated/src/animation/styleAnimation.ts
2025-02-16T00:14:27.211Z - Transforming: node_modules/@react-navigation/core/src/useOnPreventRemove.tsx
2025-02-16T00:14:27.218Z - Transforming: node_modules/react-native-reanimated/src/platformFunctions/dispatchCommand.ts
2025-02-16T00:14:27.230Z - Transforming: node_modules/react-native-reanimated/src/platformFunctions/measure.ts
2025-02-16T00:14:27.233Z - Transforming: node_modules/react-native-reanimated/src/platformFunctions/scrollTo.ts
2025-02-16T00:14:27.234Z - Transforming: node_modules/react-native-reanimated/src/platformFunctions/setGestureState.ts
2025-02-16T00:14:27.247Z - Transforming: node_modules/react-native-reanimated/src/platformFunctions/getRelativeCoords.ts
2025-02-16T00:14:27.247Z - Transforming: node_modules/react-native-reanimated/src/NativeReanimated/index.ts
2025-02-16T00:14:27.250Z - Transforming: node_modules/react-native-reanimated/src/shareables.ts
2025-02-16T00:14:27.250Z - Transforming: node_modules/react-native-reanimated/src/platformFunctions/setNativeProps.ts
2025-02-16T00:14:27.251Z - Transforming: node_modules/react-native-reanimated/src/initializers.ts
2025-02-16T00:14:27.267Z - Transforming: node_modules/react-native-reanimated/src/SensorContainer.ts
2025-02-16T00:14:27.275Z - Transforming: node_modules/react-native-reanimated/src/threads.ts
2025-02-16T00:14:27.276Z - Transforming: node_modules/react-native-reanimated/src/runtimes.ts
2025-02-16T00:14:27.296Z - Transforming: node_modules/react-native-reanimated/src/mutables.ts
2025-02-16T00:14:27.312Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/animationsManager.ts
2025-02-16T00:14:27.317Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/animationBuilder/index.ts
2025-02-16T00:14:27.319Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/index.ts
2025-02-16T00:14:27.321Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultTransitions/index.ts
2025-02-16T00:14:27.327Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/index.ts
2025-02-16T00:14:27.328Z - Transforming: node_modules/react-native-reanimated/src/platformFunctions/findNodeHandle.ts
2025-02-16T00:14:27.332Z - Transforming: node_modules/react-native-reanimated/src/ReducedMotion.ts
2025-02-16T00:14:27.334Z - Transforming: node_modules/react-native-reanimated/src/Bezier.ts
2025-02-16T00:14:27.339Z - Transforming: node_modules/react-native-reanimated/src/screenTransition/animationManager.ts
2025-02-16T00:14:27.353Z - Transforming: node_modules/react-native-reanimated/src/screenTransition/presets.ts
2025-02-16T00:14:27.355Z - Transforming: node_modules/@react-navigation/elements/src/getNamedContext.tsx
2025-02-16T00:14:27.358Z - Transforming: node_modules/@react-navigation/elements/src/MaskedView.ios.tsx
2025-02-16T00:14:27.365Z - Transforming: node_modules/expo-image/src/utils/resolveAssetSource.tsx
2025-02-16T00:14:27.365Z - Transforming: node_modules/expo-image/src/utils/resolveHashString.tsx
2025-02-16T00:14:27.369Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/computeNewIndexWhenDataChanges.ts
2025-02-16T00:14:27.371Z - Transforming: node_modules/react-native-reanimated-carousel/src/utils/handlerOffsetDirection.ts
2025-02-16T00:14:27.373Z - Transforming: node_modules/react-native-reanimated-carousel/src/layouts/index.tsx
2025-02-16T00:14:27.379Z - Transforming: node_modules/react-native-reanimated-carousel/src/constants/index.ts
2025-02-16T00:14:27.381Z - Transforming: node_modules/react-native-reanimated-carousel/src/utils/log.ts
2025-02-16T00:14:27.387Z - Transforming: node_modules/react-native-reanimated-carousel/src/utils/dealWithAnimation.ts
2025-02-16T00:14:27.389Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useCheckMounted.ts
2025-02-16T00:14:27.390Z - Transforming: node_modules/react-native-reanimated-carousel/src/LazyView.tsx
2025-02-16T00:14:27.470Z - Transforming: node_modules/react-native-reanimated-carousel/src/hooks/useOffsetX.ts
2025-02-16T00:14:27.474Z - Transforming: node_modules/expo-font/build/FontHooks.js
2025-02-16T00:14:27.475Z - Transforming: node_modules/expo-font/build/Font.js
2025-02-16T00:14:27.497Z - Transforming: node_modules/@react-native-community/slider/dist/RNCSliderNativeComponent.js
2025-02-16T00:14:27.502Z - Transforming: node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/ensure-native-module-available.js
2025-02-16T00:14:27.505Z - Transforming: node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/create-icon-source-cache.js
2025-02-16T00:14:27.506Z - Transforming: node_modules/@react-native-community/slider/dist/components/StepNumber.js
2025-02-16T00:14:27.514Z - Transforming: node_modules/@react-native-community/slider/dist/components/TrackMark.js
2025-02-16T00:14:27.514Z - Transforming: node_modules/@react-navigation/stack/src/utils/getInvertedMultiplier.tsx
2025-02-16T00:14:27.518Z - Transforming: node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/object-utils.js
2025-02-16T00:14:27.518Z - Transforming: node_modules/@react-navigation/stack/src/utils/useKeyboardManager.tsx
2025-02-16T00:14:27.520Z - Transforming: node_modules/@react-navigation/stack/src/views/ModalStatusBarManager.tsx
2025-02-16T00:14:27.530Z - Transforming: node_modules/react-native/Libraries/Debugging/DebuggingOverlayNativeComponent.js
2025-02-16T00:14:27.531Z - Transforming: node_modules/@react-navigation/stack/src/views/Stack/CardSheet.tsx
2025-02-16T00:14:27.532Z - Transforming: node_modules/react-native/Libraries/Debugging/DebuggingOverlayRegistry.js
2025-02-16T00:14:27.541Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxNotification.js
2025-02-16T00:14:27.545Z - Transforming: node_modules/react-native/src/private/components/SafeAreaView_INTERNAL_DO_NOT_USE.js
2025-02-16T00:14:27.551Z - Transforming: node_modules/react-native/Libraries/Inspector/ElementBox.js
2025-02-16T00:14:27.552Z - Transforming: node_modules/react-native/Libraries/Inspector/getInspectorDataForViewAtPoint.js
2025-02-16T00:14:27.555Z - Transforming: node_modules/react-native/Libraries/NativeComponent/BaseViewConfig.ios.js
2025-02-16T00:14:27.563Z - Transforming: node_modules/react-native/Libraries/Inspector/InspectorPanel.js
2025-02-16T00:14:27.571Z - Transforming: node_modules/react-native/Libraries/Animated/NativeAnimatedAllowlist.js
2025-02-16T00:14:27.574Z - Transforming: node_modules/react-native/Libraries/Animated/NativeAnimatedTurboModule.js
2025-02-16T00:14:27.575Z - Transforming: node_modules/react-native/Libraries/Inspector/InspectorOverlay.js
2025-02-16T00:14:27.577Z - Transforming: node_modules/react-native/Libraries/Animated/NativeAnimatedModule.js
2025-02-16T00:14:27.584Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorFooterButton.js
2025-02-16T00:14:27.591Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorCodeFrame.js
2025-02-16T00:14:27.591Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorMessageHeader.js
2025-02-16T00:14:27.591Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorReactFrames.js
2025-02-16T00:14:27.604Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorStackFrames.js
2025-02-16T00:14:27.611Z - Transforming: node_modules/react-native/Libraries/Blob/NativeBlobModule.js
2025-02-16T00:14:27.619Z - Transforming: node_modules/react-native/Libraries/Blob/BlobRegistry.js
2025-02-16T00:14:27.622Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorHeaderButton.js
2025-02-16T00:14:27.629Z - Transforming: node_modules/react-native/Libraries/Events/EventPolyfill.js
2025-02-16T00:14:27.630Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeExceptionsManager.js
2025-02-16T00:14:27.634Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/useAnimatedValue.js
2025-02-16T00:14:27.641Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedObject.js
2025-02-16T00:14:27.641Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedStyle.js
2025-02-16T00:14:27.643Z - Transforming: node_modules/react-native/Libraries/Core/Devtools/symbolicateStackTrace.js
2025-02-16T00:14:27.643Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/TabBarItemLabel.js
2025-02-16T00:14:27.647Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/PlatformPressable.js
2025-02-16T00:14:27.650Z - Transforming: node_modules/react-native-screens/src/types.tsx
2025-02-16T00:14:27.650Z - Transforming: node_modules/react-native-screens/src/fabric/NativeScreensModule.ts
2025-02-16T00:14:27.654Z - Transforming: node_modules/react-native-screens/src/components/Screen.tsx
2025-02-16T00:14:27.657Z - Transforming: node_modules/react-native-screens/src/core.ts
2025-02-16T00:14:27.660Z - Transforming: node_modules/react-native-screens/src/components/ScreenStackHeaderConfig.tsx
2025-02-16T00:14:27.662Z - Transforming: node_modules/react-native-screens/src/components/SearchBar.tsx
2025-02-16T00:14:27.663Z - Transforming: node_modules/react-native-screens/src/components/ScreenContainer.tsx
2025-02-16T00:14:27.672Z - Transforming: node_modules/react-native-screens/src/components/ScreenStack.tsx
2025-02-16T00:14:27.674Z - Transforming: node_modules/react-native-screens/src/components/ScreenStackItem.tsx
2025-02-16T00:14:27.675Z - Transforming: node_modules/react-native-screens/src/components/FullWindowOverlay.tsx
2025-02-16T00:14:27.684Z - Transforming: node_modules/react-native-screens/src/components/ScreenFooter.tsx
2025-02-16T00:14:27.686Z - Transforming: node_modules/react-native-screens/src/components/ScreenContentWrapper.tsx
2025-02-16T00:14:27.691Z - Transforming: node_modules/react-native-screens/src/utils.ts
2025-02-16T00:14:27.693Z - Transforming: node_modules/react-native-screens/src/useTransitionProgress.tsx
2025-02-16T00:14:27.695Z - Transforming: node_modules/@react-navigation/bottom-tabs/src/views/Badge.tsx
2025-02-16T00:14:27.696Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/Pager.ios.js
2025-02-16T00:14:27.699Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/SceneView.js
2025-02-16T00:14:27.706Z - Transforming: node_modules/color/node_modules/color-convert/conversions.js
2025-02-16T00:14:27.707Z - Transforming: node_modules/color/node_modules/color-convert/route.js
2025-02-16T00:14:27.715Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/PlanDetails/styles/DayWorkoutDetailScreenStyles.js
2025-02-16T00:14:27.716Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/PlanDetails/WorkoutDetailScreen.js
2025-02-16T00:14:27.722Z - Transforming: node_modules/@react-native-community/datetimepicker/src/constants.js
2025-02-16T00:14:27.727Z - Transforming: node_modules/@react-native-community/datetimepicker/src/picker.ios.js
2025-02-16T00:14:27.732Z - Transforming: node_modules/@react-native-community/datetimepicker/src/utils.js
2025-02-16T00:14:27.732Z - Transforming: src/features/WorkoutDiary/CreatePlan/EditPlan/styles/EditDaysScreenStyles.js
2025-02-16T00:14:27.738Z - Transforming: src/features/WorkoutDiary/components/styles/AddModalStyles.js
2025-02-16T00:14:27.738Z - Transforming: node_modules/prop-types/lib/ReactPropTypesSecret.js
2025-02-16T00:14:27.744Z - Transforming: node_modules/prop-types/lib/has.js
2025-02-16T00:14:27.745Z - Transforming: node_modules/object-assign/index.js
2025-02-16T00:14:27.749Z - Transforming: node_modules/prop-types/checkPropTypes.js
2025-02-16T00:14:27.749Z - Transforming: node_modules/react-native-svg/src/xml.tsx
2025-02-16T00:14:27.750Z - Transforming: node_modules/react-native-svg/src/elements/Shape.tsx
2025-02-16T00:14:27.756Z - Transforming: node_modules/react-native-svg/src/utils/fetchData.ts
2025-02-16T00:14:27.770Z - Transforming: node_modules/react-native-svg/src/fabric/index.ts
2025-02-16T00:14:27.775Z - Transforming: node_modules/react-native-svg/src/deprecated.tsx
2025-02-16T00:14:27.781Z - Transforming: node_modules/react-native-svg/src/lib/extract/types.ts
2025-02-16T00:14:27.789Z - Transforming: node_modules/react-native-svg/src/elements.ts
2025-02-16T00:14:27.801Z - Transforming: src/features/WorkoutDiary/CreatePlan/NewPlan/DayWorkouts/styles/NewProgramWorkoutScreenStyles.js
2025-02-16T00:14:27.810Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/index.js
2025-02-16T00:14:27.817Z - Transforming: node_modules/react-native-modal/dist/utils.js
2025-02-16T00:14:27.819Z - Transforming: node_modules/react-native-modal/dist/modal.style.js
2025-02-16T00:14:27.821Z - Transforming: node_modules/react-native-animatable/definitions/attention-seekers.js
2025-02-16T00:14:27.833Z - Transforming: node_modules/react-native-animatable/definitions/bouncing-entrances.js
2025-02-16T00:14:27.834Z - Transforming: node_modules/react-native-animatable/definitions/bouncing-exits.js
2025-02-16T00:14:27.841Z - Transforming: node_modules/react-native-animatable/definitions/fading-entrances.js
2025-02-16T00:14:27.841Z - Transforming: node_modules/react-native-animatable/definitions/fading-exits.js
2025-02-16T00:14:27.845Z - Transforming: node_modules/react-native-animatable/definitions/flippers.js
2025-02-16T00:14:27.845Z - Transforming: node_modules/react-native-animatable/definitions/lightspeed.js
2025-02-16T00:14:27.848Z - Transforming: node_modules/react-native-animatable/definitions/sliding-entrances.js
2025-02-16T00:14:27.849Z - Transforming: node_modules/react-native-animatable/definitions/zooming-entrances.js
2025-02-16T00:14:27.850Z - Transforming: node_modules/react-native-animatable/definitions/sliding-exits.js
2025-02-16T00:14:27.850Z - Transforming: node_modules/react-native-animatable/definitions/zooming-exits.js
2025-02-16T00:14:27.852Z - Transforming: node_modules/react-native-animatable/flattenStyle.js
2025-02-16T00:14:27.855Z - Transforming: node_modules/expo-camera/build/ExpoCameraManager.js
2025-02-16T00:14:27.856Z - Transforming: node_modules/expo-camera/build/CameraView.js
2025-02-16T00:14:27.858Z - Transforming: node_modules/expo-camera/build/Camera.types.js
2025-02-16T00:14:27.858Z - Transforming: node_modules/react-native-animatable/wrapStyleTransforms.js
2025-02-16T00:14:27.859Z - Transforming: node_modules/react-native-animatable/getStyleValues.js
2025-02-16T00:14:27.862Z - Transforming: node_modules/react-native-animatable/easing.js
2025-02-16T00:14:27.863Z - Transforming: node_modules/lodash/isEmpty.js
2025-02-16T00:14:27.864Z - Transforming: node_modules/react-native-swipe-gestures/index.js
2025-02-16T00:14:27.870Z - Transforming: node_modules/react-native-calendars/src/commons/constants.js
2025-02-16T00:14:27.874Z - Transforming: node_modules/react-native-calendars/src/dateutils.js
2025-02-16T00:14:27.876Z - Transforming: node_modules/react-native-calendars/src/interface.js
2025-02-16T00:14:27.881Z - Transforming: node_modules/react-native-calendars/src/day-state-manager.js
2025-02-16T00:14:27.886Z - Transforming: node_modules/react-native-calendars/src/componentUpdater.js
2025-02-16T00:14:27.888Z - Transforming: node_modules/react-native-calendars/src/hooks.js
2025-02-16T00:14:27.895Z - Transforming: node_modules/react-native-calendars/src/calendar/style.js
2025-02-16T00:14:27.904Z - Transforming: node_modules/react-native-calendars/src/calendar/header/index.js
2025-02-16T00:14:27.905Z - Transforming: node_modules/react-native-calendars/src/calendar/day/index.js
2025-02-16T00:14:27.906Z - Transforming: node_modules/react-native-calendars/src/calendar/day/basic/index.js
2025-02-16T00:14:27.907Z - Transforming: node_modules/react-native-calendars/src/infinite-list/index.js
2025-02-16T00:14:27.917Z - Transforming: node_modules/react-native-calendars/src/calendar-list/style.js
2025-02-16T00:14:27.920Z - Transforming: node_modules/react-native-calendars/src/calendar-list/item.js
2025-02-16T00:14:27.926Z - Transforming: node_modules/lodash/findIndex.js
2025-02-16T00:14:27.930Z - Transforming: node_modules/lodash/isEqual.js
2025-02-16T00:14:27.931Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/week.js
2025-02-16T00:14:27.933Z - Transforming: node_modules/react-native-calendars/src/commons/WeekDaysNames.js
2025-02-16T00:14:27.936Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/style.js
2025-02-16T00:14:27.942Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/commons.js
2025-02-16T00:14:27.946Z - Transforming: node_modules/lodash/isFunction.js
2025-02-16T00:14:27.948Z - Transforming: node_modules/lodash/isNumber.js
2025-02-16T00:14:27.951Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/WeekCalendar/index.js
2025-02-16T00:14:27.949Z - Transforming: node_modules/lodash/throttle.js
2025-02-16T00:14:27.952Z - Transforming: node_modules/lodash/first.js
2025-02-16T00:14:27.960Z - Transforming: node_modules/scheduler/index.native.js
2025-02-16T00:14:27.962Z - Transforming: node_modules/lodash/isUndefined.js
2025-02-16T00:14:27.962Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/Context/todayButton.js
2025-02-16T00:14:27.963Z - Transforming: node_modules/react-native/Libraries/ReactPrivate/ReactNativePrivateInitializeCore.js
2025-02-16T00:14:27.964Z - Transforming: node_modules/lodash/isDate.js
2025-02-16T00:14:27.964Z - Transforming: node_modules/lodash/isString.js
2025-02-16T00:14:27.967Z - Transforming: node_modules/lodash/flatten.js
2025-02-16T00:14:27.967Z - Transforming: node_modules/lodash/dropRight.js
2025-02-16T00:14:27.970Z - Transforming: node_modules/react-native-calendars/src/timeline-list/useTimelinePages.js
2025-02-16T00:14:27.970Z - Transforming: node_modules/lodash/get.js
2025-02-16T00:14:27.971Z - Transforming: node_modules/lodash/map.js
2025-02-16T00:14:27.973Z - Transforming: node_modules/lodash/debounce.js
2025-02-16T00:14:27.975Z - Transforming: node_modules/react-native-calendars/src/momentResolver.js
2025-02-16T00:14:27.977Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/infiniteAgendaList.js
2025-02-16T00:14:27.983Z - Transforming: node_modules/react-native-calendars/src/expandableCalendar/AgendaListsCommon.js
2025-02-16T00:14:27.986Z - Transforming: node_modules/lodash/min.js
2025-02-16T00:14:27.989Z - Transforming: node_modules/lodash/groupBy.js
2025-02-16T00:14:27.989Z - Transforming: node_modules/react-native-calendars/src/timeline/style.js
2025-02-16T00:14:27.990Z - Transforming: node_modules/react-native-calendars/src/timeline/Packer.js
2025-02-16T00:14:27.990Z - Transforming: node_modules/react-native-calendars/src/timeline/helpers/presenter.js
2025-02-16T00:14:27.998Z - Transforming: node_modules/react-native-calendars/src/timeline/TimelineHours.js
2025-02-16T00:14:27.999Z - Transforming: node_modules/react-native-calendars/src/timeline/EventBlock.js
2025-02-16T00:14:28.006Z - Transforming: node_modules/react-native-calendars/src/timeline/NowIndicator.js
2025-02-16T00:14:28.008Z - Transforming: node_modules/react-native-calendars/src/timeline/useTimelineOffset.js
2025-02-16T00:14:28.010Z - Transforming: node_modules/lodash/times.js
2025-02-16T00:14:28.017Z - Transforming: node_modules/react-native-calendars/src/testIDs.js
2025-02-16T00:14:28.017Z - Transforming: node_modules/react-native-calendars/src/velocityTracker.js
2025-02-16T00:14:28.019Z - Transforming: node_modules/react-native-calendars/src/agenda/style.js
2025-02-16T00:14:28.024Z - Transforming: node_modules/react-native-calendars/src/agenda/reservation-list/index.js
2025-02-16T00:14:28.027Z - Transforming: node_modules/@react-native-picker/picker/js/RNCPickerNativeComponent.js
2025-02-16T00:14:28.030Z - Transforming: node_modules/@react-native-picker/picker/js/PickerAndroid.js
2025-02-16T00:14:28.033Z - Transforming: node_modules/@react-native-picker/picker/js/PickerWindows.js
2025-02-16T00:14:28.033Z - Transforming: node_modules/@react-native-picker/picker/js/PickerMacOS.js
2025-02-16T00:14:28.036Z - Transforming: node_modules/expo-checkbox/build/ExpoCheckbox.js
2025-02-16T00:14:28.041Z - Transforming: node_modules/expo-checkbox/build/Checkbox.types.js
2025-02-16T00:14:28.048Z - Transforming: node_modules/simple-swizzle/node_modules/is-arrayish/index.js
2025-02-16T00:14:28.050Z - Transforming: node_modules/color-convert/node_modules/color-name/index.js
2025-02-16T00:14:28.052Z - Transforming: node_modules/paths-js/pie.js
2025-02-16T00:14:28.055Z - Transforming: node_modules/react-native-chart-kit/dist/contribution-graph/ContributionGraph.js
2025-02-16T00:14:28.062Z - Transforming: node_modules/react-native-chart-kit/dist/line-chart/LineChart.js
2025-02-16T00:14:28.064Z - Transforming: node_modules/react-native-safe-area-context/src/specs/NativeSafeAreaProvider.ts
2025-02-16T00:14:28.078Z - Transforming: node_modules/axios/lib/platform/browser/index.js
2025-02-16T00:14:28.084Z - Transforming: node_modules/axios/lib/platform/common/utils.js
2025-02-16T00:14:28.085Z - Transforming: node_modules/axios/lib/helpers/AxiosURLSearchParams.js
2025-02-16T00:14:28.092Z - Transforming: node_modules/axios/lib/helpers/isAbsoluteURL.js
2025-02-16T00:14:28.102Z - Transforming: node_modules/axios/lib/helpers/combineURLs.js
2025-02-16T00:14:28.102Z - Transforming: node_modules/axios/lib/core/transformData.js
2025-02-16T00:14:28.104Z - Transforming: node_modules/axios/lib/core/settle.js
2025-02-16T00:14:28.105Z - Transforming: node_modules/axios/lib/helpers/parseProtocol.js
2025-02-16T00:14:28.106Z - Transforming: node_modules/axios/lib/helpers/resolveConfig.js
2025-02-16T00:14:28.109Z - Transforming: node_modules/axios/lib/helpers/composeSignals.js
2025-02-16T00:14:28.110Z - Transforming: node_modules/axios/lib/helpers/progressEventReducer.js
2025-02-16T00:14:28.115Z - Transforming: node_modules/axios/lib/helpers/trackStream.js
2025-02-16T00:14:28.118Z - Transforming: node_modules/get-intrinsic/index.js
2025-02-16T00:14:28.126Z - Transforming: node_modules/call-bind/index.js
2025-02-16T00:14:28.130Z - Transforming: node_modules/object-is/implementation.js
2025-02-16T00:14:28.133Z - Transforming: node_modules/object.assign/implementation.js
2025-02-16T00:14:28.138Z - Transforming: node_modules/react-native/src/private/webapis/dom/nodes/utilities/Traversal.js
2025-02-16T00:14:28.138Z - Transforming: node_modules/util/support/types.js
2025-02-16T00:14:28.144Z - Transforming: node_modules/util/support/isBufferBrowser.js
2025-02-16T00:14:28.145Z - Transforming: node_modules/inherits/inherits_browser.js
2025-02-16T00:14:28.149Z - Transforming: node_modules/object-is/index.js
2025-02-16T00:14:28.153Z - Transforming: node_modules/is-nan/index.js
2025-02-16T00:14:28.154Z - Transforming: node_modules/whatwg-url-without-unicode/lib/URL.js
2025-02-16T00:14:28.157Z - Transforming: node_modules/whatwg-url-without-unicode/lib/URLSearchParams.js
2025-02-16T00:14:28.157Z - Transforming: node_modules/react-native/src/private/webapis/dom/geometry/DOMRect.js
2025-02-16T00:14:28.160Z - Transforming: node_modules/react-native/src/private/webapis/dom/oldstylecollections/HTMLCollection.js
2025-02-16T00:14:28.164Z - Transforming: node_modules/react-native/src/private/webapis/dom/oldstylecollections/NodeList.js
2025-02-16T00:14:28.169Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/resolvers/text-shadow.js
2025-02-16T00:14:28.172Z - Transforming: node_modules/buffer/index.js
2025-02-16T00:14:28.182Z - Transforming: node_modules/whatwg-url-without-unicode/lib/infra.js
2025-02-16T00:14:28.184Z - Transforming: node_modules/@react-navigation/core/src/StaticContainer.tsx
2025-02-16T00:14:28.186Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/createAnimatedComponent.tsx
2025-02-16T00:14:28.188Z - Transforming: node_modules/punycode/punycode.js
2025-02-16T00:14:28.191Z - Transforming: node_modules/react-native-reanimated/src/fabricUtils.ts
2025-02-16T00:14:28.199Z - Transforming: node_modules/react-native-reanimated/src/shareableMappingCache.ts
2025-02-16T00:14:28.205Z - Transforming: node_modules/react-native-reanimated/src/frameCallback/FrameCallbackRegistryJS.ts
2025-02-16T00:14:28.220Z - Transforming: node_modules/react-native-reanimated/src/WorkletEventHandler.ts
2025-02-16T00:14:28.223Z - Transforming: node_modules/react-native-reanimated/src/hook/utils.ts
2025-02-16T00:14:28.229Z - Transforming: node_modules/react-native-reanimated/src/animation/decay/decay.ts
2025-02-16T00:14:28.240Z - Transforming: node_modules/react-native-reanimated/src/UpdateProps.ts
2025-02-16T00:14:28.251Z - Transforming: node_modules/react-native-reanimated/src/ViewDescriptorsSet.ts
2025-02-16T00:14:28.253Z - Transforming: node_modules/react-native-reanimated/src/animation/springUtils.ts
2025-02-16T00:14:28.272Z - Transforming: node_modules/react-native-reanimated/src/js-reanimated/index.ts
2025-02-16T00:14:28.274Z - Transforming: node_modules/react-native-reanimated/src/NativeReanimated/NativeReanimated.ts
2025-02-16T00:14:28.275Z - Transforming: node_modules/react-native-reanimated/src/Sensor.ts
2025-02-16T00:14:28.288Z - Transforming: node_modules/react-native-reanimated/src/animation/transformationMatrix/matrixUtils.tsx
2025-02-16T00:14:28.291Z - Transforming: node_modules/react-native-reanimated/src/mockedRequestAnimationFrame.ts
2025-02-16T00:14:28.302Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/animationBuilder/BaseAnimationBuilder.ts
2025-02-16T00:14:28.304Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/animationBuilder/ComplexAnimationBuilder.ts
2025-02-16T00:14:28.308Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/animationBuilder/Keyframe.ts
2025-02-16T00:14:28.332Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/animationBuilder/commonTypes.ts
2025-02-16T00:14:28.341Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Flip.ts
2025-02-16T00:14:28.350Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Stretch.ts
2025-02-16T00:14:28.356Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Fade.ts
2025-02-16T00:14:28.358Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Slide.ts
2025-02-16T00:14:28.376Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Zoom.ts
2025-02-16T00:14:28.393Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Bounce.ts
2025-02-16T00:14:28.483Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Lightspeed.ts
2025-02-16T00:14:28.486Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Pinwheel.ts
2025-02-16T00:14:28.495Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Rotate.ts
2025-02-16T00:14:28.521Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultAnimations/Roll.ts
2025-02-16T00:14:28.555Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultTransitions/LinearTransition.ts
2025-02-16T00:14:28.576Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultTransitions/FadingTransition.ts
2025-02-16T00:14:28.578Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultTransitions/SequencedTransition.ts
2025-02-16T00:14:28.593Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultTransitions/JumpingTransition.ts
2025-02-16T00:14:28.601Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultTransitions/CurvedTransition.ts
2025-02-16T00:14:28.605Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/defaultTransitions/EntryExitTransition.ts
2025-02-16T00:14:28.616Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/SharedTransition.ts
2025-02-16T00:14:28.620Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts
2025-02-16T00:14:28.633Z - Transforming: node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts
2025-02-16T00:14:28.640Z - Transforming: node_modules/react-native-reanimated/src/screenTransition/swipeSimulator.ts
2025-02-16T00:14:28.656Z - Transforming: node_modules/react-native-reanimated/src/platform-specific/jsVersion.ts
2025-02-16T00:14:28.659Z - Transforming: node_modules/@react-navigation/elements/src/MaskedViewNative.tsx
2025-02-16T00:14:28.660Z - Transforming: node_modules/react-native-reanimated-carousel/src/layouts/normal.ts
2025-02-16T00:14:28.664Z - Transforming: node_modules/react-native-reanimated-carousel/src/layouts/parallax.ts
2025-02-16T00:14:28.666Z - Transforming: node_modules/react-native-reanimated/src/reactUtils.ts
2025-02-16T00:14:28.672Z - Transforming: node_modules/react-native-reanimated/src/valueSetter.ts
2025-02-16T00:14:28.673Z - Transforming: node_modules/expo-font/build/ExpoFontLoader.js
2025-02-16T00:14:28.674Z - Transforming: node_modules/expo-font/build/Font.types.js
2025-02-16T00:14:28.675Z - Transforming: node_modules/expo-font/build/FontLoader.js
2025-02-16T00:14:28.676Z - Transforming: node_modules/expo-font/build/memory.js
2025-02-16T00:14:28.678Z - Transforming: node_modules/expo-font/build/server.js
2025-02-16T00:14:28.682Z - Transforming: node_modules/react-native/src/private/specs/components/DebuggingOverlayNativeComponent.js
2025-02-16T00:14:28.685Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxButton.js
2025-02-16T00:14:28.687Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxNotificationCountBadge.js
2025-02-16T00:14:28.696Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxNotificationMessage.js
2025-02-16T00:14:28.702Z - Transforming: node_modules/react-native/Libraries/Inspector/BorderBox.js
2025-02-16T00:14:28.703Z - Transforming: node_modules/react-native/Libraries/Inspector/resolveBoxStyle.js
2025-02-16T00:14:28.704Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeAnimatedModule.js
2025-02-16T00:14:28.710Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxNotificationDismissButton.js
2025-02-16T00:14:28.710Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeAnimatedTurboModule.js
2025-02-16T00:14:28.711Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxMessage.js
2025-02-16T00:14:28.711Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeBlobModule.js
2025-02-16T00:14:28.717Z - Transforming: node_modules/react-native/Libraries/Core/Devtools/openFileInEditor.js
2025-02-16T00:14:28.720Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/AnsiHighlight.js
2025-02-16T00:14:28.721Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorSection.js
2025-02-16T00:14:28.728Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorSourceMapStatus.js
2025-02-16T00:14:28.731Z - Transforming: node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorStackFrame.js
2025-02-16T00:14:28.732Z - Transforming: node_modules/react-native/Libraries/Inspector/ElementProperties.js
2025-02-16T00:14:28.734Z - Transforming: node_modules/react-native/Libraries/Inspector/PerformanceOverlay.js
2025-02-16T00:14:28.745Z - Transforming: node_modules/react-native/Libraries/Network/fetch.js
2025-02-16T00:14:28.745Z - Transforming: node_modules/react-native/Libraries/Inspector/NetworkOverlay.js
2025-02-16T00:14:28.747Z - Transforming: node_modules/react-native/Libraries/Core/Devtools/getDevServer.js
2025-02-16T00:14:28.749Z - Transforming: node_modules/react-native-screens/src/fabric/ScreenContainerNativeComponent.ts
2025-02-16T00:14:28.751Z - Transforming: node_modules/react-native-screens/src/fabric/ScreenNavigationContainerNativeComponent.ts
2025-02-16T00:14:28.754Z - Transforming: node_modules/react-native-screens/src/fabric/SearchBarNativeComponent.ts
2025-02-16T00:14:28.755Z - Transforming: node_modules/react-native-screens/src/fabric/ScreenStackHeaderConfigNativeComponent.ts
2025-02-16T00:14:28.757Z - Transforming: node_modules/react-native-screens/src/fabric/ScreenStackHeaderSubviewNativeComponent.ts
2025-02-16T00:14:28.762Z - Transforming: node_modules/react-native-screens/src/fabric/FullWindowOverlayNativeComponent.ts
2025-02-16T00:14:28.762Z - Transforming: node_modules/react-native-screens/src/contexts.tsx
2025-02-16T00:14:28.767Z - Transforming: node_modules/react-native-screens/src/fabric/ScreenStackNativeComponent.ts
2025-02-16T00:14:28.771Z - Transforming: node_modules/react-native-screens/src/fabric/ScreenFooterNativeComponent.ts
2025-02-16T00:14:28.776Z - Transforming: node_modules/react-native-screens/src/fabric/ScreenContentWrapperNativeComponent.ts
2025-02-16T00:14:28.777Z - Transforming: node_modules/react-native-screens/src/components/DebugContainer.tsx
2025-02-16T00:14:28.778Z - Transforming: node_modules/react-native-screens/src/TransitionProgressContext.tsx
2025-02-16T00:14:28.778Z - Transforming: node_modules/react-native-tab-view/lib/commonjs/PagerViewAdapter.js
2025-02-16T00:14:28.783Z - Transforming: node_modules/react-native/Libraries/Animated/nodes/AnimatedTransform.js
2025-02-16T00:14:28.784Z - Transforming: node_modules/@react-native-community/datetimepicker/src/specs/DateTimePickerNativeComponent.js
2025-02-16T00:14:28.790Z - Transforming: node_modules/react-native-screens/src/components/helpers/DelayedFreeze.tsx
2025-02-16T00:14:28.799Z - Transforming: node_modules/react-native-screens/src/fabric/ScreenNativeComponent.ts
2025-02-16T00:14:28.801Z - Transforming: node_modules/react-native-screens/src/fabric/ModalScreenNativeComponent.ts
2025-02-16T00:14:28.802Z - Transforming: node_modules/react-native-screens/src/components/helpers/usePrevious.tsx
2025-02-16T00:14:28.812Z - Transforming: node_modules/react-native-svg/src/fabric/ClipPathNativeComponent.ts
2025-02-16T00:14:28.822Z - Transforming: node_modules/react-native-svg/src/fabric/CircleNativeComponent.ts
2025-02-16T00:14:28.827Z - Transforming: node_modules/react-native-svg/src/fabric/DefsNativeComponent.ts
2025-02-16T00:14:28.830Z - Transforming: node_modules/react-native-svg/src/fabric/EllipseNativeComponent.ts
2025-02-16T00:14:28.831Z - Transforming: node_modules/react-native-svg/src/fabric/ForeignObjectNativeComponent.ts
2025-02-16T00:14:28.839Z - Transforming: node_modules/react-native-svg/src/fabric/GroupNativeComponent.ts
2025-02-16T00:14:28.843Z - Transforming: node_modules/react-native-svg/src/fabric/ImageNativeComponent.ts
2025-02-16T00:14:28.844Z - Transforming: node_modules/react-native-svg/src/fabric/LinearGradientNativeComponent.ts
2025-02-16T00:14:28.844Z - Transforming: node_modules/react-native-svg/src/fabric/LineNativeComponent.ts
2025-02-16T00:14:28.854Z - Transforming: node_modules/react-native-svg/src/fabric/MarkerNativeComponent.ts
2025-02-16T00:14:28.855Z - Transforming: node_modules/react-native-svg/src/fabric/MaskNativeComponent.ts
2025-02-16T00:14:28.863Z - Transforming: node_modules/react-native-svg/src/fabric/PatternNativeComponent.ts
2025-02-16T00:14:28.866Z - Transforming: node_modules/react-native-svg/src/fabric/RadialGradientNativeComponent.ts
2025-02-16T00:14:28.866Z - Transforming: node_modules/react-native-svg/src/fabric/PathNativeComponent.ts
2025-02-16T00:14:28.873Z - Transforming: node_modules/react-native-svg/src/fabric/RectNativeComponent.ts
2025-02-16T00:14:28.880Z - Transforming: node_modules/react-native-svg/src/fabric/AndroidSvgViewNativeComponent.ts
2025-02-16T00:14:28.880Z - Transforming: node_modules/react-native-svg/src/fabric/IOSSvgViewNativeComponent.ts
2025-02-16T00:14:28.891Z - Transforming: node_modules/react-native-svg/src/fabric/SymbolNativeComponent.ts
2025-02-16T00:14:28.893Z - Transforming: node_modules/react-native-svg/src/fabric/TextNativeComponent.ts
2025-02-16T00:14:28.906Z - Transforming: node_modules/react-native-svg/src/fabric/TextPathNativeComponent.ts
2025-02-16T00:14:28.908Z - Transforming: node_modules/react-native-svg/src/fabric/TSpanNativeComponent.ts
2025-02-16T00:14:28.910Z - Transforming: node_modules/react-native-svg/src/fabric/UseNativeComponent.ts
2025-02-16T00:14:28.923Z - Transforming: node_modules/react-native-svg/src/fabric/FilterNativeComponent.ts
2025-02-16T00:14:28.923Z - Transforming: node_modules/react-native-svg/src/fabric/FeBlendNativeComponent.ts
2025-02-16T00:14:28.923Z - Transforming: node_modules/react-native-svg/src/fabric/FeColorMatrixNativeComponent.ts
2025-02-16T00:14:28.929Z - Transforming: node_modules/react-native-svg/src/fabric/FeFloodNativeComponent.ts
2025-02-16T00:14:28.929Z - Transforming: node_modules/react-native-svg/src/fabric/FeGaussianBlurNativeComponent.ts
2025-02-16T00:14:28.930Z - Transforming: node_modules/react-native-svg/src/fabric/FeMergeNativeComponent.ts
2025-02-16T00:14:28.930Z - Transforming: node_modules/react-native-svg/src/fabric/FeOffsetNativeComponent.ts
2025-02-16T00:14:28.939Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/registry.js
2025-02-16T00:14:28.939Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/index.js
2025-02-16T00:14:28.939Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/createAnimatableComponent.js
2025-02-16T00:14:28.939Z - Transforming: src/features/WorkoutDiary/WorkoutPlan/PlanDetails/styles/WorkoutDetailScreenStyles.js
2025-02-16T00:14:28.943Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/createAnimation.js
2025-02-16T00:14:28.943Z - Transforming: node_modules/react-native-svg/src/elements/ClipPath.tsx
2025-02-16T00:14:28.946Z - Transforming: node_modules/react-native-svg/src/elements/Defs.tsx
2025-02-16T00:14:28.953Z - Transforming: node_modules/react-native-svg/src/elements/ForeignObject.tsx
2025-02-16T00:14:28.953Z - Transforming: node_modules/react-native-svg/src/elements/Ellipse.tsx
2025-02-16T00:14:28.957Z - Transforming: node_modules/react-native-svg/src/elements/G.tsx
2025-02-16T00:14:28.962Z - Transforming: node_modules/react-native-svg/src/elements/Circle.tsx
2025-02-16T00:14:28.967Z - Transforming: node_modules/react-native-svg/src/elements/Image.tsx
2025-02-16T00:14:28.971Z - Transforming: node_modules/react-native-svg/src/elements/LinearGradient.tsx
2025-02-16T00:14:28.972Z - Transforming: node_modules/react-native-svg/src/elements/Line.tsx
2025-02-16T00:14:28.974Z - Transforming: node_modules/react-native-svg/src/elements/Marker.tsx
2025-02-16T00:14:28.984Z - Transforming: node_modules/react-native-svg/src/elements/Mask.tsx
2025-02-16T00:14:28.985Z - Transforming: node_modules/react-native-svg/src/elements/Path.tsx
2025-02-16T00:14:28.986Z - Transforming: node_modules/react-native-svg/src/elements/Pattern.tsx
2025-02-16T00:14:28.998Z - Transforming: node_modules/react-native-svg/src/elements/Polygon.tsx
2025-02-16T00:14:29.007Z - Transforming: node_modules/react-native-svg/src/elements/Polyline.tsx
2025-02-16T00:14:29.010Z - Transforming: node_modules/react-native-svg/src/elements/RadialGradient.tsx
2025-02-16T00:14:29.017Z - Transforming: node_modules/react-native-svg/src/elements/Rect.tsx
2025-02-16T00:14:29.017Z - Transforming: node_modules/react-native-svg/src/elements/Stop.tsx
2025-02-16T00:14:29.021Z - Transforming: node_modules/react-native-svg/src/elements/Svg.tsx
2025-02-16T00:14:29.022Z - Transforming: node_modules/react-native-svg/src/elements/Symbol.tsx
2025-02-16T00:14:29.031Z - Transforming: node_modules/react-native-svg/src/elements/TSpan.tsx
2025-02-16T00:14:29.035Z - Transforming: node_modules/react-native-svg/src/elements/Text.tsx
2025-02-16T00:14:29.041Z - Transforming: node_modules/react-native-svg/src/elements/TextPath.tsx
2025-02-16T00:14:29.046Z - Transforming: node_modules/react-native-svg/src/elements/Use.tsx
2025-02-16T00:14:29.047Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeBlend.tsx
2025-02-16T00:14:29.057Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeColorMatrix.tsx
2025-02-16T00:14:29.060Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeComponentTransfer.tsx
2025-02-16T00:14:29.062Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeComponentTransferFunction.tsx
2025-02-16T00:14:29.064Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeComposite.tsx
2025-02-16T00:14:29.067Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeConvolveMatrix.tsx
2025-02-16T00:14:29.074Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeDiffuseLighting.tsx
2025-02-16T00:14:29.075Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeDisplacementMap.tsx
2025-02-16T00:14:29.076Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeDistantLight.tsx
2025-02-16T00:14:29.076Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeDropShadow.tsx
2025-02-16T00:14:29.077Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeFlood.tsx
2025-02-16T00:14:29.083Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeGaussianBlur.tsx
2025-02-16T00:14:29.087Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeImage.tsx
2025-02-16T00:14:29.088Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeMerge.tsx
2025-02-16T00:14:29.095Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeMergeNode.tsx
2025-02-16T00:14:29.098Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeMorphology.tsx
2025-02-16T00:14:29.098Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeOffset.tsx
2025-02-16T00:14:29.102Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeSpecularLighting.tsx
2025-02-16T00:14:29.105Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeSpotLight.tsx
2025-02-16T00:14:29.109Z - Transforming: node_modules/react-native-svg/src/elements/filters/FePointLight.tsx
2025-02-16T00:14:29.110Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeTile.tsx
2025-02-16T00:14:29.110Z - Transforming: node_modules/react-native-svg/src/elements/filters/FeTurbulence.tsx
2025-02-16T00:14:29.115Z - Transforming: node_modules/react-native-svg/src/elements/filters/Filter.tsx
2025-02-16T00:14:29.118Z - Transforming: node_modules/react-native-svg/src/lib/SvgTouchableMixin.ts
2025-02-16T00:14:29.119Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractBrush.ts
2025-02-16T00:14:29.124Z - Transforming: node_modules/react-native-svg/src/lib/extract/colors.ts
2025-02-16T00:14:29.125Z - Transforming: node_modules/react-native-svg/src/fabric/NativeSvgRenderableModule.ts
2025-02-16T00:14:29.127Z - Transforming: node_modules/react-native-animatable/getDefaultStyleValue.js
2025-02-16T00:14:29.128Z - Transforming: node_modules/lodash/_getTag.js
2025-02-16T00:14:29.129Z - Transforming: node_modules/lodash/isArguments.js
2025-02-16T00:14:29.133Z - Transforming: node_modules/lodash/isArray.js
2025-02-16T00:14:29.133Z - Transforming: node_modules/lodash/isArrayLike.js
2025-02-16T00:14:29.137Z - Transforming: node_modules/lodash/isBuffer.js
2025-02-16T00:14:29.137Z - Transforming: node_modules/lodash/isTypedArray.js
2025-02-16T00:14:29.138Z - Transforming: node_modules/lodash/_isPrototype.js
2025-02-16T00:14:29.141Z - Transforming: node_modules/lodash/_baseKeys.js
2025-02-16T00:14:29.141Z - Transforming: node_modules/react-native-calendars/src/style.js
2025-02-16T00:14:29.142Z - Transforming: node_modules/lodash/omit.js
2025-02-16T00:14:29.142Z - Transforming: node_modules/lodash/pickBy.js
2025-02-16T00:14:29.144Z - Transforming: node_modules/lodash/includes.js
2025-02-16T00:14:29.147Z - Transforming: node_modules/expo-camera/build/utils/props.js
2025-02-16T00:14:29.147Z - Transforming: node_modules/expo-camera/build/ExpoCamera.js
2025-02-16T00:14:29.151Z - Transforming: node_modules/react-native-calendars/src/calendar/day/period/index.js
2025-02-16T00:14:29.151Z - Transforming: node_modules/lodash/some.js
2025-02-16T00:14:29.152Z - Transforming: node_modules/lodash/_baseFindIndex.js
2025-02-16T00:14:29.153Z - Transforming: node_modules/lodash/_baseIteratee.js
2025-02-16T00:14:29.155Z - Transforming: node_modules/lodash/toInteger.js
2025-02-16T00:14:29.157Z - Transforming: node_modules/lodash/_baseIsEqual.js
2025-02-16T00:14:29.157Z - Transforming: node_modules/react-native-svg/src/xmlTags.ts
2025-02-16T00:14:29.158Z - Transforming: node_modules/lodash/inRange.js
2025-02-16T00:14:29.161Z - Transforming: node_modules/lodash/noop.js
2025-02-16T00:14:29.162Z - Transforming: node_modules/recyclerlistview/dist/reactnative/index.js
2025-02-16T00:14:29.164Z - Transforming: node_modules/react-native-calendars/src/calendar/header/style.js
2025-02-16T00:14:29.164Z - Transforming: node_modules/lodash/_baseGetTag.js
2025-02-16T00:14:29.164Z - Transforming: node_modules/lodash/isObject.js
2025-02-16T00:14:29.165Z - Transforming: node_modules/react-native-calendars/src/calendar/day/marking/index.js
2025-02-16T00:14:29.167Z - Transforming: node_modules/react-native-calendars/src/calendar/day/basic/style.js
2025-02-16T00:14:29.169Z - Transforming: node_modules/lodash/head.js
2025-02-16T00:14:29.172Z - Transforming: node_modules/lodash/isObjectLike.js
2025-02-16T00:14:29.174Z - Transforming: node_modules/scheduler/cjs/scheduler.native.development.js
2025-02-16T00:14:29.179Z - Transforming: node_modules/lodash/_baseIsDate.js
2025-02-16T00:14:29.179Z - Transforming: node_modules/lodash/_baseUnary.js
2025-02-16T00:14:29.180Z - Transforming: node_modules/lodash/_nodeUtil.js
2025-02-16T00:14:29.184Z - Transforming: node_modules/react-native/Libraries/Core/InitializeCore.js
2025-02-16T00:14:29.190Z - Transforming: node_modules/lodash/_baseFlatten.js
2025-02-16T00:14:29.191Z - Transforming: node_modules/lodash/_baseSlice.js
2025-02-16T00:14:29.191Z - Transforming: node_modules/lodash/_arrayMap.js
2025-02-16T00:14:29.193Z - Transforming: node_modules/lodash/_baseGet.js
2025-02-16T00:14:29.197Z - Transforming: node_modules/lodash/_baseMap.js
2025-02-16T00:14:29.198Z - Transforming: node_modules/moment/moment.js
2025-02-16T00:14:29.199Z - Transforming: node_modules/lodash/now.js
2025-02-16T00:14:29.199Z - Transforming: node_modules/lodash/toNumber.js
2025-02-16T00:14:29.207Z - Transforming: node_modules/lodash/_baseExtremum.js
2025-02-16T00:14:29.209Z - Transforming: node_modules/lodash/identity.js
2025-02-16T00:14:29.211Z - Transforming: node_modules/lodash/_baseLt.js
2025-02-16T00:14:29.211Z - Transforming: node_modules/lodash/_baseAssignValue.js
2025-02-16T00:14:29.212Z - Transforming: node_modules/lodash/_createAggregator.js
2025-02-16T00:14:29.212Z - Transforming: node_modules/lodash/_baseTimes.js
2025-02-16T00:14:29.214Z - Transforming: node_modules/lodash/_castFunction.js
2025-02-16T00:14:29.215Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/dependencies/LayoutProvider.js
2025-02-16T00:14:29.215Z - Transforming: node_modules/react-native-calendars/src/agenda/platform-style.ios.js
2025-02-16T00:14:29.219Z - Transforming: node_modules/@react-native-picker/picker/js/UnimplementedView.js
2025-02-16T00:14:29.219Z - Transforming: node_modules/lodash/range.js
2025-02-16T00:14:29.219Z - Transforming: node_modules/paths-js/linear.js
2025-02-16T00:14:29.223Z - Transforming: node_modules/paths-js/sector.js
2025-02-16T00:14:29.224Z - Transforming: node_modules/paths-js/ops.js
2025-02-16T00:14:29.224Z - Transforming: node_modules/react-native-calendars/src/agenda/reservation-list/style.js
2025-02-16T00:14:29.226Z - Transforming: node_modules/react-native-calendars/src/agenda/reservation-list/reservation.js
2025-02-16T00:14:29.227Z - Transforming: node_modules/axios/lib/platform/browser/classes/URLSearchParams.js
2025-02-16T00:14:29.230Z - Transforming: node_modules/axios/lib/platform/browser/classes/FormData.js
2025-02-16T00:14:29.232Z - Transforming: node_modules/axios/lib/platform/browser/classes/Blob.js
2025-02-16T00:14:29.233Z - Transforming: node_modules/react-native-chart-kit/dist/Utils.js
2025-02-16T00:14:29.236Z - Transforming: node_modules/lodash/lodash.js
2025-02-16T00:14:29.237Z - Transforming: node_modules/react-native-chart-kit/dist/contribution-graph/DateHelpers.js
2025-02-16T00:14:29.238Z - Transforming: node_modules/react-native-chart-kit/dist/contribution-graph/constants.js
2025-02-16T00:14:29.242Z - Transforming: node_modules/axios/lib/helpers/speedometer.js
2025-02-16T00:14:29.245Z - Transforming: node_modules/axios/lib/helpers/throttle.js
2025-02-16T00:14:29.248Z - Transforming: node_modules/axios/lib/helpers/isURLSameOrigin.js
2025-02-16T00:14:29.249Z - Transforming: node_modules/axios/lib/helpers/cookies.js
2025-02-16T00:14:29.252Z - Transforming: node_modules/es-define-property/index.js
2025-02-16T00:14:29.262Z - Transforming: node_modules/set-function-length/index.js
2025-02-16T00:14:29.264Z - Transforming: node_modules/call-bind-apply-helpers/index.js
2025-02-16T00:14:29.269Z - Transforming: node_modules/call-bind-apply-helpers/applyBind.js
2025-02-16T00:14:29.269Z - Transforming: node_modules/has-symbols/shams.js
2025-02-16T00:14:29.274Z - Transforming: node_modules/object-keys/index.js
2025-02-16T00:14:29.279Z - Transforming: node_modules/call-bound/index.js
2025-02-16T00:14:29.279Z - Transforming: node_modules/es-object-atoms/index.js
2025-02-16T00:14:29.284Z - Transforming: node_modules/@babel/runtime/helpers/wrapAsyncGenerator.js
2025-02-16T00:14:29.285Z - Transforming: node_modules/@babel/runtime/helpers/awaitAsyncGenerator.js
2025-02-16T00:14:29.289Z - Transforming: node_modules/@babel/runtime/helpers/asyncGeneratorDelegate.js
2025-02-16T00:14:29.290Z - Transforming: node_modules/es-errors/index.js
2025-02-16T00:14:29.293Z - Transforming: node_modules/es-errors/range.js
2025-02-16T00:14:29.294Z - Transforming: node_modules/es-errors/ref.js
2025-02-16T00:14:29.294Z - Transforming: node_modules/es-errors/syntax.js
2025-02-16T00:14:29.296Z - Transforming: node_modules/es-errors/type.js
2025-02-16T00:14:29.298Z - Transforming: node_modules/es-errors/uri.js
2025-02-16T00:14:29.299Z - Transforming: node_modules/math-intrinsics/abs.js
2025-02-16T00:14:29.299Z - Transforming: node_modules/math-intrinsics/floor.js
2025-02-16T00:14:29.300Z - Transforming: node_modules/math-intrinsics/max.js
2025-02-16T00:14:29.302Z - Transforming: node_modules/math-intrinsics/min.js
2025-02-16T00:14:29.304Z - Transforming: node_modules/math-intrinsics/pow.js
2025-02-16T00:14:29.304Z - Transforming: node_modules/math-intrinsics/round.js
2025-02-16T00:14:29.306Z - Transforming: node_modules/math-intrinsics/sign.js
2025-02-16T00:14:29.310Z - Transforming: node_modules/gopd/index.js
2025-02-16T00:14:29.311Z - Transforming: node_modules/has-symbols/index.js
2025-02-16T00:14:29.312Z - Transforming: node_modules/get-proto/index.js
2025-02-16T00:14:29.317Z - Transforming: node_modules/get-proto/Object.getPrototypeOf.js
2025-02-16T00:14:29.318Z - Transforming: node_modules/get-proto/Reflect.getPrototypeOf.js
2025-02-16T00:14:29.322Z - Transforming: node_modules/call-bind-apply-helpers/functionApply.js
2025-02-16T00:14:29.324Z - Transforming: node_modules/call-bind-apply-helpers/functionCall.js
2025-02-16T00:14:29.324Z - Transforming: node_modules/function-bind/index.js
2025-02-16T00:14:29.325Z - Transforming: node_modules/hasown/index.js
2025-02-16T00:14:29.327Z - Transforming: node_modules/define-properties/index.js
2025-02-16T00:14:29.328Z - Transforming: node_modules/object-is/shim.js
2025-02-16T00:14:29.329Z - Transforming: node_modules/is-nan/implementation.js
2025-02-16T00:14:29.333Z - Transforming: node_modules/is-nan/polyfill.js
2025-02-16T00:14:29.337Z - Transforming: node_modules/is-nan/shim.js
2025-02-16T00:14:29.340Z - Transforming: node_modules/is-generator-function/index.js
2025-02-16T00:14:29.342Z - Transforming: node_modules/which-typed-array/index.js
2025-02-16T00:14:29.342Z - Transforming: node_modules/is-arguments/index.js
2025-02-16T00:14:29.351Z - Transforming: node_modules/is-typed-array/index.js
2025-02-16T00:14:29.351Z - Transforming: node_modules/react-native-chart-kit/dist/line-chart/LegendItem.js
2025-02-16T00:14:29.352Z - Transforming: node_modules/es-errors/eval.js
2025-02-16T00:14:29.355Z - Transforming: node_modules/react-native/src/private/webapis/dom/geometry/DOMRectReadOnly.js
2025-02-16T00:14:29.358Z - Transforming: node_modules/react-native-css-interop/dist/runtime/native/resolvers/shorthand.js
2025-02-16T00:14:29.360Z - Transforming: node_modules/react-native/src/private/webapis/dom/oldstylecollections/ArrayLikeUtils.js
2025-02-16T00:14:29.361Z - Transforming: node_modules/react-native-reanimated/src/platform-specific/findHostInstance.ts
2025-02-16T00:14:29.366Z - Transforming: node_modules/webidl-conversions/lib/index.js
2025-02-16T00:14:29.369Z - Transforming: node_modules/whatwg-url-without-unicode/lib/utils.js
2025-02-16T00:14:29.375Z - Transforming: node_modules/whatwg-url-without-unicode/lib/URLSearchParams-impl.js
2025-02-16T00:14:29.381Z - Transforming: node_modules/whatwg-url-without-unicode/lib/URL-impl.js
2025-02-16T00:14:29.386Z - Transforming: node_modules/react-native-reanimated/src/frameCallback/FrameCallbackRegistryUI.ts
2025-02-16T00:14:29.389Z - Transforming: node_modules/react-native-reanimated/src/animation/decay/rubberBandDecay.ts
2025-02-16T00:14:29.394Z - Transforming: node_modules/react-native-reanimated/src/animation/decay/utils.ts
2025-02-16T00:14:29.406Z - Transforming: node_modules/react-native-reanimated/src/animation/decay/rigidDecay.ts
2025-02-16T00:14:29.416Z - Transforming: node_modules/ieee754/index.js
2025-02-16T00:14:29.419Z - Transforming: node_modules/react-native-reanimated/src/platform-specific/checkCppVersion.ts
2025-02-16T00:14:29.428Z - Transforming: node_modules/react-native-reanimated/src/valueUnpacker.ts
2025-02-16T00:14:29.432Z - Transforming: node_modules/react-native-reanimated/src/specs/NativeReanimatedModule.ts
2025-02-16T00:14:29.436Z - Transforming: node_modules/react-native-reanimated/src/js-reanimated/JSReanimated.ts
2025-02-16T00:14:29.468Z - Transforming: node_modules/react-native-reanimated/src/js-reanimated/webUtils.ts
2025-02-16T00:14:29.469Z - Transforming: node_modules/react-native-reanimated/src/PropsRegistry.ts
2025-02-16T00:14:29.471Z - Transforming: node_modules/react-native-reanimated/src/animationBuilder.tsx
2025-02-16T00:14:29.488Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/JSPropsUpdater.ts
2025-02-16T00:14:29.488Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/utils.ts
2025-02-16T00:14:29.505Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/setAndForwardRef.ts
2025-02-16T00:14:29.545Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/InlinePropManager.ts
2025-02-16T00:14:29.549Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/PropsFilter.tsx
2025-02-16T00:14:29.580Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/index.ts
2025-02-16T00:14:29.581Z - Transforming: node_modules/react-native-reanimated/src/UpdateLayoutAnimations.ts
2025-02-16T00:14:29.583Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/domUtils.ts
2025-02-16T00:14:29.587Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/getViewInfo.ts
2025-02-16T00:14:29.592Z - Transforming: node_modules/react-native-reanimated/src/createAnimatedComponent/NativeEventsManager.ts
2025-02-16T00:14:29.600Z - Transforming: node_modules/react-native/Libraries/Animated/shouldUseTurboAnimatedModule.js
2025-02-16T00:14:29.604Z - Transforming: node_modules/anser/lib/index.js
2025-02-16T00:14:29.610Z - Transforming: node_modules/react-native-reanimated/src/screenTransition/RNScreensTurboModule.ts
2025-02-16T00:14:29.645Z - Transforming: node_modules/whatwg-fetch/dist/fetch.umd.js
2025-02-16T00:14:29.671Z - Transforming: node_modules/react-native/Libraries/Utilities/mapWithSeparator.js
2025-02-16T00:14:29.674Z - Transforming: node_modules/react-native/Libraries/Inspector/BoxInspector.js
2025-02-16T00:14:29.677Z - Transforming: node_modules/react-native/Libraries/Inspector/StyleInspector.js
2025-02-16T00:14:29.677Z - Transforming: node_modules/react-freeze/src/index.tsx
2025-02-16T00:14:29.700Z - Transforming: node_modules/react-native-pager-view/src/index.tsx
2025-02-16T00:14:29.705Z - Transforming: node_modules/react-native/Libraries/WebSocket/WebSocketInterceptor.js
2025-02-16T00:14:29.706Z - Transforming: node_modules/react-native/Libraries/Network/XHRInterceptor.js
2025-02-16T00:14:29.718Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/flattenStyle.js
2025-02-16T00:14:29.722Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractProps.ts
2025-02-16T00:14:29.723Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/attention-seekers.js
2025-02-16T00:14:29.733Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/bouncing-entrances.js
2025-02-16T00:14:29.739Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/bouncing-exits.js
2025-02-16T00:14:29.740Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/fading-entrances.js
2025-02-16T00:14:29.746Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/fading-exits.js
2025-02-16T00:14:29.748Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/lightspeed.js
2025-02-16T00:14:29.748Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/flippers.js
2025-02-16T00:14:29.749Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/sliding-entrances.js
2025-02-16T00:14:29.756Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/sliding-exits.js
2025-02-16T00:14:29.758Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/zooming-entrances.js
2025-02-16T00:14:29.758Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/definitions/zooming-exits.js
2025-02-16T00:14:29.762Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractText.tsx
2025-02-16T00:14:29.766Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractTransform.ts
2025-02-16T00:14:29.773Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractGradient.ts
2025-02-16T00:14:29.774Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractViewBox.ts
2025-02-16T00:14:29.780Z - Transforming: node_modules/react-native-svg/src/lib/units.ts
2025-02-16T00:14:29.788Z - Transforming: node_modules/react-native-svg/src/lib/maskType.ts
2025-02-16T00:14:29.789Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractPolyPoints.ts
2025-02-16T00:14:29.789Z - Transforming: node_modules/react-native-svg/src/lib/util.ts
2025-02-16T00:14:29.792Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/wrapStyleTransforms.js
2025-02-16T00:14:29.798Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/easing.js
2025-02-16T00:14:29.800Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractFilter.ts
2025-02-16T00:14:29.802Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/getStyleValues.js
2025-02-16T00:14:29.809Z - Transforming: node_modules/react-native-svg/src/elements/filters/FilterPrimitive.tsx
2025-02-16T00:14:29.814Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractResponder.ts
2025-02-16T00:14:29.814Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractOpacity.ts
2025-02-16T00:14:29.821Z - Transforming: node_modules/react-native-svg/src/fabric/NativeSvgViewModule.ts
2025-02-16T00:14:29.826Z - Transforming: node_modules/react-native-svg/src/lib/utils/convertPercentageColor.ts
2025-02-16T00:14:29.826Z - Transforming: node_modules/lodash/_baseIsArguments.js
2025-02-16T00:14:29.827Z - Transforming: node_modules/lodash/isLength.js
2025-02-16T00:14:29.830Z - Transforming: node_modules/lodash/_Promise.js
2025-02-16T00:14:29.831Z - Transforming: node_modules/lodash/_DataView.js
2025-02-16T00:14:29.834Z - Transforming: node_modules/lodash/_Map.js
2025-02-16T00:14:29.834Z - Transforming: node_modules/lodash/_Set.js
2025-02-16T00:14:29.835Z - Transforming: node_modules/lodash/_toSource.js
2025-02-16T00:14:29.835Z - Transforming: node_modules/lodash/_WeakMap.js
2025-02-16T00:14:29.837Z - Transforming: node_modules/lodash/_baseIsTypedArray.js
2025-02-16T00:14:29.838Z - Transforming: node_modules/lodash/_root.js
2025-02-16T00:14:29.838Z - Transforming: node_modules/lodash/stubFalse.js
2025-02-16T00:14:29.839Z - Transforming: node_modules/lodash/_baseClone.js
2025-02-16T00:14:29.841Z - Transforming: node_modules/lodash/_baseUnset.js
2025-02-16T00:14:29.844Z - Transforming: node_modules/lodash/_castPath.js
2025-02-16T00:14:29.850Z - Transforming: node_modules/lodash/_copyObject.js
2025-02-16T00:14:29.862Z - Transforming: node_modules/lodash/_customOmitClone.js
2025-02-16T00:14:29.863Z - Transforming: node_modules/lodash/_getAllKeysIn.js
2025-02-16T00:14:29.864Z - Transforming: node_modules/lodash/_flatRest.js
2025-02-16T00:14:29.865Z - Transforming: node_modules/lodash/_nativeKeys.js
2025-02-16T00:14:29.867Z - Transforming: node_modules/lodash/_basePickBy.js
2025-02-16T00:14:29.869Z - Transforming: node_modules/lodash/_baseIndexOf.js
2025-02-16T00:14:29.871Z - Transforming: node_modules/lodash/values.js
2025-02-16T00:14:29.872Z - Transforming: node_modules/lodash/_arraySome.js
2025-02-16T00:14:29.872Z - Transforming: node_modules/lodash/_baseSome.js
2025-02-16T00:14:29.874Z - Transforming: node_modules/lodash/_isIterateeCall.js
2025-02-16T00:14:29.874Z - Transforming: node_modules/lodash/toFinite.js
2025-02-16T00:14:29.875Z - Transforming: node_modules/lodash/_baseMatches.js
2025-02-16T00:14:29.875Z - Transforming: node_modules/lodash/_baseMatchesProperty.js
2025-02-16T00:14:29.876Z - Transforming: node_modules/lodash/property.js
2025-02-16T00:14:29.879Z - Transforming: node_modules/lodash/_baseIsEqualDeep.js
2025-02-16T00:14:29.880Z - Transforming: node_modules/lodash/_baseInRange.js
2025-02-16T00:14:29.881Z - Transforming: node_modules/lodash/_Symbol.js
2025-02-16T00:14:29.881Z - Transforming: node_modules/lodash/_getRawTag.js
2025-02-16T00:14:29.881Z - Transforming: node_modules/lodash/_objectToString.js
2025-02-16T00:14:29.884Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/dependencies/ContextProvider.js
2025-02-16T00:14:29.885Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/dependencies/DataProvider.js
2025-02-16T00:14:29.885Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/dependencies/GridLayoutProvider.js
2025-02-16T00:14:29.885Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/RecyclerListView.js
2025-02-16T00:14:29.887Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/scrollcomponent/BaseScrollView.js
2025-02-16T00:14:29.888Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/ItemAnimator.js
2025-02-16T00:14:29.899Z - Transforming: node_modules/recyclerlistview/dist/reactnative/utils/AutoScroll.js
2025-02-16T00:14:29.902Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/layoutmanager/GridLayoutManager.js
2025-02-16T00:14:29.903Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/layoutmanager/LayoutManager.js
2025-02-16T00:14:29.907Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/ProgressiveListView.js
2025-02-16T00:14:29.922Z - Transforming: node_modules/recyclerlistview/dist/reactnative/utils/ComponentCompat.js
2025-02-16T00:14:29.935Z - Transforming: node_modules/lodash/filter.js
2025-02-16T00:14:29.936Z - Transforming: node_modules/react-native-calendars/src/calendar/day/marking/style.js
2025-02-16T00:14:29.939Z - Transforming: node_modules/react-native-calendars/src/calendar/day/dot/index.js
2025-02-16T00:14:29.940Z - Transforming: node_modules/lodash/_freeGlobal.js
2025-02-16T00:14:29.944Z - Transforming: node_modules/react-native/Libraries/Core/setUpGlobals.js
2025-02-16T00:14:29.948Z - Transforming: node_modules/react-native/Libraries/Core/setUpPerformance.js
2025-02-16T00:14:29.948Z - Transforming: node_modules/react-native/src/private/setup/setUpDOM.js
2025-02-16T00:14:29.950Z - Transforming: node_modules/react-native/Libraries/Core/setUpErrorHandling.js
2025-02-16T00:14:29.951Z - Transforming: node_modules/react-native/Libraries/Core/polyfillPromise.js
2025-02-16T00:14:29.956Z - Transforming: node_modules/react-native/Libraries/Core/setUpRegeneratorRuntime.js
2025-02-16T00:14:29.957Z - Transforming: node_modules/react-native/Libraries/Core/setUpTimers.js
2025-02-16T00:14:29.965Z - Transforming: node_modules/react-native/Libraries/Core/setUpXHR.js
2025-02-16T00:14:29.965Z - Transforming: node_modules/react-native/Libraries/Core/setUpAlert.js
2025-02-16T00:14:29.969Z - Transforming: node_modules/react-native/Libraries/Core/setUpNavigator.js
2025-02-16T00:14:29.971Z - Transforming: node_modules/react-native/Libraries/Core/setUpSegmentFetcher.js
2025-02-16T00:14:29.972Z - Transforming: node_modules/react-native/Libraries/Core/setUpBatchedBridge.js
2025-02-16T00:14:29.973Z - Transforming: node_modules/react-native/Libraries/Core/checkNativeVersion.js
2025-02-16T00:14:29.976Z - Transforming: node_modules/react-native/Libraries/Core/setUpDeveloperTools.js
2025-02-16T00:14:29.982Z - Transforming: node_modules/lodash/_arrayPush.js
2025-02-16T00:14:29.982Z - Transforming: node_modules/lodash/_isFlattenable.js
2025-02-16T00:14:29.982Z - Transforming: node_modules/lodash/_toKey.js
2025-02-16T00:14:29.986Z - Transforming: node_modules/lodash/_baseEach.js
2025-02-16T00:14:29.987Z - Transforming: node_modules/lodash/_baseTrim.js
2025-02-16T00:14:29.987Z - Transforming: node_modules/react-native-calendars/src/calendar/day/period/style.js
2025-02-16T00:14:29.987Z - Transforming: node_modules/lodash/isSymbol.js
2025-02-16T00:14:29.990Z - Transforming: node_modules/lodash/_defineProperty.js
2025-02-16T00:14:29.991Z - Transforming: node_modules/lodash/_arrayAggregator.js
2025-02-16T00:14:29.992Z - Transforming: node_modules/lodash/_baseAggregator.js
2025-02-16T00:14:29.994Z - Transforming: node_modules/paths-js/path.js
2025-02-16T00:14:29.997Z - Transforming: node_modules/lodash/_createRange.js
2025-02-16T00:14:29.999Z - Transforming: node_modules/call-bind-apply-helpers/actualApply.js
2025-02-16T00:14:30.002Z - Transforming: node_modules/define-data-property/index.js
2025-02-16T00:14:30.002Z - Transforming: node_modules/has-property-descriptors/index.js
2025-02-16T00:14:30.002Z - Transforming: node_modules/object-keys/isArguments.js
2025-02-16T00:14:30.005Z - Transforming: node_modules/object-keys/implementation.js
2025-02-16T00:14:30.009Z - Transforming: node_modules/@babel/runtime/helpers/OverloadYield.js
2025-02-16T00:14:30.010Z - Transforming: node_modules/math-intrinsics/isNaN.js
2025-02-16T00:14:30.011Z - Transforming: node_modules/dunder-proto/get.js
2025-02-16T00:14:30.015Z - Transforming: node_modules/gopd/gOPD.js
2025-02-16T00:14:30.017Z - Transforming: node_modules/function-bind/implementation.js
2025-02-16T00:14:30.018Z - Transforming: node_modules/has-tostringtag/shams.js
2025-02-16T00:14:30.019Z - Transforming: node_modules/safe-regex-test/index.js
2025-02-16T00:14:30.023Z - Transforming: node_modules/for-each/index.js
2025-02-16T00:14:30.024Z - Transforming: node_modules/available-typed-arrays/index.js
2025-02-16T00:14:30.027Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animationsManager.ts
2025-02-16T00:14:30.028Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/componentUtils.ts
2025-02-16T00:14:30.033Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/componentStyle.ts
2025-02-16T00:14:30.037Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/config.ts
2025-02-16T00:14:30.042Z - Transforming: node_modules/react-native-pager-view/src/PagerView.tsx
2025-02-16T00:14:30.044Z - Transforming: node_modules/react-native-pager-view/src/usePagerView.ts
2025-02-16T00:14:30.055Z - Transforming: node_modules/react-native/Libraries/Network/XMLHttpRequest.js
2025-02-16T00:14:30.064Z - Transforming: node_modules/react-native/Libraries/WebSocket/NativeWebSocketModule.js
2025-02-16T00:14:30.077Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractFill.ts
2025-02-16T00:14:30.091Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractStroke.ts
2025-02-16T00:14:30.096Z - Transforming: node_modules/react-native-svg/src/lib/extract/extractLengthList.ts
2025-02-16T00:14:30.099Z - Transforming: node_modules/react-native-svg/src/lib/Matrix2D.ts
2025-02-16T00:14:30.103Z - Transforming: node_modules/react-native-svg/src/lib/extract/transformToRn.js
2025-02-16T00:14:30.103Z - Transforming: node_modules/react-native-svg/src/lib/extract/transform.js
2025-02-16T00:14:30.103Z - Transforming: node_modules/react-native-modal/node_modules/react-native-animatable/getDefaultStyleValue.js
2025-02-16T00:14:30.118Z - Transforming: node_modules/lodash/_assignValue.js
2025-02-16T00:14:30.113Z - Transforming: node_modules/lodash/_getNative.js
2025-02-16T00:14:30.126Z - Transforming: node_modules/lodash/last.js
2025-02-16T00:14:30.139Z - Transforming: node_modules/lodash/_isKey.js
2025-02-16T00:14:30.138Z - Transforming: node_modules/lodash/_parent.js
2025-02-16T00:14:30.148Z - Transforming: node_modules/lodash/_stringToPath.js
2025-02-16T00:14:30.149Z - Transforming: node_modules/lodash/toString.js
2025-02-16T00:14:30.157Z - Transforming: node_modules/lodash/_overRest.js
2025-02-16T00:14:30.159Z - Transforming: node_modules/lodash/isPlainObject.js
2025-02-16T00:14:30.167Z - Transforming: node_modules/lodash/_setToString.js
2025-02-16T00:14:30.167Z - Transforming: node_modules/lodash/_Stack.js
2025-02-16T00:14:30.171Z - Transforming: node_modules/lodash/_arrayEach.js
2025-02-16T00:14:30.174Z - Transforming: node_modules/lodash/_baseAssign.js
2025-02-16T00:14:30.174Z - Transforming: node_modules/lodash/_baseAssignIn.js
2025-02-16T00:14:30.179Z - Transforming: node_modules/lodash/_copySymbols.js
2025-02-16T00:14:30.179Z - Transforming: node_modules/lodash/_cloneBuffer.js
2025-02-16T00:14:30.183Z - Transforming: node_modules/lodash/_copySymbolsIn.js
2025-02-16T00:14:30.185Z - Transforming: node_modules/lodash/_getAllKeys.js
2025-02-16T00:14:30.185Z - Transforming: node_modules/lodash/_initCloneArray.js
2025-02-16T00:14:30.185Z - Transforming: node_modules/lodash/_initCloneByTag.js
2025-02-16T00:14:30.192Z - Transforming: node_modules/lodash/isMap.js
2025-02-16T00:14:30.192Z - Transforming: node_modules/lodash/_initCloneObject.js
2025-02-16T00:14:30.192Z - Transforming: node_modules/lodash/isSet.js
2025-02-16T00:14:30.194Z - Transforming: node_modules/lodash/_copyArray.js
2025-02-16T00:14:30.196Z - Transforming: node_modules/lodash/keysIn.js
2025-02-16T00:14:30.197Z - Transforming: node_modules/lodash/keys.js
2025-02-16T00:14:30.197Z - Transforming: node_modules/lodash/_overArg.js
2025-02-16T00:14:30.197Z - Transforming: node_modules/lodash/_baseGetAllKeys.js
2025-02-16T00:14:30.200Z - Transforming: node_modules/lodash/_getSymbolsIn.js
2025-02-16T00:14:30.201Z - Transforming: node_modules/lodash/_baseIsNaN.js
2025-02-16T00:14:30.202Z - Transforming: node_modules/lodash/_strictIndexOf.js
2025-02-16T00:14:30.202Z - Transforming: node_modules/lodash/_baseSet.js
2025-02-16T00:14:30.204Z - Transforming: node_modules/lodash/_baseValues.js
2025-02-16T00:14:30.207Z - Transforming: node_modules/lodash/_baseIsMatch.js
2025-02-16T00:14:30.207Z - Transforming: node_modules/lodash/_getMatchData.js
2025-02-16T00:14:30.207Z - Transforming: node_modules/lodash/_matchesStrictComparable.js
2025-02-16T00:14:30.208Z - Transforming: node_modules/lodash/eq.js
2025-02-16T00:14:30.209Z - Transforming: node_modules/lodash/_isIndex.js
2025-02-16T00:14:30.213Z - Transforming: node_modules/lodash/_baseProperty.js
2025-02-16T00:14:30.213Z - Transforming: node_modules/lodash/_basePropertyDeep.js
2025-02-16T00:14:30.213Z - Transforming: node_modules/lodash/hasIn.js
2025-02-16T00:14:30.214Z - Transforming: node_modules/lodash/_isStrictComparable.js
2025-02-16T00:14:30.217Z - Transforming: node_modules/lodash/_equalByTag.js
2025-02-16T00:14:30.219Z - Transforming: node_modules/lodash/_equalArrays.js
2025-02-16T00:14:30.219Z - Transforming: node_modules/lodash/_equalObjects.js
2025-02-16T00:14:30.222Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/exceptions/CustomError.js
2025-02-16T00:14:30.223Z - Transforming: node_modules/ts-object-utils/dist/index.js
2025-02-16T00:14:30.229Z - Transforming: node_modules/lodash/_baseFilter.js
2025-02-16T00:14:30.230Z - Transforming: node_modules/react-native-calendars/src/calendar/day/dot/style.js
2025-02-16T00:14:30.232Z - Transforming: node_modules/react-native/src/private/webapis/performance/Performance.js
2025-02-16T00:14:30.233Z - Transforming: node_modules/react-native/src/private/webapis/performance/specs/NativePerformance.js
2025-02-16T00:14:30.233Z - Transforming: node_modules/react-native/Libraries/promiseRejectionTrackingOptions.js
2025-02-16T00:14:30.238Z - Transforming: node_modules/lodash/_arrayFilter.js
2025-02-16T00:14:30.239Z - Transforming: node_modules/react-native/Libraries/Promise.js
2025-02-16T00:14:30.243Z - Transforming: node_modules/react-native/Libraries/Utilities/FeatureDetection.js
2025-02-16T00:14:30.243Z - Transforming: node_modules/regenerator-runtime/runtime.js
2025-02-16T00:14:30.245Z - Transforming: node_modules/react-native/Libraries/Core/Timers/JSTimers.js
2025-02-16T00:14:30.246Z - Transforming: node_modules/react-native/src/private/webapis/microtasks/specs/NativeMicrotasks.js
2025-02-16T00:14:30.246Z - Transforming: node_modules/react-native/src/private/webapis/idlecallbacks/specs/NativeIdleCallbacks.js
2025-02-16T00:14:30.254Z - Transforming: node_modules/react-native/Libraries/Core/Timers/immediateShim.js
2025-02-16T00:14:30.254Z - Transforming: node_modules/react-native/Libraries/Core/Timers/queueMicrotask.js
2025-02-16T00:14:30.259Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/exceptions/RecyclerListViewExceptions.js
2025-02-16T00:14:30.261Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/constants/Constants.js
2025-02-16T00:14:30.263Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/constants/Messages.js
2025-02-16T00:14:30.263Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/VirtualRenderer.js
2025-02-16T00:14:30.264Z - Transforming: node_modules/recyclerlistview/dist/reactnative/platform/reactnative/scrollcomponent/ScrollComponent.js
2025-02-16T00:14:30.276Z - Transforming: node_modules/recyclerlistview/dist/reactnative/platform/reactnative/viewrenderer/ViewRenderer.js
2025-02-16T00:14:30.300Z - Transforming: node_modules/react-native/Libraries/Core/SegmentFetcher/NativeSegmentFetcher.js
2025-02-16T00:14:30.307Z - Transforming: node_modules/react-native/Libraries/Core/ReactNativeVersionCheck.js
2025-02-16T00:14:30.309Z - Transforming: node_modules/react-native/Libraries/WebSocket/WebSocket.js
2025-02-16T00:14:30.312Z - Transforming: node_modules/react-native/Libraries/Blob/File.js
2025-02-16T00:14:30.324Z - Transforming: node_modules/react-native/Libraries/Blob/FileReader.js
2025-02-16T00:14:30.324Z - Transforming: node_modules/react-native/Libraries/Blob/URL.js
2025-02-16T00:14:30.326Z - Transforming: node_modules/react-native/Libraries/HeapCapture/HeapCapture.js
2025-02-16T00:14:30.329Z - Transforming: node_modules/react-native/Libraries/Performance/SamplingProfiler.js
2025-02-16T00:14:30.336Z - Transforming: node_modules/react-native/Libraries/Utilities/HMRClientProdShim.js
2025-02-16T00:14:30.338Z - Transforming: node_modules/react-native/Libraries/Utilities/HMRClient.js
2025-02-16T00:14:30.342Z - Transforming: node_modules/react-native/Libraries/Core/setUpReactDevTools.js
2025-02-16T00:14:30.345Z - Transforming: node_modules/react-native/Libraries/JSInspector/JSInspector.js
2025-02-16T00:14:30.345Z - Transforming: node_modules/react-native/Libraries/JSInspector/NetworkAgent.js
2025-02-16T00:14:30.349Z - Transforming: node_modules/react-native/Libraries/Core/setUpReactRefresh.js
2025-02-16T00:14:30.355Z - Transforming: node_modules/react-native/Libraries/Core/Devtools/loadBundleFromServer.js
2025-02-16T00:14:30.357Z - Transforming: node_modules/lodash/_trimmedEndIndex.js
2025-02-16T00:14:30.360Z - Transforming: node_modules/lodash/_baseForOwn.js
2025-02-16T00:14:30.360Z - Transforming: node_modules/lodash/_createBaseEach.js
2025-02-16T00:14:30.363Z - Transforming: node_modules/call-bind-apply-helpers/reflectApply.js
2025-02-16T00:14:30.365Z - Transforming: node_modules/is-regex/index.js
2025-02-16T00:14:30.366Z - Transforming: node_modules/possible-typed-array-names/index.js
2025-02-16T00:14:30.370Z - Transforming: node_modules/lodash/_baseRange.js
2025-02-16T00:14:30.371Z - Transforming: node_modules/is-callable/index.js
2025-02-16T00:14:30.375Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Bounce.web.ts
2025-02-16T00:14:30.376Z - Transforming: node_modules/paths-js/geom.js
2025-02-16T00:14:30.385Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Fade.web.ts
2025-02-16T00:14:30.386Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Flip.web.ts
2025-02-16T00:14:30.399Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Lightspeed.web.ts
2025-02-16T00:14:30.405Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Pinwheel.web.ts
2025-02-16T00:14:30.408Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Roll.web.ts
2025-02-16T00:14:30.419Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Rotate.web.ts
2025-02-16T00:14:30.425Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Slide.web.ts
2025-02-16T00:14:30.438Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Zoom.web.ts
2025-02-16T00:14:30.441Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animation/Stretch.web.ts
2025-02-16T00:14:30.443Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/createAnimation.ts
2025-02-16T00:14:30.447Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeWebSocketModule.js
2025-02-16T00:14:30.458Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/Easing.web.ts
2025-02-16T00:14:30.459Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/transition/Curved.web.ts
2025-02-16T00:14:30.462Z - Transforming: node_modules/react-native-pager-view/src/specs/PagerViewNativeComponent.ts
2025-02-16T00:14:30.462Z - Transforming: node_modules/react-native-pager-view/src/utils.tsx
2025-02-16T00:14:30.471Z - Transforming: node_modules/react-native-pager-view/src/specs/LEGACY_PagerViewNativeComponent.ts
2025-02-16T00:14:30.472Z - Transforming: node_modules/lodash/_getValue.js
2025-02-16T00:14:30.472Z - Transforming: node_modules/lodash/_baseIsNative.js
2025-02-16T00:14:30.476Z - Transforming: node_modules/lodash/_memoizeCapped.js
2025-02-16T00:14:30.479Z - Transforming: node_modules/lodash/_baseToString.js
2025-02-16T00:14:30.479Z - Transforming: node_modules/lodash/_apply.js
2025-02-16T00:14:30.481Z - Transforming: node_modules/lodash/_getPrototype.js
2025-02-16T00:14:30.484Z - Transforming: node_modules/lodash/_baseSetToString.js
2025-02-16T00:14:30.485Z - Transforming: node_modules/lodash/_shortOut.js
2025-02-16T00:14:30.485Z - Transforming: node_modules/lodash/_stackClear.js
2025-02-16T00:14:30.488Z - Transforming: node_modules/lodash/_ListCache.js
2025-02-16T00:14:30.488Z - Transforming: node_modules/lodash/_stackDelete.js
2025-02-16T00:14:30.488Z - Transforming: node_modules/lodash/_stackGet.js
2025-02-16T00:14:30.491Z - Transforming: node_modules/lodash/_stackHas.js
2025-02-16T00:14:30.491Z - Transforming: node_modules/lodash/_getSymbols.js
2025-02-16T00:14:30.491Z - Transforming: node_modules/lodash/_stackSet.js
2025-02-16T00:14:30.493Z - Transforming: node_modules/lodash/_cloneArrayBuffer.js
2025-02-16T00:14:30.494Z - Transforming: node_modules/lodash/_cloneDataView.js
2025-02-16T00:14:30.495Z - Transforming: node_modules/lodash/_cloneRegExp.js
2025-02-16T00:14:30.499Z - Transforming: node_modules/lodash/_cloneTypedArray.js
2025-02-16T00:14:30.499Z - Transforming: node_modules/lodash/_cloneSymbol.js
2025-02-16T00:14:30.500Z - Transforming: node_modules/lodash/_baseIsMap.js
2025-02-16T00:14:30.502Z - Transforming: node_modules/lodash/_baseIsSet.js
2025-02-16T00:14:30.505Z - Transforming: node_modules/lodash/_baseCreate.js
2025-02-16T00:14:30.505Z - Transforming: node_modules/lodash/_baseKeysIn.js
2025-02-16T00:14:30.506Z - Transforming: node_modules/lodash/stubArray.js
2025-02-16T00:14:30.508Z - Transforming: node_modules/lodash/_baseHasIn.js
2025-02-16T00:14:30.508Z - Transforming: node_modules/lodash/_SetCache.js
2025-02-16T00:14:30.508Z - Transforming: node_modules/lodash/_hasPath.js
2025-02-16T00:14:30.512Z - Transforming: node_modules/lodash/_cacheHas.js
2025-02-16T00:14:30.512Z - Transforming: node_modules/ts-object-utils/dist/ObjectUtil.js
2025-02-16T00:14:30.514Z - Transforming: node_modules/ts-object-utils/dist/Default.js
2025-02-16T00:14:30.514Z - Transforming: node_modules/lodash/_arrayLikeKeys.js
2025-02-16T00:14:30.516Z - Transforming: node_modules/lodash/_Uint8Array.js
2025-02-16T00:14:30.519Z - Transforming: node_modules/lodash/_setToArray.js
2025-02-16T00:14:30.519Z - Transforming: node_modules/lodash/_mapToArray.js
2025-02-16T00:14:30.519Z - Transforming: node_modules/promise/setimmediate/es6-extensions.js
2025-02-16T00:14:30.520Z - Transforming: node_modules/promise/setimmediate/finally.js
2025-02-16T00:14:30.522Z - Transforming: node_modules/promise/setimmediate/rejection-tracking.js
2025-02-16T00:14:30.524Z - Transforming: node_modules/pretty-format/build/index.js
2025-02-16T00:14:30.524Z - Transforming: node_modules/react-native/src/private/webapis/performance/EventTiming.js
2025-02-16T00:14:30.524Z - Transforming: node_modules/react-native/src/private/webapis/performance/PerformanceEntry.js
2025-02-16T00:14:30.531Z - Transforming: node_modules/react-native/src/private/webapis/performance/MemoryInfo.js
2025-02-16T00:14:30.531Z - Transforming: node_modules/react-native/src/private/webapis/performance/PerformanceObserver.js
2025-02-16T00:14:30.532Z - Transforming: node_modules/react-native/src/private/webapis/performance/RawPerformanceEntry.js
2025-02-16T00:14:30.539Z - Transforming: node_modules/react-native/src/private/webapis/performance/ReactNativeStartupTiming.js
2025-02-16T00:14:30.541Z - Transforming: node_modules/react-native/src/private/webapis/performance/specs/NativePerformanceObserver.js
2025-02-16T00:14:30.543Z - Transforming: node_modules/react-native/src/private/webapis/performance/UserTiming.js
2025-02-16T00:14:30.553Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/scrollcomponent/BaseScrollComponent.js
2025-02-16T00:14:30.554Z - Transforming: node_modules/recyclerlistview/dist/reactnative/utils/TSCast.js
2025-02-16T00:14:30.558Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeSegmentFetcher.js
2025-02-16T00:14:30.558Z - Transforming: node_modules/recyclerlistview/dist/reactnative/utils/RecycleItemPool.js
2025-02-16T00:14:30.562Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/ViewabilityTracker.js
2025-02-16T00:14:30.571Z - Transforming: node_modules/react-native/Libraries/Core/ReactNativeVersion.js
2025-02-16T00:14:30.571Z - Transforming: node_modules/react-native/Libraries/Core/Timers/NativeTiming.js
2025-02-16T00:14:30.572Z - Transforming: node_modules/recyclerlistview/dist/reactnative/core/viewrenderer/BaseViewRenderer.js
2025-02-16T00:14:30.573Z - Transforming: node_modules/react-native/Libraries/Performance/NativeJSCSamplingProfiler.js
2025-02-16T00:14:30.574Z - Transforming: node_modules/react-native/Libraries/HeapCapture/NativeJSCHeapCapture.js
2025-02-16T00:14:30.579Z - Transforming: node_modules/react-native/Libraries/Blob/URLSearchParams.js
2025-02-16T00:14:30.579Z - Transforming: node_modules/react-refresh/runtime.js
2025-02-16T00:14:30.580Z - Transforming: node_modules/react-native/src/private/fusebox/setUpFuseboxReactDevToolsDispatcher.js
2025-02-16T00:14:30.584Z - Transforming: node_modules/react-native/Libraries/DevToolsSettings/DevToolsSettingsManager.ios.js
2025-02-16T00:14:30.586Z - Transforming: node_modules/react-devtools-core/dist/backend.js
2025-02-16T00:14:30.594Z - Transforming: node_modules/react-native/Libraries/Blob/NativeFileReaderModule.js
2025-02-16T00:14:30.597Z - Transforming: node_modules/metro-runtime/src/modules/HMRClient.js
2025-02-16T00:14:30.598Z - Transforming: node_modules/react-native/Libraries/Utilities/DevLoadingView.js
2025-02-16T00:14:30.600Z - Transforming: node_modules/lodash/_baseFor.js
2025-02-16T00:14:30.605Z - Transforming: node_modules/react-native/Libraries/WebSocket/WebSocketEvent.js
2025-02-16T00:14:30.605Z - Transforming: node_modules/react-native/Libraries/JSInspector/InspectorAgent.js
2025-02-16T00:14:30.606Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/animationParser.ts
2025-02-16T00:14:30.609Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/transition/Linear.web.ts
2025-02-16T00:14:30.618Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/transition/Sequenced.web.ts
2025-02-16T00:14:30.621Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/transition/Jumping.web.ts
2025-02-16T00:14:30.622Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/transition/Fading.web.ts
2025-02-16T00:14:30.622Z - Transforming: node_modules/react-native-reanimated/src/layoutReanimation/web/transition/EntryExit.web.ts
2025-02-16T00:14:30.632Z - Transforming: node_modules/lodash/memoize.js
2025-02-16T00:14:30.633Z - Transforming: node_modules/lodash/constant.js
2025-02-16T00:14:30.632Z - Transforming: node_modules/lodash/_isMasked.js
2025-02-16T00:14:30.638Z - Transforming: node_modules/lodash/_listCacheClear.js
2025-02-16T00:14:30.638Z - Transforming: node_modules/lodash/_listCacheDelete.js
2025-02-16T00:14:30.639Z - Transforming: node_modules/lodash/_listCacheGet.js
2025-02-16T00:14:30.640Z - Transforming: node_modules/lodash/_listCacheHas.js
2025-02-16T00:14:30.640Z - Transforming: node_modules/lodash/_listCacheSet.js
2025-02-16T00:14:30.641Z - Transforming: node_modules/lodash/_MapCache.js
2025-02-16T00:14:30.643Z - Transforming: node_modules/lodash/_setCacheAdd.js
2025-02-16T00:14:30.644Z - Transforming: node_modules/lodash/_setCacheHas.js
2025-02-16T00:14:30.645Z - Transforming: node_modules/lodash/_nativeKeysIn.js
2025-02-16T00:14:30.645Z - Transforming: node_modules/react-native/src/private/webapis/performance/LongTasks.js
2025-02-16T00:14:30.648Z - Transforming: node_modules/promise/setimmediate/core.js
2025-02-16T00:14:30.649Z - Transforming: node_modules/pretty-format/build/collections.js
2025-02-16T00:14:30.649Z - Transforming: node_modules/pretty-format/node_modules/ansi-styles/index.js
2025-02-16T00:14:30.650Z - Transforming: node_modules/pretty-format/build/plugins/AsymmetricMatcher.js
2025-02-16T00:14:30.659Z - Transforming: node_modules/pretty-format/build/plugins/DOMCollection.js
2025-02-16T00:14:30.660Z - Transforming: node_modules/pretty-format/build/plugins/DOMElement.js
2025-02-16T00:14:30.665Z - Transforming: node_modules/pretty-format/build/plugins/Immutable.js
2025-02-16T00:14:30.671Z - Transforming: node_modules/pretty-format/build/plugins/ReactElement.js
2025-02-16T00:14:30.672Z - Transforming: node_modules/pretty-format/build/plugins/ReactTestComponent.js
2025-02-16T00:14:30.676Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeTiming.js
2025-02-16T00:14:30.677Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeJSCHeapCapture.js
2025-02-16T00:14:30.686Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeJSCSamplingProfiler.js
2025-02-16T00:14:30.688Z - Transforming: node_modules/recyclerlistview/dist/reactnative/utils/BinarySearch.js
2025-02-16T00:14:30.688Z - Transforming: node_modules/react-refresh/cjs/react-refresh-runtime.development.js
2025-02-16T00:14:30.689Z - Transforming: node_modules/lodash/_createBaseFor.js
2025-02-16T00:14:30.695Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeFileReaderModule.js
2025-02-16T00:14:30.698Z - Transforming: node_modules/react-native/Libraries/Utilities/NativeDevLoadingView.js
2025-02-16T00:14:30.700Z - Transforming: node_modules/metro-runtime/src/modules/vendor/eventemitter3.js
2025-02-16T00:14:30.701Z - Transforming: node_modules/lodash/_coreJsData.js
2025-02-16T00:14:30.705Z - Transforming: node_modules/lodash/_assocIndexOf.js
2025-02-16T00:14:30.707Z - Transforming: node_modules/lodash/_mapCacheClear.js
2025-02-16T00:14:30.708Z - Transforming: node_modules/lodash/_mapCacheDelete.js
2025-02-16T00:14:30.713Z - Transforming: node_modules/lodash/_mapCacheHas.js
2025-02-16T00:14:30.714Z - Transforming: node_modules/lodash/_mapCacheSet.js
2025-02-16T00:14:30.715Z - Transforming: node_modules/lodash/_mapCacheGet.js
2025-02-16T00:14:30.721Z - Transforming: node_modules/pretty-format/build/plugins/lib/markup.js
2025-02-16T00:14:30.724Z - Transforming: node_modules/react-native/src/private/specs/modules/NativeDevLoadingView.js
2025-02-16T00:14:30.724Z - Transforming: node_modules/pretty-format/node_modules/react-is/index.js
2025-02-16T00:14:30.725Z - Transforming: node_modules/lodash/_Hash.js
2025-02-16T00:14:30.729Z - Transforming: node_modules/lodash/_getMapData.js
2025-02-16T00:14:30.730Z - Transforming: node_modules/lodash/_hashClear.js
2025-02-16T00:14:30.734Z - Transforming: node_modules/lodash/_hashGet.js
2025-02-16T00:14:30.734Z - Transforming: node_modules/lodash/_hashDelete.js
2025-02-16T00:14:30.736Z - Transforming: node_modules/lodash/_hashHas.js
2025-02-16T00:14:30.737Z - Transforming: node_modules/lodash/_hashSet.js
2025-02-16T00:14:30.739Z - Transforming: node_modules/pretty-format/node_modules/react-is/cjs/react-is.development.js
2025-02-16T00:14:30.741Z - Transforming: node_modules/pretty-format/build/plugins/lib/escapeHTML.js
2025-02-16T00:14:30.740Z - Transforming: node_modules/lodash/_nativeCreate.js
2025-02-16T00:14:30.742Z - Transforming: node_modules/lodash/_isKeyable.js
2025-02-16T00:14:31.428Z - Transforming:  polyfill:environment-variables
2025-02-16T00:14:31.427Z - Transforming: node_modules/metro-runtime/src/polyfills/require.js
2025-02-16T00:14:31.428Z - Transforming:  polyfill:external-require
2025-02-16T00:14:31.428Z - Transforming: node_modules/@react-native/js-polyfills/console.js
2025-02-16T00:14:31.428Z - Transforming: node_modules/@react-native/js-polyfills/error-guard.js
